# Docker Compose configuration for Billionaires Social
# Includes Flutter app, n8n, Firebase emulators, and supporting services

version: '3.8'

services:
  # =============================================================================
  # Flutter App - Development
  # =============================================================================
  flutter-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: billionaires-social-dev
    ports:
      - "3000:3000"   # Flutter web dev server
      - "8080:8080"   # Flutter debug port
    volumes:
      - .:/app
      - flutter_pub_cache:/root/.pub-cache
      - android_gradle_cache:/root/.gradle
    environment:
      - FLUTTER_ENV=development
      - NODE_ENV=development
      - FIREBASE_PROJECT_ID=billionaires-social
      - N8N_WEBHOOK_URL=http://n8n:5678
    depends_on:
      - firebase-emulator
      - n8n
    networks:
      - billionaires-network
    stdin_open: true
    tty: true

  # =============================================================================
  # Flutter App - Production
  # =============================================================================
  flutter-prod:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: billionaires-social-prod
    ports:
      - "80:80"
    environment:
      - FLUTTER_ENV=production
      - NODE_ENV=production
      - FIREBASE_PROJECT_ID=billionaires-social
    depends_on:
      - n8n
    networks:
      - billionaires-network
    restart: unless-stopped
    profiles:
      - production

  # =============================================================================
  # n8n Workflow Automation
  # =============================================================================
  n8n:
    image: n8nio/n8n:latest
    container_name: billionaires-n8n
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=billionaires123
      - N8N_HOST=0.0.0.0
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - WEBHOOK_URL=http://localhost:5678
      - GENERIC_TIMEZONE=UTC
      - N8N_LOG_LEVEL=info
      - N8N_METRICS=true
      - N8N_DIAGNOSTICS_ENABLED=false
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=n8n
      - DB_POSTGRESDB_USER=n8n
      - DB_POSTGRESDB_PASSWORD=n8n_password
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./docs/n8n_workflows:/home/<USER>/.n8n/workflows:ro
    depends_on:
      - postgres
    networks:
      - billionaires-network
    restart: unless-stopped

  # =============================================================================
  # PostgreSQL Database for n8n
  # =============================================================================
  postgres:
    image: postgres:15-alpine
    container_name: billionaires-postgres
    environment:
      - POSTGRES_DB=n8n
      - POSTGRES_USER=n8n
      - POSTGRES_PASSWORD=n8n_password
      - POSTGRES_NON_ROOT_USER=n8n
      - POSTGRES_NON_ROOT_PASSWORD=n8n_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - billionaires-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U n8n -d n8n"]
      interval: 10s
      timeout: 5s
      retries: 5

  # =============================================================================
  # Firebase Emulator Suite
  # =============================================================================
  firebase-emulator:
    image: node:22-alpine
    container_name: billionaires-firebase-emulator
    working_dir: /app
    command: >
      sh -c "
        npm install -g firebase-tools &&
        firebase emulators:start --only firestore,auth,storage,functions --host 0.0.0.0
      "
    ports:
      - "4000:4000"   # Emulator UI
      - "8081:8081"   # Firestore
      - "9099:9099"   # Auth
      - "9199:9199"   # Storage
      - "5001:5001"   # Functions
    volumes:
      - .:/app
      - firebase_emulator_data:/app/.firebase
    environment:
      - FIREBASE_PROJECT_ID=billionaires-social
      - GCLOUD_PROJECT=billionaires-social
    networks:
      - billionaires-network
    profiles:
      - development

  # =============================================================================
  # Redis Cache (for session management and caching)
  # =============================================================================
  redis:
    image: redis:7-alpine
    container_name: billionaires-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - billionaires-network
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis_password

  # =============================================================================
  # Nginx Reverse Proxy (for production)
  # =============================================================================
  nginx:
    image: nginx:alpine
    container_name: billionaires-nginx
    ports:
      - "443:443"
      - "8443:8443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - flutter-prod
      - n8n
    networks:
      - billionaires-network
    restart: unless-stopped
    profiles:
      - production

  # =============================================================================
  # Monitoring and Logging
  # =============================================================================
  prometheus:
    image: prom/prometheus:latest
    container_name: billionaires-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - billionaires-network
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: billionaires-grafana
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./docker/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    depends_on:
      - prometheus
    networks:
      - billionaires-network
    profiles:
      - monitoring

# =============================================================================
# Networks
# =============================================================================
networks:
  billionaires-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# =============================================================================
# Volumes
# =============================================================================
volumes:
  # Flutter and build caches
  flutter_pub_cache:
    driver: local
  android_gradle_cache:
    driver: local
  
  # n8n data
  n8n_data:
    driver: local
  
  # Database data
  postgres_data:
    driver: local
  redis_data:
    driver: local
  
  # Firebase emulator data
  firebase_emulator_data:
    driver: local
  
  # Monitoring data
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  
  # Logs
  nginx_logs:
    driver: local
