# Development Environment Configuration for Billionaires Social
# This file contains development-specific settings

# =============================================================================
# Application Configuration
# =============================================================================
APP_ENV=development
FLUTTER_ENV=development
NODE_ENV=development
DEBUG=true

# =============================================================================
# Firebase Configuration (Development)
# =============================================================================
FIREBASE_PROJECT_ID=billionaires-social-dev
FIREBASE_USE_EMULATOR=true

# Firebase Emulator Configuration
FIREBASE_EMULATOR_HOST=firebase-emulator
FIRESTORE_EMULATOR_HOST=firebase-emulator:8081
FIREBASE_AUTH_EMULATOR_HOST=firebase-emulator:9099
FIREBASE_STORAGE_EMULATOR_HOST=firebase-emulator:9199
FIREBASE_FUNCTIONS_EMULATOR_HOST=firebase-emulator:5001

# =============================================================================
# n8n Configuration (Development)
# =============================================================================
N8N_HOST=n8n
N8N_PORT=5678
N8N_PROTOCOL=http
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=billionaires123

N8N_BASE_URL=http://n8n:5678
N8N_WEBHOOK_URL=http://n8n:5678
N8N_WEBHOOK_SECRET=dev_webhook_secret_123

# =============================================================================
# Database Configuration (Development)
# =============================================================================
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=n8n
POSTGRES_USER=n8n
POSTGRES_PASSWORD=n8n_password

REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=redis_password
REDIS_DB=0

# =============================================================================
# Security Configuration (Development)
# =============================================================================
JWT_SECRET=dev_jwt_secret_key_for_development_only
ENCRYPTION_KEY=dev_encryption_key_32_characters
WEBHOOK_SECRET=dev_webhook_secret_key
API_SECRET_KEY=dev_api_secret_key

# =============================================================================
# Development Server Configuration
# =============================================================================
FLUTTER_DEV_PORT=3000
FLUTTER_DEBUG_PORT=8080
FLUTTER_HOT_RELOAD=true
FLUTTER_DEBUG_MODE=true

# =============================================================================
# External Services (Development/Test Keys)
# =============================================================================
# Use test/development keys for external services
AGORA_APP_ID=test_agora_app_id
GIPHY_API_KEY=test_giphy_api_key
UNSPLASH_ACCESS_KEY=test_unsplash_access_key
SENTRY_DSN=test_sentry_dsn
SENTRY_ENVIRONMENT=development

# =============================================================================
# Logging and Monitoring (Development)
# =============================================================================
LOG_LEVEL=debug
LOG_FORMAT=pretty
PROMETHEUS_ENABLED=false
ANALYTICS_ENABLED=false

# =============================================================================
# Feature Flags (Development)
# =============================================================================
FEATURE_MULTI_ACCOUNT=true
FEATURE_N8N_INTEGRATION=true
FEATURE_VIDEO_CALLING=true
FEATURE_STORY_CREATION=true
FEATURE_REEL_CREATION=true
FEATURE_ANALYTICS=false
FEATURE_PUSH_NOTIFICATIONS=false

# =============================================================================
# Development-specific Settings
# =============================================================================
SSL_ENABLED=false
RATE_LIMIT_ENABLED=false
CACHE_ENABLED=false
BACKUP_ENABLED=false

# File upload settings for development
MAX_FILE_SIZE=50MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,mp4,mov,avi,pdf,doc,docx,txt

# =============================================================================
# Docker Development Configuration
# =============================================================================
DOCKER_NETWORK=billionaires-network
MEMORY_LIMIT=1g
CPU_LIMIT=0.5
