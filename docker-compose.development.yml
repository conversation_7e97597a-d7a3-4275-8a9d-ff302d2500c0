# Docker Compose override for development environment
# This file extends docker-compose.yml with development-specific configurations

version: '3.8'

services:
  # =============================================================================
  # Flutter Development Overrides
  # =============================================================================
  flutter-dev:
    build:
      target: development
    volumes:
      - .:/app
      - flutter_pub_cache:/root/.pub-cache
      - android_gradle_cache:/root/.gradle
      - /app/build  # Exclude build directory from volume mount
    environment:
      - FLUTTER_ENV=development
      - DEBUG=true
      - FLUTTER_WEB_AUTO_DETECT=true
    ports:
      - "3000:3000"   # Flutter web dev server
      - "8080:8080"   # Flutter debug port
      - "9229:9229"   # Node.js debug port
    command: >
      sh -c "
        echo 'Starting Flutter development server...' &&
        Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 &
        flutter doctor &&
        flutter pub get &&
        flutter run -d web-server --web-hostname 0.0.0.0 --web-port 3000 --hot
      "

  # =============================================================================
  # Firebase Emulator Development Configuration
  # =============================================================================
  firebase-emulator:
    profiles:
      - development
    environment:
      - FIREBASE_PROJECT_ID=billionaires-social-dev
      - GCLOUD_PROJECT=billionaires-social-dev
      - DEBUG=true
    volumes:
      - .:/app
      - firebase_emulator_data:/app/.firebase
    command: >
      sh -c "
        npm install -g firebase-tools &&
        echo 'Starting Firebase emulators...' &&
        firebase emulators:start --only firestore,auth,storage,functions --host 0.0.0.0 --import=./firebase-export --export-on-exit=./firebase-export
      "

  # =============================================================================
  # n8n Development Configuration
  # =============================================================================
  n8n:
    environment:
      - N8N_LOG_LEVEL=debug
      - N8N_DIAGNOSTICS_ENABLED=true
      - WEBHOOK_URL=http://localhost:5678
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./docs/n8n_workflows:/home/<USER>/.n8n/workflows:ro
      - ./n8n-backup:/backup:rw

  # =============================================================================
  # PostgreSQL Development Configuration
  # =============================================================================
  postgres:
    environment:
      - POSTGRES_DB=n8n_dev
      - POSTGRES_USER=n8n_dev
      - POSTGRES_PASSWORD=n8n_dev_password
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./docker/postgres/init-dev.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"  # Expose for development access

  # =============================================================================
  # Redis Development Configuration
  # =============================================================================
  redis:
    command: redis-server --appendonly yes --requirepass redis_dev_password
    ports:
      - "6379:6379"  # Expose for development access
    volumes:
      - redis_dev_data:/data

  # =============================================================================
  # Development Tools
  # =============================================================================
  # Flutter web test runner
  flutter-test:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: billionaires-flutter-test
    volumes:
      - .:/app
      - flutter_pub_cache:/root/.pub-cache
    environment:
      - FLUTTER_ENV=test
      - CHROME_EXECUTABLE=/usr/bin/chromium-browser
    command: >
      sh -c "
        Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 &
        flutter test --coverage
      "
    profiles:
      - testing
    networks:
      - billionaires-network

  # Database admin tool
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: billionaires-pgadmin
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin123
    ports:
      - "8081:80"
    depends_on:
      - postgres
    profiles:
      - development
      - tools
    networks:
      - billionaires-network

  # Redis admin tool
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: billionaires-redis-commander
    environment:
      - REDIS_HOSTS=local:redis:6379:0:redis_dev_password
    ports:
      - "8082:8081"
    depends_on:
      - redis
    profiles:
      - development
      - tools
    networks:
      - billionaires-network

# =============================================================================
# Development-specific volumes
# =============================================================================
volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  firebase_emulator_data:
    driver: local
