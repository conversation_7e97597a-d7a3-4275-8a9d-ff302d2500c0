-- Development database initialization script for PostgreSQL
-- This script sets up the development database with necessary extensions and initial data

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- Create additional databases if needed
CREATE DATABASE n8n_test;

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE n8n_dev TO n8n_dev;
GRANT ALL PRIVILEGES ON DATABASE n8n_test TO n8n_dev;

-- Create development-specific tables or data
-- (Add any development-specific initialization here)
