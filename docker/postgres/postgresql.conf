# PostgreSQL configuration for production
# Optimized for containerized deployment

# Connection settings
listen_addresses = '*'
port = 5432
max_connections = 100

# Memory settings
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# Checkpoint settings
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100

# Logging settings
log_destination = 'stderr'
logging_collector = off
log_min_messages = warning
log_min_error_statement = error
log_min_duration_statement = 1000

# Performance settings
random_page_cost = 1.1
effective_io_concurrency = 200

# Replication settings (if needed)
# wal_level = replica
# max_wal_senders = 3
# wal_keep_segments = 32

# Security settings
ssl = off
password_encryption = scram-sha-256
