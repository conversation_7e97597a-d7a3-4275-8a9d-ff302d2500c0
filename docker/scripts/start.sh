#!/bin/bash
# Startup script for Billionaires Social production container
# Handles both web app serving and Firebase Functions

set -e

echo "🚀 Starting Billionaires Social production services..."

# Function to check if a service is ready
wait_for_service() {
    local host=$1
    local port=$2
    local service_name=$3
    local max_attempts=30
    local attempt=1

    echo "⏳ Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if nc -z "$host" "$port" 2>/dev/null; then
            echo "✅ $service_name is ready!"
            return 0
        fi
        
        echo "🔄 Attempt $attempt/$max_attempts: $service_name not ready yet..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo "❌ $service_name failed to start within expected time"
    return 1
}

# Function to start Firebase Functions
start_firebase_functions() {
    echo "🔥 Starting Firebase Functions..."
    
    cd /app/functions
    
    # Check if functions are built
    if [ ! -d "lib" ]; then
        echo "📦 Building Firebase Functions..."
        npm run build
    fi
    
    # Start Firebase Functions in background
    firebase serve --only functions --host 0.0.0.0 --port 5001 &
    FUNCTIONS_PID=$!
    
    echo "🔥 Firebase Functions started with PID: $FUNCTIONS_PID"
    echo $FUNCTIONS_PID > /tmp/functions.pid
}

# Function to setup health check endpoint
setup_health_check() {
    echo "🏥 Setting up health check..."
    
    # Create health check endpoint
    cat > /usr/share/nginx/html/health <<EOF
{
    "status": "healthy",
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "services": {
        "nginx": "running",
        "firebase_functions": "running"
    },
    "version": "${APP_VERSION:-1.0.0}"
}
EOF
}

# Function to handle graceful shutdown
cleanup() {
    echo "🛑 Shutting down services gracefully..."
    
    # Stop Firebase Functions
    if [ -f /tmp/functions.pid ]; then
        FUNCTIONS_PID=$(cat /tmp/functions.pid)
        if kill -0 $FUNCTIONS_PID 2>/dev/null; then
            echo "🔥 Stopping Firebase Functions (PID: $FUNCTIONS_PID)..."
            kill -TERM $FUNCTIONS_PID
            wait $FUNCTIONS_PID 2>/dev/null || true
        fi
        rm -f /tmp/functions.pid
    fi
    
    # Stop nginx
    echo "🌐 Stopping nginx..."
    nginx -s quit
    
    echo "✅ Graceful shutdown completed"
    exit 0
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT

# Main startup sequence
main() {
    echo "🔧 Configuring environment..."
    
    # Set default environment variables if not provided
    export FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID:-billionaires-social}
    export NODE_ENV=${NODE_ENV:-production}
    export FLUTTER_ENV=${FLUTTER_ENV:-production}
    
    # Create necessary directories
    mkdir -p /var/log/nginx
    mkdir -p /tmp
    
    # Setup health check
    setup_health_check
    
    # Start Firebase Functions if functions directory exists
    if [ -d "/app/functions" ]; then
        start_firebase_functions
        
        # Wait for Functions to be ready
        wait_for_service localhost 5001 "Firebase Functions"
    else
        echo "⚠️  No Firebase Functions found, skipping..."
    fi
    
    # Test nginx configuration
    echo "🧪 Testing nginx configuration..."
    nginx -t
    
    if [ $? -ne 0 ]; then
        echo "❌ nginx configuration test failed"
        exit 1
    fi
    
    # Start nginx in foreground
    echo "🌐 Starting nginx..."
    exec nginx -g "daemon off;"
}

# Check if running as root (required for nginx)
if [ "$(id -u)" != "0" ]; then
    echo "❌ This script must be run as root"
    exit 1
fi

# Install netcat if not available (for service checks)
if ! command -v nc &> /dev/null; then
    echo "📦 Installing netcat..."
    apk add --no-cache netcat-openbsd
fi

# Install curl if not available (for health checks)
if ! command -v curl &> /dev/null; then
    echo "📦 Installing curl..."
    apk add --no-cache curl
fi

# Run main function
main "$@"
