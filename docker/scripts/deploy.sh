#!/bin/bash
# Deployment script for Billionaires Social Docker containers
# Handles both development and production deployments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="development"
ACTION="up"
SERVICES=""
DETACHED=true
BUILD=false
PULL=false
RECREATE=false
VERBOSE=false

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS] [ACTION] [SERVICES...]

Deploy and manage Billionaires Social Docker containers

ACTIONS:
    up          Start services (default)
    down        Stop and remove services
    restart     Restart services
    logs        Show service logs
    status      Show service status
    shell       Open shell in service container

OPTIONS:
    -e, --environment ENV    Environment (development|production) [default: development]
    -d, --detach            Run in detached mode [default: true]
    -f, --foreground        Run in foreground mode
    -b, --build             Build images before starting
    -p, --pull              Pull latest images before starting
    -r, --recreate          Recreate containers
    -v, --verbose           Verbose output
    -h, --help              Show this help message

SERVICES:
    flutter-dev             Flutter development server
    flutter-prod            Flutter production server
    n8n                     n8n workflow automation
    postgres                PostgreSQL database
    redis                   Redis cache
    firebase-emulator       Firebase emulator suite
    nginx                   Nginx reverse proxy
    prometheus              Prometheus monitoring
    grafana                 Grafana dashboards

EXAMPLES:
    $0                                      # Start development environment
    $0 -e production                       # Start production environment
    $0 down                                # Stop all services
    $0 logs flutter-dev                    # Show Flutter dev logs
    $0 shell n8n                          # Open shell in n8n container
    $0 -e production -b up flutter-prod   # Build and start production Flutter

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -d|--detach)
            DETACHED=true
            shift
            ;;
        -f|--foreground)
            DETACHED=false
            shift
            ;;
        -b|--build)
            BUILD=true
            shift
            ;;
        -p|--pull)
            PULL=true
            shift
            ;;
        -r|--recreate)
            RECREATE=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        up|down|restart|logs|status|shell)
            ACTION="$1"
            shift
            ;;
        *)
            # Assume remaining arguments are service names
            SERVICES="$SERVICES $1"
            shift
            ;;
    esac
done

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(development|production)$ ]]; then
    print_error "Invalid environment: $ENVIRONMENT. Must be 'development' or 'production'"
    exit 1
fi

# Set compose file based on environment
COMPOSE_FILE="docker-compose.yml"
if [[ -f "docker-compose.$ENVIRONMENT.yml" ]]; then
    COMPOSE_FILE="$COMPOSE_FILE:docker-compose.$ENVIRONMENT.yml"
fi

# Add testing compose file if needed
if [[ "$ACTION" == "test" ]] && [[ -f "docker-compose.test.yml" ]]; then
    COMPOSE_FILE="$COMPOSE_FILE:docker-compose.test.yml"
fi

# Set environment file
ENV_FILE=".env.$ENVIRONMENT"
if [[ ! -f "$ENV_FILE" ]]; then
    ENV_FILE=".env.template"
    print_warning "Environment file $ENV_FILE not found, using template"
fi

# Docker compose command base
COMPOSE_CMD="docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE"

# Add profile for production
if [[ "$ENVIRONMENT" == "production" ]]; then
    COMPOSE_CMD="$COMPOSE_CMD --profile production"
elif [[ "$ENVIRONMENT" == "development" ]]; then
    COMPOSE_CMD="$COMPOSE_CMD --profile development"
fi

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running"
        exit 1
    fi
    
    # Check if docker-compose is available
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not available"
        exit 1
    fi
    
    # Check if we're in the right directory
    if [[ ! -f "docker-compose.yml" ]]; then
        print_error "docker-compose.yml not found. Run this script from the project root."
        exit 1
    fi
    
    # Check environment file
    if [[ ! -f "$ENV_FILE" ]]; then
        print_error "Environment file $ENV_FILE not found"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to prepare deployment
prepare_deployment() {
    print_status "Preparing deployment for $ENVIRONMENT environment..."
    
    # Create necessary directories
    mkdir -p {logs,data/{postgres,redis,n8n,grafana,prometheus}}
    
    # Set proper permissions
    chmod -R 755 docker/scripts/ 2>/dev/null || true
    
    # Create networks if they don't exist
    if ! docker network ls | grep -q billionaires-network; then
        print_status "Creating Docker network..."
        docker network create billionaires-network --subnet=**********/16 || true
    fi
    
    print_success "Deployment preparation completed"
}

# Function to handle service actions
handle_action() {
    local action=$1
    local services=$2
    
    case $action in
        up)
            start_services "$services"
            ;;
        down)
            stop_services "$services"
            ;;
        restart)
            restart_services "$services"
            ;;
        logs)
            show_logs "$services"
            ;;
        status)
            show_status "$services"
            ;;
        shell)
            open_shell "$services"
            ;;
        *)
            print_error "Unknown action: $action"
            exit 1
            ;;
    esac
}

# Function to start services
start_services() {
    local services=$1
    
    print_status "Starting services in $ENVIRONMENT environment..."
    
    local cmd_args=""
    
    if [[ "$BUILD" == "true" ]]; then
        cmd_args="$cmd_args --build"
    fi
    
    if [[ "$PULL" == "true" ]]; then
        cmd_args="$cmd_args --pull"
    fi
    
    if [[ "$RECREATE" == "true" ]]; then
        cmd_args="$cmd_args --force-recreate"
    fi
    
    if [[ "$DETACHED" == "true" ]]; then
        cmd_args="$cmd_args -d"
    fi
    
    if [[ "$VERBOSE" == "true" ]]; then
        cmd_args="$cmd_args --verbose"
    fi
    
    # Execute docker-compose up
    if eval "$COMPOSE_CMD up $cmd_args $services"; then
        print_success "Services started successfully"
        
        if [[ "$DETACHED" == "true" ]]; then
            print_status "Services are running in the background"
            show_status "$services"
        fi
    else
        print_error "Failed to start services"
        exit 1
    fi
}

# Function to stop services
stop_services() {
    local services=$1
    
    print_status "Stopping services..."
    
    if eval "$COMPOSE_CMD down $services"; then
        print_success "Services stopped successfully"
    else
        print_error "Failed to stop services"
        exit 1
    fi
}

# Function to restart services
restart_services() {
    local services=$1
    
    print_status "Restarting services..."
    
    if eval "$COMPOSE_CMD restart $services"; then
        print_success "Services restarted successfully"
    else
        print_error "Failed to restart services"
        exit 1
    fi
}

# Function to show logs
show_logs() {
    local services=$1
    
    if [[ -z "$services" ]]; then
        print_status "Showing logs for all services..."
    else
        print_status "Showing logs for: $services"
    fi
    
    eval "$COMPOSE_CMD logs -f $services"
}

# Function to show status
show_status() {
    local services=$1
    
    print_status "Service Status:"
    eval "$COMPOSE_CMD ps $services"
    
    print_status "Network Status:"
    docker network ls | grep billionaires || true
    
    print_status "Volume Status:"
    docker volume ls | grep billionaires || true
}

# Function to open shell
open_shell() {
    local service=$1
    
    if [[ -z "$service" ]]; then
        print_error "Service name required for shell access"
        exit 1
    fi
    
    print_status "Opening shell in $service container..."
    
    # Try different shell commands
    if eval "$COMPOSE_CMD exec $service /bin/bash"; then
        return 0
    elif eval "$COMPOSE_CMD exec $service /bin/sh"; then
        return 0
    else
        print_error "Failed to open shell in $service container"
        exit 1
    fi
}

# Function to show deployment summary
show_summary() {
    print_status "Deployment Summary:"
    echo "  Environment: $ENVIRONMENT"
    echo "  Action: $ACTION"
    echo "  Services: ${SERVICES:-"all"}"
    echo "  Compose File: $COMPOSE_FILE"
    echo "  Environment File: $ENV_FILE"
    
    if [[ "$ACTION" == "up" ]]; then
        print_status "Access URLs (when services are running):"
        case $ENVIRONMENT in
            development)
                echo "  Flutter App: http://localhost:3000"
                echo "  n8n: http://localhost:5678"
                echo "  Firebase Emulator: http://localhost:4000"
                echo "  Prometheus: http://localhost:9090"
                echo "  Grafana: http://localhost:3001"
                ;;
            production)
                echo "  Flutter App: https://localhost"
                echo "  n8n Admin: https://localhost:8443"
                echo "  Prometheus: http://localhost:9090"
                echo "  Grafana: http://localhost:3001"
                ;;
        esac
    fi
}

# Main execution
main() {
    print_status "Starting deployment for Billionaires Social"
    
    # Check prerequisites
    check_prerequisites
    
    # Prepare deployment
    prepare_deployment
    
    # Handle the requested action
    handle_action "$ACTION" "$SERVICES"
    
    # Show summary
    show_summary
    
    print_success "Deployment completed successfully!"
}

# Run main function
main "$@"
