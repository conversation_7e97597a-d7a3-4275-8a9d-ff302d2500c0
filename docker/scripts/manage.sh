#!/bin/bash
# Management script for Billionaires Social Docker environment
# Provides utilities for maintenance, monitoring, and troubleshooting

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [COMMAND] [OPTIONS]

Management utilities for Billionaires Social Docker environment

COMMANDS:
    health          Check health of all services
    cleanup         Clean up unused Docker resources
    backup          Backup application data
    restore         Restore application data from backup
    update          Update all services to latest versions
    reset           Reset environment (WARNING: destroys all data)
    monitor         Show real-time resource usage
    debug           Debug service issues
    ssl             Manage SSL certificates
    db              Database management utilities

OPTIONS:
    -e, --environment ENV    Environment (development|production) [default: development]
    -v, --verbose           Verbose output
    -h, --help              Show this help message

EXAMPLES:
    $0 health                           # Check health of all services
    $0 cleanup                          # Clean up unused resources
    $0 backup -e production            # Backup production data
    $0 debug n8n                       # Debug n8n service issues
    $0 ssl generate                     # Generate SSL certificates

EOF
}

# Default values
ENVIRONMENT="development"
VERBOSE=false
COMMAND=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        health|cleanup|backup|restore|update|reset|monitor|debug|ssl|db)
            COMMAND="$1"
            shift
            ;;
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            # Pass remaining arguments to command handlers
            break
            ;;
    esac
done

# Set compose command
COMPOSE_CMD="docker-compose -f docker-compose.yml --env-file .env.$ENVIRONMENT"

# Function to check service health
check_health() {
    print_status "Checking health of all services..."
    
    # Check Docker daemon
    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running"
        return 1
    fi
    
    # Check running containers
    print_status "Running containers:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    # Check service-specific health
    local services=("flutter-dev" "flutter-prod" "n8n" "postgres" "redis")
    
    for service in "${services[@]}"; do
        if docker ps | grep -q "$service"; then
            local container_id=$(docker ps -q -f name="$service")
            if [[ -n "$container_id" ]]; then
                local health=$(docker inspect --format='{{.State.Health.Status}}' "$container_id" 2>/dev/null || echo "no-healthcheck")
                case $health in
                    "healthy")
                        print_success "$service: healthy"
                        ;;
                    "unhealthy")
                        print_error "$service: unhealthy"
                        ;;
                    "starting")
                        print_warning "$service: starting"
                        ;;
                    *)
                        print_status "$service: running (no health check)"
                        ;;
                esac
            fi
        else
            print_warning "$service: not running"
        fi
    done
    
    # Check disk usage
    print_status "Docker disk usage:"
    docker system df
    
    # Check network connectivity
    print_status "Network connectivity:"
    if docker network ls | grep -q billionaires-network; then
        print_success "billionaires-network: exists"
    else
        print_warning "billionaires-network: not found"
    fi
}

# Function to cleanup Docker resources
cleanup_resources() {
    print_status "Cleaning up Docker resources..."
    
    # Remove stopped containers
    print_status "Removing stopped containers..."
    docker container prune -f
    
    # Remove unused images
    print_status "Removing unused images..."
    docker image prune -f
    
    # Remove unused volumes
    print_status "Removing unused volumes..."
    docker volume prune -f
    
    # Remove unused networks
    print_status "Removing unused networks..."
    docker network prune -f
    
    # Show space saved
    print_status "Current Docker disk usage:"
    docker system df
    
    print_success "Cleanup completed"
}

# Function to backup data
backup_data() {
    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    print_status "Creating backup in $backup_dir..."
    
    # Backup PostgreSQL database
    if docker ps | grep -q postgres; then
        print_status "Backing up PostgreSQL database..."
        docker exec billionaires-postgres pg_dump -U n8n n8n > "$backup_dir/postgres_backup.sql"
        print_success "PostgreSQL backup completed"
    fi
    
    # Backup Redis data
    if docker ps | grep -q redis; then
        print_status "Backing up Redis data..."
        docker exec billionaires-redis redis-cli BGSAVE
        docker cp billionaires-redis:/data/dump.rdb "$backup_dir/redis_backup.rdb"
        print_success "Redis backup completed"
    fi
    
    # Backup n8n workflows
    if docker ps | grep -q n8n; then
        print_status "Backing up n8n workflows..."
        docker cp billionaires-n8n:/home/<USER>/.n8n "$backup_dir/n8n_backup"
        print_success "n8n backup completed"
    fi
    
    # Backup environment files
    print_status "Backing up configuration files..."
    cp .env.* "$backup_dir/" 2>/dev/null || true
    cp docker-compose*.yml "$backup_dir/" 2>/dev/null || true
    
    # Create backup manifest
    cat > "$backup_dir/manifest.txt" << EOF
Backup created: $(date)
Environment: $ENVIRONMENT
Services backed up:
- PostgreSQL database
- Redis data
- n8n workflows
- Configuration files
EOF
    
    print_success "Backup completed: $backup_dir"
}

# Function to restore data
restore_data() {
    local backup_dir=$1
    
    if [[ -z "$backup_dir" ]]; then
        print_error "Backup directory required"
        echo "Usage: $0 restore <backup_directory>"
        exit 1
    fi
    
    if [[ ! -d "$backup_dir" ]]; then
        print_error "Backup directory not found: $backup_dir"
        exit 1
    fi
    
    print_warning "This will overwrite existing data. Are you sure? (y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        print_status "Restore cancelled"
        exit 0
    fi
    
    print_status "Restoring from backup: $backup_dir"
    
    # Restore PostgreSQL database
    if [[ -f "$backup_dir/postgres_backup.sql" ]]; then
        print_status "Restoring PostgreSQL database..."
        docker exec -i billionaires-postgres psql -U n8n -d n8n < "$backup_dir/postgres_backup.sql"
        print_success "PostgreSQL restore completed"
    fi
    
    # Restore Redis data
    if [[ -f "$backup_dir/redis_backup.rdb" ]]; then
        print_status "Restoring Redis data..."
        docker cp "$backup_dir/redis_backup.rdb" billionaires-redis:/data/dump.rdb
        docker restart billionaires-redis
        print_success "Redis restore completed"
    fi
    
    # Restore n8n workflows
    if [[ -d "$backup_dir/n8n_backup" ]]; then
        print_status "Restoring n8n workflows..."
        docker cp "$backup_dir/n8n_backup/." billionaires-n8n:/home/<USER>/.n8n/
        docker restart billionaires-n8n
        print_success "n8n restore completed"
    fi
    
    print_success "Restore completed"
}

# Function to update services
update_services() {
    print_status "Updating all services to latest versions..."
    
    # Pull latest images
    eval "$COMPOSE_CMD pull"
    
    # Rebuild custom images
    eval "$COMPOSE_CMD build --pull"
    
    # Restart services with new images
    eval "$COMPOSE_CMD up -d"
    
    print_success "Services updated successfully"
}

# Function to reset environment
reset_environment() {
    print_warning "This will destroy ALL data and containers. Are you sure? (y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        print_status "Reset cancelled"
        exit 0
    fi
    
    print_status "Resetting environment..."
    
    # Stop and remove all containers
    eval "$COMPOSE_CMD down -v --remove-orphans"
    
    # Remove all related images
    docker images | grep billionaires | awk '{print $3}' | xargs docker rmi -f 2>/dev/null || true
    
    # Remove volumes
    docker volume ls | grep billionaires | awk '{print $2}' | xargs docker volume rm 2>/dev/null || true
    
    # Remove network
    docker network rm billionaires-network 2>/dev/null || true
    
    print_success "Environment reset completed"
}

# Function to monitor resources
monitor_resources() {
    print_status "Monitoring resource usage (Press Ctrl+C to stop)..."
    
    while true; do
        clear
        echo "=== Billionaires Social - Resource Monitor ==="
        echo "Time: $(date)"
        echo
        
        # Container stats
        echo "Container Resource Usage:"
        docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
        
        echo
        echo "System Resources:"
        echo "Disk Usage: $(df -h / | awk 'NR==2{print $5}')"
        echo "Memory Usage: $(free -h | awk 'NR==2{printf "%.1f%%", $3/$2*100}')"
        
        sleep 5
    done
}

# Function to debug service issues
debug_service() {
    local service=$1
    
    if [[ -z "$service" ]]; then
        print_error "Service name required"
        echo "Usage: $0 debug <service_name>"
        exit 1
    fi
    
    print_status "Debugging service: $service"
    
    # Show container status
    print_status "Container status:"
    docker ps -f name="$service"
    
    # Show recent logs
    print_status "Recent logs (last 50 lines):"
    docker logs --tail 50 "billionaires-$service" 2>/dev/null || docker logs --tail 50 "$service"
    
    # Show resource usage
    print_status "Resource usage:"
    docker stats --no-stream "$service" 2>/dev/null || true
    
    # Show network connectivity
    print_status "Network connectivity:"
    docker exec "$service" ping -c 3 google.com 2>/dev/null || print_warning "Network test failed"
}

# Main command handler
case $COMMAND in
    health)
        check_health
        ;;
    cleanup)
        cleanup_resources
        ;;
    backup)
        backup_data
        ;;
    restore)
        restore_data "$1"
        ;;
    update)
        update_services
        ;;
    reset)
        reset_environment
        ;;
    monitor)
        monitor_resources
        ;;
    debug)
        debug_service "$1"
        ;;
    *)
        print_error "Unknown command: $COMMAND"
        show_usage
        exit 1
        ;;
esac
