#!/bin/bash
# Build script for Billionaires Social Docker containers
# Supports both development and production builds

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="development"
BUILD_TARGET=""
PUSH_TO_REGISTRY=false
REGISTRY_URL=""
IMAGE_TAG="latest"
VERBOSE=false

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Build Docker containers for Billionaires Social

OPTIONS:
    -e, --environment ENV    Environment to build for (development|production) [default: development]
    -t, --target TARGET      Build target (development|build|production) [default: auto-detect]
    -p, --push              Push images to registry after build
    -r, --registry URL      Registry URL for pushing images
    --tag TAG               Image tag [default: latest]
    -v, --verbose           Verbose output
    -h, --help              Show this help message

EXAMPLES:
    $0                                          # Build development environment
    $0 -e production                           # Build production environment
    $0 -e production -p -r docker.io/myorg    # Build and push to registry
    $0 --target development --verbose         # Build development target with verbose output

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -t|--target)
            BUILD_TARGET="$2"
            shift 2
            ;;
        -p|--push)
            PUSH_TO_REGISTRY=true
            shift
            ;;
        -r|--registry)
            REGISTRY_URL="$2"
            shift 2
            ;;
        --tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(development|production)$ ]]; then
    print_error "Invalid environment: $ENVIRONMENT. Must be 'development' or 'production'"
    exit 1
fi

# Auto-detect build target if not specified
if [[ -z "$BUILD_TARGET" ]]; then
    case "$ENVIRONMENT" in
        development)
            BUILD_TARGET="development"
            ;;
        production)
            BUILD_TARGET="production"
            ;;
    esac
fi

# Set image names
APP_IMAGE="billionaires-social"
if [[ -n "$REGISTRY_URL" ]]; then
    APP_IMAGE="$REGISTRY_URL/$APP_IMAGE"
fi

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running"
        exit 1
    fi
    
    # Check if docker-compose is available
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_warning "Docker Compose is not available. Some features may not work."
    fi
    
    # Check if we're in the right directory
    if [[ ! -f "pubspec.yaml" ]] || [[ ! -f "Dockerfile" ]]; then
        print_error "This script must be run from the project root directory"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to prepare build context
prepare_build_context() {
    print_status "Preparing build context..."
    
    # Create necessary directories
    mkdir -p docker/{nginx,scripts,prometheus,grafana/{dashboards,datasources}}
    
    # Copy environment file if it exists
    if [[ -f ".env.$ENVIRONMENT" ]]; then
        cp ".env.$ENVIRONMENT" ".env"
        print_status "Using environment file: .env.$ENVIRONMENT"
    elif [[ -f ".env.template" ]]; then
        cp ".env.template" ".env"
        print_warning "Using template environment file. Please configure actual values."
    fi
    
    # Ensure scripts are executable
    chmod +x docker/scripts/*.sh 2>/dev/null || true
    
    print_success "Build context prepared"
}

# Function to build Docker image
build_image() {
    local target=$1
    local image_name="$APP_IMAGE:$IMAGE_TAG-$target"
    
    print_status "Building Docker image: $image_name (target: $target)"
    
    # Build arguments
    local build_args=""
    if [[ "$VERBOSE" == "true" ]]; then
        build_args="--progress=plain"
    fi
    
    # Add build context arguments
    build_args="$build_args --build-arg ENVIRONMENT=$ENVIRONMENT"
    build_args="$build_args --build-arg BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')"
    build_args="$build_args --build-arg VCS_REF=$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')"
    
    # Build the image
    if docker build \
        $build_args \
        --target "$target" \
        --tag "$image_name" \
        --file Dockerfile \
        .; then
        print_success "Successfully built: $image_name"
        
        # Tag as latest for the target
        docker tag "$image_name" "$APP_IMAGE:$target"
        print_status "Tagged as: $APP_IMAGE:$target"
        
        return 0
    else
        print_error "Failed to build: $image_name"
        return 1
    fi
}

# Function to push image to registry
push_image() {
    local target=$1
    local image_name="$APP_IMAGE:$IMAGE_TAG-$target"
    
    if [[ "$PUSH_TO_REGISTRY" != "true" ]]; then
        return 0
    fi
    
    if [[ -z "$REGISTRY_URL" ]]; then
        print_warning "Registry URL not specified, skipping push"
        return 0
    fi
    
    print_status "Pushing image to registry: $image_name"
    
    if docker push "$image_name"; then
        print_success "Successfully pushed: $image_name"
        
        # Also push the target tag
        docker push "$APP_IMAGE:$target"
        print_success "Successfully pushed: $APP_IMAGE:$target"
        
        return 0
    else
        print_error "Failed to push: $image_name"
        return 1
    fi
}

# Function to clean up build artifacts
cleanup() {
    print_status "Cleaning up build artifacts..."
    
    # Remove temporary .env file if it was created from template
    if [[ -f ".env" ]] && [[ -f ".env.template" ]]; then
        if cmp -s ".env" ".env.template"; then
            rm -f ".env"
            print_status "Removed temporary .env file"
        fi
    fi
    
    # Clean up dangling images
    if docker images -f "dangling=true" -q | grep -q .; then
        print_status "Removing dangling images..."
        docker rmi $(docker images -f "dangling=true" -q) 2>/dev/null || true
    fi
    
    print_success "Cleanup completed"
}

# Function to show build summary
show_summary() {
    print_status "Build Summary:"
    echo "  Environment: $ENVIRONMENT"
    echo "  Build Target: $BUILD_TARGET"
    echo "  Image Tag: $IMAGE_TAG"
    echo "  Registry: ${REGISTRY_URL:-"local only"}"
    echo "  Push to Registry: $PUSH_TO_REGISTRY"
    
    # Show built images
    print_status "Built images:"
    docker images "$APP_IMAGE" --format "table {{.Repository}}:{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
}

# Main execution
main() {
    print_status "Starting Docker build for Billionaires Social"
    print_status "Environment: $ENVIRONMENT, Target: $BUILD_TARGET"
    
    # Check prerequisites
    check_prerequisites
    
    # Prepare build context
    prepare_build_context
    
    # Build the image
    if build_image "$BUILD_TARGET"; then
        # Push to registry if requested
        push_image "$BUILD_TARGET"
        
        # Show summary
        show_summary
        
        print_success "Build completed successfully!"
    else
        print_error "Build failed!"
        exit 1
    fi
}

# Set up cleanup trap
trap cleanup EXIT

# Run main function
main "$@"
