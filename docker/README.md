# Docker Configuration for Billionaires Social

This directory contains the complete Docker configuration for the Billionaires Social Flutter application, including support for n8n workflow automation, Firebase services, and comprehensive monitoring.

## 🏗️ Architecture Overview

The Docker setup uses a multi-stage approach with the following components:

### Core Services
- **Flutter App**: Multi-stage build supporting both development and production
- **n8n**: Workflow automation platform for social media scheduling and user engagement
- **PostgreSQL**: Database for n8n workflows and application data
- **Redis**: Caching and session management
- **Firebase Emulator**: Local development environment for Firebase services

### Supporting Services
- **Nginx**: Reverse proxy and SSL termination for production
- **Prometheus**: Metrics collection and monitoring
- **Grafana**: Visualization and dashboards

## 📁 Directory Structure

```
docker/
├── README.md                 # This documentation
├── nginx/
│   └── nginx.conf           # Nginx reverse proxy configuration
├── scripts/
│   ├── build.sh            # Build Docker images
│   ├── deploy.sh           # Deploy and manage services
│   ├── manage.sh           # Management utilities
│   └── start.sh            # Production startup script
├── prometheus/
│   └── prometheus.yml      # Prometheus monitoring configuration
└── grafana/
    ├── dashboards/         # Grafana dashboard definitions
    └── datasources/        # Grafana datasource configurations
        └── prometheus.yml
```

## 🚀 Quick Start

### Prerequisites

1. **Docker & Docker Compose**: Install Docker Desktop or Docker Engine with Compose
2. **Git**: For cloning and version control
3. **Environment Configuration**: Copy and configure environment files

### Development Setup

1. **Clone and Configure**:
   ```bash
   git clone <repository-url>
   cd billionaires_social
   cp .env.template .env.development
   # Edit .env.development with your configuration
   ```

2. **Start Development Environment**:
   ```bash
   # Using the deployment script
   ./docker/scripts/deploy.sh -e development

   # Or using docker-compose directly
   docker-compose --profile development up -d
   ```

3. **Access Services**:
   - Flutter App: http://localhost:3000
   - n8n Workflows: http://localhost:5678
   - Firebase Emulator: http://localhost:4000
   - Prometheus: http://localhost:9090
   - Grafana: http://localhost:3001

### Production Setup

1. **Configure Production Environment**:
   ```bash
   cp .env.template .env.production
   # Edit .env.production with production credentials
   ```

2. **Build Production Images**:
   ```bash
   ./docker/scripts/build.sh -e production
   ```

3. **Deploy Production Services**:
   ```bash
   ./docker/scripts/deploy.sh -e production
   ```

4. **Access Production Services**:
   - Flutter App: https://localhost (or your domain)
   - n8n Admin: https://localhost:8443
   - Monitoring: http://localhost:9090 (Prometheus), http://localhost:3001 (Grafana)

## 🛠️ Management Scripts

### Build Script (`docker/scripts/build.sh`)

Build Docker images for different environments:

```bash
# Development build
./docker/scripts/build.sh -e development

# Production build with registry push
./docker/scripts/build.sh -e production -p -r docker.io/myorg

# Build with verbose output
./docker/scripts/build.sh -e production --verbose
```

**Options**:
- `-e, --environment`: Target environment (development|production)
- `-t, --target`: Build target (development|build|production)
- `-p, --push`: Push to registry after build
- `-r, --registry`: Registry URL for pushing
- `--tag`: Image tag (default: latest)
- `-v, --verbose`: Verbose output

### Deploy Script (`docker/scripts/deploy.sh`)

Deploy and manage services:

```bash
# Start development environment
./docker/scripts/deploy.sh -e development

# Start specific services
./docker/scripts/deploy.sh up flutter-dev n8n

# View logs
./docker/scripts/deploy.sh logs flutter-dev

# Stop all services
./docker/scripts/deploy.sh down

# Open shell in container
./docker/scripts/deploy.sh shell n8n
```

**Commands**:
- `up`: Start services (default)
- `down`: Stop and remove services
- `restart`: Restart services
- `logs`: Show service logs
- `status`: Show service status
- `shell`: Open shell in service container

### Management Script (`docker/scripts/manage.sh`)

Maintenance and monitoring utilities:

```bash
# Check health of all services
./docker/scripts/manage.sh health

# Clean up unused Docker resources
./docker/scripts/manage.sh cleanup

# Backup application data
./docker/scripts/manage.sh backup -e production

# Monitor resource usage
./docker/scripts/manage.sh monitor

# Debug service issues
./docker/scripts/manage.sh debug n8n
```

**Commands**:
- `health`: Check service health
- `cleanup`: Clean unused resources
- `backup`: Backup application data
- `restore`: Restore from backup
- `update`: Update services
- `reset`: Reset environment (destroys data)
- `monitor`: Real-time resource monitoring
- `debug`: Debug service issues

## 🔧 Configuration

### Environment Files

The Docker setup uses environment-specific configuration files:

- `.env.template`: Template with all available options
- `.env.development`: Development-specific settings
- `.env.production`: Production-specific settings

**Key Configuration Sections**:

1. **Application Settings**: App name, version, environment
2. **Firebase Configuration**: Project ID, API keys, emulator settings
3. **n8n Configuration**: API keys, webhook URLs, database settings
4. **Security Settings**: JWT secrets, encryption keys
5. **External Services**: Agora, Giphy, Unsplash API keys
6. **Monitoring**: Prometheus, Grafana settings

### Service Configuration

#### Flutter App
- **Development**: Hot reload enabled, debug mode, Firebase emulators
- **Production**: Optimized build, SSL enabled, production Firebase

#### n8n Workflows
- **Database**: PostgreSQL for workflow storage
- **Authentication**: Basic auth with configurable credentials
- **Webhooks**: Secure webhook endpoints for Flutter app integration

#### Monitoring
- **Prometheus**: Metrics collection from all services
- **Grafana**: Pre-configured dashboards for application monitoring

## 🔒 Security Considerations

### Development Environment
- Uses test credentials and disabled security features
- Firebase emulators for safe development
- No SSL/TLS encryption (HTTP only)

### Production Environment
- Strong passwords and API keys required
- SSL/TLS encryption enabled
- Rate limiting and security headers
- Webhook signature validation
- Network isolation between services

### Best Practices
1. **Never commit real credentials** to version control
2. **Use strong, unique passwords** for all services
3. **Enable SSL/TLS** in production
4. **Regularly update** Docker images and dependencies
5. **Monitor logs** for security issues
6. **Backup data regularly**

## 🐛 Troubleshooting

### Common Issues

1. **Port Conflicts**:
   ```bash
   # Check what's using a port
   lsof -i :3000
   
   # Kill process using port
   kill -9 $(lsof -t -i:3000)
   ```

2. **Permission Issues**:
   ```bash
   # Fix script permissions
   chmod +x docker/scripts/*.sh
   
   # Fix Docker socket permissions (Linux)
   sudo usermod -aG docker $USER
   ```

3. **Out of Disk Space**:
   ```bash
   # Clean up Docker resources
   ./docker/scripts/manage.sh cleanup
   
   # Remove all unused data
   docker system prune -a --volumes
   ```

4. **Service Won't Start**:
   ```bash
   # Check service logs
   ./docker/scripts/deploy.sh logs <service-name>
   
   # Debug specific service
   ./docker/scripts/manage.sh debug <service-name>
   ```

### Health Checks

Monitor service health:

```bash
# Overall health check
./docker/scripts/manage.sh health

# Check specific service
docker ps
docker logs <container-name>

# Check resource usage
docker stats
```

### Log Analysis

Access logs for debugging:

```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f flutter-dev

# Last N lines
docker-compose logs --tail 100 n8n
```

## 📊 Monitoring and Metrics

### Prometheus Metrics

Access Prometheus at http://localhost:9090 to view:
- Container resource usage
- n8n workflow execution metrics
- Database performance
- Network traffic

### Grafana Dashboards

Access Grafana at http://localhost:3001 (admin/admin123) for:
- Application performance dashboards
- Infrastructure monitoring
- Custom alerting rules

### Log Aggregation

Logs are available through:
- Docker Compose logs
- Individual container logs
- Centralized logging (if configured)

## 🔄 CI/CD Integration

The Docker configuration supports CI/CD pipelines:

```yaml
# Example GitHub Actions workflow
- name: Build Docker Image
  run: ./docker/scripts/build.sh -e production --tag ${{ github.sha }}

- name: Deploy to Production
  run: ./docker/scripts/deploy.sh -e production
```

## 🚀 Quick Commands Reference

### Development Workflow
```bash
# Start development environment
./docker/scripts/deploy.sh -e development

# View Flutter app logs
./docker/scripts/deploy.sh logs flutter-dev

# Open shell in n8n container
./docker/scripts/deploy.sh shell n8n

# Check health of all services
./docker/scripts/manage.sh health

# Stop all services
./docker/scripts/deploy.sh down
```

### Production Deployment
```bash
# Build production images
./docker/scripts/build.sh -e production

# Deploy to production
./docker/scripts/deploy.sh -e production

# Monitor resources
./docker/scripts/manage.sh monitor

# Backup production data
./docker/scripts/manage.sh backup -e production
```

### Maintenance
```bash
# Clean up unused resources
./docker/scripts/manage.sh cleanup

# Update all services
./docker/scripts/manage.sh update

# Debug service issues
./docker/scripts/manage.sh debug <service-name>
```

## 📚 Additional Resources

- [Docker Documentation](https://docs.docker.com/)
- [Docker Compose Reference](https://docs.docker.com/compose/)
- [n8n Documentation](https://docs.n8n.io/)
- [Firebase Documentation](https://firebase.google.com/docs)
- [Flutter Docker Guide](https://flutter.dev/docs/deployment/docker)

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section above
2. Review service logs using the management scripts
3. Consult the individual service documentation
4. Create an issue in the project repository
