# Prometheus configuration for Billionaires Social monitoring

global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # n8n metrics
  - job_name: 'n8n'
    static_configs:
      - targets: ['n8n:5678']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Node exporter (if available)
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']

  # Docker daemon metrics (if enabled)
  - job_name: 'docker'
    static_configs:
      - targets: ['host.docker.internal:9323']

  # PostgreSQL metrics (if pg_exporter is available)
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  # Redis metrics (if redis_exporter is available)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  # Nginx metrics (if nginx-prometheus-exporter is available)
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']

# Alerting configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Remote write configuration (for external monitoring services)
# remote_write:
#   - url: "https://your-remote-prometheus-endpoint/api/v1/write"
#     basic_auth:
#       username: "your-username"
#       password: "your-password"
