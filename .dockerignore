# Docker ignore file for Billionaires Social Flutter App
# Optimizes build performance by excluding unnecessary files

# =============================================================================
# Flutter Build Artifacts
# =============================================================================
build/
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
coverage/

# =============================================================================
# Platform-specific build directories
# =============================================================================
android/app/build/
android/.gradle/
android/gradle/
android/gradlew
android/gradlew.bat
android/local.properties
android/key.properties
android/app/release/
android/app/debug/

ios/build/
ios/Pods/
ios/Podfile.lock
ios/.symlinks/
ios/Flutter/flutter_assets/
ios/Runner.xcworkspace/xcuserdata/
ios/Runner.xcodeproj/xcuserdata/
ios/Runner.xcodeproj/project.xcworkspace/xcuserdata/

macos/build/
macos/Pods/
macos/Podfile.lock
macos/.symlinks/
macos/Flutter/flutter_assets/

linux/build/
windows/build/
web/build/

# =============================================================================
# Firebase and Functions
# =============================================================================
functions/node_modules/
functions/lib/
functions/.runtimeconfig.json
functions/firebase-debug.log
functions/firebase-debug.*.log
functions/ui-debug.log
functions/firestore-debug.log
functions/pubsub-debug.log
functions/storage-debug.log
functions/deploy*.log

# Firebase emulator data
.firebase/
firebase-debug.log
firebase-debug.*.log
firestore-debug.log
ui-debug.log
database-debug.log
pubsub-debug.log
storage-debug.log

# =============================================================================
# Development and IDE files
# =============================================================================
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# IntelliJ
*.iml
*.ipr
*.iws
.idea/

# Android Studio
.gradle/
local.properties
*.log

# =============================================================================
# Version Control
# =============================================================================
.git/
.gitignore
.gitattributes
.gitmodules

# =============================================================================
# Documentation and Reports
# =============================================================================
*.md
docs/
README*
CHANGELOG*
LICENSE*
CONTRIBUTING*

# Exclude specific documentation that might be needed
!docker/README.md

# =============================================================================
# Test files and coverage
# =============================================================================
test/
test_driver/
integration_test/
coverage/
*.coverage
.nyc_output/

# =============================================================================
# Logs and temporary files
# =============================================================================
*.log
logs/
*.tmp
*.temp
.tmp/
.temp/

# Health check logs
health-check-*.log
scheduler*.log
pglite-debug.log

# =============================================================================
# App Store and Distribution
# =============================================================================
app_store/
app_store_assets/
build_output/
*.aab
*.apk
*.ipa
*.dmg
*.exe
*.msi

# =============================================================================
# Scripts and automation
# =============================================================================
*.sh
scripts/
automated-*.sh
comprehensive-*.sh
daily-*.sh
deploy-*.sh
fix_*.sh
health-*.sh
monitor-*.sh
schedule-*.sh
setup-*.sh
test-*.sh

# Keep essential Docker scripts
!docker/*.sh

# =============================================================================
# Analysis and debugging
# =============================================================================
analysis_*.txt
debug-info/
*.diagnostic
dependency_diagnostic.dart
performance_*.dart
validate_*.dart
fix_*.dart
final_*.dart

# =============================================================================
# Environment and configuration files (sensitive)
# =============================================================================
.env
.env.local
.env.development
.env.production
.env.staging
*.env

# Keep template files
!.env.template
!.env.example

# =============================================================================
# Security and keys
# =============================================================================
*.key
*.pem
*.p12
*.keystore
*.jks
google-services.json
GoogleService-Info.plist
firebase-adminsdk-*.json

# Keep template/example files
!google-services.json.template
!GoogleService-Info.plist.template

# =============================================================================
# Node.js (for functions)
# =============================================================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# =============================================================================
# Miscellaneous
# =============================================================================
*.zip
*.tar.gz
*.rar
*.7z

# Temporary Flutter files
.metadata
.flutter-plugins-dependencies

# Generated files
*.g.dart
*.freezed.dart
*.mocks.dart

# Keep essential generated files that might be needed
!lib/firebase_options.dart

# =============================================================================
# Docker specific
# =============================================================================
Dockerfile*
docker-compose*.yml
.dockerignore

# Keep the main files
!Dockerfile
!docker-compose.yml
