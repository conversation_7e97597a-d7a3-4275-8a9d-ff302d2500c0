<?xml version="1.0" encoding="UTF-8"?>
<svg width="1024" height="1024" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
  <!-- Luxury gradient background -->
  <defs>
    <radialGradient id="luxuryGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#000000;stop-opacity:1" />
    </radialGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="512" cy="512" r="512" fill="url(#luxuryGradient)"/>
  
  <!-- Silver border -->
  <circle cx="512" cy="512" r="490" fill="none" stroke="#C0C0C0" stroke-width="20"/>
  
  <!-- Stylized "B" letter -->
  <g transform="translate(512, 512)" filter="url(#shadow)">
    <!-- Main vertical bar -->
    <rect x="-120" y="-180" width="60" height="360" fill="white"/>
    
    <!-- Top curve -->
    <path d="M -60 -180 L 80 -180 Q 140 -180 140 -120 Q 140 -60 80 -60 L -60 -60 Z" fill="white"/>
    
    <!-- Bottom curve -->
    <path d="M -60 0 L 100 0 Q 160 0 160 60 Q 160 120 100 120 L -60 120 Z" fill="white"/>
    
    <!-- Inner cuts for "B" shape -->
    <rect x="-60" y="-120" width="80" height="60" fill="url(#luxuryGradient)"/>
    <rect x="-60" y="60" width="100" height="60" fill="url(#luxuryGradient)"/>
  </g>
  
  <!-- Luxury shine effect -->
  <ellipse cx="400" cy="300" rx="150" ry="80" fill="white" opacity="0.2" transform="rotate(-30 400 300)"/>
</svg>
