<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Billionaires Social - App Icon Generator</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
            font-weight: 700;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.1em;
        }
        
        #iconCanvas {
            border: 2px solid #ddd;
            border-radius: 15px;
            margin: 20px 0;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .button-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 30px;
        }
        
        button {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #333;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
        }
        
        .size-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #FFD700;
        }
        
        .size-info h3 {
            margin-top: 0;
            color: #333;
        }
        
        .instructions {
            text-align: left;
            background: #e8f4fd;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #0066cc;
        }
        
        .instructions ol {
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏆 Billionaires Social</h1>
        <p class="subtitle">App Icon Generator</p>
        
        <canvas id="iconCanvas" width="512" height="512"></canvas>
        
        <div class="size-info">
            <h3>📱 Current Size: 512x512px</h3>
            <p>Perfect for most app stores and development. You can generate different sizes below.</p>
        </div>
        
        <div class="button-group">
            <button onclick="generateIcon(1024)">Generate 1024x1024 (App Store)</button>
            <button onclick="generateIcon(512)">Generate 512x512 (Standard)</button>
            <button onclick="generateIcon(256)">Generate 256x256 (Small)</button>
            <button onclick="downloadIcon()">💾 Download Current Icon</button>
        </div>
        
        <div class="instructions">
            <h3>📋 How to Use:</h3>
            <ol>
                <li><strong>Generate Icon:</strong> Click any size button above to create the icon</li>
                <li><strong>Download:</strong> Right-click the icon and "Save Image As..." or use the download button</li>
                <li><strong>Save as PNG:</strong> Name it "app_icon_source.png" in the app_store_assets folder</li>
                <li><strong>Run Flutter Command:</strong> Execute <code>flutter pub get && flutter pub run flutter_launcher_icons</code></li>
                <li><strong>Build App:</strong> Your icons will be automatically applied to iOS and Android</li>
            </ol>
        </div>
    </div>

    <script>
        function drawLuxuryIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const center = size / 2;
            const radius = size / 2;
            
            // Clear canvas
            ctx.clearRect(0, 0, size, size);
            
            // Create radial gradient background
            const gradient = ctx.createRadialGradient(center, center, 0, center, center, radius);
            gradient.addColorStop(0, '#FFD700'); // Gold
            gradient.addColorStop(1, '#000000'); // Black
            
            // Draw background circle
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(center, center, radius, 0, 2 * Math.PI);
            ctx.fill();
            
            // Draw silver border
            ctx.strokeStyle = '#C0C0C0';
            ctx.lineWidth = size * 0.02;
            ctx.beginPath();
            ctx.arc(center, center, radius - ctx.lineWidth / 2, 0, 2 * Math.PI);
            ctx.stroke();
            
            // Draw stylized "B" letter
            ctx.fillStyle = 'white';
            ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
            ctx.shadowBlur = size * 0.01;
            ctx.shadowOffsetY = size * 0.005;
            
            const letterSize = size * 0.4;
            const letterX = center - letterSize / 3;
            const letterY = center - letterSize / 2;
            
            // Main vertical bar
            ctx.fillRect(letterX, letterY, letterSize / 6, letterSize);
            
            // Top curve of B
            ctx.beginPath();
            ctx.moveTo(letterX + letterSize / 6, letterY);
            ctx.lineTo(letterX + letterSize / 2, letterY);
            ctx.quadraticCurveTo(letterX + letterSize * 0.7, letterY, letterX + letterSize * 0.7, letterY + letterSize / 4);
            ctx.quadraticCurveTo(letterX + letterSize * 0.7, letterY + letterSize / 2, letterX + letterSize / 2, letterY + letterSize / 2);
            ctx.lineTo(letterX + letterSize / 6, letterY + letterSize / 2);
            ctx.closePath();
            ctx.fill();
            
            // Bottom curve of B
            ctx.beginPath();
            ctx.moveTo(letterX + letterSize / 6, letterY + letterSize / 2);
            ctx.lineTo(letterX + letterSize * 0.6, letterY + letterSize / 2);
            ctx.quadraticCurveTo(letterX + letterSize * 0.8, letterY + letterSize / 2, letterX + letterSize * 0.8, letterY + letterSize * 0.75);
            ctx.quadraticCurveTo(letterX + letterSize * 0.8, letterY + letterSize, letterX + letterSize * 0.6, letterY + letterSize);
            ctx.lineTo(letterX + letterSize / 6, letterY + letterSize);
            ctx.closePath();
            ctx.fill();
            
            // Add luxury shine effect
            ctx.shadowColor = 'transparent';
            ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
            ctx.save();
            ctx.translate(center * 0.8, center * 0.6);
            ctx.rotate(-Math.PI / 6);
            ctx.beginPath();
            ctx.ellipse(0, 0, size * 0.15, size * 0.08, 0, 0, 2 * Math.PI);
            ctx.fill();
            ctx.restore();
        }
        
        function generateIcon(size) {
            const canvas = document.getElementById('iconCanvas');
            canvas.width = size;
            canvas.height = size;
            canvas.style.width = '512px';
            canvas.style.height = '512px';
            
            drawLuxuryIcon(canvas, size);
            
            // Update size info
            document.querySelector('.size-info h3').textContent = `📱 Current Size: ${size}x${size}px`;
        }
        
        function downloadIcon() {
            const canvas = document.getElementById('iconCanvas');
            const link = document.createElement('a');
            link.download = 'app_icon_source.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // Initialize with default icon
        generateIcon(512);
    </script>
</body>
</html>
