#!/usr/bin/env python3
"""
Billionaires Social - App Icon Generator
Creates placeholder app icons for immediate app store submission
"""

import os
from PIL import Image, ImageDraw, ImageFont
import math

# Icon sizes for different platforms
ICON_SIZES = {
    'ios': [1024, 180, 167, 152, 120, 87, 80, 76, 60, 58, 40, 29, 20],
    'android': [512, 432, 192, 144, 96, 72, 48, 36],
}

# Luxury color scheme
GOLD_COLOR = (255, 215, 0)  # #FFD700
BLACK_COLOR = (0, 0, 0)     # #000000
WHITE_COLOR = (255, 255, 255)  # #FFFFFF
SILVER_COLOR = (192, 192, 192)  # #C0C0C0

def create_directories():
    """Create necessary directories for icons"""
    directories = [
        'app_store_assets/app_icons/ios',
        'app_store_assets/app_icons/android',
        'app_store_assets/app_icons/adaptive',
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    print("📁 Created icon directories")

def create_gradient_background(size):
    """Create a radial gradient background from gold to black"""
    image = Image.new('RGB', (size, size), BLACK_COLOR)
    draw = ImageDraw.Draw(image)
    
    center_x, center_y = size // 2, size // 2
    max_radius = size // 2
    
    # Create radial gradient effect
    for radius in range(max_radius, 0, -1):
        # Calculate color interpolation
        ratio = radius / max_radius
        r = int(GOLD_COLOR[0] * ratio + BLACK_COLOR[0] * (1 - ratio))
        g = int(GOLD_COLOR[1] * ratio + BLACK_COLOR[1] * (1 - ratio))
        b = int(GOLD_COLOR[2] * ratio + BLACK_COLOR[2] * (1 - ratio))
        color = (r, g, b)
        
        # Draw circle with current color
        left = center_x - radius
        top = center_y - radius
        right = center_x + radius
        bottom = center_y + radius
        draw.ellipse([left, top, right, bottom], fill=color)
    
    return image

def draw_stylized_b(draw, size):
    """Draw a stylized 'B' letter on the icon"""
    center_x, center_y = size // 2, size // 2
    letter_size = int(size * 0.4)  # 40% of icon size
    
    # Calculate B dimensions
    left = center_x - letter_size // 3
    right = center_x + letter_size // 3
    top = center_y - letter_size // 2
    bottom = center_y + letter_size // 2
    middle = center_y
    
    # Draw vertical line
    line_width = max(2, size // 40)
    draw.rectangle([left, top, left + line_width * 2, bottom], fill=WHITE_COLOR)
    
    # Draw top horizontal line
    draw.rectangle([left, top, right - letter_size // 6, top + line_width], fill=WHITE_COLOR)
    
    # Draw middle horizontal line
    draw.rectangle([left, middle - line_width // 2, right - letter_size // 8, middle + line_width // 2], fill=WHITE_COLOR)
    
    # Draw bottom horizontal line
    draw.rectangle([left, bottom - line_width, right, bottom], fill=WHITE_COLOR)
    
    # Draw top curve (simplified as rectangle)
    curve_width = letter_size // 6
    draw.rectangle([right - curve_width, top, right, middle], fill=WHITE_COLOR)
    
    # Draw bottom curve (simplified as rectangle)
    curve_width = letter_size // 4
    draw.rectangle([right - curve_width, middle, right, bottom], fill=WHITE_COLOR)

def create_app_icon(size, platform):
    """Create a single app icon of specified size"""
    # Create gradient background
    image = create_gradient_background(size)
    draw = ImageDraw.Draw(image)
    
    # Draw stylized B
    draw_stylized_b(draw, size)
    
    # Add silver border
    border_width = max(1, size // 50)
    draw.ellipse([border_width, border_width, size - border_width, size - border_width], 
                outline=SILVER_COLOR, width=border_width)
    
    # Save icon
    filename = f'app_store_assets/app_icons/{platform}/app_icon_{size}x{size}.png'
    image.save(filename, 'PNG')
    return filename

def create_adaptive_icons():
    """Create Android adaptive icon components"""
    size = 432
    
    # Create foreground (transparent background with B)
    foreground = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(foreground)
    draw_stylized_b(draw, size)
    foreground.save('app_store_assets/app_icons/adaptive/ic_launcher_foreground.png', 'PNG')
    
    # Create background (gradient)
    background = create_gradient_background(size)
    background.save('app_store_assets/app_icons/adaptive/ic_launcher_background.png', 'PNG')
    
    print("🎨 Created adaptive icon components")

def create_implementation_guide():
    """Create implementation guide"""
    guide_content = f"""# 🚀 App Icon Implementation Guide

## Generated Icons

### iOS Icons (app_store_assets/app_icons/ios/)
{chr(10).join([f'- app_icon_{size}x{size}.png' for size in ICON_SIZES['ios']])}

### Android Icons (app_store_assets/app_icons/android/)
{chr(10).join([f'- app_icon_{size}x{size}.png' for size in ICON_SIZES['android']])}

### Android Adaptive Icons (app_store_assets/app_icons/adaptive/)
- ic_launcher_foreground.png (432x432)
- ic_launcher_background.png (432x432)

## Implementation Steps

### iOS Implementation
1. Open `ios/Runner.xcworkspace` in Xcode
2. Navigate to `Runner/Assets.xcassets/AppIcon.appiconset/`
3. Replace existing icons with generated ones:
   - 1024x1024 → App Store
   - 180x180 → iPhone App iOS 14+
   - 120x120 → iPhone App iOS 7-13
   - 167x167 → iPad Pro App
   - 152x152 → iPad App

### Android Implementation
1. Replace icons in `android/app/src/main/res/`:
   - 512x512 → Use for Google Play Store
   - 192x192 → `mipmap-xxxhdpi/ic_launcher.png`
   - 144x144 → `mipmap-xxhdpi/ic_launcher.png`
   - 96x96 → `mipmap-xhdpi/ic_launcher.png`
   - 72x72 → `mipmap-hdpi/ic_launcher.png`
   - 48x48 → `mipmap-mdpi/ic_launcher.png`

2. For adaptive icons (Android 8.0+):
   - Copy adaptive icons to `mipmap-anydpi-v26/`

## Design Features
- Luxury gold to black radial gradient background
- Stylized white "B" logo for Billionaires Social
- Silver border for premium appearance
- Optimized for readability at all sizes

## Testing
1. Build and run on physical devices
2. Test icon visibility on different backgrounds
3. Verify icon appears correctly in app stores

Generated: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    with open('app_store_assets/app_icons/IMPLEMENTATION_GUIDE.md', 'w') as f:
        f.write(guide_content)
    
    print("📋 Created implementation guide")

def main():
    """Main function to generate all app icons"""
    print("🎨 Generating Billionaires Social App Icons...")
    
    # Create directories
    create_directories()
    
    # Generate iOS icons
    print("📱 Generating iOS icons...")
    for size in ICON_SIZES['ios']:
        filename = create_app_icon(size, 'ios')
        print(f"   ✅ Generated {size}x{size} iOS icon")
    
    # Generate Android icons
    print("🤖 Generating Android icons...")
    for size in ICON_SIZES['android']:
        filename = create_app_icon(size, 'android')
        print(f"   ✅ Generated {size}x{size} Android icon")
    
    # Generate adaptive icons
    create_adaptive_icons()
    
    # Create implementation guide
    create_implementation_guide()
    
    print("✅ App icon generation complete!")
    print("📁 Icons saved in app_store_assets/app_icons/")
    print("📋 See IMPLEMENTATION_GUIDE.md for next steps")

if __name__ == "__main__":
    main()
