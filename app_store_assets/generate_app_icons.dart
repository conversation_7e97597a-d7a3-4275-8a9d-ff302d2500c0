// ignore_for_file: avoid_print
// cSpell:ignore LTWH xcworkspace xcassets appiconset xxxhdpi xxhdpi xhdpi hdpi mdpi anydpi
// This is a utility script where print statements are appropriate for CLI output

import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';

/// App Icon Generator for Billionaires Social
///
/// This script generates all required app icon sizes for iOS and Android
/// with a luxury-themed design featuring a stylized "B" logo.
void main() async {
  print('🎨 Generating Billionaires Social App Icons...');

  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  final generator = AppIconGenerator();
  await generator.generateAllIcons();

  print('✅ App icon generation complete!');
}

class AppIconGenerator {
  // Icon sizes for different platforms
  static const Map<String, List<int>> iconSizes = {
    'ios': [1024, 180, 167, 152, 120, 87, 80, 76, 60, 58, 40, 29, 20],
    'android': [512, 432, 192, 144, 96, 72, 48, 36],
  };

  // Luxury color scheme
  static const Color goldColor = Color(0xFFFFD700);
  static const Color blackColor = Color(0xFF000000);
  static const Color silverColor = Color(0xFFC0C0C0);

  /// Generate all required app icons
  Future<void> generateAllIcons() async {
    // Create output directories
    await _createDirectories();

    // Generate iOS icons
    print('📱 Generating iOS icons...');
    for (final size in iconSizes['ios']!) {
      await _generateIcon(size, 'ios');
      print('   ✅ Generated ${size}x$size iOS icon');
    }

    // Generate Android icons
    print('🤖 Generating Android icons...');
    for (final size in iconSizes['android']!) {
      await _generateIcon(size, 'android');
      print('   ✅ Generated ${size}x$size Android icon');
    }

    // Generate adaptive icon components
    print('🎨 Generating Android adaptive icon components...');
    await _generateAdaptiveIconComponents();

    print('📋 Creating implementation guide...');
    await _createImplementationGuide();
  }

  /// Create necessary directories
  Future<void> _createDirectories() async {
    final directories = [
      'app_store_assets/app_icons/ios',
      'app_store_assets/app_icons/android',
      'app_store_assets/app_icons/adaptive',
    ];

    for (final dir in directories) {
      await Directory(dir).create(recursive: true);
    }
  }

  /// Generate a single icon of specified size
  Future<void> _generateIcon(int size, String platform) async {
    // Create a custom painter for the icon
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);

    // Draw the icon
    _drawLuxuryIcon(canvas, size.toDouble());

    // Convert to image
    final picture = recorder.endRecording();
    final image = await picture.toImage(size, size);
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);

    if (byteData != null) {
      final bytes = byteData.buffer.asUint8List();
      final file = File(
        'app_store_assets/app_icons/$platform/app_icon_${size}x$size.png',
      );
      await file.writeAsBytes(bytes);
    }
  }

  /// Draw the luxury-themed app icon
  void _drawLuxuryIcon(Canvas canvas, double size) {
    final paint = Paint();
    final center = Offset(size / 2, size / 2);
    final radius = size / 2;

    // Background gradient (gold to black)
    paint.shader = ui.Gradient.radial(
      center,
      radius,
      [goldColor, blackColor],
      [0.0, 1.0],
    );

    // Draw background circle
    canvas.drawCircle(center, radius, paint);

    // Draw stylized "B" letter
    _drawStylizedB(canvas, size);

    // Add luxury border
    paint.shader = null;
    paint.color = silverColor;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = size * 0.02; // 2% of size
    canvas.drawCircle(center, radius - paint.strokeWidth / 2, paint);
  }

  /// Draw stylized "B" letter
  void _drawStylizedB(Canvas canvas, double size) {
    final paint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    final path = Path();
    final centerX = size / 2;
    final centerY = size / 2;
    final letterSize = size * 0.4; // 40% of icon size

    // Create a stylized "B" shape
    // Left vertical line
    path.moveTo(centerX - letterSize / 3, centerY - letterSize / 2);
    path.lineTo(centerX - letterSize / 3, centerY + letterSize / 2);
    path.lineTo(centerX - letterSize / 6, centerY + letterSize / 2);
    path.lineTo(centerX - letterSize / 6, centerY + letterSize / 8);

    // Top curve
    path.lineTo(centerX + letterSize / 6, centerY + letterSize / 8);
    path.quadraticBezierTo(
      centerX + letterSize / 3,
      centerY + letterSize / 8,
      centerX + letterSize / 3,
      centerY - letterSize / 8,
    );
    path.quadraticBezierTo(
      centerX + letterSize / 3,
      centerY - letterSize / 3,
      centerX + letterSize / 6,
      centerY - letterSize / 3,
    );
    path.lineTo(centerX - letterSize / 6, centerY - letterSize / 3);
    path.lineTo(centerX - letterSize / 6, centerY - letterSize / 2);
    path.close();

    // Bottom curve
    final bottomPath = Path();
    bottomPath.moveTo(centerX - letterSize / 6, centerY);
    bottomPath.lineTo(centerX + letterSize / 4, centerY);
    bottomPath.quadraticBezierTo(
      centerX + letterSize / 2,
      centerY,
      centerX + letterSize / 2,
      centerY + letterSize / 4,
    );
    bottomPath.quadraticBezierTo(
      centerX + letterSize / 2,
      centerY + letterSize / 2,
      centerX + letterSize / 4,
      centerY + letterSize / 2,
    );
    bottomPath.lineTo(centerX - letterSize / 6, centerY + letterSize / 2);
    bottomPath.lineTo(centerX - letterSize / 6, centerY + letterSize / 3);
    bottomPath.lineTo(centerX + letterSize / 8, centerY + letterSize / 3);
    bottomPath.quadraticBezierTo(
      centerX + letterSize / 3,
      centerY + letterSize / 3,
      centerX + letterSize / 3,
      centerY + letterSize / 6,
    );
    bottomPath.quadraticBezierTo(
      centerX + letterSize / 3,
      centerY,
      centerX + letterSize / 8,
      centerY,
    );
    bottomPath.close();

    // Draw both parts of the "B"
    canvas.drawPath(path, paint);
    canvas.drawPath(bottomPath, paint);
  }

  /// Generate adaptive icon components for Android
  Future<void> _generateAdaptiveIconComponents() async {
    // Generate foreground (the "B" logo)
    await _generateAdaptiveForeground();

    // Generate background (gradient)
    await _generateAdaptiveBackground();
  }

  /// Generate adaptive icon foreground
  Future<void> _generateAdaptiveForeground() async {
    const size = 432;
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);

    // Transparent background
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.toDouble(), size.toDouble()),
      Paint()..color = Colors.transparent,
    );

    // Draw only the "B" letter (centered in safe zone)
    _drawStylizedB(canvas, size.toDouble());

    final picture = recorder.endRecording();
    final image = await picture.toImage(size, size);
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);

    if (byteData != null) {
      final bytes = byteData.buffer.asUint8List();
      final file = File(
        'app_store_assets/app_icons/adaptive/ic_launcher_foreground.png',
      );
      await file.writeAsBytes(bytes);
    }
  }

  /// Generate adaptive icon background
  Future<void> _generateAdaptiveBackground() async {
    const size = 432;
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);

    final paint = Paint();
    final center = Offset(size / 2, size / 2);
    final radius = size / 2;

    // Background gradient
    paint.shader = ui.Gradient.radial(
      center,
      radius,
      [goldColor, blackColor],
      [0.0, 1.0],
    );

    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.toDouble(), size.toDouble()),
      paint,
    );

    final picture = recorder.endRecording();
    final image = await picture.toImage(size, size);
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);

    if (byteData != null) {
      final bytes = byteData.buffer.asUint8List();
      final file = File(
        'app_store_assets/app_icons/adaptive/ic_launcher_background.png',
      );
      await file.writeAsBytes(bytes);
    }
  }

  /// Create implementation guide
  Future<void> _createImplementationGuide() async {
    final guide =
        '''
# 🚀 App Icon Implementation Guide

## Generated Icons

### iOS Icons (app_store_assets/app_icons/ios/)
${iconSizes['ios']!.map((size) => '- app_icon_${size}x$size.png').join('\n')}

### Android Icons (app_store_assets/app_icons/android/)
${iconSizes['android']!.map((size) => '- app_icon_${size}x$size.png').join('\n')}

### Android Adaptive Icons (app_store_assets/app_icons/adaptive/)
- ic_launcher_foreground.png (432x432)
- ic_launcher_background.png (432x432)

## Implementation Steps

### iOS Implementation
1. Open `ios/Runner.xcworkspace` in Xcode
2. Navigate to `Runner/Assets.xcassets/AppIcon.appiconset/`
3. Replace existing icons with generated ones:
   - 1024x1024 → App Store
   - 180x180 → iPhone App iOS 14+
   - 120x120 → iPhone App iOS 7-13
   - 167x167 → iPad Pro App
   - 152x152 → iPad App
   - (Continue for all sizes)

### Android Implementation
1. Replace icons in `android/app/src/main/res/`:
   - 512x512 → Use for Google Play Store
   - 192x192 → `mipmap-xxxhdpi/ic_launcher.png`
   - 144x144 → `mipmap-xxhdpi/ic_launcher.png`
   - 96x96 → `mipmap-xhdpi/ic_launcher.png`
   - 72x72 → `mipmap-hdpi/ic_launcher.png`
   - 48x48 → `mipmap-mdpi/ic_launcher.png`

2. For adaptive icons (Android 8.0+):
   - Copy `ic_launcher_foreground.png` to `mipmap-anydpi-v26/`
   - Copy `ic_launcher_background.png` to `mipmap-anydpi-v26/`

## Testing
1. Build and run on physical devices
2. Test icon visibility on different backgrounds
3. Verify icon appears correctly in app stores
4. Check icon scaling at different sizes

## Notes
- All icons use luxury gold/black gradient theme
- Stylized "B" logo represents Billionaires Social
- Icons are optimized for readability at all sizes
- Follows platform-specific design guidelines

Generated: ${DateTime.now()}
''';

    final file = File('app_store_assets/app_icons/IMPLEMENTATION_GUIDE.md');
    await file.writeAsString(guide);
  }
}
