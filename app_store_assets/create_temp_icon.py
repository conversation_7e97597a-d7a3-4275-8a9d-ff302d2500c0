#!/usr/bin/env python3
"""
Create a temporary app icon for testing flutter_launcher_icons
"""

try:
    from PIL import Image, ImageDraw
    import os
    
    # Create a 1024x1024 image
    size = 1024
    image = Image.new('RGB', (size, size), color='black')
    draw = ImageDraw.Draw(image)
    
    # Create a simple gradient effect
    for y in range(size):
        # Calculate color based on distance from center
        center_y = size // 2
        distance = abs(y - center_y) / center_y
        
        # Gold to black gradient
        gold_r, gold_g, gold_b = 255, 215, 0
        black_r, black_g, black_b = 0, 0, 0
        
        r = int(gold_r * (1 - distance) + black_r * distance)
        g = int(gold_g * (1 - distance) + black_g * distance)
        b = int(gold_b * (1 - distance) + black_b * distance)
        
        color = (r, g, b)
        draw.line([(0, y), (size, y)], fill=color)
    
    # Draw a simple "B" shape
    # This is a basic representation - the HTML version looks much better
    draw.rectangle([size//3, size//4, size//3 + size//8, size*3//4], fill='white')
    draw.rectangle([size//3 + size//8, size//4, size*2//3, size//2 - size//16], fill='white')
    draw.rectangle([size//3 + size//8, size//2 + size//16, size*2//3 + size//16, size*3//4], fill='white')
    
    # Save the image
    image.save('app_icon_source.png', 'PNG')
    print("✅ Temporary app icon created: app_icon_source.png")
    print("📝 Note: Use the HTML generator for a much better-looking icon!")
    
except ImportError:
    print("❌ PIL (Pillow) not installed. Please install with: pip3 install Pillow")
    print("🌐 Or use the HTML generator that just opened in your browser")
except Exception as e:
    print(f"❌ Error creating icon: {e}")
    print("🌐 Please use the HTML generator that opened in your browser")
