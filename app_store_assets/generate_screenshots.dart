#!/usr/bin/env dart

/// Screenshot generation script for Billionaires Social app store assets
///
/// This script automates the process of generating screenshots for all required
/// device sizes and platforms for app store submission.
///
/// Usage: dart run app_store_assets/generate_screenshots.dart
library;

import 'dart:io';

void main() async {
  stdout.writeln('🚀 Starting Billionaires Social Screenshot Generation');
  stdout.writeln('=' * 60);

  // Check if Flutter is available
  if (!await _checkFlutterInstallation()) {
    stdout.writeln('❌ Flutter not found. Please install Flutter first.');
    exit(1);
  }

  // Device configurations for screenshots
  final devices = [
    // iOS Devices
    {
      'platform': 'ios',
      'name': 'iPhone 15 Pro Max',
      'size': 'iphone_6_7',
      'resolution': '1290x2796',
      'simulator': 'iPhone 15 Pro Max',
    },
    {
      'platform': 'ios',
      'name': 'iPhone 13 Pro Max',
      'size': 'iphone_6_5',
      'resolution': '1242x2688',
      'simulator': 'iPhone 13 Pro Max',
    },
    {
      'platform': 'ios',
      'name': 'iPhone 8 Plus',
      'size': 'iphone_5_5',
      'resolution': '1242x2208',
      'simulator': 'iPhone 8 Plus',
    },
    {
      'platform': 'ios',
      'name': 'iPad Pro 12.9"',
      'size': 'ipad_12_9',
      'resolution': '2048x2732',
      'simulator': 'iPad Pro (12.9-inch)',
    },
    {
      'platform': 'ios',
      'name': 'iPad Pro 11"',
      'size': 'ipad_11',
      'resolution': '1668x2388',
      'simulator': 'iPad Pro (11-inch)',
    },
    // Android Devices
    {
      'platform': 'android',
      'name': 'Pixel 7 Pro',
      'size': 'phone',
      'resolution': '1080x1920',
      'emulator': 'pixel_7_pro',
    },
    {
      'platform': 'android',
      'name': 'Pixel Tablet',
      'size': 'tablet',
      'resolution': '1200x1920',
      'emulator': 'pixel_tablet',
    },
  ];

  // Screenshot scenarios
  final scenarios = [
    {
      'name': '01_welcome',
      'description': 'Welcome/Onboarding Screen',
      'route': '/',
      'wait_seconds': 3,
    },
    {
      'name': '02_feed',
      'description': 'Main Feed with Posts',
      'route': '/feed',
      'wait_seconds': 5,
    },
    {
      'name': '03_profile',
      'description': 'User Profile Screen',
      'route': '/profile',
      'wait_seconds': 3,
    },
    {
      'name': '04_stories',
      'description': 'Stories Feature',
      'route': '/stories',
      'wait_seconds': 4,
    },
    {
      'name': '05_messaging',
      'description': 'Messaging Interface',
      'route': '/messages',
      'wait_seconds': 3,
    },
  ];

  stdout.writeln('📱 Configured ${devices.length} devices');
  stdout.writeln('📸 Configured ${scenarios.length} screenshot scenarios');
  stdout.writeln('');

  // Generate screenshots for each device
  for (final device in devices) {
    await _generateScreenshotsForDevice(device, scenarios);
  }

  stdout.writeln('');
  stdout.writeln('✅ Screenshot generation completed!');
  stdout.writeln('📁 Screenshots saved to: app_store_assets/screenshots/');
  stdout.writeln('');
  stdout.writeln('📋 Next steps:');
  stdout.writeln('1. Review generated screenshots for quality');
  stdout.writeln('2. Add text overlays using design tools');
  stdout.writeln('3. Optimize images for app store guidelines');
  stdout.writeln('4. Upload to App Store Connect / Google Play Console');
}

/// Check if Flutter is installed and available
Future<bool> _checkFlutterInstallation() async {
  try {
    final result = await Process.run('flutter', ['--version']);
    return result.exitCode == 0;
  } catch (e) {
    return false;
  }
}

/// Generate screenshots for a specific device
Future<void> _generateScreenshotsForDevice(
  Map<String, String> device,
  List<Map<String, dynamic>> scenarios,
) async {
  final platform = device['platform']!;
  final deviceName = device['name']!;
  final size = device['size']!;

  stdout.writeln('📱 Generating screenshots for $deviceName ($platform)');

  // Create output directory
  final outputDir = 'app_store_assets/screenshots/$platform/$size';
  await Directory(outputDir).create(recursive: true);

  // Start device/simulator
  stdout.writeln(
    '   🔄 Starting ${platform == 'ios' ? 'simulator' : 'emulator'}...',
  );

  if (platform == 'ios') {
    await _startIOSSimulator(device['simulator']!);
  } else {
    await _startAndroidEmulator(device['emulator']!);
  }

  // Wait for device to be ready
  await Future.delayed(Duration(seconds: 10));

  // Generate screenshots for each scenario
  for (final scenario in scenarios) {
    await _captureScreenshot(device, scenario, outputDir);
  }

  stdout.writeln('   ✅ Completed $deviceName');
}

/// Start iOS Simulator
Future<void> _startIOSSimulator(String simulator) async {
  try {
    await Process.run('xcrun', ['simctl', 'boot', simulator]);

    await Process.run('open', [
      '-a',
      'Simulator',
      '--args',
      '-CurrentDeviceUDID',
      simulator,
    ]);
  } catch (e) {
    stdout.writeln('   ⚠️ Could not start iOS simulator: $e');
  }
}

/// Start Android Emulator
Future<void> _startAndroidEmulator(String emulator) async {
  try {
    await Process.start('emulator', ['-avd', emulator]);
  } catch (e) {
    stdout.writeln('   ⚠️ Could not start Android emulator: $e');
  }
}

/// Capture screenshot for a specific scenario
Future<void> _captureScreenshot(
  Map<String, String> device,
  Map<String, dynamic> scenario,
  String outputDir,
) async {
  final scenarioName = scenario['name'] as String;
  final description = scenario['description'] as String;
  final waitSeconds = scenario['wait_seconds'] as int;

  stdout.writeln('   📸 Capturing $scenarioName - $description');

  try {
    // Run Flutter app and capture screenshot
    final process = await Process.start('flutter', [
      'drive',
      '--driver=test_driver/screenshot_test.dart',
      '--target=test_driver/app.dart',
      '--screenshot=$outputDir/$scenarioName.png',
    ]);

    // Wait for screenshot to be captured
    await Future.delayed(Duration(seconds: waitSeconds));

    // Kill the process
    process.kill();

    stdout.writeln('     ✅ Saved $scenarioName.png');
  } catch (e) {
    stdout.writeln('     ❌ Failed to capture $scenarioName: $e');
  }
}
