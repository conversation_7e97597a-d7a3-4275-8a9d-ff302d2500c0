<!DOCTYPE html>
<html>
<head>
    <title>Create App Icon</title>
</head>
<body>
    <h1>Creating App Icon...</h1>
    <canvas id="canvas" width="1024" height="1024" style="border: 1px solid #ccc;"></canvas>
    <br><br>
    <button onclick="downloadIcon()">Download Icon</button>
    
    <script>
        function createIcon() {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            const size = 1024;
            const center = size / 2;
            
            // Create gradient background
            const gradient = ctx.createRadialGradient(center, center, 0, center, center, center);
            gradient.addColorStop(0, '#FFD700'); // Gold
            gradient.addColorStop(1, '#000000'); // Black
            
            // Fill background
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // Draw "B" letter
            ctx.fillStyle = 'white';
            ctx.font = 'bold 400px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('B', center, center);
            
            // Add border
            ctx.strokeStyle = '#C0C0C0';
            ctx.lineWidth = 20;
            ctx.beginPath();
            ctx.arc(center, center, center - 10, 0, 2 * Math.PI);
            ctx.stroke();
        }
        
        function downloadIcon() {
            const canvas = document.getElementById('canvas');
            const link = document.createElement('a');
            link.download = 'app_icon_source.png';
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        // Create icon on load
        createIcon();
        
        // Auto-download after 2 seconds
        setTimeout(() => {
            downloadIcon();
            document.body.innerHTML += '<p><strong>Icon downloaded!</strong> Save it as "app_icon_source.png" in the app_store_assets folder.</p>';
        }, 2000);
    </script>
</body>
</html>
