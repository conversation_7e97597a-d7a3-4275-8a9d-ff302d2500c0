# 🎨 App Icon Assets

This directory contains all required app icon sizes for iOS and Android platforms.

## 📱 iOS App Icons

### iPhone/iPad Icons
- **1024x1024** - App Store (app_icon_1024.png)
- **180x180** - iPhone 6 Plus, 6s Plus, 7 Plus, 8 Plus, X, XS, XS Max, 11, 11 Pro, 11 Pro Max, 12, 12 Pro, 12 Pro Max, 13, 13 Pro, 13 Pro Max, 14, 14 Pro, 14 Pro Max, 15, 15 Pro, 15 Pro Max
- **167x167** - iPad Pro 12.9", iPad Pro 10.5", iPad Pro 11", iPad Air 3rd gen, iPad mini 5th gen
- **152x152** - iPad 7th gen, iPad Air 2, iPad mini 4
- **120x120** - iPhone 6, 6s, 7, 8, SE 2nd gen, SE 3rd gen
- **87x87** - iPhone 6 Plus, 6s Plus, 7 Plus, 8 Plus (Settings)
- **80x80** - iPhone 6, 6s, 7, 8, SE 2nd gen, SE 3rd gen (Spotlight)
- **76x76** - iPad 7th gen, iPad Air 2, iPad mini 4
- **60x60** - iPhone 6, 6s, 7, 8, SE 2nd gen, SE 3rd gen (Spotlight)
- **58x58** - iPhone 6, 6s, 7, 8, SE 2nd gen, SE 3rd gen (Settings)
- **40x40** - iPhone 6, 6s, 7, 8, SE 2nd gen, SE 3rd gen (Spotlight)
- **29x29** - iPhone 6, 6s, 7, 8, SE 2nd gen, SE 3rd gen (Settings)
- **20x20** - iPhone 6, 6s, 7, 8, SE 2nd gen, SE 3rd gen (Notification)

### Apple Watch Icons (if supported)
- **1024x1024** - App Store
- **108x108** - Apple Watch Series 4+
- **102x102** - Apple Watch Series 4+ (Notification)
- **88x88** - Apple Watch Series 1-3
- **80x80** - Apple Watch Series 1-3 (Notification)

## 🤖 Android App Icons

### Adaptive Icons (API 26+)
- **432x432** - Adaptive icon (foreground + background)
- **108x108** - Adaptive icon safe zone

### Legacy Icons
- **512x512** - Google Play Store
- **192x192** - xxxhdpi (4.0x)
- **144x144** - xxhdpi (3.0x)
- **96x96** - xhdpi (2.0x)
- **72x72** - hdpi (1.5x)
- **48x48** - mdpi (1.0x)
- **36x36** - ldpi (0.75x)

### Notification Icons
- **96x96** - xxxhdpi
- **72x72** - xxhdpi
- **48x48** - xhdpi
- **36x36** - hdpi
- **24x24** - mdpi
- **18x18** - ldpi

## 🎨 Design Guidelines

### Visual Identity
- **Primary Color**: Luxury Gold (#FFD700)
- **Secondary Color**: Deep Black (#000000)
- **Accent Color**: Premium Silver (#C0C0C0)

### Icon Concept
- **Symbol**: Stylized "B" for Billionaires
- **Style**: Minimalist, luxury aesthetic
- **Background**: Gradient from gold to black
- **Typography**: Premium serif or elegant sans-serif

### Design Requirements
- **Scalability**: Must be readable at all sizes
- **Contrast**: High contrast for visibility
- **Simplicity**: Clean, uncluttered design
- **Brand Consistency**: Matches app's luxury positioning

## 📋 Icon Checklist

### iOS Icons
- [ ] 1024x1024 (App Store)
- [ ] 180x180 (iPhone)
- [ ] 167x167 (iPad Pro)
- [ ] 152x152 (iPad)
- [ ] 120x120 (iPhone)
- [ ] 87x87 (iPhone Settings)
- [ ] 80x80 (iPhone Spotlight)
- [ ] 76x76 (iPad)
- [ ] 60x60 (iPhone Spotlight)
- [ ] 58x58 (iPhone Settings)
- [ ] 40x40 (iPhone Spotlight)
- [ ] 29x29 (iPhone Settings)
- [ ] 20x20 (iPhone Notification)

### Android Icons
- [ ] 512x512 (Play Store)
- [ ] 432x432 (Adaptive)
- [ ] 192x192 (xxxhdpi)
- [ ] 144x144 (xxhdpi)
- [ ] 96x96 (xhdpi)
- [ ] 72x72 (hdpi)
- [ ] 48x48 (mdpi)
- [ ] 36x36 (ldpi)

### Notification Icons
- [ ] Android notification icons (all densities)
- [ ] iOS notification icons

## 🛠️ Generation Tools

### Recommended Tools
1. **Adobe Illustrator** - Vector design
2. **Sketch** - UI/UX design
3. **Figma** - Collaborative design
4. **Icon Generator** - Automated resizing

### Online Generators
- **App Icon Generator** (appicon.co)
- **Icon Kitchen** (romannurik.github.io/AndroidAssetStudio)
- **MakeAppIcon** (makeappicon.com)

## 📝 Notes

1. All icons must be PNG format
2. No transparency for iOS icons
3. Android adaptive icons support transparency
4. Test icons on various backgrounds
5. Ensure readability at smallest sizes
6. Follow platform-specific guidelines

## 🚀 Implementation

After generating icons:
1. Replace placeholder icons in `ios/Runner/Assets.xcassets/AppIcon.appiconset/`
2. Replace placeholder icons in `android/app/src/main/res/`
3. Update `android/app/src/main/res/mipmap-anydpi-v26/` for adaptive icons
4. Test on physical devices
5. Validate with app store guidelines
