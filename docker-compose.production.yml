# Docker Compose override for production environment
# This file extends docker-compose.yml with production-specific configurations

version: '3.8'

services:
  # =============================================================================
  # Flutter Production Configuration
  # =============================================================================
  flutter-prod:
    build:
      target: production
    restart: unless-stopped
    environment:
      - FLUTTER_ENV=production
      - NODE_ENV=production
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  # =============================================================================
  # n8n Production Configuration
  # =============================================================================
  n8n:
    restart: unless-stopped
    environment:
      - N8N_LOG_LEVEL=warn
      - N8N_DIAGNOSTICS_ENABLED=false
      - N8N_METRICS=true
      - WEBHOOK_URL=https://billionaires-social.com/webhooks
    volumes:
      - n8n_prod_data:/home/<USER>/.n8n
      - ./n8n-backup:/backup:rw
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # =============================================================================
  # PostgreSQL Production Configuration
  # =============================================================================
  postgres:
    restart: unless-stopped
    environment:
      - POSTGRES_DB=n8n_prod
      - POSTGRES_USER=n8n_prod
      - POSTGRES_PASSWORD_FILE=/run/secrets/postgres_password
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
      - ./docker/postgres/postgresql.conf:/etc/postgresql/postgresql.conf:ro
    secrets:
      - postgres_password
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
    command: postgres -c config_file=/etc/postgresql/postgresql.conf

  # =============================================================================
  # Redis Production Configuration
  # =============================================================================
  redis:
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass-file /run/secrets/redis_password
    volumes:
      - redis_prod_data:/data
    secrets:
      - redis_password
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

  # =============================================================================
  # Nginx Production Configuration
  # =============================================================================
  nginx:
    profiles:
      - production
    restart: unless-stopped
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
      - static_assets:/usr/share/nginx/html/static:ro
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

  # =============================================================================
  # Monitoring Production Configuration
  # =============================================================================
  prometheus:
    profiles:
      - production
      - monitoring
    restart: unless-stopped
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_prod_data:/prometheus
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

  grafana:
    profiles:
      - production
      - monitoring
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_PASSWORD_FILE=/run/secrets/grafana_password
      - GF_INSTALL_PLUGINS=grafana-piechart-panel
    volumes:
      - grafana_prod_data:/var/lib/grafana
      - ./docker/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./docker/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    secrets:
      - grafana_password
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

  # =============================================================================
  # Backup Service
  # =============================================================================
  backup:
    image: alpine:latest
    container_name: billionaires-backup
    volumes:
      - postgres_prod_data:/backup/postgres:ro
      - redis_prod_data:/backup/redis:ro
      - n8n_prod_data:/backup/n8n:ro
      - ./backups:/backups
    environment:
      - BACKUP_SCHEDULE=0 2 * * *
      - BACKUP_RETENTION_DAYS=30
    command: >
      sh -c "
        apk add --no-cache postgresql-client redis &&
        echo '0 2 * * * /backup-script.sh' | crontab - &&
        crond -f
      "
    profiles:
      - production
      - backup
    networks:
      - billionaires-network
    restart: unless-stopped

  # =============================================================================
  # Log Aggregation
  # =============================================================================
  fluentd:
    image: fluent/fluentd:v1.16-debian-1
    container_name: billionaires-fluentd
    volumes:
      - ./docker/fluentd/fluent.conf:/fluentd/etc/fluent.conf:ro
      - nginx_logs:/var/log/nginx:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
    ports:
      - "24224:24224"
    profiles:
      - production
      - logging
    networks:
      - billionaires-network
    restart: unless-stopped

# =============================================================================
# Production Secrets
# =============================================================================
secrets:
  postgres_password:
    file: ./secrets/postgres_password.txt
  redis_password:
    file: ./secrets/redis_password.txt
  grafana_password:
    file: ./secrets/grafana_password.txt

# =============================================================================
# Production-specific volumes
# =============================================================================
volumes:
  postgres_prod_data:
    driver: local
  redis_prod_data:
    driver: local
  n8n_prod_data:
    driver: local
  prometheus_prod_data:
    driver: local
  grafana_prod_data:
    driver: local
  static_assets:
    driver: local
