# Billionaires Social - ProGuard Rules for Release Builds

# Keep Flutter framework classes
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.embedding.** { *; }

# Keep Firebase classes
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }
-dontwarn com.google.firebase.**
-dontwarn com.google.android.gms.**

# Keep Crashlytics
-keepattributes SourceFile,LineNumberTable
-keep public class * extends java.lang.Exception
-keep class com.crashlytics.** { *; }
-dontwarn com.crashlytics.**

# Keep Sentry
-keep class io.sentry.** { *; }
-dontwarn io.sentry.**

# Keep camera and media classes
-keep class androidx.camera.** { *; }
-keep class android.media.** { *; }
-dontwarn androidx.camera.**

# Keep notification classes
-keep class androidx.work.** { *; }
-keep class com.dexterous.** { *; }
-dontwarn androidx.work.**

# Keep biometric authentication
-keep class androidx.biometric.** { *; }
-dontwarn androidx.biometric.**

# Keep location services
-keep class com.google.android.gms.location.** { *; }
-dontwarn com.google.android.gms.location.**

# Keep WebView classes (if used)
-keep class android.webkit.** { *; }
-dontwarn android.webkit.**

# Keep Gson (if used for JSON serialization)
-keep class com.google.gson.** { *; }
-keepclassmembers class * {
    @com.google.gson.annotations.SerializedName <fields>;
}

# Keep model classes (adjust package names as needed)
-keep class com.billionairessocial.app.models.** { *; }

# General rules
-keepattributes Signature
-keepattributes *Annotation*
-keepattributes EnclosingMethod
-keepattributes InnerClasses

# Remove logging in release builds
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# Optimize and obfuscate
-optimizationpasses 5
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-dontpreverify
-verbose
