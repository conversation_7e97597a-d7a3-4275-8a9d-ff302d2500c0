# Docker Setup Guide for Billionaires Social

This guide provides step-by-step instructions for setting up and running the Billionaires Social Flutter app using Docker containers.

## 🎯 Overview

The Docker configuration provides:
- **Complete development environment** with hot reload and Firebase emulators
- **Production-ready deployment** with SSL, monitoring, and scaling
- **n8n workflow automation** for social media scheduling and user engagement
- **Comprehensive monitoring** with Prometheus and Grafana
- **Easy management scripts** for common operations

## 🏁 Quick Start (5 Minutes)

### 1. Prerequisites
- Docker Desktop (Windows/Mac) or Docker Engine + Docker Compose (Linux)
- Git
- 8GB+ RAM recommended

### 2. Clone and Setup
```bash
git clone <your-repo-url>
cd billionaires_social

# Copy environment template
cp .env.template .env.development

# Make scripts executable (Linux/Mac)
chmod +x docker/scripts/*.sh
```

### 3. Start Development Environment
```bash
# Start all development services
./docker/scripts/deploy.sh -e development

# Or using docker-compose directly
docker-compose --profile development up -d
```

### 4. Access Your App
- **Flutter App**: http://localhost:3000
- **n8n Workflows**: http://localhost:5678 (admin/billionaires123)
- **Firebase Emulator**: http://localhost:4000
- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3001 (admin/admin123)

## 🔧 Configuration

### Environment Variables

Edit `.env.development` for development or `.env.production` for production:

```bash
# Essential settings
FIREBASE_PROJECT_ID=billionaires-social-dev
N8N_BASE_URL=http://n8n:5678
N8N_WEBHOOK_SECRET=your_webhook_secret

# External API keys (optional for development)
AGORA_APP_ID=your_agora_app_id
GIPHY_API_KEY=your_giphy_api_key
```

### n8n Integration

The app includes pre-configured n8n workflows for:
- **Account switching automation**
- **Social media post scheduling**
- **User engagement tracking**
- **Push notification delivery**

Access n8n at http://localhost:5678 to manage workflows.

## 🚀 Production Deployment

### 1. Configure Production Environment
```bash
cp .env.template .env.production
# Edit .env.production with real credentials
```

### 2. Build Production Images
```bash
./docker/scripts/build.sh -e production
```

### 3. Deploy Production Services
```bash
./docker/scripts/deploy.sh -e production
```

### 4. Setup SSL (Production)
```bash
# Generate self-signed certificates (for testing)
mkdir -p docker/nginx/ssl
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout docker/nginx/ssl/key.pem \
  -out docker/nginx/ssl/cert.pem

# Or copy your real SSL certificates
cp your-cert.pem docker/nginx/ssl/cert.pem
cp your-key.pem docker/nginx/ssl/key.pem
```

## 🛠️ Management Commands

### Service Management
```bash
# Check health of all services
./docker/scripts/manage.sh health

# View logs for specific service
./docker/scripts/deploy.sh logs flutter-dev

# Restart a service
./docker/scripts/deploy.sh restart n8n

# Open shell in container
./docker/scripts/deploy.sh shell postgres
```

### Maintenance
```bash
# Clean up unused Docker resources
./docker/scripts/manage.sh cleanup

# Backup application data
./docker/scripts/manage.sh backup -e production

# Monitor resource usage
./docker/scripts/manage.sh monitor

# Update all services
./docker/scripts/manage.sh update
```

## 🔍 Monitoring and Debugging

### Health Checks
```bash
# Overall system health
./docker/scripts/manage.sh health

# Check specific service
docker ps | grep billionaires
docker logs billionaires-flutter-dev
```

### Performance Monitoring
- **Prometheus**: http://localhost:9090 - Metrics and alerts
- **Grafana**: http://localhost:3001 - Dashboards and visualization
- **Container Stats**: `docker stats` or `./docker/scripts/manage.sh monitor`

### Debugging Issues
```bash
# Debug specific service
./docker/scripts/manage.sh debug n8n

# View detailed logs
./docker/scripts/deploy.sh logs -f flutter-dev

# Check resource usage
docker stats --no-stream
```

## 🔒 Security Best Practices

### Development
- Uses test credentials and disabled security features
- Firebase emulators for safe development
- No SSL encryption (HTTP only)

### Production
- **Change all default passwords** in `.env.production`
- **Enable SSL/TLS** with real certificates
- **Use strong API keys** for external services
- **Enable rate limiting** and security headers
- **Regular backups** of application data

## 🐛 Troubleshooting

### Common Issues

**Port Already in Use**:
```bash
# Find what's using the port
lsof -i :3000
# Kill the process
kill -9 $(lsof -t -i:3000)
```

**Permission Denied**:
```bash
# Fix script permissions
chmod +x docker/scripts/*.sh
# Add user to docker group (Linux)
sudo usermod -aG docker $USER
```

**Out of Disk Space**:
```bash
# Clean up Docker resources
./docker/scripts/manage.sh cleanup
# Remove everything (careful!)
docker system prune -a --volumes
```

**Service Won't Start**:
```bash
# Check logs
./docker/scripts/deploy.sh logs <service-name>
# Debug the service
./docker/scripts/manage.sh debug <service-name>
```

### Getting Help

1. Check service logs: `./docker/scripts/deploy.sh logs <service>`
2. Run health check: `./docker/scripts/manage.sh health`
3. Review the full documentation: `docker/README.md`
4. Check Docker system status: `docker system df` and `docker system events`

## 📊 Service Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Flutter App   │    │       n8n       │    │   PostgreSQL    │
│   (Port 3000)   │◄──►│   (Port 5678)   │◄──►│   (Port 5432)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Firebase Emulator│    │      Redis      │    │   Prometheus    │
│   (Port 4000)   │    │   (Port 6379)   │    │   (Port 9090)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │      Nginx      │    │     Grafana     │
                       │   (Port 80/443) │    │   (Port 3001)   │
                       └─────────────────┘    └─────────────────┘
```

## 🎉 Next Steps

After getting Docker running:

1. **Configure n8n workflows** for your automation needs
2. **Set up Firebase project** and replace emulator with real Firebase
3. **Configure external APIs** (Agora, Giphy, etc.)
4. **Set up monitoring alerts** in Grafana
5. **Plan production deployment** with proper SSL and domain

## 📚 Additional Resources

- [Full Docker Documentation](docker/README.md)
- [n8n Setup Guide](docs/n8n_setup_complete_guide.md)
- [Firebase Configuration](firebase.json)
- [Production Deployment Checklist](docs/PRODUCTION_DEPLOYMENT_CHECKLIST.md)

---

**Need help?** Check the troubleshooting section above or review the detailed documentation in the `docker/` directory.
