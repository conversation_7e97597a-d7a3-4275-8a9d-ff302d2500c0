# Environment Configuration Template for Billionaires Social
# Copy this file to .env and fill in your actual values

# =============================================================================
# Application Configuration
# =============================================================================
APP_NAME=Billionaires Social
APP_VERSION=1.0.0
APP_ENV=development
FLUTTER_ENV=development
NODE_ENV=development

# =============================================================================
# Firebase Configuration
# =============================================================================
FIREBASE_PROJECT_ID=billionaires-social
FIREBASE_API_KEY=your_firebase_api_key_here
FIREBASE_AUTH_DOMAIN=billionaires-social.firebaseapp.com
FIREBASE_DATABASE_URL=https://billionaires-social-default-rtdb.firebaseio.com
FIREBASE_STORAGE_BUCKET=billionaires-social.appspot.com
FIREBASE_MESSAGING_SENDER_ID=409365364995
FIREBASE_APP_ID=1:409365364995:web:279c38a92d715ae7b5a936

# Firebase Admin SDK (for server-side operations)
FIREBASE_ADMIN_SDK_PATH=/path/to/firebase-adminsdk.json
FIREBASE_ADMIN_PROJECT_ID=billionaires-social

# Firebase Emulator Configuration (for development)
FIREBASE_EMULATOR_HOST=localhost
FIRESTORE_EMULATOR_HOST=localhost:8081
FIREBASE_AUTH_EMULATOR_HOST=localhost:9099
FIREBASE_STORAGE_EMULATOR_HOST=localhost:9199
FIREBASE_FUNCTIONS_EMULATOR_HOST=localhost:5001

# =============================================================================
# n8n Configuration
# =============================================================================
N8N_HOST=localhost
N8N_PORT=5678
N8N_PROTOCOL=http
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=your_n8n_password_here

# n8n API Configuration
N8N_API_KEY=your_n8n_api_key_here
N8N_BASE_URL=http://localhost:5678
N8N_WEBHOOK_SECRET=your_webhook_secret_here
N8N_WEBHOOK_URL=http://localhost:5678

# n8n Database Configuration
N8N_DB_TYPE=postgresdb
N8N_DB_HOST=postgres
N8N_DB_PORT=5432
N8N_DB_NAME=n8n
N8N_DB_USER=n8n
N8N_DB_PASSWORD=your_postgres_password_here

# =============================================================================
# Database Configuration
# =============================================================================
# PostgreSQL (for n8n)
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=n8n
POSTGRES_USER=n8n
POSTGRES_PASSWORD=your_postgres_password_here

# Redis (for caching and sessions)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password_here
REDIS_DB=0

# =============================================================================
# Security Configuration
# =============================================================================
# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=24h

# Encryption keys
ENCRYPTION_KEY=your_32_character_encryption_key_here
WEBHOOK_SECRET=your_webhook_secret_key_here

# API Keys and Secrets
API_SECRET_KEY=your_api_secret_key_here
WEBHOOK_SIGNATURE_SECRET=your_webhook_signature_secret_here

# =============================================================================
# External Service Configuration
# =============================================================================
# Agora (for video/voice calling)
AGORA_APP_ID=your_agora_app_id_here
AGORA_APP_CERTIFICATE=your_agora_app_certificate_here

# Giphy API
GIPHY_API_KEY=your_giphy_api_key_here

# Unsplash API (for image search)
UNSPLASH_ACCESS_KEY=your_unsplash_access_key_here
UNSPLASH_SECRET_KEY=your_unsplash_secret_key_here

# Sentry (for error tracking)
SENTRY_DSN=your_sentry_dsn_here
SENTRY_ENVIRONMENT=development

# =============================================================================
# SSL/TLS Configuration
# =============================================================================
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem
SSL_ENABLED=false

# =============================================================================
# Monitoring and Logging
# =============================================================================
LOG_LEVEL=info
LOG_FORMAT=json

# Prometheus monitoring
PROMETHEUS_ENABLED=false
PROMETHEUS_PORT=9090

# Grafana
GRAFANA_ADMIN_PASSWORD=your_grafana_password_here
GRAFANA_PORT=3001

# =============================================================================
# Development Configuration
# =============================================================================
# Flutter development server
FLUTTER_DEV_PORT=3000
FLUTTER_DEBUG_PORT=8080

# Hot reload and debugging
FLUTTER_HOT_RELOAD=true
FLUTTER_DEBUG_MODE=true

# =============================================================================
# Production Configuration
# =============================================================================
# Domain configuration
DOMAIN_NAME=billionaires-social.com
WWW_DOMAIN=www.billionaires-social.com
N8N_DOMAIN=n8n.billionaires-social.com

# CDN Configuration
CDN_URL=https://cdn.billionaires-social.com
STATIC_ASSETS_URL=https://static.billionaires-social.com

# Performance settings
GZIP_ENABLED=true
CACHE_MAX_AGE=31536000
RATE_LIMIT_REQUESTS_PER_MINUTE=60

# =============================================================================
# Docker Configuration
# =============================================================================
DOCKER_NETWORK=billionaires-network
DOCKER_SUBNET=**********/16

# Container resource limits
MEMORY_LIMIT=2g
CPU_LIMIT=1.0

# =============================================================================
# Backup and Storage
# =============================================================================
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=/backups

# File upload limits
MAX_FILE_SIZE=100MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,mp4,mov,avi,pdf,doc,docx

# =============================================================================
# Feature Flags
# =============================================================================
FEATURE_MULTI_ACCOUNT=true
FEATURE_N8N_INTEGRATION=true
FEATURE_VIDEO_CALLING=true
FEATURE_STORY_CREATION=true
FEATURE_REEL_CREATION=true
FEATURE_ANALYTICS=true
FEATURE_PUSH_NOTIFICATIONS=true

# =============================================================================
# Localization
# =============================================================================
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,ar
RTL_LANGUAGES=ar

# =============================================================================
# Analytics and Tracking
# =============================================================================
ANALYTICS_ENABLED=true
GOOGLE_ANALYTICS_ID=your_ga_tracking_id_here
FIREBASE_ANALYTICS_ENABLED=true

# =============================================================================
# Email Configuration (if needed)
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password_here
SMTP_FROM_NAME=Billionaires Social
SMTP_FROM_EMAIL=<EMAIL>
