# Makefile for Billionaires Social Docker Management
# Provides convenient shortcuts for common Docker operations

.PHONY: help build dev prod test clean logs shell health backup

# Default target
help: ## Show this help message
	@echo "Billionaires Social Docker Management"
	@echo "====================================="
	@echo ""
	@echo "Available commands:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Development commands
dev: ## Start development environment
	@echo "🚀 Starting development environment..."
	./docker/scripts/deploy.sh -e development

dev-build: ## Build and start development environment
	@echo "🔨 Building and starting development environment..."
	./docker/scripts/build.sh -e development
	./docker/scripts/deploy.sh -e development -b

dev-logs: ## Show development logs
	@echo "📋 Showing development logs..."
	./docker/scripts/deploy.sh -e development logs

dev-shell: ## Open shell in development container
	@echo "🐚 Opening development shell..."
	./docker/scripts/deploy.sh -e development shell flutter-dev

# Production commands
prod: ## Start production environment
	@echo "🚀 Starting production environment..."
	./docker/scripts/deploy.sh -e production

prod-build: ## Build production images
	@echo "🔨 Building production images..."
	./docker/scripts/build.sh -e production

prod-deploy: ## Build and deploy production
	@echo "🚀 Building and deploying production..."
	./docker/scripts/build.sh -e production
	./docker/scripts/deploy.sh -e production

prod-logs: ## Show production logs
	@echo "📋 Showing production logs..."
	./docker/scripts/deploy.sh -e production logs

# Testing commands
test: ## Run tests in Docker
	@echo "🧪 Running tests..."
	docker-compose -f docker-compose.yml -f docker-compose.test.yml up --build flutter-test

test-integration: ## Run integration tests
	@echo "🧪 Running integration tests..."
	docker-compose -f docker-compose.yml -f docker-compose.test.yml up --build flutter-test firebase-emulator-test

test-coverage: ## Generate test coverage report
	@echo "📊 Generating test coverage..."
	docker-compose -f docker-compose.yml -f docker-compose.test.yml up --build test-reporter

# Management commands
build: ## Build all images
	@echo "🔨 Building all images..."
	./docker/scripts/build.sh -e development
	./docker/scripts/build.sh -e production

clean: ## Clean up Docker resources
	@echo "🧹 Cleaning up Docker resources..."
	./docker/scripts/manage.sh cleanup

clean-all: ## Clean up everything (WARNING: destroys all data)
	@echo "⚠️  WARNING: This will destroy all data!"
	@read -p "Are you sure? (y/N): " confirm && [ "$$confirm" = "y" ]
	./docker/scripts/manage.sh reset

# Monitoring commands
health: ## Check health of all services
	@echo "🏥 Checking service health..."
	./docker/scripts/manage.sh health

monitor: ## Monitor resource usage
	@echo "📊 Monitoring resources..."
	./docker/scripts/manage.sh monitor

logs: ## Show logs for all services
	@echo "📋 Showing all logs..."
	docker-compose logs -f

# Database commands
db-shell: ## Open PostgreSQL shell
	@echo "🗄️ Opening database shell..."
	./docker/scripts/deploy.sh shell postgres

db-backup: ## Backup database
	@echo "💾 Backing up database..."
	./docker/scripts/manage.sh backup

db-restore: ## Restore database (requires backup path)
	@echo "🔄 Restoring database..."
	@read -p "Enter backup path: " backup_path && \
	./docker/scripts/manage.sh restore "$$backup_path"

# n8n commands
n8n-shell: ## Open n8n shell
	@echo "🔄 Opening n8n shell..."
	./docker/scripts/deploy.sh shell n8n

n8n-logs: ## Show n8n logs
	@echo "📋 Showing n8n logs..."
	./docker/scripts/deploy.sh logs n8n

# Utility commands
stop: ## Stop all services
	@echo "🛑 Stopping all services..."
	./docker/scripts/deploy.sh down

restart: ## Restart all services
	@echo "🔄 Restarting all services..."
	./docker/scripts/deploy.sh restart

status: ## Show service status
	@echo "📊 Showing service status..."
	./docker/scripts/deploy.sh status

update: ## Update all services
	@echo "🔄 Updating all services..."
	./docker/scripts/manage.sh update

# Setup commands
setup: ## Initial setup (copy environment files)
	@echo "⚙️ Setting up environment..."
	@if [ ! -f .env.development ]; then \
		cp .env.template .env.development; \
		echo "✅ Created .env.development"; \
	fi
	@if [ ! -f .env.production ]; then \
		cp .env.template .env.production; \
		echo "✅ Created .env.production"; \
	fi
	@chmod +x docker/scripts/*.sh
	@echo "✅ Setup complete!"

setup-ssl: ## Generate self-signed SSL certificates
	@echo "🔒 Generating SSL certificates..."
	@mkdir -p docker/nginx/ssl
	@openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
		-keyout docker/nginx/ssl/key.pem \
		-out docker/nginx/ssl/cert.pem \
		-subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"
	@echo "✅ SSL certificates generated!"

# Quick start commands
quick-dev: setup ## Quick development start
	@echo "🚀 Quick development start..."
	$(MAKE) dev

quick-prod: setup setup-ssl ## Quick production start
	@echo "🚀 Quick production start..."
	$(MAKE) prod-deploy

# Documentation
docs: ## Open documentation
	@echo "📚 Opening documentation..."
	@if command -v open >/dev/null 2>&1; then \
		open docker/README.md; \
	elif command -v xdg-open >/dev/null 2>&1; then \
		xdg-open docker/README.md; \
	else \
		echo "Please open docker/README.md manually"; \
	fi
