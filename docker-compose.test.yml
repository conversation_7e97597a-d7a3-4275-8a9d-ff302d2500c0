# Docker Compose configuration for testing
# This file provides testing-specific services and configurations

version: '3.8'

services:
  # =============================================================================
  # Flutter Testing Service
  # =============================================================================
  flutter-test:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: billionaires-flutter-test
    volumes:
      - .:/app
      - flutter_pub_cache:/root/.pub-cache
      - test_coverage:/app/coverage
    environment:
      - FLUTTER_ENV=test
      - CHROME_EXECUTABLE=/usr/bin/chromium-browser
      - DISPLAY=:99
    command: >
      sh -c "
        echo 'Starting Flutter tests...' &&
        Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 &
        flutter doctor &&
        flutter pub get &&
        flutter test --coverage --reporter=json > test-results.json &&
        flutter test integration_test/
      "
    networks:
      - billionaires-network

  # =============================================================================
  # Test Database
  # =============================================================================
  postgres-test:
    image: postgres:15-alpine
    container_name: billionaires-postgres-test
    environment:
      - POSTGRES_DB=n8n_test
      - POSTGRES_USER=n8n_test
      - POSTGRES_PASSWORD=n8n_test_password
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      - ./docker/postgres/init-test.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - billionaires-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U n8n_test -d n8n_test"]
      interval: 10s
      timeout: 5s
      retries: 5

  # =============================================================================
  # Test Redis
  # =============================================================================
  redis-test:
    image: redis:7-alpine
    container_name: billionaires-redis-test
    command: redis-server --appendonly yes
    volumes:
      - redis_test_data:/data
    networks:
      - billionaires-network

  # =============================================================================
  # n8n Test Instance
  # =============================================================================
  n8n-test:
    image: n8nio/n8n:latest
    container_name: billionaires-n8n-test
    environment:
      - N8N_BASIC_AUTH_ACTIVE=false
      - N8N_HOST=0.0.0.0
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres-test
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=n8n_test
      - DB_POSTGRESDB_USER=n8n_test
      - DB_POSTGRESDB_PASSWORD=n8n_test_password
    volumes:
      - n8n_test_data:/home/<USER>/.n8n
    depends_on:
      - postgres-test
    networks:
      - billionaires-network

  # =============================================================================
  # Firebase Emulator for Testing
  # =============================================================================
  firebase-emulator-test:
    image: node:22-alpine
    container_name: billionaires-firebase-emulator-test
    working_dir: /app
    command: >
      sh -c "
        npm install -g firebase-tools &&
        firebase emulators:start --only firestore,auth,storage --host 0.0.0.0 --project test-project
      "
    ports:
      - "8081:8081"   # Firestore
      - "9099:9099"   # Auth
      - "9199:9199"   # Storage
    volumes:
      - .:/app
      - firebase_test_data:/app/.firebase
    environment:
      - FIREBASE_PROJECT_ID=test-project
      - GCLOUD_PROJECT=test-project
    networks:
      - billionaires-network

  # =============================================================================
  # Test Results Collector
  # =============================================================================
  test-reporter:
    image: node:22-alpine
    container_name: billionaires-test-reporter
    working_dir: /app
    volumes:
      - .:/app
      - test_coverage:/app/coverage
    command: >
      sh -c "
        npm install -g lcov-result-merger &&
        echo 'Collecting test results...' &&
        sleep 30 &&
        lcov-result-merger 'coverage/*.info' 'coverage/merged.info' &&
        echo 'Test results collected'
      "
    depends_on:
      - flutter-test
    networks:
      - billionaires-network

# =============================================================================
# Test-specific volumes
# =============================================================================
volumes:
  postgres_test_data:
    driver: local
  redis_test_data:
    driver: local
  n8n_test_data:
    driver: local
  firebase_test_data:
    driver: local
  test_coverage:
    driver: local
