# Complete n8n Setup Guide for Billionaires Social

This guide will walk you through setting up n8n Cloud with your Flutter app integration.

## Prerequisites

- ✅ Flutter app with n8n integration implemented
- ✅ Firebase project with Firestore enabled
- ✅ Google account for n8n Cloud
- ✅ Basic understanding of webhooks and APIs

## Step 1: Create n8n Cloud Account

### 1.1 Sign Up for n8n Cloud
1. Go to [n8n.cloud](https://n8n.cloud)
2. Click "Start for free"
3. Sign up with your Google account or email
4. Choose the **Starter plan** (free tier with 5,000 workflow executions/month)

### 1.2 Create Your First Workspace
1. After signup, you'll be prompted to create a workspace
2. Name it: `billionaires-social-workflows`
3. Choose your region (closest to your users)
4. Click "Create workspace"

### 1.3 Get Your API Credentials
1. In your n8n workspace, click on your profile (top right)
2. Go to "Settings" → "API Keys"
3. Click "Create API Key"
4. Name it: `billionaires-social-app`
5. **Copy and save the API key** - you'll need this for your Flutter app

## Step 2: Import Workflow Templates

### 2.1 Import Account Switch Workflow
1. In your n8n workspace, click "+" to create a new workflow
2. Click the "..." menu → "Import from file"
3. Upload `docs/n8n_workflows/account_switch_workflow.json`
4. The workflow will be imported with all nodes configured

### 2.2 Import User Engagement Workflow
1. Create another new workflow
2. Import `docs/n8n_workflows/user_engagement_workflow.json`
3. Verify all nodes are connected properly

### 2.3 Import Post Scheduling Workflow
1. Create another new workflow
2. Import `docs/n8n_workflows/post_scheduling_workflow.json`
3. Check the cron trigger is set to run every 5 minutes

### 2.4 Import Notification Workflow
1. Create the final workflow
2. Import `docs/n8n_workflows/notification_workflow.json`
3. Verify all webhook endpoints are configured

## Step 3: Configure Firebase Integration

### 3.1 Set Up Firebase Service Account
1. Go to [Firebase Console](https://console.firebase.google.com)
2. Select your project
3. Go to "Project Settings" → "Service accounts"
4. Click "Generate new private key"
5. Download the JSON file

### 3.2 Add Firebase Credentials to n8n
1. In n8n, go to "Settings" → "Credentials"
2. Click "Add credential"
3. Search for "Google Service Account"
4. Upload your Firebase service account JSON file
5. Name it: `firebase-billionaires-social`

### 3.3 Update Workflow Firebase Configuration
For each workflow that uses Firestore:
1. Open the workflow
2. Click on any "Google Firestore" node
3. In "Authentication" → select your Firebase credential
4. Update "Project ID" to your Firebase project ID
5. Save the workflow

## Step 4: Configure Webhook URLs

### 4.1 Get Webhook URLs
For each workflow:
1. Open the workflow
2. Click on the "Webhook" node (first node)
3. Copy the "Production URL" 
4. Save these URLs - you'll need them for your Flutter app

Your webhook URLs will look like:
```
Account Switch: https://your-instance.app.n8n.cloud/webhook/account-switch
User Engagement: https://your-instance.app.n8n.cloud/webhook/user-engagement
Post Scheduling: https://your-instance.app.n8n.cloud/webhook/schedule-post
Notifications: https://your-instance.app.n8n.cloud/webhook/send-notification
```

### 4.2 Generate Webhook Secret
1. Generate a secure random string (32+ characters)
2. You can use: `openssl rand -hex 32`
3. Save this as your webhook secret

## Step 5: Activate Workflows

### 5.1 Activate Each Workflow
1. Open each workflow
2. Click the toggle switch in the top right to "Active"
3. Verify the status shows "Active" with a green dot

### 5.2 Test Webhook Endpoints
1. Use a tool like Postman or curl to test each endpoint
2. Send a test POST request to each webhook URL
3. Check the execution log in n8n to verify it's working

Example test for account switch:
```bash
curl -X POST https://your-instance.app.n8n.cloud/webhook/account-switch \
  -H "Content-Type: application/json" \
  -d '{
    "fromAccountId": "test-from",
    "toAccountId": "test-to", 
    "userId": "test-user",
    "context": {
      "switchMethod": "manual",
      "deviceInfo": "test"
    }
  }'
```

## Step 6: Configure Your Flutter App

### 6.1 Update n8n Configuration
In your Flutter app, use the configuration widget or directly set:

```dart
await N8nConfig.initialize(
  apiKey: 'your-n8n-api-key-from-step-1.3',
  baseUrl: 'https://your-instance.app.n8n.cloud',
  webhookSecret: 'your-webhook-secret-from-step-4.2',
  enabled: true,
);
```

### 6.2 Test Integration
1. Open your Flutter app
2. Navigate to the n8n integration test screen
3. Check that the health status shows "healthy"
4. Test account switching to trigger workflows
5. Monitor the n8n execution logs

## Step 7: Production Considerations

### 7.1 Security Setup
1. **Enable webhook authentication** in n8n settings
2. **Use HTTPS only** for all communications
3. **Rotate API keys regularly** (monthly recommended)
4. **Monitor execution logs** for suspicious activity

### 7.2 Monitoring Setup
1. Set up **execution alerts** in n8n for failed workflows
2. Configure **email notifications** for critical failures
3. Monitor **execution quotas** to avoid hitting limits
4. Set up **Firebase monitoring** for database operations

### 7.3 Scaling Considerations
- **Starter Plan**: 5,000 executions/month (good for testing)
- **Pro Plan**: 50,000 executions/month ($20/month)
- **Business Plan**: 500,000 executions/month ($50/month)

## Step 8: Testing Your Complete Integration

### 8.1 End-to-End Test Checklist
- [ ] Account switching triggers n8n workflow
- [ ] User engagement events are logged
- [ ] Post scheduling works correctly
- [ ] Notifications are sent successfully
- [ ] Firebase data is updated properly
- [ ] Error handling works as expected

### 8.2 Test Scenarios
1. **Normal Account Switch**:
   - Switch between accounts in your Flutter app
   - Verify n8n logs the event in Firestore
   - Check analytics are updated

2. **Post Scheduling**:
   - Schedule a test post
   - Wait for the cron job to trigger
   - Verify post status is updated

3. **Notification Flow**:
   - Send a test notification
   - Check FCM delivery
   - Verify notification is logged

## Troubleshooting

### Common Issues

**Issue**: Webhook returns 404
- **Solution**: Check webhook URL is correct and workflow is active

**Issue**: Firebase permission denied
- **Solution**: Verify service account has proper Firestore permissions

**Issue**: Workflow execution fails
- **Solution**: Check execution logs in n8n for detailed error messages

**Issue**: Flutter app shows "unhealthy" status
- **Solution**: Verify API key and base URL are correct

### Debug Mode
Enable debug logging in your Flutter app:
```dart
const bool kN8nDebugMode = true; // Set to true for debugging
```

## Next Steps

Once your integration is working:
1. **Customize workflows** for your specific business needs
2. **Add more automation** (content moderation, user onboarding, etc.)
3. **Set up monitoring dashboards** 
4. **Scale to production** with appropriate n8n plan
5. **Implement Firebase Functions** for webhook handling (optional)

## Support Resources

- [n8n Documentation](https://docs.n8n.io/)
- [n8n Community Forum](https://community.n8n.io/)
- [Firebase Documentation](https://firebase.google.com/docs)
- Your workflow templates are in `docs/n8n_workflows/`

---

🎉 **Congratulations!** Your n8n integration is now ready for production use with your Billionaires Social app!
