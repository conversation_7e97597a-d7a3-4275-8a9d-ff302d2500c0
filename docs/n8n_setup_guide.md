# n8n Integration Setup Guide for Billionaires Social

This guide will help you set up n8n workflows for your Billionaires Social Flutter app.

## Prerequisites

1. n8n Cloud account or self-hosted n8n instance
2. Firebase project with Firestore enabled
3. Flutter app with the n8n integration services implemented

## Step 1: n8n Cloud Setup

### 1.1 Create n8n Cloud Account
1. Go to [n8n.cloud](https://n8n.cloud)
2. Sign up for an account
3. Create a new workflow instance

### 1.2 Configure API Access
1. Go to Settings > API Keys
2. Generate a new API key
3. Store this key securely in your Flutter app

## Step 2: Workflow Templates

### 2.1 Account Switch Workflow

```json
{
  "name": "Account Switch Workflow",
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "account-switch",
        "responseMode": "responseNode"
      },
      "id": "webhook-account-switch",
      "name": "Account Switch Webhook",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [240, 300]
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "={{$json.fromAccountId}}",
              "operation": "isNotEmpty"
            },
            {
              "value1": "={{$json.toAccountId}}",
              "operation": "isNotEmpty"
            }
          ]
        }
      },
      "id": "validate-input",
      "name": "Validate Input",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [460, 300]
    },
    {
      "parameters": {
        "authentication": "serviceAccount",
        "projectId": "your-firebase-project-id",
        "operation": "update",
        "documentId": "={{$json.userId}}",
        "collection": "users",
        "updateFields": {
          "activeAccountId": "={{$json.toAccountId}}",
          "lastAccountSwitch": "={{new Date().toISOString()}}",
          "switchHistory": "={{$json.context}}"
        }
      },
      "id": "update-firestore",
      "name": "Update Firestore",
      "type": "n8n-nodes-base.googleFirestore",
      "typeVersion": 1,
      "position": [680, 300]
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": {
          "success": true,
          "sessionId": "={{$json.sessionId}}",
          "switchedAt": "={{new Date().toISOString()}}"
        }
      },
      "id": "success-response",
      "name": "Success Response",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [900, 300]
    }
  ],
  "connections": {
    "Account Switch Webhook": {
      "main": [
        [
          {
            "node": "Validate Input",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Validate Input": {
      "main": [
        [
          {
            "node": "Update Firestore",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Update Firestore": {
      "main": [
        [
          {
            "node": "Success Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  }
}
```

### 2.2 Post Scheduling Workflow

```json
{
  "name": "Post Scheduling Workflow",
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "schedule-post",
        "responseMode": "responseNode"
      },
      "id": "webhook-schedule-post",
      "name": "Schedule Post Webhook",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [240, 300]
    },
    {
      "parameters": {
        "rule": {
          "interval": [
            {
              "field": "cronExpression",
              "expression": "={{$json.scheduledTime}}"
            }
          ]
        }
      },
      "id": "schedule-trigger",
      "name": "Schedule Trigger",
      "type": "n8n-nodes-base.cron",
      "typeVersion": 1,
      "position": [460, 300]
    },
    {
      "parameters": {
        "content": "={{$json.content}}",
        "platforms": "={{$json.platforms}}"
      },
      "id": "publish-post",
      "name": "Publish Post",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 1,
      "position": [680, 300]
    }
  ]
}
```

### 2.3 User Engagement Workflow

```json
{
  "name": "User Engagement Workflow",
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "user-engagement",
        "responseMode": "responseNode"
      },
      "id": "webhook-engagement",
      "name": "User Engagement Webhook",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [240, 300]
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "={{$json.eventType}}",
              "value2": "account_validation",
              "operation": "equal"
            }
          ]
        }
      },
      "id": "check-event-type",
      "name": "Check Event Type",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [460, 300]
    },
    {
      "parameters": {
        "authentication": "serviceAccount",
        "projectId": "your-firebase-project-id",
        "operation": "get",
        "documentId": "={{$json.eventData.accountId}}",
        "collection": "accounts"
      },
      "id": "get-account-data",
      "name": "Get Account Data",
      "type": "n8n-nodes-base.googleFirestore",
      "typeVersion": 1,
      "position": [680, 300]
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": {
          "success": true,
          "actionTaken": "account_validated",
          "analytics": {
            "isValid": "={{$json.isActive}}",
            "lastValidated": "={{new Date().toISOString()}}"
          }
        }
      },
      "id": "validation-response",
      "name": "Validation Response",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [900, 300]
    }
  ]
}
```

## Step 3: Security Configuration

### 3.1 Webhook Security
1. Generate a webhook secret in n8n
2. Store it securely in your Flutter app using `flutter_secure_storage`
3. Implement signature verification for all webhook requests

### 3.2 API Key Management
```dart
// Store API key securely
await N8nSecurityService.storeApiKey('your-n8n-api-key');

// Store webhook secret
await N8nSecurityService.storeWebhookSecret('your-webhook-secret');
```

## Step 4: Firebase Functions Setup (Production)

Create a Firebase Function to handle n8n webhooks:

```typescript
// functions/src/index.ts
import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import * as crypto from 'crypto';

admin.initializeApp();

export const n8nWebhook = functions.https.onRequest(async (req, res) => {
  // Verify webhook signature
  const signature = req.headers['x-signature'] as string;
  const timestamp = req.headers['x-timestamp'] as string;
  const payload = JSON.stringify(req.body);
  
  if (!verifySignature(payload, signature, timestamp)) {
    res.status(401).send('Unauthorized');
    return;
  }

  const webhookData = req.body;
  const webhookType = webhookData.type;

  try {
    switch (webhookType) {
      case 'account_switch_complete':
        await handleAccountSwitch(webhookData);
        break;
      case 'post_scheduled':
        await handlePostScheduled(webhookData);
        break;
      case 'notification_sent':
        await handleNotificationSent(webhookData);
        break;
      default:
        console.log('Unknown webhook type:', webhookType);
    }

    res.status(200).json({ status: 'success' });
  } catch (error) {
    console.error('Webhook processing error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

function verifySignature(payload: string, signature: string, timestamp: string): boolean {
  const secret = functions.config().n8n.webhook_secret;
  const message = `${payload}:${timestamp}`;
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(message)
    .digest('hex');
  
  return crypto.timingSafeEqual(
    Buffer.from(signature, 'hex'),
    Buffer.from(expectedSignature, 'hex')
  );
}

async function handleAccountSwitch(data: any) {
  await admin.firestore()
    .collection('users')
    .doc(data.userId)
    .update({
      lastAccountSwitch: admin.firestore.FieldValue.serverTimestamp(),
      activeAccountId: data.toAccountId,
      switchHistory: admin.firestore.FieldValue.arrayUnion(data)
    });
}

async function handlePostScheduled(data: any) {
  await admin.firestore()
    .collection('scheduledPosts')
    .doc(data.scheduleId)
    .set({
      ...data,
      status: 'scheduled',
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    });
}

async function handleNotificationSent(data: any) {
  await admin.firestore()
    .collection('notifications')
    .doc(data.notificationId)
    .update({
      status: data.status,
      deliveredAt: admin.firestore.FieldValue.serverTimestamp()
    });
}
```

## Step 5: Testing Your Integration

### 5.1 Test Account Switching
```dart
// In your Flutter app
final n8nIntegration = ref.read(n8nAccountIntegrationProvider);

await n8nIntegration.triggerAccountSwitchWorkflow(
  fromAccountId: 'current-account-id',
  toAccountId: 'target-account-id',
  switchContext: {
    'reason': 'user_initiated',
    'deviceInfo': 'iPhone 15 Pro',
  },
);
```

### 5.2 Test Post Scheduling
```dart
final n8nService = ref.read(n8nServiceProvider);

final result = await n8nService.schedulePost(
  content: 'Test post from n8n integration',
  mediaUrls: [],
  scheduledTime: DateTime.now().add(Duration(minutes: 5)),
  platforms: ['instagram', 'twitter'],
);
```

## Step 6: Monitoring and Analytics

### 6.1 Workflow Monitoring
- Set up n8n workflow monitoring
- Configure error notifications
- Track workflow execution metrics

### 6.2 Flutter App Monitoring
```dart
// Monitor workflow results
ref.listen(workflowResultsProvider, (previous, next) {
  next.when(
    data: (results) {
      // Handle workflow results
      for (final result in results) {
        print('Workflow ${result.workflowType} completed: ${result.result}');
      }
    },
    error: (error, stack) {
      // Handle errors
      print('Workflow monitoring error: $error');
    },
    loading: () {
      // Handle loading state
    },
  );
});
```

## Troubleshooting

### Common Issues
1. **Webhook not receiving data**: Check firewall settings and webhook URL
2. **Authentication failures**: Verify API keys and signatures
3. **Workflow timeouts**: Increase timeout settings in n8n
4. **Firebase permission errors**: Check Firestore security rules

### Debug Mode
Enable debug logging in your Flutter app:
```dart
// Enable debug mode for n8n integration
const bool kN8nDebugMode = true;

if (kN8nDebugMode) {
  print('N8n request: $payload');
  print('N8n response: $response');
}
```

## Best Practices

1. **Security**: Always validate webhook signatures
2. **Error Handling**: Implement comprehensive error handling
3. **Rate Limiting**: Implement rate limiting for API calls
4. **Monitoring**: Set up proper monitoring and alerting
5. **Testing**: Test workflows thoroughly before production
6. **Documentation**: Keep workflow documentation up to date

## Support

For additional support:
- n8n Documentation: https://docs.n8n.io/
- Firebase Documentation: https://firebase.google.com/docs
- Flutter Documentation: https://flutter.dev/docs
