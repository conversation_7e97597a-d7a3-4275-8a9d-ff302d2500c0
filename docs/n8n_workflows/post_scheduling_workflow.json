{"name": "Billionaires Social - Post Scheduling", "nodes": [{"parameters": {"httpMethod": "POST", "path": "schedule-post", "responseMode": "responseNode", "options": {}}, "id": "webhook-schedule-post", "name": "Schedule Post Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "schedule-post-webhook"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "validate-content", "leftValue": "={{ $json.content }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "validate-scheduled-time", "leftValue": "={{ $json.scheduledTime }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "validate-platforms", "leftValue": "={{ $json.platforms.length }}", "rightValue": "0", "operator": {"type": "number", "operation": "gt"}}], "combinator": "and"}}, "id": "validate-post-data", "name": "Validate Post Data", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"authentication": "serviceAccount", "projectId": "your-firebase-project-id", "operation": "create", "collection": "scheduled_posts", "fieldsToSend": "defineFields", "fields": {"scheduleId": "={{ 'post_' + new Date().getTime() + '_' + Math.random().toString(36).substr(2, 9) }}", "userId": "={{ $json.userId }}", "content": "={{ $json.content }}", "mediaUrls": "={{ $json.mediaUrls }}", "scheduledTime": "={{ $json.scheduledTime }}", "platforms": "={{ $json.platforms }}", "metadata": "={{ $json.metadata }}", "status": "scheduled", "createdAt": "={{ new Date().toISOString() }}", "accountId": "={{ $json.metadata.accountId }}", "accountType": "={{ $json.metadata.accountType }}"}}, "id": "save-scheduled-post", "name": "Save Scheduled Post", "type": "n8n-nodes-base.googleFirestore", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "*/5 * * * *"}]}}, "id": "schedule-checker", "name": "Schedule Checker (Every 5 min)", "type": "n8n-nodes-base.cron", "typeVersion": 1, "position": [240, 600]}, {"parameters": {"authentication": "serviceAccount", "projectId": "your-firebase-project-id", "operation": "getAll", "collection": "scheduled_posts", "options": {"where": {"values": [{"key": "status", "operation": "==", "value": "scheduled"}, {"key": "scheduledTime", "operation": "<=", "value": "={{ new Date().toISOString() }}"}]}}}, "id": "get-due-posts", "name": "Get Due Posts", "type": "n8n-nodes-base.googleFirestore", "typeVersion": 1, "position": [460, 600]}, {"parameters": {"batchSize": 1, "options": {}}, "id": "process-each-post", "name": "Process Each Post", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [680, 600]}, {"parameters": {"url": "https://api.your-social-media-service.com/publish", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Authorization", "value": "Bearer YOUR_SOCIAL_MEDIA_API_TOKEN"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "content", "value": "={{ $json.content }}"}, {"name": "media_urls", "value": "={{ $json.mediaUrls }}"}, {"name": "platforms", "value": "={{ $json.platforms }}"}, {"name": "account_id", "value": "={{ $json.accountId }}"}]}, "options": {}}, "id": "publish-post", "name": "Publish Post", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [900, 600], "continueOnFail": true}, {"parameters": {"authentication": "serviceAccount", "projectId": "your-firebase-project-id", "operation": "update", "documentId": "={{ $json.scheduleId }}", "collection": "scheduled_posts", "updateFields": {"status": "published", "publishedAt": "={{ new Date().toISOString() }}", "publishResult": "success"}}, "id": "update-post-status-success", "name": "Update Post Status (Success)", "type": "n8n-nodes-base.googleFirestore", "typeVersion": 1, "position": [1120, 550]}, {"parameters": {"authentication": "serviceAccount", "projectId": "your-firebase-project-id", "operation": "update", "documentId": "={{ $json.scheduleId }}", "collection": "scheduled_posts", "updateFields": {"status": "failed", "failedAt": "={{ new Date().toISOString() }}", "publishResult": "failed", "errorMessage": "{{ $json.error }}"}}, "id": "update-post-status-failed", "name": "Update Post Status (Failed)", "type": "n8n-nodes-base.googleFirestore", "typeVersion": 1, "position": [1120, 650]}, {"parameters": {"respondWith": "json", "responseBody": {"success": true, "scheduleId": "={{ $json.scheduleId }}", "scheduledTime": "={{ $json.scheduledTime }}", "platforms": "={{ $json.platforms }}", "message": "Post scheduled successfully"}}, "id": "schedule-success-response", "name": "Schedule Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [900, 300]}, {"parameters": {"respondWith": "json", "responseBody": {"success": false, "error": "Invalid post data", "message": "Missing required fields: content, scheduledTime, or platforms"}, "options": {"responseCode": 400}}, "id": "schedule-error-response", "name": "Schedule Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 500]}], "connections": {"Schedule Post Webhook": {"main": [[{"node": "Validate Post Data", "type": "main", "index": 0}]]}, "Validate Post Data": {"main": [[{"node": "Save Scheduled Post", "type": "main", "index": 0}], [{"node": "Schedule Error Response", "type": "main", "index": 0}]]}, "Save Scheduled Post": {"main": [[{"node": "Schedule Success Response", "type": "main", "index": 0}]]}, "Schedule Checker (Every 5 min)": {"main": [[{"node": "Get Due Posts", "type": "main", "index": 0}]]}, "Get Due Posts": {"main": [[{"node": "Process Each Post", "type": "main", "index": 0}]]}, "Process Each Post": {"main": [[{"node": "Publish Post", "type": "main", "index": 0}]]}, "Publish Post": {"main": [[{"node": "Update Post Status (Success)", "type": "main", "index": 0}]], "error": [[{"node": "Update Post Status (Failed)", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "billionaires-social", "name": "Billionaires Social"}], "triggerCount": 2, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}