{"name": "Billionaires Social - Account <PERSON>witch", "nodes": [{"parameters": {"httpMethod": "POST", "path": "account-switch", "responseMode": "responseNode", "options": {}}, "id": "webhook-account-switch", "name": "Account Switch Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "account-switch-webhook"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "validate-from-account", "leftValue": "={{ $json.fromAccountId }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "validate-to-account", "leftValue": "={{ $json.toAccountId }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "validate-user-id", "leftValue": "={{ $json.userId }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}], "combinator": "and"}}, "id": "validate-input", "name": "Validate Input", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"authentication": "serviceAccount", "projectId": "your-firebase-project-id", "operation": "update", "documentId": "={{ $json.userId }}", "collection": "users", "updateFields": {"activeAccountId": "={{ $json.toAccountId }}", "lastAccountSwitch": "={{ new Date().toISOString() }}", "switchHistory": "={{ $json.context }}", "switchMethod": "={{ $json.context.switchMethod }}", "deviceInfo": "={{ $json.context.deviceInfo }}"}}, "id": "update-firestore-user", "name": "Update User in Firestore", "type": "n8n-nodes-base.googleFirestore", "typeVersion": 1, "position": [680, 200]}, {"parameters": {"authentication": "serviceAccount", "projectId": "your-firebase-project-id", "operation": "create", "collection": "account_switch_history", "fieldsToSend": "defineFields", "fields": {"userId": "={{ $json.userId }}", "fromAccountId": "={{ $json.fromAccountId }}", "toAccountId": "={{ $json.toAccountId }}", "switchedAt": "={{ new Date().toISOString() }}", "switchMethod": "={{ $json.context.switchMethod }}", "deviceInfo": "={{ $json.context.deviceInfo }}", "accountType": "={{ $json.context.accountType }}", "isVerified": "={{ $json.context.isVerified }}", "isBillionaire": "={{ $json.context.isBillionaire }}", "success": true}}, "id": "log-switch-history", "name": "Log Switch History", "type": "n8n-nodes-base.googleFirestore", "typeVersion": 1, "position": [680, 400]}, {"parameters": {"url": "https://api.your-analytics-service.com/events", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "event_type", "value": "account_switch"}, {"name": "user_id", "value": "={{ $json.userId }}"}, {"name": "from_account_id", "value": "={{ $json.fromAccountId }}"}, {"name": "to_account_id", "value": "={{ $json.toAccountId }}"}, {"name": "switch_method", "value": "={{ $json.context.switchMethod }}"}, {"name": "timestamp", "value": "={{ new Date().toISOString() }}"}]}, "options": {}}, "id": "send-analytics", "name": "Send Analytics Event", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [900, 300], "continueOnFail": true}, {"parameters": {"respondWith": "json", "responseBody": {"success": true, "sessionId": "={{ $json.userId }}_{{ new Date().getTime() }}", "switchedAt": "={{ new Date().toISOString() }}", "fromAccountId": "={{ $json.fromAccountId }}", "toAccountId": "={{ $json.toAccountId }}", "message": "Account switch completed successfully"}}, "id": "success-response", "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 300]}, {"parameters": {"respondWith": "json", "responseBody": {"success": false, "error": "Invalid input data", "message": "Missing required fields: fromAccountId, toAccountId, or userId"}, "options": {"responseCode": 400}}, "id": "error-response", "name": "Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 500]}], "connections": {"Account Switch Webhook": {"main": [[{"node": "Validate Input", "type": "main", "index": 0}]]}, "Validate Input": {"main": [[{"node": "Update User in Firestore", "type": "main", "index": 0}, {"node": "Log Switch History", "type": "main", "index": 0}], [{"node": "Error Response", "type": "main", "index": 0}]]}, "Update User in Firestore": {"main": [[{"node": "Send Analytics Event", "type": "main", "index": 0}]]}, "Log Switch History": {"main": [[{"node": "Send Analytics Event", "type": "main", "index": 0}]]}, "Send Analytics Event": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "billionaires-social", "name": "Billionaires Social"}], "triggerCount": 1, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}