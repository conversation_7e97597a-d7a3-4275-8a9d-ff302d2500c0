{"name": "Billionaires Social - Notifications", "nodes": [{"parameters": {"httpMethod": "POST", "path": "send-notification", "responseMode": "responseNode", "options": {}}, "id": "webhook-send-notification", "name": "Send Notification Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "send-notification-webhook"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "validate-recipient", "leftValue": "={{ $json.recipientId }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "validate-title", "leftValue": "={{ $json.title }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "validate-body", "leftValue": "={{ $json.body }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}], "combinator": "and"}}, "id": "validate-notification-data", "name": "Validate Notification Data", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"authentication": "serviceAccount", "projectId": "your-firebase-project-id", "operation": "get", "documentId": "={{ $json.recipientId }}", "collection": "users"}, "id": "get-recipient-data", "name": "Get Recipient Data", "type": "n8n-nodes-base.googleFirestore", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "check-fcm-token", "leftValue": "={{ $json.fcmToken }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "check-notifications-enabled", "leftValue": "={{ $json.notificationsEnabled }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}}, "id": "check-notification-eligibility", "name": "Check Notification Eligibility", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"url": "https://fcm.googleapis.com/v1/projects/your-firebase-project-id/messages:send", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleApi", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "message", "value": {"token": "={{ $json.fcmToken }}", "notification": {"title": "={{ $('webhook-send-notification').first().json.title }}", "body": "={{ $('webhook-send-notification').first().json.body }}", "image": "={{ $('webhook-send-notification').first().json.imageUrl }}"}, "data": "={{ $('webhook-send-notification').first().json.data }}", "android": {"priority": "high", "notification": {"channel_id": "billionaires_social_notifications"}}, "apns": {"payload": {"aps": {"sound": "default", "badge": 1}}}}}]}, "options": {}}, "id": "send-fcm-notification", "name": "Send FCM Notification", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [1120, 200], "continueOnFail": true}, {"parameters": {"authentication": "serviceAccount", "projectId": "your-firebase-project-id", "operation": "create", "collection": "notification_logs", "fieldsToSend": "defineFields", "fields": {"notificationId": "={{ 'notif_' + new Date().getTime() + '_' + Math.random().toString(36).substr(2, 9) }}", "recipientId": "={{ $('webhook-send-notification').first().json.recipientId }}", "senderId": "={{ $('webhook-send-notification').first().json.senderId }}", "title": "={{ $('webhook-send-notification').first().json.title }}", "body": "={{ $('webhook-send-notification').first().json.body }}", "data": "={{ $('webhook-send-notification').first().json.data }}", "imageUrl": "={{ $('webhook-send-notification').first().json.imageUrl }}", "status": "sent", "sentAt": "={{ new Date().toISOString() }}", "deliveryMethod": "fcm", "fcmResponse": "={{ $json }}"}}, "id": "log-notification-success", "name": "Log Notification Success", "type": "n8n-nodes-base.googleFirestore", "typeVersion": 1, "position": [1340, 200]}, {"parameters": {"authentication": "serviceAccount", "projectId": "your-firebase-project-id", "operation": "create", "collection": "notification_logs", "fieldsToSend": "defineFields", "fields": {"notificationId": "={{ 'notif_' + new Date().getTime() + '_' + Math.random().toString(36).substr(2, 9) }}", "recipientId": "={{ $('webhook-send-notification').first().json.recipientId }}", "senderId": "={{ $('webhook-send-notification').first().json.senderId }}", "title": "={{ $('webhook-send-notification').first().json.title }}", "body": "={{ $('webhook-send-notification').first().json.body }}", "status": "failed", "failedAt": "={{ new Date().toISOString() }}", "deliveryMethod": "fcm", "errorMessage": "{{ $json.error }}", "reason": "fcm_send_failed"}}, "id": "log-notification-failure", "name": "Log Notification Failure", "type": "n8n-nodes-base.googleFirestore", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"authentication": "serviceAccount", "projectId": "your-firebase-project-id", "operation": "create", "collection": "notification_logs", "fieldsToSend": "defineFields", "fields": {"notificationId": "={{ 'notif_' + new Date().getTime() + '_' + Math.random().toString(36).substr(2, 9) }}", "recipientId": "={{ $('webhook-send-notification').first().json.recipientId }}", "title": "={{ $('webhook-send-notification').first().json.title }}", "body": "={{ $('webhook-send-notification').first().json.body }}", "status": "skipped", "skippedAt": "={{ new Date().toISOString() }}", "reason": "recipient_not_eligible", "details": "No FCM token or notifications disabled"}}, "id": "log-notification-skipped", "name": "Log Notification Skipped", "type": "n8n-nodes-base.googleFirestore", "typeVersion": 1, "position": [900, 500]}, {"parameters": {"respondWith": "json", "responseBody": {"success": true, "notificationId": "={{ $json.notificationId }}", "deliveryStatus": "sent", "message": "Notification sent successfully"}}, "id": "success-response", "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1560, 200]}, {"parameters": {"respondWith": "json", "responseBody": {"success": false, "notificationId": "={{ $json.notificationId }}", "deliveryStatus": "failed", "error": "Failed to send notification"}}, "id": "failure-response", "name": "Failure Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1560, 300]}, {"parameters": {"respondWith": "json", "responseBody": {"success": false, "notificationId": "={{ $json.notificationId }}", "deliveryStatus": "skipped", "message": "Recipient not eligible for notifications"}}, "id": "skipped-response", "name": "Skipped Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 500]}, {"parameters": {"respondWith": "json", "responseBody": {"success": false, "error": "Invalid notification data", "message": "Missing required fields: recipientId, title, or body"}, "options": {"responseCode": 400}}, "id": "validation-error-response", "name": "Validation Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 500]}], "connections": {"Send Notification Webhook": {"main": [[{"node": "Validate Notification Data", "type": "main", "index": 0}]]}, "Validate Notification Data": {"main": [[{"node": "Get Recipient Data", "type": "main", "index": 0}], [{"node": "Validation Error Response", "type": "main", "index": 0}]]}, "Get Recipient Data": {"main": [[{"node": "Check Notification Eligibility", "type": "main", "index": 0}]]}, "Check Notification Eligibility": {"main": [[{"node": "Send FCM Notification", "type": "main", "index": 0}], [{"node": "Log Notification Skipped", "type": "main", "index": 0}]]}, "Send FCM Notification": {"main": [[{"node": "Log Notification Success", "type": "main", "index": 0}]], "error": [[{"node": "Log Notification Failure", "type": "main", "index": 0}]]}, "Log Notification Success": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}, "Log Notification Failure": {"main": [[{"node": "Failure Response", "type": "main", "index": 0}]]}, "Log Notification Skipped": {"main": [[{"node": "Skipped Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "billionaires-social", "name": "Billionaires Social"}], "triggerCount": 1, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}