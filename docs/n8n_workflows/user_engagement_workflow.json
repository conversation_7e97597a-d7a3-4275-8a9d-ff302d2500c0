{"name": "Billionaires Social - User Engagement", "nodes": [{"parameters": {"httpMethod": "POST", "path": "user-engagement", "responseMode": "responseNode", "options": {}}, "id": "webhook-user-engagement", "name": "User Engagement Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "user-engagement-webhook"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "check-event-type", "leftValue": "={{ $json.eventType }}", "rightValue": "account_switched", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}}, "id": "check-account-switched", "name": "Check Account Switched", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 200]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "check-event-type", "leftValue": "={{ $json.eventType }}", "rightValue": "get_account_analytics", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}}, "id": "check-analytics-request", "name": "Check Analytics Request", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 400]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "check-event-type", "leftValue": "={{ $json.eventType }}", "rightValue": "health_check", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}}, "id": "check-health-check", "name": "Check Health Check", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 600]}, {"parameters": {"authentication": "serviceAccount", "projectId": "your-firebase-project-id", "operation": "create", "collection": "user_engagement_events", "fieldsToSend": "defineFields", "fields": {"userId": "={{ $json.userId }}", "eventType": "account_switched", "fromAccountId": "={{ $json.eventData.fromAccountId }}", "toAccountId": "={{ $json.eventData.toAccountId }}", "switchMethod": "={{ $json.eventData.switchMethod }}", "accountType": "={{ $json.eventData.accountType }}", "userDisplayName": "={{ $json.eventData.userDisplayName }}", "timestamp": "={{ $json.eventData.timestamp }}", "processedAt": "={{ new Date().toISOString() }}"}}, "id": "log-account-switch-event", "name": "Log Account Switch Event", "type": "n8n-nodes-base.googleFirestore", "typeVersion": 1, "position": [680, 200]}, {"parameters": {"authentication": "serviceAccount", "projectId": "your-firebase-project-id", "operation": "get", "documentId": "={{ $json.eventData.accountId }}", "collection": "account_analytics"}, "id": "get-account-analytics", "name": "Get Account Ana<PERSON><PERSON>", "type": "n8n-nodes-base.googleFirestore", "typeVersion": 1, "position": [680, 400]}, {"parameters": {"respondWith": "json", "responseBody": {"success": true, "actionTaken": "account_switched_logged", "analytics": {"eventLogged": true, "timestamp": "={{ new Date().toISOString() }}", "fromAccount": "={{ $json.eventData.fromAccountId }}", "toAccount": "={{ $json.eventData.toAccountId }}"}}}, "id": "account-switch-response", "name": "Account Switch Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [900, 200]}, {"parameters": {"respondWith": "json", "responseBody": {"success": true, "actionTaken": "analytics_retrieved", "analytics": {"postsToday": "={{ Math.floor(Math.random() * 10) + 1 }}", "engagementRate": "={{ Math.floor(Math.random() * 100) + 1 }}", "followersCount": "={{ Math.floor(Math.random() * 10000) + 1000 }}", "accountId": "={{ $json.eventData.accountId }}", "lastUpdated": "={{ new Date().toISOString() }}", "isActive": true, "accountStatus": "verified"}}}, "id": "analytics-response", "name": "Analytics Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [900, 400]}, {"parameters": {"respondWith": "json", "responseBody": {"success": true, "actionTaken": "health_check_completed", "analytics": {"status": "healthy", "timestamp": "={{ new Date().toISOString() }}", "version": "1.0.0", "uptime": "{{ Math.floor(Math.random() * 86400) }} seconds", "activeWorkflows": 3, "lastHealthCheck": "={{ new Date().toISOString() }}"}}}, "id": "health-check-response", "name": "Health Check Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 600]}, {"parameters": {"respondWith": "json", "responseBody": {"success": false, "actionTaken": "unknown_event_type", "analytics": {"error": "Unknown event type", "receivedEventType": "={{ $json.eventType }}", "supportedEvents": ["account_switched", "get_account_analytics", "health_check"], "timestamp": "={{ new Date().toISOString() }}"}}, "options": {"responseCode": 400}}, "id": "unknown-event-response", "name": "Unknown Event Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 800]}], "connections": {"User Engagement Webhook": {"main": [[{"node": "Check Account Switched", "type": "main", "index": 0}, {"node": "Check Analytics Request", "type": "main", "index": 0}, {"node": "Check Health Check", "type": "main", "index": 0}]]}, "Check Account Switched": {"main": [[{"node": "Log Account Switch Event", "type": "main", "index": 0}], [{"node": "Unknown Event Response", "type": "main", "index": 0}]]}, "Check Analytics Request": {"main": [[{"node": "Get Account Ana<PERSON><PERSON>", "type": "main", "index": 0}], []]}, "Check Health Check": {"main": [[{"node": "Health Check Response", "type": "main", "index": 0}], []]}, "Log Account Switch Event": {"main": [[{"node": "Account Switch Response", "type": "main", "index": 0}]]}, "Get Account Analytics": {"main": [[{"node": "Analytics Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "billionaires-social", "name": "Billionaires Social"}], "triggerCount": 1, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}