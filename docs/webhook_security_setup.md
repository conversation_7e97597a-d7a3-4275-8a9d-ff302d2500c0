# Webhook Security Configuration Guide

This guide covers securing your n8n webhooks and Flutter app integration.

## Security Overview

Your n8n integration uses multiple layers of security:
1. **HTTPS encryption** for all communications
2. **API key authentication** for n8n API calls
3. **Webhook signature verification** for incoming webhooks
4. **Request timestamp validation** to prevent replay attacks
5. **Rate limiting** to prevent abuse

## Step 1: Configure Webhook Signatures

### 1.1 Generate Webhook Secret
```bash
# Generate a secure 32-byte secret
openssl rand -hex 32
# Example output: a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456
```

### 1.2 Store Secret in Flutter App
```dart
// In your Flutter app initialization
await N8nConfig.initialize(
  apiKey: 'your-n8n-api-key',
  baseUrl: 'https://your-instance.app.n8n.cloud',
  webhookSecret: 'a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456',
  enabled: true,
);
```

### 1.3 Add Signature Validation to n8n Workflows

For each webhook in your n8n workflows, add signature validation:

1. **Add HTTP Request Validation Node** after the webhook trigger
2. **Configure the validation logic**:

```javascript
// Add this as a Code node in your n8n workflow
const crypto = require('crypto');

// Get the webhook secret (store this in n8n environment variables)
const webhookSecret = 'a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456';

// Get request data
const payload = JSON.stringify($input.first().json);
const receivedSignature = $input.first().headers['x-signature'];
const timestamp = $input.first().headers['x-timestamp'];

// Validate timestamp (within 5 minutes)
const currentTime = Date.now();
const requestTime = parseInt(timestamp);
if (currentTime - requestTime > 300000) {
  throw new Error('Request timestamp too old');
}

// Calculate expected signature
const message = `${payload}:${timestamp}`;
const expectedSignature = crypto
  .createHmac('sha256', webhookSecret)
  .update(message)
  .digest('hex');

// Verify signature
if (expectedSignature !== receivedSignature) {
  throw new Error('Invalid webhook signature');
}

// If we get here, the request is valid
return $input.all();
```

## Step 2: Configure n8n Environment Variables

### 2.1 Set Environment Variables in n8n Cloud
1. Go to your n8n workspace settings
2. Navigate to "Environment Variables"
3. Add these variables:

```
WEBHOOK_SECRET=a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456
FIREBASE_PROJECT_ID=your-firebase-project-id
SOCIAL_MEDIA_API_TOKEN=your-social-media-api-token
ANALYTICS_API_KEY=your-analytics-api-key
```

### 2.2 Update Workflows to Use Environment Variables
In your n8n workflows, replace hardcoded values:

```javascript
// Instead of hardcoded values, use:
const webhookSecret = $env.WEBHOOK_SECRET;
const projectId = $env.FIREBASE_PROJECT_ID;
```

## Step 3: Implement Rate Limiting

### 3.1 Add Rate Limiting Node to Workflows
Add this code node at the beginning of each workflow:

```javascript
// Rate limiting logic
const userId = $json.userId || 'anonymous';
const currentTime = Date.now();
const windowMs = 15 * 60 * 1000; // 15 minutes
const maxRequests = 100; // Max requests per window

// Get or initialize rate limit data
let rateLimitData = $workflow.staticData.rateLimits || {};
let userRequests = rateLimitData[userId] || [];

// Clean old requests
userRequests = userRequests.filter(time => currentTime - time < windowMs);

// Check if user has exceeded rate limit
if (userRequests.length >= maxRequests) {
  throw new Error(`Rate limit exceeded for user ${userId}`);
}

// Add current request
userRequests.push(currentTime);
rateLimitData[userId] = userRequests;
$workflow.staticData.rateLimits = rateLimitData;

return $input.all();
```

## Step 4: Configure CORS and Headers

### 4.1 Set Secure Headers in n8n Responses
Update your response nodes to include security headers:

```json
{
  "headers": {
    "X-Content-Type-Options": "nosniff",
    "X-Frame-Options": "DENY",
    "X-XSS-Protection": "1; mode=block",
    "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
    "Content-Security-Policy": "default-src 'self'"
  },
  "body": {
    "success": true,
    "data": "..."
  }
}
```

## Step 5: Monitor and Alert

### 5.1 Set Up Monitoring Workflow
Create a monitoring workflow that runs every hour:

```json
{
  "name": "Security Monitoring",
  "trigger": "cron: 0 * * * *",
  "actions": [
    {
      "type": "check_failed_authentications",
      "threshold": 10
    },
    {
      "type": "check_rate_limit_violations", 
      "threshold": 5
    },
    {
      "type": "send_alert_if_needed"
    }
  ]
}
```

### 5.2 Configure Alerts
Set up email/Slack alerts for:
- Failed webhook authentications
- Rate limit violations
- Unusual traffic patterns
- Workflow execution failures

## Step 6: Production Security Checklist

### 6.1 Pre-Production Checklist
- [ ] All webhooks use HTTPS
- [ ] Webhook signatures are validated
- [ ] API keys are stored securely
- [ ] Rate limiting is implemented
- [ ] Environment variables are used (no hardcoded secrets)
- [ ] Monitoring and alerting is configured
- [ ] Error messages don't leak sensitive information
- [ ] Request logging excludes sensitive data

### 6.2 Regular Security Maintenance
- [ ] Rotate API keys monthly
- [ ] Review webhook logs weekly
- [ ] Update dependencies regularly
- [ ] Monitor for security advisories
- [ ] Test security measures quarterly

## Step 7: Testing Security

### 7.1 Security Test Scripts

Test webhook signature validation:
```bash
#!/bin/bash
# test_webhook_security.sh

WEBHOOK_URL="https://your-instance.app.n8n.cloud/webhook/account-switch"
SECRET="your-webhook-secret"
TIMESTAMP=$(date +%s)000
PAYLOAD='{"test": "data"}'

# Create signature
SIGNATURE=$(echo -n "${PAYLOAD}:${TIMESTAMP}" | openssl dgst -sha256 -hmac "$SECRET" -hex | cut -d' ' -f2)

# Test valid request
curl -X POST "$WEBHOOK_URL" \
  -H "Content-Type: application/json" \
  -H "X-Signature: $SIGNATURE" \
  -H "X-Timestamp: $TIMESTAMP" \
  -d "$PAYLOAD"

# Test invalid signature (should fail)
curl -X POST "$WEBHOOK_URL" \
  -H "Content-Type: application/json" \
  -H "X-Signature: invalid-signature" \
  -H "X-Timestamp: $TIMESTAMP" \
  -d "$PAYLOAD"
```

### 7.2 Rate Limiting Test
```bash
#!/bin/bash
# test_rate_limiting.sh

WEBHOOK_URL="https://your-instance.app.n8n.cloud/webhook/user-engagement"

# Send 105 requests rapidly (should trigger rate limit)
for i in {1..105}; do
  curl -X POST "$WEBHOOK_URL" \
    -H "Content-Type: application/json" \
    -d '{"eventType": "test", "userId": "test-user"}' &
done
wait
```

## Step 8: Incident Response

### 8.1 Security Incident Playbook
1. **Detect**: Monitor alerts and logs
2. **Assess**: Determine severity and impact
3. **Contain**: Block malicious traffic
4. **Investigate**: Analyze logs and traces
5. **Recover**: Restore normal operations
6. **Learn**: Update security measures

### 8.2 Emergency Actions
- Disable compromised webhooks immediately
- Rotate all API keys and secrets
- Review and update security rules
- Notify stakeholders of any data exposure

## Troubleshooting Security Issues

### Common Security Problems

**Issue**: Webhook signature validation fails
```
Solution: Check timestamp is within 5-minute window and signature calculation matches
```

**Issue**: Rate limiting blocks legitimate users
```
Solution: Adjust rate limits or implement user-specific limits
```

**Issue**: CORS errors in browser
```
Solution: Configure proper CORS headers in n8n responses
```

## Security Best Practices Summary

1. **Never hardcode secrets** - use environment variables
2. **Validate all inputs** - check signatures, timestamps, and data
3. **Implement rate limiting** - prevent abuse and DoS attacks
4. **Monitor continuously** - set up alerts for suspicious activity
5. **Rotate credentials regularly** - monthly for API keys
6. **Use HTTPS everywhere** - encrypt all communications
7. **Log security events** - but exclude sensitive data
8. **Test security measures** - regular penetration testing

---

🔒 **Your n8n integration is now secured!** Follow this guide to maintain strong security posture.
