# Production Environment Configuration for Billionaires Social
# This file contains production-specific settings
# IMPORTANT: Replace all placeholder values with actual production credentials

# =============================================================================
# Application Configuration
# =============================================================================
APP_ENV=production
FLUTTER_ENV=production
NODE_ENV=production
DEBUG=false

# =============================================================================
# Firebase Configuration (Production)
# =============================================================================
FIREBASE_PROJECT_ID=billionaires-social
FIREBASE_USE_EMULATOR=false

# Production Firebase credentials (replace with actual values)
FIREBASE_API_KEY=REPLACE_WITH_ACTUAL_FIREBASE_API_KEY
FIREBASE_AUTH_DOMAIN=billionaires-social.firebaseapp.com
FIREBASE_DATABASE_URL=https://billionaires-social-default-rtdb.firebaseio.com
FIREBASE_STORAGE_BUCKET=billionaires-social.appspot.com
FIREBASE_MESSAGING_SENDER_ID=409365364995
FIREBASE_APP_ID=1:409365364995:web:279c38a92d715ae7b5a936

# =============================================================================
# n8n Configuration (Production)
# =============================================================================
N8N_HOST=n8n
N8N_PORT=5678
N8N_PROTOCOL=https
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=REPLACE_WITH_ACTUAL_N8N_USERNAME
N8N_BASIC_AUTH_PASSWORD=REPLACE_WITH_ACTUAL_N8N_PASSWORD

N8N_API_KEY=REPLACE_WITH_ACTUAL_N8N_API_KEY
N8N_BASE_URL=https://n8n.billionaires-social.com
N8N_WEBHOOK_URL=https://billionaires-social.com/webhooks
N8N_WEBHOOK_SECRET=REPLACE_WITH_ACTUAL_WEBHOOK_SECRET

# =============================================================================
# Database Configuration (Production)
# =============================================================================
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=n8n
POSTGRES_USER=n8n
POSTGRES_PASSWORD=REPLACE_WITH_ACTUAL_POSTGRES_PASSWORD

REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=REPLACE_WITH_ACTUAL_REDIS_PASSWORD
REDIS_DB=0

# =============================================================================
# Security Configuration (Production)
# =============================================================================
# IMPORTANT: Generate strong, unique keys for production
JWT_SECRET=REPLACE_WITH_ACTUAL_JWT_SECRET_KEY_MINIMUM_32_CHARACTERS
ENCRYPTION_KEY=REPLACE_WITH_ACTUAL_32_CHARACTER_ENCRYPTION_KEY
WEBHOOK_SECRET=REPLACE_WITH_ACTUAL_WEBHOOK_SECRET_KEY
API_SECRET_KEY=REPLACE_WITH_ACTUAL_API_SECRET_KEY

# =============================================================================
# Domain Configuration (Production)
# =============================================================================
DOMAIN_NAME=billionaires-social.com
WWW_DOMAIN=www.billionaires-social.com
N8N_DOMAIN=n8n.billionaires-social.com

# CDN Configuration
CDN_URL=https://cdn.billionaires-social.com
STATIC_ASSETS_URL=https://static.billionaires-social.com

# =============================================================================
# SSL/TLS Configuration (Production)
# =============================================================================
SSL_ENABLED=true
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# =============================================================================
# External Services (Production Keys)
# =============================================================================
# IMPORTANT: Replace with actual production API keys
AGORA_APP_ID=REPLACE_WITH_ACTUAL_AGORA_APP_ID
AGORA_APP_CERTIFICATE=REPLACE_WITH_ACTUAL_AGORA_CERTIFICATE

GIPHY_API_KEY=REPLACE_WITH_ACTUAL_GIPHY_API_KEY

UNSPLASH_ACCESS_KEY=REPLACE_WITH_ACTUAL_UNSPLASH_ACCESS_KEY
UNSPLASH_SECRET_KEY=REPLACE_WITH_ACTUAL_UNSPLASH_SECRET_KEY

SENTRY_DSN=REPLACE_WITH_ACTUAL_SENTRY_DSN
SENTRY_ENVIRONMENT=production

# =============================================================================
# Performance and Caching (Production)
# =============================================================================
GZIP_ENABLED=true
CACHE_ENABLED=true
CACHE_MAX_AGE=31536000
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100

# =============================================================================
# Monitoring and Logging (Production)
# =============================================================================
LOG_LEVEL=warn
LOG_FORMAT=json
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090

GRAFANA_ADMIN_PASSWORD=REPLACE_WITH_ACTUAL_GRAFANA_PASSWORD
GRAFANA_PORT=3001

# =============================================================================
# Analytics and Tracking (Production)
# =============================================================================
ANALYTICS_ENABLED=true
GOOGLE_ANALYTICS_ID=REPLACE_WITH_ACTUAL_GA_TRACKING_ID
FIREBASE_ANALYTICS_ENABLED=true

# =============================================================================
# Feature Flags (Production)
# =============================================================================
FEATURE_MULTI_ACCOUNT=true
FEATURE_N8N_INTEGRATION=true
FEATURE_VIDEO_CALLING=true
FEATURE_STORY_CREATION=true
FEATURE_REEL_CREATION=true
FEATURE_ANALYTICS=true
FEATURE_PUSH_NOTIFICATIONS=true

# =============================================================================
# Backup and Storage (Production)
# =============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=/backups

# File upload limits
MAX_FILE_SIZE=100MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,mp4,mov,avi,pdf,doc,docx

# =============================================================================
# Email Configuration (Production)
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=REPLACE_WITH_ACTUAL_EMAIL
SMTP_PASSWORD=REPLACE_WITH_ACTUAL_EMAIL_PASSWORD
SMTP_FROM_NAME=Billionaires Social
SMTP_FROM_EMAIL=<EMAIL>

# =============================================================================
# Docker Production Configuration
# =============================================================================
DOCKER_NETWORK=billionaires-network
MEMORY_LIMIT=4g
CPU_LIMIT=2.0

# =============================================================================
# Health Check Configuration
# =============================================================================
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3
