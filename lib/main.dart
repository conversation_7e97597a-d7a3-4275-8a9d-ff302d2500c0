import 'package:billionaires_social/core/providers/initialization_provider.dart';
import 'package:billionaires_social/core/app_themes.dart';
import 'package:billionaires_social/features/settings/providers/theme_provider.dart';
import 'package:billionaires_social/features/settings/providers/language_provider.dart';
import 'package:billionaires_social/core/localization/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:billionaires_social/core/widgets/auth_wrapper.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/core/main_navigation.dart';
import 'package:billionaires_social/features/auth/screens/n8n_integration_test_screen.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/core/services/universal_account_service.dart';
import 'package:billionaires_social/core/services/user_data_consistency_service.dart';
import 'package:billionaires_social/features/profile/services/account_management_service.dart';
import 'package:billionaires_social/core/services/data_migration_service.dart';
import 'package:billionaires_social/core/services/version_control_service.dart';
import 'package:billionaires_social/core/services/performance_monitoring_service.dart';
import 'package:billionaires_social/core/services/memory_management_service.dart';
import 'package:billionaires_social/core/performance/performance_optimizer.dart';
import 'package:billionaires_social/core/performance/ui_performance_service.dart';
import 'package:billionaires_social/core/services/background_media_cleanup_service.dart';

import 'package:billionaires_social/core/security/certificate_pinning_service.dart';
import 'package:billionaires_social/core/config/firebase_config.dart';
import 'package:billionaires_social/debug_billionaires_test.dart';
import 'package:billionaires_social/core/services/debug_crash_filter.dart';
import 'package:billionaires_social/core/services/enhanced_notification_service.dart';
import 'package:billionaires_social/features/onboarding/screens/beta_tester_onboarding_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'dart:async';
import 'dart:isolate';
import 'dart:ui';

void main() {
  // BEST PRACTICE: Initialize everything within runZonedGuarded to avoid zone mismatch
  runZonedGuarded<Future<void>>(
    () async {
      // Ensure Flutter bindings are initialized first
      WidgetsFlutterBinding.ensureInitialized();

      // Set debug zone errors to non-fatal during development
      if (kDebugMode) {
        BindingBase.debugZoneErrorsAreFatal = false;
      }

      // Initialize Firebase
      await Firebase.initializeApp();

      // Initialize Crashlytics
      await _initializeCrashlytics();

      // Initialize Sentry with production configuration
      await SentryFlutter.init((options) {
        // Production Sentry DSN - Replace with your actual production DSN
        options.dsn = const String.fromEnvironment(
          'SENTRY_DSN',
          defaultValue:
              '', // Leave empty for now - set via environment variable
        );

        // Only initialize Sentry if DSN is provided
        if (options.dsn?.isEmpty ?? true) {
          debugPrint('⚠️ Sentry DSN not configured - error reporting disabled');
          return;
        }

        options.environment = kDebugMode ? 'development' : 'production';
        options.release = '1.0.0+1';
        options.attachStacktrace = true;
        options.enableAutoSessionTracking = true;
        options.maxRequestBodySize = MaxRequestBodySize.medium;

        // Performance monitoring (reduced for production)
        options.tracesSampleRate = kDebugMode ? 0.1 : 0.01; // 1% in production
        options.enableAppHangTracking = true;

        // Debug options
        options.debug = kDebugMode;

        // Filter out debug-only errors in production
        options.beforeSend = (event, hint) {
          // Don't send debug-mode specific errors to production
          if (!kDebugMode &&
              event.message?.formatted != null &&
              event.message!.formatted.contains('debug')) {
            return null;
          }
          return event;
        };
      });

      // Initialize app services and run the app
      await runBillionairesApp();
    },
    (error, stackTrace) async {
      // Global error handler for the entire app
      debugPrint('🔥 GLOBAL ERROR: $error');
      debugPrint('Stack trace: $stackTrace');

      // Report to Firebase Crashlytics
      try {
        await FirebaseCrashlytics.instance.recordError(
          error,
          stackTrace,
          fatal: false,
          information: [
            'Error caught by main() runZonedGuarded',
            'Global error handler',
          ],
        );
      } catch (e) {
        debugPrint('❌ Failed to report to Crashlytics: $e');
      }

      // Report to Sentry as backup
      try {
        await Sentry.captureException(
          error,
          stackTrace: stackTrace,
          withScope: (scope) {
            scope.setTag('error_type', 'global_zone_error');
            scope.setTag('error_source', 'main_runZonedGuarded');
            scope.level = SentryLevel.error;
          },
        );
      } catch (e) {
        debugPrint('❌ Failed to report to Sentry: $e');
      }
    },
  );
}

/// Initialize Firebase Crashlytics with comprehensive error handling
Future<void> _initializeCrashlytics() async {
  try {
    // Enable Crashlytics collection
    await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);

    // Set up debug crash filtering
    await DebugCrashFilter.setupCrashlyticsWithFiltering();

    // Set up Flutter error handling for Crashlytics
    FlutterError.onError = (FlutterErrorDetails details) {
      // Log to console for debugging
      FlutterError.presentError(details);
      debugPrint('🔥 FLUTTER ERROR: ${details.exceptionAsString()}');
      if (details.stack != null) debugPrint(details.stack.toString());

      // Report to Firebase Crashlytics
      FirebaseCrashlytics.instance.recordFlutterFatalError(details);

      // Also report to Sentry for redundancy
      Sentry.captureException(
        details.exception,
        stackTrace: details.stack,
        withScope: (scope) {
          scope.setTag('error_type', 'flutter_error');
          scope.setTag('error_source', 'crashlytics_integration');
          scope.level = SentryLevel.error;
        },
      );
    };

    // Handle errors that occur outside of the Flutter framework
    PlatformDispatcher.instance.onError = (error, stack) {
      debugPrint('🔥 PLATFORM ERROR: $error');
      debugPrint('Stack trace: $stack');

      // Report to Firebase Crashlytics
      FirebaseCrashlytics.instance.recordError(
        error,
        stack,
        fatal: true,
        information: ['Platform error occurred outside Flutter framework'],
      );

      // Also report to Sentry
      Sentry.captureException(
        error,
        stackTrace: stack,
        withScope: (scope) {
          scope.setTag('error_type', 'platform_error');
          scope.setTag('error_source', 'crashlytics_integration');
          scope.level = SentryLevel.fatal;
        },
      );

      return true; // Indicates that the error was handled
    };

    // Handle isolate errors
    Isolate.current.addErrorListener(
      RawReceivePort((pair) async {
        final List<dynamic> errorAndStacktrace = pair;
        final error = errorAndStacktrace.first;
        final stackTrace = StackTrace.fromString(
          errorAndStacktrace.last.toString(),
        );

        debugPrint('🔥 ISOLATE ERROR: $error');
        debugPrint('Stack trace: $stackTrace');

        // Report to Firebase Crashlytics
        await FirebaseCrashlytics.instance.recordError(
          error,
          stackTrace,
          fatal: true,
          information: ['Isolate error occurred'],
        );

        // Also report to Sentry
        await Sentry.captureException(
          error,
          stackTrace: stackTrace,
          withScope: (scope) {
            scope.setTag('error_type', 'isolate_error');
            scope.setTag('error_source', 'crashlytics_integration');
            scope.level = SentryLevel.fatal;
          },
        );
      }).sendPort,
    );

    debugPrint('✅ Crashlytics error handlers configured');
  } catch (e, stackTrace) {
    debugPrint('❌ Failed to initialize Crashlytics: $e');
    debugPrint('Stack trace: $stackTrace');
    // Don't rethrow - allow app to continue without Crashlytics
  }
}

Future<void> runBillionairesApp() async {
  // Error handling is now managed by Crashlytics initialization
  // This function focuses on app initialization

  try {
    debugPrint('🚀 Starting Billionaires Social App...');

    // Firebase is already initialized in _initializeAndRunApp()
    debugPrint('✅ Firebase already initialized');

    // Setup service locator
    debugPrint('⚙️ Setting up service locator...');
    try {
      await setupServiceLocator();
      debugPrint('✅ Service locator setup completed');
    } catch (e, stackTrace) {
      debugPrint('❌ Service locator setup failed: $e');
      debugPrint('Stack trace: $stackTrace');
      rethrow;
    }

    // Initialize performance optimization services
    debugPrint('🚀 Initializing performance optimizations...');
    try {
      await PerformanceOptimizer().initialize();
      await UIPerformanceService().initialize();
      await BackgroundMediaCleanupService().initialize();
      debugPrint('✅ Performance optimization services initialized');
    } catch (e) {
      debugPrint('⚠️ Performance optimization initialization failed: $e');
      // Don't rethrow - allow app to continue without optimizations
    }

    // Initialize security services
    debugPrint('🔒 Initializing security services...');
    await CertificatePinningService().initialize();
    debugPrint('✅ Certificate pinning service initialized');

    // Initialize enhanced notification service
    debugPrint('🔔 Initializing notification service...');
    try {
      final notificationService = EnhancedNotificationService();
      final initialized = await notificationService.initializeNotifications();
      if (initialized) {
        debugPrint('✅ Enhanced notification service initialized');
      } else {
        debugPrint('⚠️ Notification service initialization failed');
      }
    } catch (e) {
      debugPrint('❌ Failed to initialize notification service: $e');
      // Don't rethrow - allow app to continue without notifications
    }

    // Ensure current user has universal defaults
    debugPrint('👤 Checking current user...');
    await UniversalAccountService.checkCurrentUser();
    debugPrint('✅ User check completed');

    // Initialize persistent account system
    debugPrint('💾 Initializing persistent account system...');
    try {
      final accountService = getIt<AccountManagementService>();
      await accountService.initializePersistentAccounts();
      debugPrint('✅ Persistent account system initialized');
    } catch (e) {
      debugPrint('⚠️ Persistent account initialization failed: $e');
      // Don't rethrow - allow app to continue without persistent accounts
    }

    // Validate and fix user data consistency
    debugPrint('🔍 Validating user data consistency...');
    await UserDataConsistencyService.validateCurrentUserDataConsistency();
    debugPrint('✅ User data consistency validated');

    // Run data migrations to fix existing posts
    debugPrint('🔧 Running data migrations...');
    await DataMigrationService.fixIncorrectMediaTypes();
    debugPrint('✅ Data migrations completed');

    // Start performance monitoring
    debugPrint('📊 Starting performance monitoring...');
    await PerformanceMonitoringService().startMonitoring();
    debugPrint('✅ Performance monitoring started');

    // Start memory management monitoring
    debugPrint('🧠 Starting memory management...');
    await getIt<MemoryManagementService>().startMonitoring();
    debugPrint('✅ Memory management started');

    // Verify Firebase indexes
    debugPrint('🔍 Verifying Firebase indexes...');
    try {
      await FirebaseConfig().setupIndexes();
      debugPrint('✅ Firebase index verification completed');
    } catch (e, stackTrace) {
      debugPrint('⚠️ Firebase index verification failed: $e');

      // Report to Firebase Crashlytics
      FirebaseCrashlytics.instance.recordError(
        e,
        stackTrace,
        fatal: false,
        information: [
          'Firebase index verification failed during app initialization',
        ],
      );

      // Also report to Sentry
      Sentry.captureException(
        e,
        stackTrace: stackTrace,
        withScope: (scope) {
          scope.setTag('error_type', 'firebase_indexes');
          scope.setTag('error_source', 'app_initialization');
          scope.level = SentryLevel.warning;
        },
      );
    }

    debugPrint('🎉 App initialization completed successfully!');
  } catch (e, stackTrace) {
    debugPrint('💥 CRITICAL ERROR during app initialization: $e');
    debugPrint('Stack trace: $stackTrace');

    // Report critical initialization error to Firebase Crashlytics
    try {
      await FirebaseCrashlytics.instance.recordError(
        e,
        stackTrace,
        fatal: true,
        information: [
          'Critical error during app initialization',
          'App will attempt to continue with basic functionality',
        ],
      );
      debugPrint('✅ Critical error reported to Crashlytics');
    } catch (crashlyticsError) {
      debugPrint('❌ Failed to report to Crashlytics: $crashlyticsError');
    }

    // Also report to Sentry as backup
    try {
      await Sentry.captureException(
        e,
        stackTrace: stackTrace,
        withScope: (scope) {
          scope.setTag('error_type', 'app_initialization');
          scope.setTag('error_source', 'critical_initialization_error');
          scope.setTag('app_recovery_attempted', 'true');
          scope.level = SentryLevel.fatal;
        },
      );
      debugPrint('✅ Critical error reported to Sentry');
    } catch (sentryError) {
      debugPrint('❌ Failed to report to Sentry: $sentryError');
    }

    // Still try to run the app with basic functionality
    debugPrint('⚠️ Running app with basic functionality...');
  }

  runApp(const ProviderScope(child: MyApp()));
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentTheme = ref.watch(themeProvider);
    final currentLocale = ref.watch(languageProvider);

    debugPrint(
      '🌍 Main app building with locale: ${currentLocale.languageCode}-${currentLocale.countryCode}',
    );
    debugPrint('🌍 Language provider state: $currentLocale');

    return MaterialApp(
      key: ValueKey(
        'app_${currentLocale.languageCode}_${currentLocale.countryCode}',
      ),
      title: 'Billionaires Social',
      theme: AppThemes.getTheme(currentTheme),
      darkTheme: AppThemes.getTheme(
        AppThemeType.luxuryWhiteGold,
      ), // Always use white theme for dark mode
      themeMode: ThemeMode.light, // Force light theme
      // Localization support
      locale: currentLocale,
      supportedLocales: AppLocalizations.supportedLocales,
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      // RTL support for Arabic
      builder: (context, child) {
        final locale = Localizations.localeOf(context);
        final isRTL =
            locale.languageCode == 'ar' ||
            locale.languageCode == 'he' ||
            locale.languageCode == 'fa';

        debugPrint(
          '🌍 App builder - Locale: ${locale.languageCode}, RTL: $isRTL',
        );

        return Directionality(
          textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
          child: child!,
        );
      },
      // Add route definitions
      routes: {
        '/main': (context) => const MainNavigation(),
        '/debug-billionaires': (context) => const DebugBillionairesTest(),
        '/n8n-test': (context) => const N8nIntegrationTestScreen(),
      },
      // Handle unknown routes
      onUnknownRoute: (settings) {
        debugPrint('🚨 Unknown route: ${settings.name}');
        return MaterialPageRoute(builder: (context) => const AppFlowManager());
      },
      home: const AppFlowManager(), // Directly to AppFlowManager
    );
  }
}

class AppFlowManager extends ConsumerWidget {
  const AppFlowManager({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appStatus = ref.watch(initializationProvider);
    debugPrint('AppFlowManager appStatus: $appStatus');
    return appStatus.when(
      data: (status) {
        debugPrint('AppFlowManager status: $status');
        switch (status) {
          case AppStatus.forceUpdate:
            return _buildForceUpdateDialog(context);
          case AppStatus.onboarding:
            return BetaTesterOnboardingScreen(
              onComplete: () async {
                // cspell:ignore prefs
                final prefs = await SharedPreferences.getInstance();
                await prefs.setBool('beta_onboarding_completed', true);
                ref.invalidate(initializationProvider);
              },
            );
          case AppStatus.authenticated:
            return const AuthWrapper();
          default:
            return const LaunchSplashScreen();
        }
      },
      loading: () => const LaunchSplashScreen(),
      error: (err, stack) {
        debugPrint('AppFlowManager error: $err');
        return Scaffold(
          body: Center(child: Text('Initialization Error: $err')),
        );
      },
    );
  }

  Widget _buildForceUpdateDialog(BuildContext context) {
    final versionService = getIt<VersionControlService>();
    final latestVersion = versionService.getLatestVersion();

    return Scaffold(
      backgroundColor: Colors.black87,
      body: Center(
        child: Container(
          margin: const EdgeInsets.all(32),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.system_update, size: 64, color: Colors.blue),
              const SizedBox(height: 16),
              const Text(
                'Update Required',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              Text(
                latestVersion?.updateMessage ??
                    'A new version is available and required to continue using the app.',
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 24),
              if (latestVersion?.changelog != null) ...[
                const Text(
                  'What\'s New:',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                ...latestVersion!.changelog!
                    .take(3)
                    .map(
                      (item) => Padding(
                        padding: const EdgeInsets.symmetric(vertical: 2),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.check,
                              size: 16,
                              color: Colors.green,
                            ),
                            const SizedBox(width: 8),
                            Expanded(child: Text(item)),
                          ],
                        ),
                      ),
                    ),
                const SizedBox(height: 24),
              ],
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => versionService.launchAppStore(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'Update Now',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class LaunchSplashScreen extends StatelessWidget {
  const LaunchSplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Initializing...'),
          ],
        ),
      ),
    );
  }
}
