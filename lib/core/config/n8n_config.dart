import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Configuration service for n8n integration
class N8nConfig {
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(),
    iOptions: IOSOptions(),
  );

  // Storage keys
  static const String _apiKeyKey = 'n8n_api_key';
  static const String _baseUrlKey = 'n8n_base_url';
  static const String _webhookSecretKey = 'n8n_webhook_secret';
  static const String _enabledKey = 'n8n_enabled';

  // Default configuration
  static const String defaultBaseUrl =
      'https://your-n8n-instance.app.n8n.cloud';
  static const bool defaultEnabled = false;

  /// Initialize n8n configuration
  static Future<void> initialize({
    required String apiKey,
    required String baseUrl,
    required String webhookSecret,
    bool enabled = true,
  }) async {
    await Future.wait([
      _storage.write(key: _api<PERSON><PERSON><PERSON>ey, value: apiKey),
      _storage.write(key: _baseUrlKey, value: baseUrl),
      _storage.write(key: _webhookSecretKey, value: webhookSecret),
      _storage.write(key: _enabledKey, value: enabled.toString()),
    ]);
  }

  /// Get API key
  static Future<String?> getApiKey() async {
    return await _storage.read(key: _apiKeyKey);
  }

  /// Get base URL
  static Future<String> getBaseUrl() async {
    final url = await _storage.read(key: _baseUrlKey);
    return url ?? defaultBaseUrl;
  }

  /// Get webhook secret
  static Future<String?> getWebhookSecret() async {
    return await _storage.read(key: _webhookSecretKey);
  }

  /// Check if n8n integration is enabled
  static Future<bool> isEnabled() async {
    final enabled = await _storage.read(key: _enabledKey);
    return enabled == 'true';
  }

  /// Enable/disable n8n integration
  static Future<void> setEnabled(bool enabled) async {
    await _storage.write(key: _enabledKey, value: enabled.toString());
  }

  /// Update API key
  static Future<void> updateApiKey(String apiKey) async {
    await _storage.write(key: _apiKeyKey, value: apiKey);
  }

  /// Update base URL
  static Future<void> updateBaseUrl(String baseUrl) async {
    await _storage.write(key: _baseUrlKey, value: baseUrl);
  }

  /// Update webhook secret
  static Future<void> updateWebhookSecret(String secret) async {
    await _storage.write(key: _webhookSecretKey, value: secret);
  }

  /// Clear all configuration
  static Future<void> clearConfiguration() async {
    await Future.wait([
      _storage.delete(key: _apiKeyKey),
      _storage.delete(key: _baseUrlKey),
      _storage.delete(key: _webhookSecretKey),
      _storage.delete(key: _enabledKey),
    ]);
  }

  /// Get complete configuration
  static Future<N8nConfiguration> getConfiguration() async {
    final results = await Future.wait([
      getApiKey(),
      getBaseUrl(),
      getWebhookSecret(),
      isEnabled(),
    ]);

    return N8nConfiguration(
      apiKey: results[0] as String?,
      baseUrl: results[1] as String,
      webhookSecret: results[2] as String?,
      enabled: results[3] as bool,
    );
  }

  /// Validate configuration
  static Future<bool> isConfigurationValid() async {
    final config = await getConfiguration();
    return config.apiKey != null &&
        config.webhookSecret != null &&
        config.baseUrl.isNotEmpty &&
        config.enabled;
  }

  /// Get webhook URLs for different workflows
  static Future<N8nWebhookUrls> getWebhookUrls() async {
    final baseUrl = await getBaseUrl();
    return N8nWebhookUrls(
      accountSwitch: '$baseUrl/webhook/account-switch',
      postScheduling: '$baseUrl/webhook/schedule-post',
      userEngagement: '$baseUrl/webhook/user-engagement',
      notification: '$baseUrl/webhook/send-notification',
      validation: '$baseUrl/webhook/validate-account',
    );
  }
}

/// Configuration model
class N8nConfiguration {
  final String? apiKey;
  final String baseUrl;
  final String? webhookSecret;
  final bool enabled;

  const N8nConfiguration({
    required this.apiKey,
    required this.baseUrl,
    required this.webhookSecret,
    required this.enabled,
  });

  bool get isValid =>
      apiKey != null && webhookSecret != null && baseUrl.isNotEmpty;

  N8nConfiguration copyWith({
    String? apiKey,
    String? baseUrl,
    String? webhookSecret,
    bool? enabled,
  }) {
    return N8nConfiguration(
      apiKey: apiKey ?? this.apiKey,
      baseUrl: baseUrl ?? this.baseUrl,
      webhookSecret: webhookSecret ?? this.webhookSecret,
      enabled: enabled ?? this.enabled,
    );
  }

  @override
  String toString() {
    return 'N8nConfiguration(baseUrl: $baseUrl, enabled: $enabled, hasApiKey: ${apiKey != null}, hasWebhookSecret: ${webhookSecret != null})';
  }
}

/// Webhook URLs model
class N8nWebhookUrls {
  final String accountSwitch;
  final String postScheduling;
  final String userEngagement;
  final String notification;
  final String validation;

  const N8nWebhookUrls({
    required this.accountSwitch,
    required this.postScheduling,
    required this.userEngagement,
    required this.notification,
    required this.validation,
  });
}

/// Riverpod providers for n8n configuration
final n8nConfigurationProvider = FutureProvider<N8nConfiguration>((ref) async {
  return await N8nConfig.getConfiguration();
});

final n8nWebhookUrlsProvider = FutureProvider<N8nWebhookUrls>((ref) async {
  return await N8nConfig.getWebhookUrls();
});

final n8nEnabledProvider = FutureProvider<bool>((ref) async {
  return await N8nConfig.isEnabled();
});

/// State notifier for managing n8n configuration
class N8nConfigurationNotifier
    extends StateNotifier<AsyncValue<N8nConfiguration>> {
  N8nConfigurationNotifier() : super(const AsyncValue.loading()) {
    _loadConfiguration();
  }

  Future<void> _loadConfiguration() async {
    try {
      final config = await N8nConfig.getConfiguration();
      state = AsyncValue.data(config);
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
    }
  }

  Future<void> updateConfiguration({
    String? apiKey,
    String? baseUrl,
    String? webhookSecret,
    bool? enabled,
  }) async {
    try {
      if (apiKey != null) await N8nConfig.updateApiKey(apiKey);
      if (baseUrl != null) await N8nConfig.updateBaseUrl(baseUrl);
      if (webhookSecret != null) {
        await N8nConfig.updateWebhookSecret(webhookSecret);
      }
      if (enabled != null) await N8nConfig.setEnabled(enabled);

      await _loadConfiguration();
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
    }
  }

  Future<void> initializeConfiguration({
    required String apiKey,
    required String baseUrl,
    required String webhookSecret,
    bool enabled = true,
  }) async {
    try {
      await N8nConfig.initialize(
        apiKey: apiKey,
        baseUrl: baseUrl,
        webhookSecret: webhookSecret,
        enabled: enabled,
      );
      await _loadConfiguration();
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
    }
  }

  Future<void> clearConfiguration() async {
    try {
      await N8nConfig.clearConfiguration();
      await _loadConfiguration();
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
    }
  }

  Future<void> toggleEnabled() async {
    final currentConfig = state.value;
    if (currentConfig != null) {
      await updateConfiguration(enabled: !currentConfig.enabled);
    }
  }

  void refresh() {
    _loadConfiguration();
  }
}

/// Provider for n8n configuration state notifier
final n8nConfigurationNotifierProvider =
    StateNotifierProvider<
      N8nConfigurationNotifier,
      AsyncValue<N8nConfiguration>
    >((ref) {
      return N8nConfigurationNotifier();
    });

/// Helper extension for easy access to configuration
extension N8nConfigurationRef on WidgetRef {
  AsyncValue<N8nConfiguration> get n8nConfig =>
      watch(n8nConfigurationNotifierProvider);
  N8nConfigurationNotifier get n8nConfigNotifier =>
      read(n8nConfigurationNotifierProvider.notifier);
}
