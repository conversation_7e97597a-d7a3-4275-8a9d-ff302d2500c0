import 'package:billionaires_social/features/feed/services/feed_service.dart';
import 'package:billionaires_social/features/feed/services/content_discovery_service.dart';
import 'package:billionaires_social/features/feed/services/content_access_service.dart';
import 'package:billionaires_social/features/feed/services/discovery_analytics_service.dart';
import 'package:billionaires_social/features/feed/providers/feed_filter_provider.dart';
import 'package:billionaires_social/features/stories/services/story_service.dart';
import 'package:billionaires_social/features/stories/services/story_settings_service.dart';
import 'package:billionaires_social/features/profile/services/profile_service.dart';
import 'package:billionaires_social/features/profile/services/close_friends_group_service.dart';
import 'package:billionaires_social/features/profile/services/close_friends_service.dart';
import 'package:billionaires_social/features/profile/services/followers_service.dart';
import 'package:billionaires_social/features/profile/services/account_management_service.dart';
import 'package:billionaires_social/features/profile/services/persistent_account_service.dart';
import 'package:billionaires_social/features/settings/services/settings_service.dart';
import 'package:billionaires_social/features/settings/services/chat_privacy_service.dart';
import 'package:billionaires_social/features/messaging/services/chat_service.dart';
import 'package:billionaires_social/features/messaging/services/chat_settings_service.dart';
import 'package:billionaires_social/features/messaging/services/call_service.dart';
import 'package:billionaires_social/features/payment/services/payment_service.dart';
import 'package:billionaires_social/features/payment/services/payment_processor_service.dart';
import 'package:billionaires_social/features/live/services/live_stream_service.dart';
import 'package:billionaires_social/features/analytics/services/analytics_tracking_service.dart';
import 'package:billionaires_social/features/settings/services/theme_service.dart';
import 'package:billionaires_social/core/services/firebase_service.dart';
import 'package:billionaires_social/core/services/performance_service.dart';
import 'package:billionaires_social/core/services/universal_account_service.dart';
import 'package:billionaires_social/core/services/universal_validation_service.dart';
import 'package:billionaires_social/core/services/user_data_consistency_service.dart';
import 'package:billionaires_social/core/services/analytics_service.dart';
import 'package:billionaires_social/core/services/analytics/unified_analytics_service.dart';
import 'package:billionaires_social/core/services/analytics/user_analytics_service.dart';
import 'package:billionaires_social/core/services/analytics/content_analytics_service.dart';
import 'package:billionaires_social/core/services/analytics/performance_analytics_service.dart';
import 'package:billionaires_social/core/services/cache_service.dart';
import 'package:billionaires_social/core/caching/advanced_cache_service.dart';
import 'package:billionaires_social/core/services/version_control_service.dart';
import 'package:billionaires_social/core/services/trending_service.dart';
import 'package:billionaires_social/features/events/services/events_service.dart';
import 'package:billionaires_social/features/notifications/services/notification_service.dart';
import 'package:billionaires_social/core/services/content_moderation_service.dart';
import 'package:billionaires_social/core/services/error_handling_service.dart';
import 'package:billionaires_social/core/services/image_optimization_service.dart';
import 'package:billionaires_social/core/services/performance_monitoring_service.dart';
import 'package:billionaires_social/core/services/memory_management_service.dart';
import 'package:billionaires_social/core/services/crashlytics_test_service.dart';
import 'package:billionaires_social/core/services/security_service.dart';
import 'package:billionaires_social/core/services/input_validation_service.dart';
import 'package:billionaires_social/core/services/rate_limiting_service.dart';
import 'package:billionaires_social/core/services/session_management_service.dart';

import 'package:billionaires_social/features/stories/services/story_interaction_service.dart';
import 'package:billionaires_social/features/stories/services/unified_story_service.dart';
import 'package:billionaires_social/features/auth/services/verification_service.dart';
import 'package:billionaires_social/features/verification/services/verification_payment_service.dart';
import 'package:billionaires_social/features/verification/services/verification_criteria_service.dart';
import 'package:billionaires_social/features/verification/services/verification_security_audit.dart';
import 'package:billionaires_social/services/n8n_service.dart';
import 'package:billionaires_social/services/n8n_security_service.dart';
import 'package:billionaires_social/services/n8n_webhook_service.dart';
import 'package:billionaires_social/services/n8n_firebase_integration.dart';
import 'package:billionaires_social/features/auth/services/n8n_account_integration.dart';
import 'package:get_it/get_it.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';

final GetIt getIt = GetIt.instance;

Future<void> setupServiceLocator() async {
  // Core Services
  if (!getIt.isRegistered<FirebaseService>()) {
    getIt.registerSingleton<FirebaseService>(FirebaseService());
  }
  if (!getIt.isRegistered<PerformanceService>()) {
    getIt.registerSingleton<PerformanceService>(PerformanceService());
  }
  if (!getIt.isRegistered<AnalyticsService>()) {
    getIt.registerSingleton<AnalyticsService>(AnalyticsService());
  }
  if (!getIt.isRegistered<UnifiedAnalyticsService>()) {
    getIt.registerSingleton<UnifiedAnalyticsService>(UnifiedAnalyticsService());
  }
  if (!getIt.isRegistered<UserAnalyticsService>()) {
    getIt.registerSingleton<UserAnalyticsService>(UserAnalyticsService());
  }
  if (!getIt.isRegistered<ContentAnalyticsService>()) {
    getIt.registerSingleton<ContentAnalyticsService>(ContentAnalyticsService());
  }
  if (!getIt.isRegistered<PerformanceAnalyticsService>()) {
    getIt.registerSingleton<PerformanceAnalyticsService>(
      PerformanceAnalyticsService(),
    );
  }
  if (!getIt.isRegistered<CacheService>()) {
    getIt.registerSingleton<CacheService>(CacheService());
  }
  if (!getIt.isRegistered<AdvancedCacheService>()) {
    getIt.registerSingleton<AdvancedCacheService>(AdvancedCacheService());
  }
  if (!getIt.isRegistered<VersionControlService>()) {
    getIt.registerSingleton<VersionControlService>(VersionControlService());
  }
  if (!getIt.isRegistered<ErrorHandlingService>()) {
    getIt.registerSingleton<ErrorHandlingService>(ErrorHandlingService());
  }
  if (!getIt.isRegistered<SecurityService>()) {
    getIt.registerSingleton<SecurityService>(SecurityService());
  }
  if (!getIt.isRegistered<SessionManagementService>()) {
    getIt.registerSingleton<SessionManagementService>(
      SessionManagementService(),
    );
  }
  if (!getIt.isRegistered<InputValidationService>()) {
    getIt.registerSingleton<InputValidationService>(InputValidationService());
  }
  if (!getIt.isRegistered<RateLimitingService>()) {
    getIt.registerSingleton<RateLimitingService>(RateLimitingService());
  }
  if (!getIt.isRegistered<MemoryManagementService>()) {
    getIt.registerSingleton<MemoryManagementService>(MemoryManagementService());
  }
  if (!getIt.isRegistered<PerformanceMonitoringService>()) {
    getIt.registerSingleton<PerformanceMonitoringService>(
      PerformanceMonitoringService(),
    );
  }
  if (!getIt.isRegistered<ImageOptimizationService>()) {
    getIt.registerSingleton<ImageOptimizationService>(
      ImageOptimizationService(),
    );
  }
  if (!getIt.isRegistered<UniversalAccountService>()) {
    getIt.registerSingleton<UniversalAccountService>(UniversalAccountService());
  }
  if (!getIt.isRegistered<UniversalValidationService>()) {
    getIt.registerSingleton<UniversalValidationService>(
      UniversalValidationService(),
    );
  }
  if (!getIt.isRegistered<UserDataConsistencyService>()) {
    getIt.registerSingleton<UserDataConsistencyService>(
      UserDataConsistencyService(),
    );
  }

  // Feature Services
  if (!getIt.isRegistered<NotificationService>()) {
    getIt.registerSingleton<NotificationService>(NotificationService());
  }
  if (!getIt.isRegistered<FeedService>()) {
    getIt.registerSingleton<FeedService>(FeedService());
  }
  if (!getIt.isRegistered<ContentDiscoveryService>()) {
    getIt.registerSingleton<ContentDiscoveryService>(ContentDiscoveryService());
  }
  if (!getIt.isRegistered<ContentAccessService>()) {
    getIt.registerSingleton<ContentAccessService>(ContentAccessService());
  }
  if (!getIt.isRegistered<DiscoveryAnalyticsService>()) {
    getIt.registerSingleton<DiscoveryAnalyticsService>(
      DiscoveryAnalyticsService(),
    );
  }
  if (!getIt.isRegistered<FeedFilterNotifier>()) {
    getIt.registerSingleton<FeedFilterNotifier>(FeedFilterNotifier());
  }
  if (!getIt.isRegistered<StoryService>()) {
    getIt.registerSingleton<StoryService>(StoryService());
  }
  if (!getIt.isRegistered<StorySettingsService>()) {
    getIt.registerSingleton<StorySettingsService>(StorySettingsService());
  }
  if (!getIt.isRegistered<ProfileService>()) {
    getIt.registerSingleton<ProfileService>(ProfileService());
  }
  if (!getIt.isRegistered<CloseFriendsGroupService>()) {
    getIt.registerSingleton<CloseFriendsGroupService>(
      CloseFriendsGroupService(),
    );
  }
  if (!getIt.isRegistered<CloseFriendsService>()) {
    getIt.registerSingleton<CloseFriendsService>(CloseFriendsService());
  }
  if (!getIt.isRegistered<FollowersService>()) {
    getIt.registerSingleton<FollowersService>(FollowersService());
  }
  if (!getIt.isRegistered<AccountManagementService>()) {
    getIt.registerSingleton<AccountManagementService>(
      AccountManagementService(),
    );
  }
  if (!getIt.isRegistered<PersistentAccountService>()) {
    getIt.registerSingleton<PersistentAccountService>(
      PersistentAccountService(),
    );
  }
  if (!getIt.isRegistered<SettingsService>()) {
    getIt.registerSingleton<SettingsService>(SettingsService());
  }
  if (!getIt.isRegistered<ChatPrivacyService>()) {
    getIt.registerSingleton<ChatPrivacyService>(ChatPrivacyService());
  }
  if (!getIt.isRegistered<ChatService>()) {
    getIt.registerSingleton<ChatService>(ChatService());
  }
  if (!getIt.isRegistered<ChatSettingsService>()) {
    getIt.registerSingleton<ChatSettingsService>(ChatSettingsService());
  }
  if (!getIt.isRegistered<CallService>()) {
    getIt.registerSingleton<CallService>(CallService());
  }
  if (!getIt.isRegistered<PaymentProcessorService>()) {
    getIt.registerSingleton<PaymentProcessorService>(PaymentProcessorService());
  }
  if (!getIt.isRegistered<PaymentService>()) {
    getIt.registerSingleton<PaymentService>(PaymentService());
  }
  if (!getIt.isRegistered<LiveStreamService>()) {
    getIt.registerSingleton<LiveStreamService>(LiveStreamService());
  }
  if (!getIt.isRegistered<AnalyticsTrackingService>()) {
    getIt.registerSingleton<AnalyticsTrackingService>(
      AnalyticsTrackingService(),
    );
  }
  if (!getIt.isRegistered<ThemeService>()) {
    getIt.registerSingleton<ThemeService>(ThemeService());
    debugPrint('[ServiceLocator] ThemeService registered');
  }
  if (!getIt.isRegistered<EventsService>()) {
    getIt.registerSingleton<EventsService>(EventsService());
  }
  if (!getIt.isRegistered<TrendingService>()) {
    getIt.registerSingleton<TrendingService>(TrendingService());
  }
  if (!getIt.isRegistered<ContentModerationService>()) {
    getIt.registerSingleton<ContentModerationService>(
      ContentModerationService(),
    );
  }
  if (!getIt.isRegistered<StoryInteractionService>()) {
    debugPrint('📝 Registering StoryInteractionService...');
    getIt.registerSingleton<StoryInteractionService>(StoryInteractionService());
    debugPrint('✅ StoryInteractionService registered successfully');
  } else {
    debugPrint('ℹ️ StoryInteractionService already registered');
  }
  if (!getIt.isRegistered<UnifiedStoryService>()) {
    getIt.registerSingleton<UnifiedStoryService>(UnifiedStoryService());
  }
  if (!getIt.isRegistered<VerificationService>()) {
    getIt.registerSingleton<VerificationService>(VerificationService());
  }
  if (!getIt.isRegistered<VerificationPaymentService>()) {
    getIt.registerSingleton<VerificationPaymentService>(
      VerificationPaymentService(),
    );
  }
  if (!getIt.isRegistered<VerificationCriteriaService>()) {
    getIt.registerSingleton<VerificationCriteriaService>(
      VerificationCriteriaService(),
    );
  }
  if (!getIt.isRegistered<VerificationSecurityAudit>()) {
    getIt.registerSingleton<VerificationSecurityAudit>(
      VerificationSecurityAudit(),
    );
  }

  // n8n Integration Services
  if (!getIt.isRegistered<N8nService>()) {
    getIt.registerSingleton<N8nService>(N8nService());
    debugPrint('[ServiceLocator] N8nService registered');
  }
  if (!getIt.isRegistered<N8nSecurityService>()) {
    getIt.registerSingleton<N8nSecurityService>(N8nSecurityService());
    debugPrint('[ServiceLocator] N8nSecurityService registered');
  }
  if (!getIt.isRegistered<N8nWebhookService>()) {
    getIt.registerSingleton<N8nWebhookService>(N8nWebhookService());
    debugPrint('[ServiceLocator] N8nWebhookService registered');
  }
  if (!getIt.isRegistered<N8nFirebaseIntegration>()) {
    final n8nService = getIt<N8nService>();
    getIt.registerSingleton<N8nFirebaseIntegration>(
      N8nFirebaseIntegration(n8nService: n8nService),
    );
    debugPrint('[ServiceLocator] N8nFirebaseIntegration registered');
  }
  if (!getIt.isRegistered<N8nAccountIntegration>()) {
    final n8nService = getIt<N8nService>();
    final n8nFirebaseIntegration = getIt<N8nFirebaseIntegration>();
    getIt.registerSingleton<N8nAccountIntegration>(
      N8nAccountIntegration(
        n8nService: n8nService,
        firebaseIntegration: n8nFirebaseIntegration,
      ),
    );
    debugPrint('[ServiceLocator] N8nAccountIntegration registered');
  }
  if (!getIt.isRegistered<CrashlyticsTestService>()) {
    getIt.registerSingleton<CrashlyticsTestService>(CrashlyticsTestService());
  }

  // Initialize services
  await getIt<PerformanceService>().initialize();
  await getIt<AnalyticsService>().initialize();
  await getIt<CacheService>().initialize();
  await getIt<AdvancedCacheService>().initialize();
  await getIt<VersionControlService>().initialize();

  // Initialize notification service with error handling
  try {
    debugPrint('🔔 Initializing notification service...');
    await getIt<NotificationService>().initialize();
    debugPrint('✅ Notification service initialized successfully');
  } catch (e) {
    debugPrint(
      '⚠️ Notification service initialization failed (this is normal): $e',
    );
    // Don't rethrow - allow app to continue without notifications
  }

  // Initialize n8n services with error handling
  try {
    debugPrint('🔗 Initializing n8n services...');
    await getIt<N8nService>().initialize();
    debugPrint('✅ n8n services initialized successfully');
  } catch (e) {
    debugPrint('⚠️ n8n service initialization failed (this is normal): $e');
    // Don't rethrow - allow app to continue without n8n integration
  }

  // Initialize Firebase Crashlytics
  await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);

  // Set up error handling for Flutter errors
  FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterFatalError;

  // Set up error handling for async errors
  PlatformDispatcher.instance.onError = (error, stack) {
    FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
    return true;
  };
}
