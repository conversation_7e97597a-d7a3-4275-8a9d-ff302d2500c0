import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'n8n_security_service.dart';

/// Service for handling webhooks from n8n workflows
class N8nWebhookService {
  HttpServer? _server;
  final List<N8nWebhookHandler> _handlers = [];

  /// Start webhook server (for development/testing)
  /// In production, use Firebase Functions or similar
  Future<void> startWebhookServer({int port = 8080}) async {
    if (kDebugMode) {
      try {
        _server = await HttpServer.bind(InternetAddress.anyIPv4, port);
        print('N8n webhook server started on port $port');

        await for (HttpRequest request in _server!) {
          _handleWebhookRequest(request);
        }
      } catch (e) {
        print('Failed to start webhook server: $e');
      }
    }
  }

  /// Handle incoming webhook requests
  Future<void> _handleWebhookRequest(HttpRequest request) async {
    try {
      if (request.method != 'POST') {
        request.response.statusCode = 405;
        await request.response.close();
        return;
      }

      // Read request body
      final body = await utf8.decoder.bind(request).join();

      // Extract headers
      final headers = <String, String>{};
      request.headers.forEach((name, values) {
        headers[name.toLowerCase()] = values.first;
      });

      // Validate webhook signature
      final isValid = await N8nSecurityMiddleware.validateIncomingWebhook(
        payload: body,
        headers: headers,
      );

      if (!isValid) {
        request.response.statusCode = 401;
        await request.response.close();
        return;
      }

      // Parse webhook data
      final webhookData = json.decode(body) as Map<String, dynamic>;
      final webhookType = webhookData['type'] as String?;

      if (webhookType == null) {
        request.response.statusCode = 400;
        await request.response.close();
        return;
      }

      // Process webhook
      await _processWebhook(webhookType, webhookData);

      // Send success response
      request.response.statusCode = 200;
      request.response.headers.contentType = ContentType.json;
      request.response.write(json.encode({'status': 'success'}));
      await request.response.close();
    } catch (e) {
      debugPrint('Error handling webhook: $e');
      request.response.statusCode = 500;
      await request.response.close();
    }
  }

  /// Process different types of webhooks
  Future<void> _processWebhook(String type, Map<String, dynamic> data) async {
    for (final handler in _handlers) {
      if (handler.canHandle(type)) {
        await handler.handle(type, data);
      }
    }

    // Built-in handlers
    switch (type) {
      case 'account_switch_complete':
        await _handleAccountSwitchComplete(data);
        break;
      case 'post_scheduled':
        await _handlePostScheduled(data);
        break;
      case 'notification_sent':
        await _handleNotificationSent(data);
        break;
      case 'workflow_error':
        await _handleWorkflowError(data);
        break;
      default:
        debugPrint('Unknown webhook type: $type');
    }
  }

  /// Handle account switch completion
  Future<void> _handleAccountSwitchComplete(Map<String, dynamic> data) async {
    final userId = data['userId'] as String?;
    final success = data['success'] as bool? ?? false;
    final sessionId = data['sessionId'] as String?;

    if (userId != null) {
      // Update local state or notify UI
      _notifyAccountSwitchComplete(userId, success, sessionId);
    }
  }

  /// Handle post scheduling confirmation
  Future<void> _handlePostScheduled(Map<String, dynamic> data) async {
    final scheduleId = data['scheduleId'] as String?;
    final scheduledTime = data['scheduledTime'] as String?;
    final platforms = data['platforms'] as List<dynamic>?;

    if (scheduleId != null) {
      _notifyPostScheduled(
        scheduleId,
        scheduledTime,
        platforms?.cast<String>(),
      );
    }
  }

  /// Handle notification delivery status
  Future<void> _handleNotificationSent(Map<String, dynamic> data) async {
    final notificationId = data['notificationId'] as String?;
    final status = data['status'] as String?;
    final recipientId = data['recipientId'] as String?;

    if (notificationId != null && status != null) {
      _notifyNotificationStatus(notificationId, status, recipientId);
    }
  }

  /// Handle workflow errors
  Future<void> _handleWorkflowError(Map<String, dynamic> data) async {
    final workflowId = data['workflowId'] as String?;
    final error = data['error'] as String?;
    final userId = data['userId'] as String?;

    debugPrint(
      'N8n workflow error - ID: $workflowId, Error: $error, User: $userId',
    );

    // You might want to show user-friendly error messages
    if (userId != null && error != null) {
      _notifyWorkflowError(userId, workflowId, error);
    }
  }

  /// Register custom webhook handler
  void registerHandler(N8nWebhookHandler handler) {
    _handlers.add(handler);
  }

  /// Remove webhook handler
  void unregisterHandler(N8nWebhookHandler handler) {
    _handlers.remove(handler);
  }

  /// Stop webhook server
  Future<void> stopWebhookServer() async {
    await _server?.close();
    _server = null;
  }

  // Notification methods (implement based on your state management)
  void _notifyAccountSwitchComplete(
    String userId,
    bool success,
    String? sessionId,
  ) {
    // Implement notification to UI layer
    debugPrint('Account switch complete: $userId, success: $success');
  }

  void _notifyPostScheduled(
    String scheduleId,
    String? scheduledTime,
    List<String>? platforms,
  ) {
    // Implement notification to UI layer
    debugPrint('Post scheduled: $scheduleId at $scheduledTime for $platforms');
  }

  void _notifyNotificationStatus(
    String notificationId,
    String status,
    String? recipientId,
  ) {
    // Implement notification to UI layer
    debugPrint('Notification $notificationId status: $status for $recipientId');
  }

  void _notifyWorkflowError(String userId, String? workflowId, String error) {
    // Implement error notification to UI layer
    debugPrint('Workflow error for $userId: $error');
  }
}

/// Abstract base class for custom webhook handlers
abstract class N8nWebhookHandler {
  bool canHandle(String webhookType);
  Future<void> handle(String webhookType, Map<String, dynamic> data);
}

/// Firebase Cloud Messaging integration for n8n webhooks
class N8nFCMWebhookHandler extends N8nWebhookHandler {
  @override
  bool canHandle(String webhookType) {
    return webhookType.startsWith('fcm_') || webhookType == 'push_notification';
  }

  @override
  Future<void> handle(String webhookType, Map<String, dynamic> data) async {
    switch (webhookType) {
      case 'push_notification':
        await _handlePushNotification(data);
        break;
      case 'fcm_token_update':
        await _handleTokenUpdate(data);
        break;
    }
  }

  Future<void> _handlePushNotification(Map<String, dynamic> data) async {
    // Handle push notification data
    final title = data['title'] as String?;
    final body = data['body'] as String?;
    final payload = data['data'] as Map<String, dynamic>?;

    if (title != null && body != null) {
      // Process the notification (show local notification, update UI, etc.)
      debugPrint('Received push notification: $title - $body');
      if (payload != null) {
        debugPrint('Notification payload: $payload');
      }
    }
  }

  Future<void> _handleTokenUpdate(Map<String, dynamic> data) async {
    // Update FCM token if needed
    final newToken = data['token'] as String?;
    if (newToken != null) {
      // Update token in your backend/n8n
      debugPrint('FCM token updated: $newToken');
    }
  }
}

/// Riverpod providers for webhook service
final n8nWebhookServiceProvider = Provider<N8nWebhookService>((ref) {
  final service = N8nWebhookService();

  // Register default handlers
  service.registerHandler(N8nFCMWebhookHandler());

  return service;
});

/// State provider for webhook events
final webhookEventsProvider =
    StateNotifierProvider<WebhookEventsNotifier, List<WebhookEvent>>((ref) {
      return WebhookEventsNotifier();
    });

/// Webhook event model
class WebhookEvent {
  final String id;
  final String type;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final bool processed;

  WebhookEvent({
    required this.id,
    required this.type,
    required this.data,
    required this.timestamp,
    this.processed = false,
  });

  WebhookEvent copyWith({
    String? id,
    String? type,
    Map<String, dynamic>? data,
    DateTime? timestamp,
    bool? processed,
  }) {
    return WebhookEvent(
      id: id ?? this.id,
      type: type ?? this.type,
      data: data ?? this.data,
      timestamp: timestamp ?? this.timestamp,
      processed: processed ?? this.processed,
    );
  }
}

/// State notifier for managing webhook events
class WebhookEventsNotifier extends StateNotifier<List<WebhookEvent>> {
  WebhookEventsNotifier() : super([]);

  void addEvent(WebhookEvent event) {
    state = [...state, event];
  }

  void markEventProcessed(String eventId) {
    state = state.map((event) {
      if (event.id == eventId) {
        return event.copyWith(processed: true);
      }
      return event;
    }).toList();
  }

  void clearProcessedEvents() {
    state = state.where((event) => !event.processed).toList();
  }

  List<WebhookEvent> getEventsByType(String type) {
    return state.where((event) => event.type == type).toList();
  }
}

/// Firebase Functions integration for production webhook handling
class N8nFirebaseFunctionsIntegration {
  /// Setup Firebase Functions to handle n8n webhooks
  /// This would be implemented in your Firebase Functions project
  static const String functionsSetupInstructions = '''
  // functions/src/index.ts
  import * as functions from 'firebase-functions';
  import * as admin from 'firebase-admin';

  admin.initializeApp();

  export const n8nWebhook = functions.https.onRequest(async (req, res) => {
    if (req.method !== 'POST') {
      res.status(405).send('Method not allowed');
      return;
    }

    try {
      const webhookData = req.body;
      const webhookType = webhookData.type;

      // Validate webhook signature (implement your validation logic)

      // Process webhook based on type
      switch (webhookType) {
        case 'account_switch_complete':
          await handleAccountSwitch(webhookData);
          break;
        case 'post_scheduled':
          await handlePostScheduled(webhookData);
          break;
        // Add more cases as needed
      }

      res.status(200).json({ status: 'success' });
    } catch (error) {
      console.error('Webhook processing error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  });

  async function handleAccountSwitch(data: any) {
    // Update Firestore with account switch status
    await admin.firestore()
      .collection('users')
      .doc(data.userId)
      .update({
        lastAccountSwitch: admin.firestore.FieldValue.serverTimestamp(),
        activeAccountId: data.toAccountId
      });
  }

  async function handlePostScheduled(data: any) {
    // Update scheduled posts collection
    await admin.firestore()
      .collection('scheduledPosts')
      .doc(data.scheduleId)
      .set({
        ...data,
        status: 'scheduled',
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      });
  }
  ''';
}
