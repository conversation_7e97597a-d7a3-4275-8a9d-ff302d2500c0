import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'n8n_service.dart';
import 'n8n_security_service.dart';

/// Integration service between n8n workflows and Firebase
class N8nFirebaseIntegration {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;
  final N8nService _n8nService;

  N8nFirebaseIntegration({
    FirebaseFirestore? firestore,
    FirebaseAuth? auth,
    required N8nService n8nService,
  }) : _firestore = firestore ?? FirebaseFirestore.instance,
       _auth = auth ?? FirebaseAuth.instance,
       _n8nService = n8nService;

  /// Sync user account data with n8n workflows
  Future<void> syncUserAccountData(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) return;

      final userData = userDoc.data()!;
      
      // Trigger n8n workflow to sync user data
      await _n8nService.triggerUserEngagement(
        eventType: 'user_data_sync',
        eventData: {
          'userId': userId,
          'userData': userData,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      print('Error syncing user data: $e');
    }
  }

  /// Listen to Firestore changes and trigger n8n workflows
  Stream<void> listenToFirestoreChanges() {
    final userId = _auth.currentUser?.uid;
    if (userId == null) return const Stream.empty();

    return _firestore
        .collection('n8n_triggers')
        .where('userId', isEqualTo: userId)
        .where('processed', isEqualTo: false)
        .snapshots()
        .asyncMap((snapshot) async {
      for (final doc in snapshot.docChanges) {
        if (doc.type == DocumentChangeType.added) {
          await _processTriggerDocument(doc.doc);
        }
      }
    });
  }

  /// Process trigger documents from Firestore
  Future<void> _processTriggerDocument(DocumentSnapshot doc) async {
    try {
      final data = doc.data() as Map<String, dynamic>;
      final triggerType = data['triggerType'] as String;
      final payload = data['payload'] as Map<String, dynamic>;

      switch (triggerType) {
        case 'account_switch':
          await _handleAccountSwitchTrigger(payload);
          break;
        case 'schedule_post':
          await _handleSchedulePostTrigger(payload);
          break;
        case 'send_notification':
          await _handleNotificationTrigger(payload);
          break;
        default:
          print('Unknown trigger type: $triggerType');
      }

      // Mark as processed
      await doc.reference.update({'processed': true});
    } catch (e) {
      print('Error processing trigger document: $e');
      await doc.reference.update({
        'processed': true,
        'error': e.toString(),
      });
    }
  }

  /// Handle account switch triggers
  Future<void> _handleAccountSwitchTrigger(Map<String, dynamic> payload) async {
    final fromAccountId = payload['fromAccountId'] as String?;
    final toAccountId = payload['toAccountId'] as String;
    final context = payload['context'] as Map<String, dynamic>? ?? {};

    if (fromAccountId != null) {
      await _n8nService.triggerAccountSwitch(
        fromAccountId: fromAccountId,
        toAccountId: toAccountId,
        switchContext: context,
      );
    }
  }

  /// Handle post scheduling triggers
  Future<void> _handleSchedulePostTrigger(Map<String, dynamic> payload) async {
    final content = payload['content'] as String;
    final mediaUrls = (payload['mediaUrls'] as List?)?.cast<String>() ?? [];
    final scheduledTime = DateTime.parse(payload['scheduledTime'] as String);
    final platforms = (payload['platforms'] as List?)?.cast<String>() ?? [];
    final metadata = payload['metadata'] as Map<String, dynamic>? ?? {};

    await _n8nService.schedulePost(
      content: content,
      mediaUrls: mediaUrls,
      scheduledTime: scheduledTime,
      platforms: platforms,
      metadata: metadata,
    );
  }

  /// Handle notification triggers
  Future<void> _handleNotificationTrigger(Map<String, dynamic> payload) async {
    final recipientId = payload['recipientId'] as String;
    final title = payload['title'] as String;
    final body = payload['body'] as String;
    final data = payload['data'] as Map<String, dynamic>? ?? {};
    final imageUrl = payload['imageUrl'] as String?;

    await _n8nService.sendNotification(
      recipientId: recipientId,
      title: title,
      body: body,
      data: data,
      imageUrl: imageUrl,
    );
  }

  /// Create a trigger document in Firestore for n8n processing
  Future<void> createTrigger({
    required String triggerType,
    required Map<String, dynamic> payload,
    DateTime? scheduledFor,
  }) async {
    final userId = _auth.currentUser?.uid;
    if (userId == null) throw Exception('User not authenticated');

    await _firestore.collection('n8n_triggers').add({
      'userId': userId,
      'triggerType': triggerType,
      'payload': payload,
      'processed': false,
      'createdAt': FieldValue.serverTimestamp(),
      'scheduledFor': scheduledFor?.toIso8601String(),
    });
  }

  /// Sync n8n workflow results back to Firestore
  Future<void> syncWorkflowResult({
    required String workflowId,
    required String workflowType,
    required Map<String, dynamic> result,
  }) async {
    final userId = _auth.currentUser?.uid;
    if (userId == null) return;

    await _firestore.collection('n8n_results').add({
      'userId': userId,
      'workflowId': workflowId,
      'workflowType': workflowType,
      'result': result,
      'timestamp': FieldValue.serverTimestamp(),
    });
  }

  /// Get workflow results from Firestore
  Stream<List<N8nWorkflowResult>> getWorkflowResults() {
    final userId = _auth.currentUser?.uid;
    if (userId == null) return const Stream.empty();

    return _firestore
        .collection('n8n_results')
        .where('userId', isEqualTo: userId)
        .orderBy('timestamp', descending: true)
        .limit(50)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => N8nWorkflowResult.fromFirestore(doc))
            .toList());
  }

  /// Update user preferences for n8n workflows
  Future<void> updateWorkflowPreferences({
    required Map<String, dynamic> preferences,
  }) async {
    final userId = _auth.currentUser?.uid;
    if (userId == null) return;

    await _firestore
        .collection('users')
        .doc(userId)
        .update({'n8nPreferences': preferences});
  }

  /// Get user workflow preferences
  Future<Map<String, dynamic>?> getWorkflowPreferences() async {
    final userId = _auth.currentUser?.uid;
    if (userId == null) return null;

    final doc = await _firestore.collection('users').doc(userId).get();
    return doc.data()?['n8nPreferences'] as Map<String, dynamic>?;
  }
}

/// Model for n8n workflow results
class N8nWorkflowResult {
  final String id;
  final String userId;
  final String workflowId;
  final String workflowType;
  final Map<String, dynamic> result;
  final DateTime timestamp;

  N8nWorkflowResult({
    required this.id,
    required this.userId,
    required this.workflowId,
    required this.workflowType,
    required this.result,
    required this.timestamp,
  });

  factory N8nWorkflowResult.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return N8nWorkflowResult(
      id: doc.id,
      userId: data['userId'] ?? '',
      workflowId: data['workflowId'] ?? '',
      workflowType: data['workflowType'] ?? '',
      result: data['result'] ?? {},
      timestamp: (data['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }
}

/// Riverpod providers for Firebase integration
final n8nFirebaseIntegrationProvider = Provider<N8nFirebaseIntegration>((ref) {
  final n8nService = ref.watch(n8nServiceProvider);
  return N8nFirebaseIntegration(n8nService: n8nService);
});

/// Provider for workflow results stream
final workflowResultsProvider = StreamProvider<List<N8nWorkflowResult>>((ref) {
  final integration = ref.watch(n8nFirebaseIntegrationProvider);
  return integration.getWorkflowResults();
});

/// Provider for workflow preferences
final workflowPreferencesProvider = FutureProvider<Map<String, dynamic>?>((ref) {
  final integration = ref.watch(n8nFirebaseIntegrationProvider);
  return integration.getWorkflowPreferences();
});

/// State notifier for managing n8n triggers
class N8nTriggersNotifier extends StateNotifier<AsyncValue<List<N8nTrigger>>> {
  final N8nFirebaseIntegration _integration;

  N8nTriggersNotifier(this._integration) : super(const AsyncValue.loading()) {
    _loadTriggers();
  }

  Future<void> _loadTriggers() async {
    try {
      // Load pending triggers from Firestore
      final userId = FirebaseAuth.instance.currentUser?.uid;
      if (userId == null) {
        state = const AsyncValue.data([]);
        return;
      }

      final snapshot = await FirebaseFirestore.instance
          .collection('n8n_triggers')
          .where('userId', isEqualTo: userId)
          .where('processed', isEqualTo: false)
          .get();

      final triggers = snapshot.docs
          .map((doc) => N8nTrigger.fromFirestore(doc))
          .toList();

      state = AsyncValue.data(triggers);
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
    }
  }

  Future<void> createTrigger({
    required String triggerType,
    required Map<String, dynamic> payload,
    DateTime? scheduledFor,
  }) async {
    await _integration.createTrigger(
      triggerType: triggerType,
      payload: payload,
      scheduledFor: scheduledFor,
    );
    await _loadTriggers();
  }

  Future<void> refresh() async {
    await _loadTriggers();
  }
}

/// Model for n8n triggers
class N8nTrigger {
  final String id;
  final String userId;
  final String triggerType;
  final Map<String, dynamic> payload;
  final bool processed;
  final DateTime createdAt;
  final DateTime? scheduledFor;

  N8nTrigger({
    required this.id,
    required this.userId,
    required this.triggerType,
    required this.payload,
    required this.processed,
    required this.createdAt,
    this.scheduledFor,
  });

  factory N8nTrigger.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return N8nTrigger(
      id: doc.id,
      userId: data['userId'] ?? '',
      triggerType: data['triggerType'] ?? '',
      payload: data['payload'] ?? {},
      processed: data['processed'] ?? false,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      scheduledFor: data['scheduledFor'] != null 
          ? DateTime.parse(data['scheduledFor']) 
          : null,
    );
  }
}

/// Provider for n8n triggers
final n8nTriggersProvider = StateNotifierProvider<N8nTriggersNotifier, AsyncValue<List<N8nTrigger>>>((ref) {
  final integration = ref.watch(n8nFirebaseIntegrationProvider);
  return N8nTriggersNotifier(integration);
});
