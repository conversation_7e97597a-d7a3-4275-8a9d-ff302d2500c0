import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../core/config/n8n_config.dart';

/// Service for integrating with n8n workflows
class N8nService {
  final http.Client _httpClient;
  final FirebaseAuth _auth;
  N8nConfiguration? _config;
  N8nWebhookUrls? _webhookUrls;

  N8nService({http.Client? httpClient, FirebaseAuth? auth})
    : _httpClient = httpClient ?? http.Client(),
      _auth = auth ?? FirebaseAuth.instance;

  /// Initialize the service with configuration
  Future<void> initialize() async {
    _config = await N8nConfig.getConfiguration();
    _webhookUrls = await N8nConfig.getWebhookUrls();
  }

  /// Get authentication headers for n8n requests
  Future<Map<String, String>> _getHeaders() async {
    if (_config == null) await initialize();

    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ${_config?.apiKey ?? ''}',
      'X-User-ID': _auth.currentUser?.uid ?? '',
      'X-Timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
    };
  }

  /// Check if n8n integration is enabled and configured
  Future<bool> get isEnabled async {
    if (_config == null) await initialize();
    return _config?.enabled == true && _config?.isValid == true;
  }

  /// Trigger account switch workflow
  Future<N8nResponse<AccountSwitchResult>> triggerAccountSwitch({
    required String fromAccountId,
    required String toAccountId,
    required Map<String, dynamic> switchContext,
  }) async {
    try {
      if (!await isEnabled) {
        return N8nResponse.error(
          'n8n integration is not enabled or configured',
        );
      }

      if (_webhookUrls == null) await initialize();

      final payload = {
        'fromAccountId': fromAccountId,
        'toAccountId': toAccountId,
        'userId': _auth.currentUser?.uid,
        'timestamp': DateTime.now().toIso8601String(),
        'context': switchContext,
      };

      final headers = await _getHeaders();
      final response = await _httpClient.post(
        Uri.parse(_webhookUrls!.accountSwitch),
        headers: headers,
        body: json.encode(payload),
      );

      return _handleResponse<AccountSwitchResult>(
        response,
        (data) => AccountSwitchResult.fromJson(data),
      );
    } catch (e) {
      return N8nResponse.error('Failed to trigger account switch: $e');
    }
  }

  /// Schedule social media post
  Future<N8nResponse<PostScheduleResult>> schedulePost({
    required String content,
    required List<String> mediaUrls,
    required DateTime scheduledTime,
    required List<String> platforms,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      if (!await isEnabled) {
        return N8nResponse.error(
          'n8n integration is not enabled or configured',
        );
      }

      if (_webhookUrls == null) await initialize();

      final payload = {
        'content': content,
        'mediaUrls': mediaUrls,
        'scheduledTime': scheduledTime.toIso8601String(),
        'platforms': platforms,
        'userId': _auth.currentUser?.uid,
        'metadata': metadata ?? {},
      };

      final headers = await _getHeaders();
      final response = await _httpClient.post(
        Uri.parse(_webhookUrls!.postScheduling),
        headers: headers,
        body: json.encode(payload),
      );

      return _handleResponse<PostScheduleResult>(
        response,
        (data) => PostScheduleResult.fromJson(data),
      );
    } catch (e) {
      return N8nResponse.error('Failed to schedule post: $e');
    }
  }

  /// Trigger user engagement workflow
  Future<N8nResponse<EngagementResult>> triggerUserEngagement({
    required String eventType,
    required Map<String, dynamic> eventData,
  }) async {
    try {
      if (!await isEnabled) {
        return N8nResponse.error(
          'n8n integration is not enabled or configured',
        );
      }

      if (_webhookUrls == null) await initialize();

      final payload = {
        'eventType': eventType,
        'eventData': eventData,
        'userId': _auth.currentUser?.uid,
        'timestamp': DateTime.now().toIso8601String(),
      };

      final headers = await _getHeaders();
      final response = await _httpClient.post(
        Uri.parse(_webhookUrls!.userEngagement),
        headers: headers,
        body: json.encode(payload),
      );

      return _handleResponse<EngagementResult>(
        response,
        (data) => EngagementResult.fromJson(data),
      );
    } catch (e) {
      return N8nResponse.error('Failed to trigger engagement workflow: $e');
    }
  }

  /// Send notification through n8n
  Future<N8nResponse<NotificationResult>> sendNotification({
    required String recipientId,
    required String title,
    required String body,
    Map<String, dynamic>? data,
    String? imageUrl,
  }) async {
    try {
      if (!await isEnabled) {
        return N8nResponse.error(
          'n8n integration is not enabled or configured',
        );
      }

      if (_webhookUrls == null) await initialize();

      final payload = {
        'recipientId': recipientId,
        'title': title,
        'body': body,
        'data': data ?? {},
        'imageUrl': imageUrl,
        'senderId': _auth.currentUser?.uid,
        'timestamp': DateTime.now().toIso8601String(),
      };

      final headers = await _getHeaders();
      final response = await _httpClient.post(
        Uri.parse(_webhookUrls!.notification),
        headers: headers,
        body: json.encode(payload),
      );

      return _handleResponse<NotificationResult>(
        response,
        (data) => NotificationResult.fromJson(data),
      );
    } catch (e) {
      return N8nResponse.error('Failed to send notification: $e');
    }
  }

  /// Generic response handler
  N8nResponse<T> _handleResponse<T>(
    http.Response response,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      final data = json.decode(response.body) as Map<String, dynamic>;
      return N8nResponse.success(fromJson(data));
    } else {
      final error = json.decode(response.body);
      return N8nResponse.error(
        error['message'] ?? 'Unknown error occurred',
        statusCode: response.statusCode,
      );
    }
  }

  void dispose() {
    _httpClient.close();
  }
}

/// Generic response wrapper for n8n operations
class N8nResponse<T> {
  final T? data;
  final String? error;
  final int? statusCode;
  final bool isSuccess;

  N8nResponse._({
    this.data,
    this.error,
    this.statusCode,
    required this.isSuccess,
  });

  factory N8nResponse.success(T data) =>
      N8nResponse._(data: data, isSuccess: true);

  factory N8nResponse.error(String error, {int? statusCode}) =>
      N8nResponse._(error: error, statusCode: statusCode, isSuccess: false);
}

/// Result models for different n8n workflows
class AccountSwitchResult {
  final bool success;
  final String? sessionId;
  final Map<String, dynamic>? userData;
  final String? error;

  AccountSwitchResult({
    required this.success,
    this.sessionId,
    this.userData,
    this.error,
  });

  factory AccountSwitchResult.fromJson(Map<String, dynamic> json) {
    return AccountSwitchResult(
      success: json['success'] ?? false,
      sessionId: json['sessionId'],
      userData: json['userData'],
      error: json['error'],
    );
  }
}

class PostScheduleResult {
  final bool success;
  final String? scheduleId;
  final DateTime? scheduledTime;
  final List<String>? platforms;
  final String? error;

  PostScheduleResult({
    required this.success,
    this.scheduleId,
    this.scheduledTime,
    this.platforms,
    this.error,
  });

  factory PostScheduleResult.fromJson(Map<String, dynamic> json) {
    return PostScheduleResult(
      success: json['success'] ?? false,
      scheduleId: json['scheduleId'],
      scheduledTime: json['scheduledTime'] != null
          ? DateTime.parse(json['scheduledTime'])
          : null,
      platforms: json['platforms']?.cast<String>(),
      error: json['error'],
    );
  }
}

class EngagementResult {
  final bool success;
  final String? actionTaken;
  final Map<String, dynamic>? analytics;
  final String? error;

  EngagementResult({
    required this.success,
    this.actionTaken,
    this.analytics,
    this.error,
  });

  factory EngagementResult.fromJson(Map<String, dynamic> json) {
    return EngagementResult(
      success: json['success'] ?? false,
      actionTaken: json['actionTaken'],
      analytics: json['analytics'],
      error: json['error'],
    );
  }
}

class NotificationResult {
  final bool success;
  final String? notificationId;
  final String? deliveryStatus;
  final String? error;

  NotificationResult({
    required this.success,
    this.notificationId,
    this.deliveryStatus,
    this.error,
  });

  factory NotificationResult.fromJson(Map<String, dynamic> json) {
    return NotificationResult(
      success: json['success'] ?? false,
      notificationId: json['notificationId'],
      deliveryStatus: json['deliveryStatus'],
      error: json['error'],
    );
  }
}

/// Riverpod provider for n8n service
final n8nServiceProvider = Provider<N8nService>((ref) {
  return N8nService();
});
