import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Security service for n8n integration
class N8nSecurityService {
  static const _storage = FlutterSecureStorage();
  static const String _apiKeyKey = 'n8n_api_key';
  static const String _webhookSecretKey = 'n8n_webhook_secret';
  static const String _encryptionKeyKey = 'n8n_encryption_key';

  /// Store n8n API key securely
  static Future<void> storeApiKey(String apiKey) async {
    await _storage.write(key: _apiKeyKey, value: apiKey);
  }

  /// Retrieve n8n API key
  static Future<String?> getApiKey() async {
    return await _storage.read(key: _apiKeyKey);
  }

  /// Store webhook secret for signature verification
  static Future<void> storeWebhookSecret(String secret) async {
    await _storage.write(key: _webhookSecretKey, value: secret);
  }

  /// Get webhook secret
  static Future<String?> getWebhookSecret() async {
    return await _storage.read(key: _webhookSecretKey);
  }

  /// Generate and store encryption key for sensitive data
  static Future<String> generateEncryptionKey() async {
    final random = Random.secure();
    final key = List.generate(32, (i) => random.nextInt(256));
    final keyString = base64.encode(key);
    await _storage.write(key: _encryptionKeyKey, value: keyString);
    return keyString;
  }

  /// Get encryption key
  static Future<String?> getEncryptionKey() async {
    return await _storage.read(key: _encryptionKeyKey);
  }

  /// Create secure headers for n8n requests
  static Future<Map<String, String>> createSecureHeaders({
    String? customUserId,
  }) async {
    final apiKey = await getApiKey();
    final user = FirebaseAuth.instance.currentUser;
    final userId = customUserId ?? user?.uid ?? '';
    
    // Create timestamp for request freshness
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    
    // Create signature for request integrity
    final signature = await _createRequestSignature(userId, timestamp);

    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $apiKey',
      'X-User-ID': userId,
      'X-Timestamp': timestamp,
      'X-Signature': signature,
      'X-App-Version': '1.0.0', // Your app version
    };
  }

  /// Verify webhook signature from n8n
  static Future<bool> verifyWebhookSignature({
    required String payload,
    required String receivedSignature,
    required String timestamp,
  }) async {
    final secret = await getWebhookSecret();
    if (secret == null) return false;

    // Check timestamp freshness (within 5 minutes)
    final requestTime = int.tryParse(timestamp) ?? 0;
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    if (currentTime - requestTime > 300000) return false; // 5 minutes

    final expectedSignature = _createWebhookSignature(payload, secret, timestamp);
    return expectedSignature == receivedSignature;
  }

  /// Encrypt sensitive data before sending to n8n
  static Future<String> encryptData(String data) async {
    final key = await getEncryptionKey() ?? await generateEncryptionKey();
    final keyBytes = base64.decode(key);
    
    // Simple XOR encryption (use AES for production)
    final dataBytes = utf8.encode(data);
    final encrypted = <int>[];
    
    for (int i = 0; i < dataBytes.length; i++) {
      encrypted.add(dataBytes[i] ^ keyBytes[i % keyBytes.length]);
    }
    
    return base64.encode(encrypted);
  }

  /// Decrypt data received from n8n
  static Future<String> decryptData(String encryptedData) async {
    final key = await getEncryptionKey();
    if (key == null) throw Exception('Encryption key not found');
    
    final keyBytes = base64.decode(key);
    final encryptedBytes = base64.decode(encryptedData);
    final decrypted = <int>[];
    
    for (int i = 0; i < encryptedBytes.length; i++) {
      decrypted.add(encryptedBytes[i] ^ keyBytes[i % keyBytes.length]);
    }
    
    return utf8.decode(decrypted);
  }

  /// Create request signature for integrity verification
  static Future<String> _createRequestSignature(String userId, String timestamp) async {
    final secret = await getWebhookSecret() ?? 'default-secret';
    final message = '$userId:$timestamp';
    final key = utf8.encode(secret);
    final bytes = utf8.encode(message);
    final hmac = Hmac(sha256, key);
    final digest = hmac.convert(bytes);
    return digest.toString();
  }

  /// Create webhook signature for verification
  static String _createWebhookSignature(String payload, String secret, String timestamp) {
    final message = '$payload:$timestamp';
    final key = utf8.encode(secret);
    final bytes = utf8.encode(message);
    final hmac = Hmac(sha256, key);
    final digest = hmac.convert(bytes);
    return digest.toString();
  }

  /// Validate user permissions for n8n operations
  static Future<bool> validateUserPermissions({
    required String operation,
    String? targetUserId,
  }) async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return false;

    // Get user's custom claims for role-based access
    final idTokenResult = await user.getIdTokenResult();
    final claims = idTokenResult.claims;

    switch (operation) {
      case 'account_switch':
        // User can only switch their own accounts
        return targetUserId == null || targetUserId == user.uid;
      
      case 'schedule_post':
        // Check if user has posting permissions
        return claims?['canPost'] == true;
      
      case 'send_notification':
        // Check if user has notification permissions
        return claims?['canNotify'] == true || claims?['role'] == 'admin';
      
      case 'admin_operation':
        // Only admins can perform admin operations
        return claims?['role'] == 'admin';
      
      default:
        return false;
    }
  }

  /// Rate limiting for n8n requests
  static final Map<String, List<int>> _requestHistory = {};
  
  static bool checkRateLimit({
    required String userId,
    int maxRequests = 100,
    Duration window = const Duration(minutes: 15),
  }) {
    final now = DateTime.now().millisecondsSinceEpoch;
    final windowStart = now - window.inMilliseconds;
    
    // Clean old requests
    _requestHistory[userId]?.removeWhere((timestamp) => timestamp < windowStart);
    
    // Check current request count
    final currentRequests = _requestHistory[userId]?.length ?? 0;
    if (currentRequests >= maxRequests) {
      return false;
    }
    
    // Add current request
    _requestHistory[userId] = (_requestHistory[userId] ?? [])..add(now);
    return true;
  }

  /// Clear stored credentials (for logout)
  static Future<void> clearCredentials() async {
    await _storage.delete(key: _apiKeyKey);
    await _storage.delete(key: _webhookSecretKey);
    await _storage.delete(key: _encryptionKeyKey);
  }
}

/// Secure n8n configuration provider
final n8nSecurityProvider = Provider<N8nSecurityService>((ref) {
  return N8nSecurityService();
});

/// Security middleware for n8n requests
class N8nSecurityMiddleware {
  static Future<Map<String, String>> prepareSecureRequest({
    required String operation,
    String? targetUserId,
    Map<String, dynamic>? additionalData,
  }) async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      throw Exception('User not authenticated');
    }

    // Check permissions
    final hasPermission = await N8nSecurityService.validateUserPermissions(
      operation: operation,
      targetUserId: targetUserId,
    );
    if (!hasPermission) {
      throw Exception('Insufficient permissions for operation: $operation');
    }

    // Check rate limit
    final withinLimit = N8nSecurityService.checkRateLimit(userId: user.uid);
    if (!withinLimit) {
      throw Exception('Rate limit exceeded');
    }

    // Create secure headers
    return await N8nSecurityService.createSecureHeaders();
  }

  /// Validate incoming webhook from n8n
  static Future<bool> validateIncomingWebhook({
    required String payload,
    required Map<String, String> headers,
  }) async {
    final signature = headers['x-signature'];
    final timestamp = headers['x-timestamp'];
    
    if (signature == null || timestamp == null) {
      return false;
    }

    return await N8nSecurityService.verifyWebhookSignature(
      payload: payload,
      receivedSignature: signature,
      timestamp: timestamp,
    );
  }
}

/// Exception classes for n8n security
class N8nSecurityException implements Exception {
  final String message;
  final String code;

  N8nSecurityException(this.message, this.code);

  @override
  String toString() => 'N8nSecurityException: $message (Code: $code)';
}

class N8nRateLimitException extends N8nSecurityException {
  N8nRateLimitException() : super('Rate limit exceeded', 'RATE_LIMIT');
}

class N8nPermissionException extends N8nSecurityException {
  N8nPermissionException(String operation) 
      : super('Insufficient permissions for $operation', 'PERMISSION_DENIED');
}
