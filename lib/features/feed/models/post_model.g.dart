// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'post_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PostImpl _$$PostImplFromJson(Map<String, dynamic> json) => _$PostImpl(
  id: json['id'] as String,
  userId: json['userId'] as String,
  username: json['username'] as String,
  userAvatarUrl: json['userAvatarUrl'] as String,
  userRole: json['userRole'] as String?,
  isVerified: json['isVerified'] as bool?,
  isBillionaire: json['isBillionaire'] as bool?,
  isCelebrity: json['isCelebrity'] as bool?,
  verificationTier: json['verificationTier'] as String?,
  mediaType: $enumDecode(_$MediaTypeEnumMap, json['mediaType']),
  mediaUrl: json['mediaUrl'] as String,
  mediaUrls: (json['mediaUrls'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  mediaTypes: (json['mediaTypes'] as List<dynamic>?)
      ?.map((e) => $enumDecode(_$MediaTypeEnumMap, e))
      .toList(),
  caption: json['caption'] as String,
  location: json['location'] as String?,
  locationId: json['locationId'] as String?,
  likeCount: (json['likeCount'] as num).toInt(),
  commentCount: (json['commentCount'] as num).toInt(),
  timestamp: DateTime.parse(json['timestamp'] as String),
  localCreatedAt: json['localCreatedAt'] == null
      ? null
      : DateTime.parse(json['localCreatedAt'] as String),
  isLiked: json['isLiked'] as bool? ?? false,
  isBookmarked: json['isBookmarked'] as bool? ?? false,
  isReposted: json['isReposted'] as bool? ?? false,
  coAuthorIds: (json['coAuthorIds'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  coAuthorUsernames: (json['coAuthorUsernames'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  coAuthorAvatars: (json['coAuthorAvatars'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  mentionedUsers: (json['mentionedUsers'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  hashtags: (json['hashtags'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  mediaTags: (json['mediaTags'] as List<dynamic>?)
      ?.map((e) => MediaTag.fromJson(e as Map<String, dynamic>))
      .toList(),
  taggedUserIds: (json['taggedUserIds'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  likedBy: (json['likedBy'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  bookmarkedBy: (json['bookmarkedBy'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  repostedBy: (json['repostedBy'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  isPublic: json['isPublic'] as bool? ?? true,
  visibility: json['visibility'] as String? ?? 'public',
  isArchived: json['isArchived'] as bool? ?? false,
  isReported: json['isReported'] as bool? ?? false,
  isPinned: json['isPinned'] as bool? ?? false,
  isDeleted: json['isDeleted'] as bool? ?? false,
  isFlagged: json['isFlagged'] as bool? ?? false,
  status: json['status'] as String? ?? 'active',
  viewCount: (json['viewCount'] as num?)?.toInt(),
  shareCount: (json['shareCount'] as num?)?.toInt(),
  repostCount: (json['repostCount'] as num?)?.toInt(),
  saveCount: (json['saveCount'] as num?)?.toInt(),
  trending: json['trending'] as bool? ?? false,
  trendingScore: json['trendingScore'] as num?,
  lastEditedAt: json['lastEditedAt'] == null
      ? null
      : DateTime.parse(json['lastEditedAt'] as String),
  editedBy: json['editedBy'] as String?,
  isRemix: json['isRemix'] as bool? ?? false,
  allowRemix: json['allowRemix'] as bool? ?? false,
  allowRepost: json['allowRepost'] as bool? ?? true,
  allowShare: json['allowShare'] as bool? ?? true,
  originalPostId: json['originalPostId'] as String?,
  originalUserId: json['originalUserId'] as String?,
  originalUsername: json['originalUsername'] as String?,
  commentsDisabled: json['commentsDisabled'] as bool? ?? false,
  likesHidden: json['likesHidden'] as bool? ?? false,
  hasAIContent: json['hasAIContent'] as bool? ?? false,
  musicTrack: json['musicTrack'] as String?,
  musicUrl: json['musicUrl'] as String?,
  flaggedReasons: (json['flaggedReasons'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  flaggedAt: json['flaggedAt'] == null
      ? null
      : DateTime.parse(json['flaggedAt'] as String),
  flaggedBy: json['flaggedBy'] as String?,
  reportCount: (json['reportCount'] as num?)?.toInt(),
  reviewedAt: json['reviewedAt'] == null
      ? null
      : DateTime.parse(json['reviewedAt'] as String),
  reviewedBy: json['reviewedBy'] as String?,
  moderationAction: json['moderationAction'] as String?,
  closeFriendsGroupIds: (json['closeFriendsGroupIds'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  metadata: json['metadata'] as Map<String, dynamic>?,
  contentWarnings: (json['contentWarnings'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  ageRestriction: json['ageRestriction'] as String?,
);

Map<String, dynamic> _$$PostImplToJson(_$PostImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'username': instance.username,
      'userAvatarUrl': instance.userAvatarUrl,
      'userRole': instance.userRole,
      'isVerified': instance.isVerified,
      'isBillionaire': instance.isBillionaire,
      'isCelebrity': instance.isCelebrity,
      'verificationTier': instance.verificationTier,
      'mediaType': _$MediaTypeEnumMap[instance.mediaType]!,
      'mediaUrl': instance.mediaUrl,
      'mediaUrls': instance.mediaUrls,
      'mediaTypes': instance.mediaTypes
          ?.map((e) => _$MediaTypeEnumMap[e]!)
          .toList(),
      'caption': instance.caption,
      'location': instance.location,
      'locationId': instance.locationId,
      'likeCount': instance.likeCount,
      'commentCount': instance.commentCount,
      'timestamp': instance.timestamp.toIso8601String(),
      'localCreatedAt': instance.localCreatedAt?.toIso8601String(),
      'isLiked': instance.isLiked,
      'isBookmarked': instance.isBookmarked,
      'isReposted': instance.isReposted,
      'coAuthorIds': instance.coAuthorIds,
      'coAuthorUsernames': instance.coAuthorUsernames,
      'coAuthorAvatars': instance.coAuthorAvatars,
      'mentionedUsers': instance.mentionedUsers,
      'hashtags': instance.hashtags,
      'mediaTags': instance.mediaTags,
      'taggedUserIds': instance.taggedUserIds,
      'likedBy': instance.likedBy,
      'bookmarkedBy': instance.bookmarkedBy,
      'repostedBy': instance.repostedBy,
      'isPublic': instance.isPublic,
      'visibility': instance.visibility,
      'isArchived': instance.isArchived,
      'isReported': instance.isReported,
      'isPinned': instance.isPinned,
      'isDeleted': instance.isDeleted,
      'isFlagged': instance.isFlagged,
      'status': instance.status,
      'viewCount': instance.viewCount,
      'shareCount': instance.shareCount,
      'repostCount': instance.repostCount,
      'saveCount': instance.saveCount,
      'trending': instance.trending,
      'trendingScore': instance.trendingScore,
      'lastEditedAt': instance.lastEditedAt?.toIso8601String(),
      'editedBy': instance.editedBy,
      'isRemix': instance.isRemix,
      'allowRemix': instance.allowRemix,
      'allowRepost': instance.allowRepost,
      'allowShare': instance.allowShare,
      'originalPostId': instance.originalPostId,
      'originalUserId': instance.originalUserId,
      'originalUsername': instance.originalUsername,
      'commentsDisabled': instance.commentsDisabled,
      'likesHidden': instance.likesHidden,
      'hasAIContent': instance.hasAIContent,
      'musicTrack': instance.musicTrack,
      'musicUrl': instance.musicUrl,
      'flaggedReasons': instance.flaggedReasons,
      'flaggedAt': instance.flaggedAt?.toIso8601String(),
      'flaggedBy': instance.flaggedBy,
      'reportCount': instance.reportCount,
      'reviewedAt': instance.reviewedAt?.toIso8601String(),
      'reviewedBy': instance.reviewedBy,
      'moderationAction': instance.moderationAction,
      'closeFriendsGroupIds': instance.closeFriendsGroupIds,
      'metadata': instance.metadata,
      'contentWarnings': instance.contentWarnings,
      'ageRestriction': instance.ageRestriction,
    };

const _$MediaTypeEnumMap = {
  MediaType.image: 'image',
  MediaType.video: 'video',
  MediaType.text: 'text',
};

_$MediaTagImpl _$$MediaTagImplFromJson(Map<String, dynamic> json) =>
    _$MediaTagImpl(
      userId: json['userId'] as String,
      username: json['username'] as String,
      x: (json['x'] as num).toDouble(),
      y: (json['y'] as num).toDouble(),
    );

Map<String, dynamic> _$$MediaTagImplToJson(_$MediaTagImpl instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'username': instance.username,
      'x': instance.x,
      'y': instance.y,
    };

_$PostInteractionImpl _$$PostInteractionImplFromJson(
  Map<String, dynamic> json,
) => _$PostInteractionImpl(
  postId: json['postId'] as String,
  userId: json['userId'] as String,
  username: json['username'] as String,
  userAvatarUrl: json['userAvatarUrl'] as String,
  type: $enumDecode(_$InteractionTypeEnumMap, json['type']),
  timestamp: DateTime.parse(json['timestamp'] as String),
  comment: json['comment'] as String?,
);

Map<String, dynamic> _$$PostInteractionImplToJson(
  _$PostInteractionImpl instance,
) => <String, dynamic>{
  'postId': instance.postId,
  'userId': instance.userId,
  'username': instance.username,
  'userAvatarUrl': instance.userAvatarUrl,
  'type': _$InteractionTypeEnumMap[instance.type]!,
  'timestamp': instance.timestamp.toIso8601String(),
  'comment': instance.comment,
};

const _$InteractionTypeEnumMap = {
  InteractionType.like: 'like',
  InteractionType.comment: 'comment',
  InteractionType.bookmark: 'bookmark',
  InteractionType.share: 'share',
  InteractionType.repost: 'repost',
  InteractionType.report: 'report',
};

_$PostPermissionImpl _$$PostPermissionImplFromJson(Map<String, dynamic> json) =>
    _$PostPermissionImpl(
      postId: json['postId'] as String,
      userId: json['userId'] as String,
      canEdit: json['canEdit'] as bool,
      canDelete: json['canDelete'] as bool,
      canModerateComments: json['canModerateComments'] as bool,
      canArchive: json['canArchive'] as bool,
      canReport: json['canReport'] as bool,
    );

Map<String, dynamic> _$$PostPermissionImplToJson(
  _$PostPermissionImpl instance,
) => <String, dynamic>{
  'postId': instance.postId,
  'userId': instance.userId,
  'canEdit': instance.canEdit,
  'canDelete': instance.canDelete,
  'canModerateComments': instance.canModerateComments,
  'canArchive': instance.canArchive,
  'canReport': instance.canReport,
};
