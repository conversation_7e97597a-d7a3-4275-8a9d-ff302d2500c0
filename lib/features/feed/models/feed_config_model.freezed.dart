// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'feed_config_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

FeedConfig _$FeedConfigFromJson(Map<String, dynamic> json) {
  return _FeedConfig.fromJson(json);
}

/// @nodoc
mixin _$FeedConfig {
  FeedType get feedType => throw _privateConstructorUsedError;
  MediaFilter get mediaFilter => throw _privateConstructorUsedError;
  SortOrder get sortOrder => throw _privateConstructorUsedError;
  int get postsPerPage => throw _privateConstructorUsedError;
  bool get showBusinessPosts => throw _privateConstructorUsedError;
  bool get showVerifiedPosts => throw _privateConstructorUsedError;
  bool get showTrendingPosts => throw _privateConstructorUsedError;
  List<String> get excludedUserIds => throw _privateConstructorUsedError;
  List<String> get excludedHashtags => throw _privateConstructorUsedError;
  List<String> get preferredTopics => throw _privateConstructorUsedError;
  bool get autoRefresh => throw _privateConstructorUsedError;
  int get autoRefreshIntervalSeconds => throw _privateConstructorUsedError;

  /// Serializes this FeedConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FeedConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FeedConfigCopyWith<FeedConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FeedConfigCopyWith<$Res> {
  factory $FeedConfigCopyWith(
    FeedConfig value,
    $Res Function(FeedConfig) then,
  ) = _$FeedConfigCopyWithImpl<$Res, FeedConfig>;
  @useResult
  $Res call({
    FeedType feedType,
    MediaFilter mediaFilter,
    SortOrder sortOrder,
    int postsPerPage,
    bool showBusinessPosts,
    bool showVerifiedPosts,
    bool showTrendingPosts,
    List<String> excludedUserIds,
    List<String> excludedHashtags,
    List<String> preferredTopics,
    bool autoRefresh,
    int autoRefreshIntervalSeconds,
  });
}

/// @nodoc
class _$FeedConfigCopyWithImpl<$Res, $Val extends FeedConfig>
    implements $FeedConfigCopyWith<$Res> {
  _$FeedConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FeedConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? feedType = null,
    Object? mediaFilter = null,
    Object? sortOrder = null,
    Object? postsPerPage = null,
    Object? showBusinessPosts = null,
    Object? showVerifiedPosts = null,
    Object? showTrendingPosts = null,
    Object? excludedUserIds = null,
    Object? excludedHashtags = null,
    Object? preferredTopics = null,
    Object? autoRefresh = null,
    Object? autoRefreshIntervalSeconds = null,
  }) {
    return _then(
      _value.copyWith(
            feedType: null == feedType
                ? _value.feedType
                : feedType // ignore: cast_nullable_to_non_nullable
                      as FeedType,
            mediaFilter: null == mediaFilter
                ? _value.mediaFilter
                : mediaFilter // ignore: cast_nullable_to_non_nullable
                      as MediaFilter,
            sortOrder: null == sortOrder
                ? _value.sortOrder
                : sortOrder // ignore: cast_nullable_to_non_nullable
                      as SortOrder,
            postsPerPage: null == postsPerPage
                ? _value.postsPerPage
                : postsPerPage // ignore: cast_nullable_to_non_nullable
                      as int,
            showBusinessPosts: null == showBusinessPosts
                ? _value.showBusinessPosts
                : showBusinessPosts // ignore: cast_nullable_to_non_nullable
                      as bool,
            showVerifiedPosts: null == showVerifiedPosts
                ? _value.showVerifiedPosts
                : showVerifiedPosts // ignore: cast_nullable_to_non_nullable
                      as bool,
            showTrendingPosts: null == showTrendingPosts
                ? _value.showTrendingPosts
                : showTrendingPosts // ignore: cast_nullable_to_non_nullable
                      as bool,
            excludedUserIds: null == excludedUserIds
                ? _value.excludedUserIds
                : excludedUserIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            excludedHashtags: null == excludedHashtags
                ? _value.excludedHashtags
                : excludedHashtags // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            preferredTopics: null == preferredTopics
                ? _value.preferredTopics
                : preferredTopics // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            autoRefresh: null == autoRefresh
                ? _value.autoRefresh
                : autoRefresh // ignore: cast_nullable_to_non_nullable
                      as bool,
            autoRefreshIntervalSeconds: null == autoRefreshIntervalSeconds
                ? _value.autoRefreshIntervalSeconds
                : autoRefreshIntervalSeconds // ignore: cast_nullable_to_non_nullable
                      as int,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$FeedConfigImplCopyWith<$Res>
    implements $FeedConfigCopyWith<$Res> {
  factory _$$FeedConfigImplCopyWith(
    _$FeedConfigImpl value,
    $Res Function(_$FeedConfigImpl) then,
  ) = __$$FeedConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    FeedType feedType,
    MediaFilter mediaFilter,
    SortOrder sortOrder,
    int postsPerPage,
    bool showBusinessPosts,
    bool showVerifiedPosts,
    bool showTrendingPosts,
    List<String> excludedUserIds,
    List<String> excludedHashtags,
    List<String> preferredTopics,
    bool autoRefresh,
    int autoRefreshIntervalSeconds,
  });
}

/// @nodoc
class __$$FeedConfigImplCopyWithImpl<$Res>
    extends _$FeedConfigCopyWithImpl<$Res, _$FeedConfigImpl>
    implements _$$FeedConfigImplCopyWith<$Res> {
  __$$FeedConfigImplCopyWithImpl(
    _$FeedConfigImpl _value,
    $Res Function(_$FeedConfigImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of FeedConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? feedType = null,
    Object? mediaFilter = null,
    Object? sortOrder = null,
    Object? postsPerPage = null,
    Object? showBusinessPosts = null,
    Object? showVerifiedPosts = null,
    Object? showTrendingPosts = null,
    Object? excludedUserIds = null,
    Object? excludedHashtags = null,
    Object? preferredTopics = null,
    Object? autoRefresh = null,
    Object? autoRefreshIntervalSeconds = null,
  }) {
    return _then(
      _$FeedConfigImpl(
        feedType: null == feedType
            ? _value.feedType
            : feedType // ignore: cast_nullable_to_non_nullable
                  as FeedType,
        mediaFilter: null == mediaFilter
            ? _value.mediaFilter
            : mediaFilter // ignore: cast_nullable_to_non_nullable
                  as MediaFilter,
        sortOrder: null == sortOrder
            ? _value.sortOrder
            : sortOrder // ignore: cast_nullable_to_non_nullable
                  as SortOrder,
        postsPerPage: null == postsPerPage
            ? _value.postsPerPage
            : postsPerPage // ignore: cast_nullable_to_non_nullable
                  as int,
        showBusinessPosts: null == showBusinessPosts
            ? _value.showBusinessPosts
            : showBusinessPosts // ignore: cast_nullable_to_non_nullable
                  as bool,
        showVerifiedPosts: null == showVerifiedPosts
            ? _value.showVerifiedPosts
            : showVerifiedPosts // ignore: cast_nullable_to_non_nullable
                  as bool,
        showTrendingPosts: null == showTrendingPosts
            ? _value.showTrendingPosts
            : showTrendingPosts // ignore: cast_nullable_to_non_nullable
                  as bool,
        excludedUserIds: null == excludedUserIds
            ? _value._excludedUserIds
            : excludedUserIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        excludedHashtags: null == excludedHashtags
            ? _value._excludedHashtags
            : excludedHashtags // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        preferredTopics: null == preferredTopics
            ? _value._preferredTopics
            : preferredTopics // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        autoRefresh: null == autoRefresh
            ? _value.autoRefresh
            : autoRefresh // ignore: cast_nullable_to_non_nullable
                  as bool,
        autoRefreshIntervalSeconds: null == autoRefreshIntervalSeconds
            ? _value.autoRefreshIntervalSeconds
            : autoRefreshIntervalSeconds // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$FeedConfigImpl implements _FeedConfig {
  const _$FeedConfigImpl({
    this.feedType = FeedType.hybrid,
    this.mediaFilter = MediaFilter.all,
    this.sortOrder = SortOrder.latest,
    this.postsPerPage = 20,
    this.showBusinessPosts = true,
    this.showVerifiedPosts = true,
    this.showTrendingPosts = true,
    final List<String> excludedUserIds = const [],
    final List<String> excludedHashtags = const [],
    final List<String> preferredTopics = const [],
    this.autoRefresh = false,
    this.autoRefreshIntervalSeconds = 300,
  }) : _excludedUserIds = excludedUserIds,
       _excludedHashtags = excludedHashtags,
       _preferredTopics = preferredTopics;

  factory _$FeedConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$FeedConfigImplFromJson(json);

  @override
  @JsonKey()
  final FeedType feedType;
  @override
  @JsonKey()
  final MediaFilter mediaFilter;
  @override
  @JsonKey()
  final SortOrder sortOrder;
  @override
  @JsonKey()
  final int postsPerPage;
  @override
  @JsonKey()
  final bool showBusinessPosts;
  @override
  @JsonKey()
  final bool showVerifiedPosts;
  @override
  @JsonKey()
  final bool showTrendingPosts;
  final List<String> _excludedUserIds;
  @override
  @JsonKey()
  List<String> get excludedUserIds {
    if (_excludedUserIds is EqualUnmodifiableListView) return _excludedUserIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_excludedUserIds);
  }

  final List<String> _excludedHashtags;
  @override
  @JsonKey()
  List<String> get excludedHashtags {
    if (_excludedHashtags is EqualUnmodifiableListView)
      return _excludedHashtags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_excludedHashtags);
  }

  final List<String> _preferredTopics;
  @override
  @JsonKey()
  List<String> get preferredTopics {
    if (_preferredTopics is EqualUnmodifiableListView) return _preferredTopics;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_preferredTopics);
  }

  @override
  @JsonKey()
  final bool autoRefresh;
  @override
  @JsonKey()
  final int autoRefreshIntervalSeconds;

  @override
  String toString() {
    return 'FeedConfig(feedType: $feedType, mediaFilter: $mediaFilter, sortOrder: $sortOrder, postsPerPage: $postsPerPage, showBusinessPosts: $showBusinessPosts, showVerifiedPosts: $showVerifiedPosts, showTrendingPosts: $showTrendingPosts, excludedUserIds: $excludedUserIds, excludedHashtags: $excludedHashtags, preferredTopics: $preferredTopics, autoRefresh: $autoRefresh, autoRefreshIntervalSeconds: $autoRefreshIntervalSeconds)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FeedConfigImpl &&
            (identical(other.feedType, feedType) ||
                other.feedType == feedType) &&
            (identical(other.mediaFilter, mediaFilter) ||
                other.mediaFilter == mediaFilter) &&
            (identical(other.sortOrder, sortOrder) ||
                other.sortOrder == sortOrder) &&
            (identical(other.postsPerPage, postsPerPage) ||
                other.postsPerPage == postsPerPage) &&
            (identical(other.showBusinessPosts, showBusinessPosts) ||
                other.showBusinessPosts == showBusinessPosts) &&
            (identical(other.showVerifiedPosts, showVerifiedPosts) ||
                other.showVerifiedPosts == showVerifiedPosts) &&
            (identical(other.showTrendingPosts, showTrendingPosts) ||
                other.showTrendingPosts == showTrendingPosts) &&
            const DeepCollectionEquality().equals(
              other._excludedUserIds,
              _excludedUserIds,
            ) &&
            const DeepCollectionEquality().equals(
              other._excludedHashtags,
              _excludedHashtags,
            ) &&
            const DeepCollectionEquality().equals(
              other._preferredTopics,
              _preferredTopics,
            ) &&
            (identical(other.autoRefresh, autoRefresh) ||
                other.autoRefresh == autoRefresh) &&
            (identical(
                  other.autoRefreshIntervalSeconds,
                  autoRefreshIntervalSeconds,
                ) ||
                other.autoRefreshIntervalSeconds ==
                    autoRefreshIntervalSeconds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    feedType,
    mediaFilter,
    sortOrder,
    postsPerPage,
    showBusinessPosts,
    showVerifiedPosts,
    showTrendingPosts,
    const DeepCollectionEquality().hash(_excludedUserIds),
    const DeepCollectionEquality().hash(_excludedHashtags),
    const DeepCollectionEquality().hash(_preferredTopics),
    autoRefresh,
    autoRefreshIntervalSeconds,
  );

  /// Create a copy of FeedConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FeedConfigImplCopyWith<_$FeedConfigImpl> get copyWith =>
      __$$FeedConfigImplCopyWithImpl<_$FeedConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FeedConfigImplToJson(this);
  }
}

abstract class _FeedConfig implements FeedConfig {
  const factory _FeedConfig({
    final FeedType feedType,
    final MediaFilter mediaFilter,
    final SortOrder sortOrder,
    final int postsPerPage,
    final bool showBusinessPosts,
    final bool showVerifiedPosts,
    final bool showTrendingPosts,
    final List<String> excludedUserIds,
    final List<String> excludedHashtags,
    final List<String> preferredTopics,
    final bool autoRefresh,
    final int autoRefreshIntervalSeconds,
  }) = _$FeedConfigImpl;

  factory _FeedConfig.fromJson(Map<String, dynamic> json) =
      _$FeedConfigImpl.fromJson;

  @override
  FeedType get feedType;
  @override
  MediaFilter get mediaFilter;
  @override
  SortOrder get sortOrder;
  @override
  int get postsPerPage;
  @override
  bool get showBusinessPosts;
  @override
  bool get showVerifiedPosts;
  @override
  bool get showTrendingPosts;
  @override
  List<String> get excludedUserIds;
  @override
  List<String> get excludedHashtags;
  @override
  List<String> get preferredTopics;
  @override
  bool get autoRefresh;
  @override
  int get autoRefreshIntervalSeconds;

  /// Create a copy of FeedConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FeedConfigImplCopyWith<_$FeedConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

FeedPreferences _$FeedPreferencesFromJson(Map<String, dynamic> json) {
  return _FeedPreferences.fromJson(json);
}

/// @nodoc
mixin _$FeedPreferences {
  String get userId => throw _privateConstructorUsedError;
  FeedConfig get config => throw _privateConstructorUsedError;
  DateTime get lastUpdated => throw _privateConstructorUsedError;
  Map<String, dynamic> get userInterests => throw _privateConstructorUsedError;
  List<String> get followedTopics => throw _privateConstructorUsedError;
  List<String> get mutedUsers => throw _privateConstructorUsedError;
  List<String> get mutedHashtags => throw _privateConstructorUsedError;
  bool get showSensitiveContent => throw _privateConstructorUsedError;
  bool get showPoliticalContent => throw _privateConstructorUsedError;
  bool get showControversialTopics => throw _privateConstructorUsedError;

  /// Serializes this FeedPreferences to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FeedPreferences
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FeedPreferencesCopyWith<FeedPreferences> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FeedPreferencesCopyWith<$Res> {
  factory $FeedPreferencesCopyWith(
    FeedPreferences value,
    $Res Function(FeedPreferences) then,
  ) = _$FeedPreferencesCopyWithImpl<$Res, FeedPreferences>;
  @useResult
  $Res call({
    String userId,
    FeedConfig config,
    DateTime lastUpdated,
    Map<String, dynamic> userInterests,
    List<String> followedTopics,
    List<String> mutedUsers,
    List<String> mutedHashtags,
    bool showSensitiveContent,
    bool showPoliticalContent,
    bool showControversialTopics,
  });

  $FeedConfigCopyWith<$Res> get config;
}

/// @nodoc
class _$FeedPreferencesCopyWithImpl<$Res, $Val extends FeedPreferences>
    implements $FeedPreferencesCopyWith<$Res> {
  _$FeedPreferencesCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FeedPreferences
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? config = null,
    Object? lastUpdated = null,
    Object? userInterests = null,
    Object? followedTopics = null,
    Object? mutedUsers = null,
    Object? mutedHashtags = null,
    Object? showSensitiveContent = null,
    Object? showPoliticalContent = null,
    Object? showControversialTopics = null,
  }) {
    return _then(
      _value.copyWith(
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            config: null == config
                ? _value.config
                : config // ignore: cast_nullable_to_non_nullable
                      as FeedConfig,
            lastUpdated: null == lastUpdated
                ? _value.lastUpdated
                : lastUpdated // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            userInterests: null == userInterests
                ? _value.userInterests
                : userInterests // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            followedTopics: null == followedTopics
                ? _value.followedTopics
                : followedTopics // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            mutedUsers: null == mutedUsers
                ? _value.mutedUsers
                : mutedUsers // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            mutedHashtags: null == mutedHashtags
                ? _value.mutedHashtags
                : mutedHashtags // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            showSensitiveContent: null == showSensitiveContent
                ? _value.showSensitiveContent
                : showSensitiveContent // ignore: cast_nullable_to_non_nullable
                      as bool,
            showPoliticalContent: null == showPoliticalContent
                ? _value.showPoliticalContent
                : showPoliticalContent // ignore: cast_nullable_to_non_nullable
                      as bool,
            showControversialTopics: null == showControversialTopics
                ? _value.showControversialTopics
                : showControversialTopics // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }

  /// Create a copy of FeedPreferences
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FeedConfigCopyWith<$Res> get config {
    return $FeedConfigCopyWith<$Res>(_value.config, (value) {
      return _then(_value.copyWith(config: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$FeedPreferencesImplCopyWith<$Res>
    implements $FeedPreferencesCopyWith<$Res> {
  factory _$$FeedPreferencesImplCopyWith(
    _$FeedPreferencesImpl value,
    $Res Function(_$FeedPreferencesImpl) then,
  ) = __$$FeedPreferencesImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String userId,
    FeedConfig config,
    DateTime lastUpdated,
    Map<String, dynamic> userInterests,
    List<String> followedTopics,
    List<String> mutedUsers,
    List<String> mutedHashtags,
    bool showSensitiveContent,
    bool showPoliticalContent,
    bool showControversialTopics,
  });

  @override
  $FeedConfigCopyWith<$Res> get config;
}

/// @nodoc
class __$$FeedPreferencesImplCopyWithImpl<$Res>
    extends _$FeedPreferencesCopyWithImpl<$Res, _$FeedPreferencesImpl>
    implements _$$FeedPreferencesImplCopyWith<$Res> {
  __$$FeedPreferencesImplCopyWithImpl(
    _$FeedPreferencesImpl _value,
    $Res Function(_$FeedPreferencesImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of FeedPreferences
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? config = null,
    Object? lastUpdated = null,
    Object? userInterests = null,
    Object? followedTopics = null,
    Object? mutedUsers = null,
    Object? mutedHashtags = null,
    Object? showSensitiveContent = null,
    Object? showPoliticalContent = null,
    Object? showControversialTopics = null,
  }) {
    return _then(
      _$FeedPreferencesImpl(
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        config: null == config
            ? _value.config
            : config // ignore: cast_nullable_to_non_nullable
                  as FeedConfig,
        lastUpdated: null == lastUpdated
            ? _value.lastUpdated
            : lastUpdated // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        userInterests: null == userInterests
            ? _value._userInterests
            : userInterests // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        followedTopics: null == followedTopics
            ? _value._followedTopics
            : followedTopics // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        mutedUsers: null == mutedUsers
            ? _value._mutedUsers
            : mutedUsers // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        mutedHashtags: null == mutedHashtags
            ? _value._mutedHashtags
            : mutedHashtags // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        showSensitiveContent: null == showSensitiveContent
            ? _value.showSensitiveContent
            : showSensitiveContent // ignore: cast_nullable_to_non_nullable
                  as bool,
        showPoliticalContent: null == showPoliticalContent
            ? _value.showPoliticalContent
            : showPoliticalContent // ignore: cast_nullable_to_non_nullable
                  as bool,
        showControversialTopics: null == showControversialTopics
            ? _value.showControversialTopics
            : showControversialTopics // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$FeedPreferencesImpl implements _FeedPreferences {
  const _$FeedPreferencesImpl({
    required this.userId,
    required this.config,
    required this.lastUpdated,
    final Map<String, dynamic> userInterests = const {},
    final List<String> followedTopics = const [],
    final List<String> mutedUsers = const [],
    final List<String> mutedHashtags = const [],
    this.showSensitiveContent = true,
    this.showPoliticalContent = true,
    this.showControversialTopics = true,
  }) : _userInterests = userInterests,
       _followedTopics = followedTopics,
       _mutedUsers = mutedUsers,
       _mutedHashtags = mutedHashtags;

  factory _$FeedPreferencesImpl.fromJson(Map<String, dynamic> json) =>
      _$$FeedPreferencesImplFromJson(json);

  @override
  final String userId;
  @override
  final FeedConfig config;
  @override
  final DateTime lastUpdated;
  final Map<String, dynamic> _userInterests;
  @override
  @JsonKey()
  Map<String, dynamic> get userInterests {
    if (_userInterests is EqualUnmodifiableMapView) return _userInterests;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_userInterests);
  }

  final List<String> _followedTopics;
  @override
  @JsonKey()
  List<String> get followedTopics {
    if (_followedTopics is EqualUnmodifiableListView) return _followedTopics;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_followedTopics);
  }

  final List<String> _mutedUsers;
  @override
  @JsonKey()
  List<String> get mutedUsers {
    if (_mutedUsers is EqualUnmodifiableListView) return _mutedUsers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_mutedUsers);
  }

  final List<String> _mutedHashtags;
  @override
  @JsonKey()
  List<String> get mutedHashtags {
    if (_mutedHashtags is EqualUnmodifiableListView) return _mutedHashtags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_mutedHashtags);
  }

  @override
  @JsonKey()
  final bool showSensitiveContent;
  @override
  @JsonKey()
  final bool showPoliticalContent;
  @override
  @JsonKey()
  final bool showControversialTopics;

  @override
  String toString() {
    return 'FeedPreferences(userId: $userId, config: $config, lastUpdated: $lastUpdated, userInterests: $userInterests, followedTopics: $followedTopics, mutedUsers: $mutedUsers, mutedHashtags: $mutedHashtags, showSensitiveContent: $showSensitiveContent, showPoliticalContent: $showPoliticalContent, showControversialTopics: $showControversialTopics)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FeedPreferencesImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.config, config) || other.config == config) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated) &&
            const DeepCollectionEquality().equals(
              other._userInterests,
              _userInterests,
            ) &&
            const DeepCollectionEquality().equals(
              other._followedTopics,
              _followedTopics,
            ) &&
            const DeepCollectionEquality().equals(
              other._mutedUsers,
              _mutedUsers,
            ) &&
            const DeepCollectionEquality().equals(
              other._mutedHashtags,
              _mutedHashtags,
            ) &&
            (identical(other.showSensitiveContent, showSensitiveContent) ||
                other.showSensitiveContent == showSensitiveContent) &&
            (identical(other.showPoliticalContent, showPoliticalContent) ||
                other.showPoliticalContent == showPoliticalContent) &&
            (identical(
                  other.showControversialTopics,
                  showControversialTopics,
                ) ||
                other.showControversialTopics == showControversialTopics));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    userId,
    config,
    lastUpdated,
    const DeepCollectionEquality().hash(_userInterests),
    const DeepCollectionEquality().hash(_followedTopics),
    const DeepCollectionEquality().hash(_mutedUsers),
    const DeepCollectionEquality().hash(_mutedHashtags),
    showSensitiveContent,
    showPoliticalContent,
    showControversialTopics,
  );

  /// Create a copy of FeedPreferences
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FeedPreferencesImplCopyWith<_$FeedPreferencesImpl> get copyWith =>
      __$$FeedPreferencesImplCopyWithImpl<_$FeedPreferencesImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$FeedPreferencesImplToJson(this);
  }
}

abstract class _FeedPreferences implements FeedPreferences {
  const factory _FeedPreferences({
    required final String userId,
    required final FeedConfig config,
    required final DateTime lastUpdated,
    final Map<String, dynamic> userInterests,
    final List<String> followedTopics,
    final List<String> mutedUsers,
    final List<String> mutedHashtags,
    final bool showSensitiveContent,
    final bool showPoliticalContent,
    final bool showControversialTopics,
  }) = _$FeedPreferencesImpl;

  factory _FeedPreferences.fromJson(Map<String, dynamic> json) =
      _$FeedPreferencesImpl.fromJson;

  @override
  String get userId;
  @override
  FeedConfig get config;
  @override
  DateTime get lastUpdated;
  @override
  Map<String, dynamic> get userInterests;
  @override
  List<String> get followedTopics;
  @override
  List<String> get mutedUsers;
  @override
  List<String> get mutedHashtags;
  @override
  bool get showSensitiveContent;
  @override
  bool get showPoliticalContent;
  @override
  bool get showControversialTopics;

  /// Create a copy of FeedPreferences
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FeedPreferencesImplCopyWith<_$FeedPreferencesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

FeedAnalytics _$FeedAnalyticsFromJson(Map<String, dynamic> json) {
  return _FeedAnalytics.fromJson(json);
}

/// @nodoc
mixin _$FeedAnalytics {
  String get userId => throw _privateConstructorUsedError;
  DateTime get date => throw _privateConstructorUsedError;
  int get postsViewed => throw _privateConstructorUsedError;
  int get postsLiked => throw _privateConstructorUsedError;
  int get postsCommented => throw _privateConstructorUsedError;
  int get postsShared => throw _privateConstructorUsedError;
  int get timeSpentSeconds => throw _privateConstructorUsedError;
  Map<String, int> get topicEngagement => throw _privateConstructorUsedError;
  Map<String, int> get userEngagement => throw _privateConstructorUsedError;
  List<String> get trendingTopics => throw _privateConstructorUsedError;

  /// Serializes this FeedAnalytics to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FeedAnalytics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FeedAnalyticsCopyWith<FeedAnalytics> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FeedAnalyticsCopyWith<$Res> {
  factory $FeedAnalyticsCopyWith(
    FeedAnalytics value,
    $Res Function(FeedAnalytics) then,
  ) = _$FeedAnalyticsCopyWithImpl<$Res, FeedAnalytics>;
  @useResult
  $Res call({
    String userId,
    DateTime date,
    int postsViewed,
    int postsLiked,
    int postsCommented,
    int postsShared,
    int timeSpentSeconds,
    Map<String, int> topicEngagement,
    Map<String, int> userEngagement,
    List<String> trendingTopics,
  });
}

/// @nodoc
class _$FeedAnalyticsCopyWithImpl<$Res, $Val extends FeedAnalytics>
    implements $FeedAnalyticsCopyWith<$Res> {
  _$FeedAnalyticsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FeedAnalytics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? date = null,
    Object? postsViewed = null,
    Object? postsLiked = null,
    Object? postsCommented = null,
    Object? postsShared = null,
    Object? timeSpentSeconds = null,
    Object? topicEngagement = null,
    Object? userEngagement = null,
    Object? trendingTopics = null,
  }) {
    return _then(
      _value.copyWith(
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            date: null == date
                ? _value.date
                : date // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            postsViewed: null == postsViewed
                ? _value.postsViewed
                : postsViewed // ignore: cast_nullable_to_non_nullable
                      as int,
            postsLiked: null == postsLiked
                ? _value.postsLiked
                : postsLiked // ignore: cast_nullable_to_non_nullable
                      as int,
            postsCommented: null == postsCommented
                ? _value.postsCommented
                : postsCommented // ignore: cast_nullable_to_non_nullable
                      as int,
            postsShared: null == postsShared
                ? _value.postsShared
                : postsShared // ignore: cast_nullable_to_non_nullable
                      as int,
            timeSpentSeconds: null == timeSpentSeconds
                ? _value.timeSpentSeconds
                : timeSpentSeconds // ignore: cast_nullable_to_non_nullable
                      as int,
            topicEngagement: null == topicEngagement
                ? _value.topicEngagement
                : topicEngagement // ignore: cast_nullable_to_non_nullable
                      as Map<String, int>,
            userEngagement: null == userEngagement
                ? _value.userEngagement
                : userEngagement // ignore: cast_nullable_to_non_nullable
                      as Map<String, int>,
            trendingTopics: null == trendingTopics
                ? _value.trendingTopics
                : trendingTopics // ignore: cast_nullable_to_non_nullable
                      as List<String>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$FeedAnalyticsImplCopyWith<$Res>
    implements $FeedAnalyticsCopyWith<$Res> {
  factory _$$FeedAnalyticsImplCopyWith(
    _$FeedAnalyticsImpl value,
    $Res Function(_$FeedAnalyticsImpl) then,
  ) = __$$FeedAnalyticsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String userId,
    DateTime date,
    int postsViewed,
    int postsLiked,
    int postsCommented,
    int postsShared,
    int timeSpentSeconds,
    Map<String, int> topicEngagement,
    Map<String, int> userEngagement,
    List<String> trendingTopics,
  });
}

/// @nodoc
class __$$FeedAnalyticsImplCopyWithImpl<$Res>
    extends _$FeedAnalyticsCopyWithImpl<$Res, _$FeedAnalyticsImpl>
    implements _$$FeedAnalyticsImplCopyWith<$Res> {
  __$$FeedAnalyticsImplCopyWithImpl(
    _$FeedAnalyticsImpl _value,
    $Res Function(_$FeedAnalyticsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of FeedAnalytics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? date = null,
    Object? postsViewed = null,
    Object? postsLiked = null,
    Object? postsCommented = null,
    Object? postsShared = null,
    Object? timeSpentSeconds = null,
    Object? topicEngagement = null,
    Object? userEngagement = null,
    Object? trendingTopics = null,
  }) {
    return _then(
      _$FeedAnalyticsImpl(
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        date: null == date
            ? _value.date
            : date // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        postsViewed: null == postsViewed
            ? _value.postsViewed
            : postsViewed // ignore: cast_nullable_to_non_nullable
                  as int,
        postsLiked: null == postsLiked
            ? _value.postsLiked
            : postsLiked // ignore: cast_nullable_to_non_nullable
                  as int,
        postsCommented: null == postsCommented
            ? _value.postsCommented
            : postsCommented // ignore: cast_nullable_to_non_nullable
                  as int,
        postsShared: null == postsShared
            ? _value.postsShared
            : postsShared // ignore: cast_nullable_to_non_nullable
                  as int,
        timeSpentSeconds: null == timeSpentSeconds
            ? _value.timeSpentSeconds
            : timeSpentSeconds // ignore: cast_nullable_to_non_nullable
                  as int,
        topicEngagement: null == topicEngagement
            ? _value._topicEngagement
            : topicEngagement // ignore: cast_nullable_to_non_nullable
                  as Map<String, int>,
        userEngagement: null == userEngagement
            ? _value._userEngagement
            : userEngagement // ignore: cast_nullable_to_non_nullable
                  as Map<String, int>,
        trendingTopics: null == trendingTopics
            ? _value._trendingTopics
            : trendingTopics // ignore: cast_nullable_to_non_nullable
                  as List<String>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$FeedAnalyticsImpl implements _FeedAnalytics {
  const _$FeedAnalyticsImpl({
    required this.userId,
    required this.date,
    required this.postsViewed,
    required this.postsLiked,
    required this.postsCommented,
    required this.postsShared,
    required this.timeSpentSeconds,
    required final Map<String, int> topicEngagement,
    required final Map<String, int> userEngagement,
    required final List<String> trendingTopics,
  }) : _topicEngagement = topicEngagement,
       _userEngagement = userEngagement,
       _trendingTopics = trendingTopics;

  factory _$FeedAnalyticsImpl.fromJson(Map<String, dynamic> json) =>
      _$$FeedAnalyticsImplFromJson(json);

  @override
  final String userId;
  @override
  final DateTime date;
  @override
  final int postsViewed;
  @override
  final int postsLiked;
  @override
  final int postsCommented;
  @override
  final int postsShared;
  @override
  final int timeSpentSeconds;
  final Map<String, int> _topicEngagement;
  @override
  Map<String, int> get topicEngagement {
    if (_topicEngagement is EqualUnmodifiableMapView) return _topicEngagement;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_topicEngagement);
  }

  final Map<String, int> _userEngagement;
  @override
  Map<String, int> get userEngagement {
    if (_userEngagement is EqualUnmodifiableMapView) return _userEngagement;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_userEngagement);
  }

  final List<String> _trendingTopics;
  @override
  List<String> get trendingTopics {
    if (_trendingTopics is EqualUnmodifiableListView) return _trendingTopics;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_trendingTopics);
  }

  @override
  String toString() {
    return 'FeedAnalytics(userId: $userId, date: $date, postsViewed: $postsViewed, postsLiked: $postsLiked, postsCommented: $postsCommented, postsShared: $postsShared, timeSpentSeconds: $timeSpentSeconds, topicEngagement: $topicEngagement, userEngagement: $userEngagement, trendingTopics: $trendingTopics)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FeedAnalyticsImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.postsViewed, postsViewed) ||
                other.postsViewed == postsViewed) &&
            (identical(other.postsLiked, postsLiked) ||
                other.postsLiked == postsLiked) &&
            (identical(other.postsCommented, postsCommented) ||
                other.postsCommented == postsCommented) &&
            (identical(other.postsShared, postsShared) ||
                other.postsShared == postsShared) &&
            (identical(other.timeSpentSeconds, timeSpentSeconds) ||
                other.timeSpentSeconds == timeSpentSeconds) &&
            const DeepCollectionEquality().equals(
              other._topicEngagement,
              _topicEngagement,
            ) &&
            const DeepCollectionEquality().equals(
              other._userEngagement,
              _userEngagement,
            ) &&
            const DeepCollectionEquality().equals(
              other._trendingTopics,
              _trendingTopics,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    userId,
    date,
    postsViewed,
    postsLiked,
    postsCommented,
    postsShared,
    timeSpentSeconds,
    const DeepCollectionEquality().hash(_topicEngagement),
    const DeepCollectionEquality().hash(_userEngagement),
    const DeepCollectionEquality().hash(_trendingTopics),
  );

  /// Create a copy of FeedAnalytics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FeedAnalyticsImplCopyWith<_$FeedAnalyticsImpl> get copyWith =>
      __$$FeedAnalyticsImplCopyWithImpl<_$FeedAnalyticsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FeedAnalyticsImplToJson(this);
  }
}

abstract class _FeedAnalytics implements FeedAnalytics {
  const factory _FeedAnalytics({
    required final String userId,
    required final DateTime date,
    required final int postsViewed,
    required final int postsLiked,
    required final int postsCommented,
    required final int postsShared,
    required final int timeSpentSeconds,
    required final Map<String, int> topicEngagement,
    required final Map<String, int> userEngagement,
    required final List<String> trendingTopics,
  }) = _$FeedAnalyticsImpl;

  factory _FeedAnalytics.fromJson(Map<String, dynamic> json) =
      _$FeedAnalyticsImpl.fromJson;

  @override
  String get userId;
  @override
  DateTime get date;
  @override
  int get postsViewed;
  @override
  int get postsLiked;
  @override
  int get postsCommented;
  @override
  int get postsShared;
  @override
  int get timeSpentSeconds;
  @override
  Map<String, int> get topicEngagement;
  @override
  Map<String, int> get userEngagement;
  @override
  List<String> get trendingTopics;

  /// Create a copy of FeedAnalytics
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FeedAnalyticsImplCopyWith<_$FeedAnalyticsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
