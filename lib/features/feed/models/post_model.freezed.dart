// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'post_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

Post _$PostFromJson(Map<String, dynamic> json) {
  return _Post.fromJson(json);
}

/// @nodoc
mixin _$Post {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get username => throw _privateConstructorUsedError;
  String get userAvatarUrl => throw _privateConstructorUsedError;
  String? get userRole =>
      throw _privateConstructorUsedError; // CEO, Plastic Surgeon, etc.
  bool? get isVerified =>
      throw _privateConstructorUsedError; // General verification
  bool? get isBillionaire =>
      throw _privateConstructorUsedError; // Billionaire verification
  bool? get isCelebrity =>
      throw _privateConstructorUsedError; // Celebrity verification
  String? get verificationTier =>
      throw _privateConstructorUsedError; // Verification tier as string
  MediaType get mediaType => throw _privateConstructorUsedError;
  String get mediaUrl =>
      throw _privateConstructorUsedError; // Multiple media support
  List<String>? get mediaUrls =>
      throw _privateConstructorUsedError; // For multiple images/videos
  List<MediaType>? get mediaTypes =>
      throw _privateConstructorUsedError; // Corresponding media types
  String get caption => throw _privateConstructorUsedError;
  String? get location => throw _privateConstructorUsedError;
  String? get locationId =>
      throw _privateConstructorUsedError; // For location navigation
  int get likeCount => throw _privateConstructorUsedError;
  int get commentCount => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;
  DateTime? get localCreatedAt => throw _privateConstructorUsedError;
  bool get isLiked => throw _privateConstructorUsedError;
  bool get isBookmarked => throw _privateConstructorUsedError;
  bool get isReposted =>
      throw _privateConstructorUsedError; // Co-author system (Remax feature)
  List<String>? get coAuthorIds => throw _privateConstructorUsedError;
  List<String>? get coAuthorUsernames => throw _privateConstructorUsedError;
  List<String>? get coAuthorAvatars =>
      throw _privateConstructorUsedError; // Tagging system
  List<String>? get mentionedUsers =>
      throw _privateConstructorUsedError; // @mentions in caption
  List<String>? get hashtags =>
      throw _privateConstructorUsedError; // #hashtags in caption
  List<MediaTag>? get mediaTags =>
      throw _privateConstructorUsedError; // Tags on photos/videos
  List<String>? get taggedUserIds =>
      throw _privateConstructorUsedError; // Users tagged in media
  // Interaction tracking
  List<String>? get likedBy => throw _privateConstructorUsedError;
  List<String>? get bookmarkedBy => throw _privateConstructorUsedError;
  List<String>? get repostedBy =>
      throw _privateConstructorUsedError; // Enhanced permissions and visibility
  bool get isPublic => throw _privateConstructorUsedError;
  String get visibility =>
      throw _privateConstructorUsedError; // 'public', 'followers', 'closeFriends', 'private'
  bool get isArchived => throw _privateConstructorUsedError;
  bool get isReported => throw _privateConstructorUsedError;
  bool get isPinned =>
      throw _privateConstructorUsedError; // For pinned posts on profile
  bool get isDeleted => throw _privateConstructorUsedError;
  bool get isFlagged => throw _privateConstructorUsedError;
  String get status =>
      throw _privateConstructorUsedError; // 'active', 'archived', 'deleted', 'flagged', 'review'
  // Analytics and engagement
  int? get viewCount => throw _privateConstructorUsedError;
  int? get shareCount => throw _privateConstructorUsedError;
  int? get repostCount => throw _privateConstructorUsedError;
  int? get saveCount => throw _privateConstructorUsedError;
  bool get trending => throw _privateConstructorUsedError; // Trending badge
  num? get trendingScore =>
      throw _privateConstructorUsedError; // Trending score for ranking
  DateTime? get lastEditedAt => throw _privateConstructorUsedError;
  String? get editedBy =>
      throw _privateConstructorUsedError; // Remix and Repost features
  bool get isRemix => throw _privateConstructorUsedError;
  bool get allowRemix => throw _privateConstructorUsedError;
  bool get allowRepost => throw _privateConstructorUsedError;
  bool get allowShare => throw _privateConstructorUsedError;
  String? get originalPostId =>
      throw _privateConstructorUsedError; // For remixes and reposts
  String? get originalUserId => throw _privateConstructorUsedError;
  String? get originalUsername =>
      throw _privateConstructorUsedError; // Enhanced post features
  bool get commentsDisabled => throw _privateConstructorUsedError;
  bool get likesHidden => throw _privateConstructorUsedError;
  bool get hasAIContent => throw _privateConstructorUsedError;
  String? get musicTrack => throw _privateConstructorUsedError;
  String? get musicUrl => throw _privateConstructorUsedError; // Admin features
  List<String>? get flaggedReasons => throw _privateConstructorUsedError;
  DateTime? get flaggedAt => throw _privateConstructorUsedError;
  String? get flaggedBy => throw _privateConstructorUsedError;
  int? get reportCount => throw _privateConstructorUsedError;
  DateTime? get reviewedAt => throw _privateConstructorUsedError;
  String? get reviewedBy => throw _privateConstructorUsedError;
  String? get moderationAction =>
      throw _privateConstructorUsedError; // Close friends and groups
  List<String>? get closeFriendsGroupIds =>
      throw _privateConstructorUsedError; // Content metadata
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;
  List<String>? get contentWarnings => throw _privateConstructorUsedError;
  String? get ageRestriction => throw _privateConstructorUsedError;

  /// Serializes this Post to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Post
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PostCopyWith<Post> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PostCopyWith<$Res> {
  factory $PostCopyWith(Post value, $Res Function(Post) then) =
      _$PostCopyWithImpl<$Res, Post>;
  @useResult
  $Res call({
    String id,
    String userId,
    String username,
    String userAvatarUrl,
    String? userRole,
    bool? isVerified,
    bool? isBillionaire,
    bool? isCelebrity,
    String? verificationTier,
    MediaType mediaType,
    String mediaUrl,
    List<String>? mediaUrls,
    List<MediaType>? mediaTypes,
    String caption,
    String? location,
    String? locationId,
    int likeCount,
    int commentCount,
    DateTime timestamp,
    DateTime? localCreatedAt,
    bool isLiked,
    bool isBookmarked,
    bool isReposted,
    List<String>? coAuthorIds,
    List<String>? coAuthorUsernames,
    List<String>? coAuthorAvatars,
    List<String>? mentionedUsers,
    List<String>? hashtags,
    List<MediaTag>? mediaTags,
    List<String>? taggedUserIds,
    List<String>? likedBy,
    List<String>? bookmarkedBy,
    List<String>? repostedBy,
    bool isPublic,
    String visibility,
    bool isArchived,
    bool isReported,
    bool isPinned,
    bool isDeleted,
    bool isFlagged,
    String status,
    int? viewCount,
    int? shareCount,
    int? repostCount,
    int? saveCount,
    bool trending,
    num? trendingScore,
    DateTime? lastEditedAt,
    String? editedBy,
    bool isRemix,
    bool allowRemix,
    bool allowRepost,
    bool allowShare,
    String? originalPostId,
    String? originalUserId,
    String? originalUsername,
    bool commentsDisabled,
    bool likesHidden,
    bool hasAIContent,
    String? musicTrack,
    String? musicUrl,
    List<String>? flaggedReasons,
    DateTime? flaggedAt,
    String? flaggedBy,
    int? reportCount,
    DateTime? reviewedAt,
    String? reviewedBy,
    String? moderationAction,
    List<String>? closeFriendsGroupIds,
    Map<String, dynamic>? metadata,
    List<String>? contentWarnings,
    String? ageRestriction,
  });
}

/// @nodoc
class _$PostCopyWithImpl<$Res, $Val extends Post>
    implements $PostCopyWith<$Res> {
  _$PostCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Post
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? username = null,
    Object? userAvatarUrl = null,
    Object? userRole = freezed,
    Object? isVerified = freezed,
    Object? isBillionaire = freezed,
    Object? isCelebrity = freezed,
    Object? verificationTier = freezed,
    Object? mediaType = null,
    Object? mediaUrl = null,
    Object? mediaUrls = freezed,
    Object? mediaTypes = freezed,
    Object? caption = null,
    Object? location = freezed,
    Object? locationId = freezed,
    Object? likeCount = null,
    Object? commentCount = null,
    Object? timestamp = null,
    Object? localCreatedAt = freezed,
    Object? isLiked = null,
    Object? isBookmarked = null,
    Object? isReposted = null,
    Object? coAuthorIds = freezed,
    Object? coAuthorUsernames = freezed,
    Object? coAuthorAvatars = freezed,
    Object? mentionedUsers = freezed,
    Object? hashtags = freezed,
    Object? mediaTags = freezed,
    Object? taggedUserIds = freezed,
    Object? likedBy = freezed,
    Object? bookmarkedBy = freezed,
    Object? repostedBy = freezed,
    Object? isPublic = null,
    Object? visibility = null,
    Object? isArchived = null,
    Object? isReported = null,
    Object? isPinned = null,
    Object? isDeleted = null,
    Object? isFlagged = null,
    Object? status = null,
    Object? viewCount = freezed,
    Object? shareCount = freezed,
    Object? repostCount = freezed,
    Object? saveCount = freezed,
    Object? trending = null,
    Object? trendingScore = freezed,
    Object? lastEditedAt = freezed,
    Object? editedBy = freezed,
    Object? isRemix = null,
    Object? allowRemix = null,
    Object? allowRepost = null,
    Object? allowShare = null,
    Object? originalPostId = freezed,
    Object? originalUserId = freezed,
    Object? originalUsername = freezed,
    Object? commentsDisabled = null,
    Object? likesHidden = null,
    Object? hasAIContent = null,
    Object? musicTrack = freezed,
    Object? musicUrl = freezed,
    Object? flaggedReasons = freezed,
    Object? flaggedAt = freezed,
    Object? flaggedBy = freezed,
    Object? reportCount = freezed,
    Object? reviewedAt = freezed,
    Object? reviewedBy = freezed,
    Object? moderationAction = freezed,
    Object? closeFriendsGroupIds = freezed,
    Object? metadata = freezed,
    Object? contentWarnings = freezed,
    Object? ageRestriction = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            username: null == username
                ? _value.username
                : username // ignore: cast_nullable_to_non_nullable
                      as String,
            userAvatarUrl: null == userAvatarUrl
                ? _value.userAvatarUrl
                : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            userRole: freezed == userRole
                ? _value.userRole
                : userRole // ignore: cast_nullable_to_non_nullable
                      as String?,
            isVerified: freezed == isVerified
                ? _value.isVerified
                : isVerified // ignore: cast_nullable_to_non_nullable
                      as bool?,
            isBillionaire: freezed == isBillionaire
                ? _value.isBillionaire
                : isBillionaire // ignore: cast_nullable_to_non_nullable
                      as bool?,
            isCelebrity: freezed == isCelebrity
                ? _value.isCelebrity
                : isCelebrity // ignore: cast_nullable_to_non_nullable
                      as bool?,
            verificationTier: freezed == verificationTier
                ? _value.verificationTier
                : verificationTier // ignore: cast_nullable_to_non_nullable
                      as String?,
            mediaType: null == mediaType
                ? _value.mediaType
                : mediaType // ignore: cast_nullable_to_non_nullable
                      as MediaType,
            mediaUrl: null == mediaUrl
                ? _value.mediaUrl
                : mediaUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            mediaUrls: freezed == mediaUrls
                ? _value.mediaUrls
                : mediaUrls // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            mediaTypes: freezed == mediaTypes
                ? _value.mediaTypes
                : mediaTypes // ignore: cast_nullable_to_non_nullable
                      as List<MediaType>?,
            caption: null == caption
                ? _value.caption
                : caption // ignore: cast_nullable_to_non_nullable
                      as String,
            location: freezed == location
                ? _value.location
                : location // ignore: cast_nullable_to_non_nullable
                      as String?,
            locationId: freezed == locationId
                ? _value.locationId
                : locationId // ignore: cast_nullable_to_non_nullable
                      as String?,
            likeCount: null == likeCount
                ? _value.likeCount
                : likeCount // ignore: cast_nullable_to_non_nullable
                      as int,
            commentCount: null == commentCount
                ? _value.commentCount
                : commentCount // ignore: cast_nullable_to_non_nullable
                      as int,
            timestamp: null == timestamp
                ? _value.timestamp
                : timestamp // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            localCreatedAt: freezed == localCreatedAt
                ? _value.localCreatedAt
                : localCreatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            isLiked: null == isLiked
                ? _value.isLiked
                : isLiked // ignore: cast_nullable_to_non_nullable
                      as bool,
            isBookmarked: null == isBookmarked
                ? _value.isBookmarked
                : isBookmarked // ignore: cast_nullable_to_non_nullable
                      as bool,
            isReposted: null == isReposted
                ? _value.isReposted
                : isReposted // ignore: cast_nullable_to_non_nullable
                      as bool,
            coAuthorIds: freezed == coAuthorIds
                ? _value.coAuthorIds
                : coAuthorIds // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            coAuthorUsernames: freezed == coAuthorUsernames
                ? _value.coAuthorUsernames
                : coAuthorUsernames // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            coAuthorAvatars: freezed == coAuthorAvatars
                ? _value.coAuthorAvatars
                : coAuthorAvatars // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            mentionedUsers: freezed == mentionedUsers
                ? _value.mentionedUsers
                : mentionedUsers // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            hashtags: freezed == hashtags
                ? _value.hashtags
                : hashtags // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            mediaTags: freezed == mediaTags
                ? _value.mediaTags
                : mediaTags // ignore: cast_nullable_to_non_nullable
                      as List<MediaTag>?,
            taggedUserIds: freezed == taggedUserIds
                ? _value.taggedUserIds
                : taggedUserIds // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            likedBy: freezed == likedBy
                ? _value.likedBy
                : likedBy // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            bookmarkedBy: freezed == bookmarkedBy
                ? _value.bookmarkedBy
                : bookmarkedBy // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            repostedBy: freezed == repostedBy
                ? _value.repostedBy
                : repostedBy // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            isPublic: null == isPublic
                ? _value.isPublic
                : isPublic // ignore: cast_nullable_to_non_nullable
                      as bool,
            visibility: null == visibility
                ? _value.visibility
                : visibility // ignore: cast_nullable_to_non_nullable
                      as String,
            isArchived: null == isArchived
                ? _value.isArchived
                : isArchived // ignore: cast_nullable_to_non_nullable
                      as bool,
            isReported: null == isReported
                ? _value.isReported
                : isReported // ignore: cast_nullable_to_non_nullable
                      as bool,
            isPinned: null == isPinned
                ? _value.isPinned
                : isPinned // ignore: cast_nullable_to_non_nullable
                      as bool,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
            isFlagged: null == isFlagged
                ? _value.isFlagged
                : isFlagged // ignore: cast_nullable_to_non_nullable
                      as bool,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as String,
            viewCount: freezed == viewCount
                ? _value.viewCount
                : viewCount // ignore: cast_nullable_to_non_nullable
                      as int?,
            shareCount: freezed == shareCount
                ? _value.shareCount
                : shareCount // ignore: cast_nullable_to_non_nullable
                      as int?,
            repostCount: freezed == repostCount
                ? _value.repostCount
                : repostCount // ignore: cast_nullable_to_non_nullable
                      as int?,
            saveCount: freezed == saveCount
                ? _value.saveCount
                : saveCount // ignore: cast_nullable_to_non_nullable
                      as int?,
            trending: null == trending
                ? _value.trending
                : trending // ignore: cast_nullable_to_non_nullable
                      as bool,
            trendingScore: freezed == trendingScore
                ? _value.trendingScore
                : trendingScore // ignore: cast_nullable_to_non_nullable
                      as num?,
            lastEditedAt: freezed == lastEditedAt
                ? _value.lastEditedAt
                : lastEditedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            editedBy: freezed == editedBy
                ? _value.editedBy
                : editedBy // ignore: cast_nullable_to_non_nullable
                      as String?,
            isRemix: null == isRemix
                ? _value.isRemix
                : isRemix // ignore: cast_nullable_to_non_nullable
                      as bool,
            allowRemix: null == allowRemix
                ? _value.allowRemix
                : allowRemix // ignore: cast_nullable_to_non_nullable
                      as bool,
            allowRepost: null == allowRepost
                ? _value.allowRepost
                : allowRepost // ignore: cast_nullable_to_non_nullable
                      as bool,
            allowShare: null == allowShare
                ? _value.allowShare
                : allowShare // ignore: cast_nullable_to_non_nullable
                      as bool,
            originalPostId: freezed == originalPostId
                ? _value.originalPostId
                : originalPostId // ignore: cast_nullable_to_non_nullable
                      as String?,
            originalUserId: freezed == originalUserId
                ? _value.originalUserId
                : originalUserId // ignore: cast_nullable_to_non_nullable
                      as String?,
            originalUsername: freezed == originalUsername
                ? _value.originalUsername
                : originalUsername // ignore: cast_nullable_to_non_nullable
                      as String?,
            commentsDisabled: null == commentsDisabled
                ? _value.commentsDisabled
                : commentsDisabled // ignore: cast_nullable_to_non_nullable
                      as bool,
            likesHidden: null == likesHidden
                ? _value.likesHidden
                : likesHidden // ignore: cast_nullable_to_non_nullable
                      as bool,
            hasAIContent: null == hasAIContent
                ? _value.hasAIContent
                : hasAIContent // ignore: cast_nullable_to_non_nullable
                      as bool,
            musicTrack: freezed == musicTrack
                ? _value.musicTrack
                : musicTrack // ignore: cast_nullable_to_non_nullable
                      as String?,
            musicUrl: freezed == musicUrl
                ? _value.musicUrl
                : musicUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            flaggedReasons: freezed == flaggedReasons
                ? _value.flaggedReasons
                : flaggedReasons // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            flaggedAt: freezed == flaggedAt
                ? _value.flaggedAt
                : flaggedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            flaggedBy: freezed == flaggedBy
                ? _value.flaggedBy
                : flaggedBy // ignore: cast_nullable_to_non_nullable
                      as String?,
            reportCount: freezed == reportCount
                ? _value.reportCount
                : reportCount // ignore: cast_nullable_to_non_nullable
                      as int?,
            reviewedAt: freezed == reviewedAt
                ? _value.reviewedAt
                : reviewedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            reviewedBy: freezed == reviewedBy
                ? _value.reviewedBy
                : reviewedBy // ignore: cast_nullable_to_non_nullable
                      as String?,
            moderationAction: freezed == moderationAction
                ? _value.moderationAction
                : moderationAction // ignore: cast_nullable_to_non_nullable
                      as String?,
            closeFriendsGroupIds: freezed == closeFriendsGroupIds
                ? _value.closeFriendsGroupIds
                : closeFriendsGroupIds // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            metadata: freezed == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
            contentWarnings: freezed == contentWarnings
                ? _value.contentWarnings
                : contentWarnings // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            ageRestriction: freezed == ageRestriction
                ? _value.ageRestriction
                : ageRestriction // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PostImplCopyWith<$Res> implements $PostCopyWith<$Res> {
  factory _$$PostImplCopyWith(
    _$PostImpl value,
    $Res Function(_$PostImpl) then,
  ) = __$$PostImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    String username,
    String userAvatarUrl,
    String? userRole,
    bool? isVerified,
    bool? isBillionaire,
    bool? isCelebrity,
    String? verificationTier,
    MediaType mediaType,
    String mediaUrl,
    List<String>? mediaUrls,
    List<MediaType>? mediaTypes,
    String caption,
    String? location,
    String? locationId,
    int likeCount,
    int commentCount,
    DateTime timestamp,
    DateTime? localCreatedAt,
    bool isLiked,
    bool isBookmarked,
    bool isReposted,
    List<String>? coAuthorIds,
    List<String>? coAuthorUsernames,
    List<String>? coAuthorAvatars,
    List<String>? mentionedUsers,
    List<String>? hashtags,
    List<MediaTag>? mediaTags,
    List<String>? taggedUserIds,
    List<String>? likedBy,
    List<String>? bookmarkedBy,
    List<String>? repostedBy,
    bool isPublic,
    String visibility,
    bool isArchived,
    bool isReported,
    bool isPinned,
    bool isDeleted,
    bool isFlagged,
    String status,
    int? viewCount,
    int? shareCount,
    int? repostCount,
    int? saveCount,
    bool trending,
    num? trendingScore,
    DateTime? lastEditedAt,
    String? editedBy,
    bool isRemix,
    bool allowRemix,
    bool allowRepost,
    bool allowShare,
    String? originalPostId,
    String? originalUserId,
    String? originalUsername,
    bool commentsDisabled,
    bool likesHidden,
    bool hasAIContent,
    String? musicTrack,
    String? musicUrl,
    List<String>? flaggedReasons,
    DateTime? flaggedAt,
    String? flaggedBy,
    int? reportCount,
    DateTime? reviewedAt,
    String? reviewedBy,
    String? moderationAction,
    List<String>? closeFriendsGroupIds,
    Map<String, dynamic>? metadata,
    List<String>? contentWarnings,
    String? ageRestriction,
  });
}

/// @nodoc
class __$$PostImplCopyWithImpl<$Res>
    extends _$PostCopyWithImpl<$Res, _$PostImpl>
    implements _$$PostImplCopyWith<$Res> {
  __$$PostImplCopyWithImpl(_$PostImpl _value, $Res Function(_$PostImpl) _then)
    : super(_value, _then);

  /// Create a copy of Post
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? username = null,
    Object? userAvatarUrl = null,
    Object? userRole = freezed,
    Object? isVerified = freezed,
    Object? isBillionaire = freezed,
    Object? isCelebrity = freezed,
    Object? verificationTier = freezed,
    Object? mediaType = null,
    Object? mediaUrl = null,
    Object? mediaUrls = freezed,
    Object? mediaTypes = freezed,
    Object? caption = null,
    Object? location = freezed,
    Object? locationId = freezed,
    Object? likeCount = null,
    Object? commentCount = null,
    Object? timestamp = null,
    Object? localCreatedAt = freezed,
    Object? isLiked = null,
    Object? isBookmarked = null,
    Object? isReposted = null,
    Object? coAuthorIds = freezed,
    Object? coAuthorUsernames = freezed,
    Object? coAuthorAvatars = freezed,
    Object? mentionedUsers = freezed,
    Object? hashtags = freezed,
    Object? mediaTags = freezed,
    Object? taggedUserIds = freezed,
    Object? likedBy = freezed,
    Object? bookmarkedBy = freezed,
    Object? repostedBy = freezed,
    Object? isPublic = null,
    Object? visibility = null,
    Object? isArchived = null,
    Object? isReported = null,
    Object? isPinned = null,
    Object? isDeleted = null,
    Object? isFlagged = null,
    Object? status = null,
    Object? viewCount = freezed,
    Object? shareCount = freezed,
    Object? repostCount = freezed,
    Object? saveCount = freezed,
    Object? trending = null,
    Object? trendingScore = freezed,
    Object? lastEditedAt = freezed,
    Object? editedBy = freezed,
    Object? isRemix = null,
    Object? allowRemix = null,
    Object? allowRepost = null,
    Object? allowShare = null,
    Object? originalPostId = freezed,
    Object? originalUserId = freezed,
    Object? originalUsername = freezed,
    Object? commentsDisabled = null,
    Object? likesHidden = null,
    Object? hasAIContent = null,
    Object? musicTrack = freezed,
    Object? musicUrl = freezed,
    Object? flaggedReasons = freezed,
    Object? flaggedAt = freezed,
    Object? flaggedBy = freezed,
    Object? reportCount = freezed,
    Object? reviewedAt = freezed,
    Object? reviewedBy = freezed,
    Object? moderationAction = freezed,
    Object? closeFriendsGroupIds = freezed,
    Object? metadata = freezed,
    Object? contentWarnings = freezed,
    Object? ageRestriction = freezed,
  }) {
    return _then(
      _$PostImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        username: null == username
            ? _value.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        userAvatarUrl: null == userAvatarUrl
            ? _value.userAvatarUrl
            : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        userRole: freezed == userRole
            ? _value.userRole
            : userRole // ignore: cast_nullable_to_non_nullable
                  as String?,
        isVerified: freezed == isVerified
            ? _value.isVerified
            : isVerified // ignore: cast_nullable_to_non_nullable
                  as bool?,
        isBillionaire: freezed == isBillionaire
            ? _value.isBillionaire
            : isBillionaire // ignore: cast_nullable_to_non_nullable
                  as bool?,
        isCelebrity: freezed == isCelebrity
            ? _value.isCelebrity
            : isCelebrity // ignore: cast_nullable_to_non_nullable
                  as bool?,
        verificationTier: freezed == verificationTier
            ? _value.verificationTier
            : verificationTier // ignore: cast_nullable_to_non_nullable
                  as String?,
        mediaType: null == mediaType
            ? _value.mediaType
            : mediaType // ignore: cast_nullable_to_non_nullable
                  as MediaType,
        mediaUrl: null == mediaUrl
            ? _value.mediaUrl
            : mediaUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        mediaUrls: freezed == mediaUrls
            ? _value._mediaUrls
            : mediaUrls // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        mediaTypes: freezed == mediaTypes
            ? _value._mediaTypes
            : mediaTypes // ignore: cast_nullable_to_non_nullable
                  as List<MediaType>?,
        caption: null == caption
            ? _value.caption
            : caption // ignore: cast_nullable_to_non_nullable
                  as String,
        location: freezed == location
            ? _value.location
            : location // ignore: cast_nullable_to_non_nullable
                  as String?,
        locationId: freezed == locationId
            ? _value.locationId
            : locationId // ignore: cast_nullable_to_non_nullable
                  as String?,
        likeCount: null == likeCount
            ? _value.likeCount
            : likeCount // ignore: cast_nullable_to_non_nullable
                  as int,
        commentCount: null == commentCount
            ? _value.commentCount
            : commentCount // ignore: cast_nullable_to_non_nullable
                  as int,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        localCreatedAt: freezed == localCreatedAt
            ? _value.localCreatedAt
            : localCreatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        isLiked: null == isLiked
            ? _value.isLiked
            : isLiked // ignore: cast_nullable_to_non_nullable
                  as bool,
        isBookmarked: null == isBookmarked
            ? _value.isBookmarked
            : isBookmarked // ignore: cast_nullable_to_non_nullable
                  as bool,
        isReposted: null == isReposted
            ? _value.isReposted
            : isReposted // ignore: cast_nullable_to_non_nullable
                  as bool,
        coAuthorIds: freezed == coAuthorIds
            ? _value._coAuthorIds
            : coAuthorIds // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        coAuthorUsernames: freezed == coAuthorUsernames
            ? _value._coAuthorUsernames
            : coAuthorUsernames // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        coAuthorAvatars: freezed == coAuthorAvatars
            ? _value._coAuthorAvatars
            : coAuthorAvatars // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        mentionedUsers: freezed == mentionedUsers
            ? _value._mentionedUsers
            : mentionedUsers // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        hashtags: freezed == hashtags
            ? _value._hashtags
            : hashtags // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        mediaTags: freezed == mediaTags
            ? _value._mediaTags
            : mediaTags // ignore: cast_nullable_to_non_nullable
                  as List<MediaTag>?,
        taggedUserIds: freezed == taggedUserIds
            ? _value._taggedUserIds
            : taggedUserIds // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        likedBy: freezed == likedBy
            ? _value._likedBy
            : likedBy // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        bookmarkedBy: freezed == bookmarkedBy
            ? _value._bookmarkedBy
            : bookmarkedBy // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        repostedBy: freezed == repostedBy
            ? _value._repostedBy
            : repostedBy // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        isPublic: null == isPublic
            ? _value.isPublic
            : isPublic // ignore: cast_nullable_to_non_nullable
                  as bool,
        visibility: null == visibility
            ? _value.visibility
            : visibility // ignore: cast_nullable_to_non_nullable
                  as String,
        isArchived: null == isArchived
            ? _value.isArchived
            : isArchived // ignore: cast_nullable_to_non_nullable
                  as bool,
        isReported: null == isReported
            ? _value.isReported
            : isReported // ignore: cast_nullable_to_non_nullable
                  as bool,
        isPinned: null == isPinned
            ? _value.isPinned
            : isPinned // ignore: cast_nullable_to_non_nullable
                  as bool,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
        isFlagged: null == isFlagged
            ? _value.isFlagged
            : isFlagged // ignore: cast_nullable_to_non_nullable
                  as bool,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as String,
        viewCount: freezed == viewCount
            ? _value.viewCount
            : viewCount // ignore: cast_nullable_to_non_nullable
                  as int?,
        shareCount: freezed == shareCount
            ? _value.shareCount
            : shareCount // ignore: cast_nullable_to_non_nullable
                  as int?,
        repostCount: freezed == repostCount
            ? _value.repostCount
            : repostCount // ignore: cast_nullable_to_non_nullable
                  as int?,
        saveCount: freezed == saveCount
            ? _value.saveCount
            : saveCount // ignore: cast_nullable_to_non_nullable
                  as int?,
        trending: null == trending
            ? _value.trending
            : trending // ignore: cast_nullable_to_non_nullable
                  as bool,
        trendingScore: freezed == trendingScore
            ? _value.trendingScore
            : trendingScore // ignore: cast_nullable_to_non_nullable
                  as num?,
        lastEditedAt: freezed == lastEditedAt
            ? _value.lastEditedAt
            : lastEditedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        editedBy: freezed == editedBy
            ? _value.editedBy
            : editedBy // ignore: cast_nullable_to_non_nullable
                  as String?,
        isRemix: null == isRemix
            ? _value.isRemix
            : isRemix // ignore: cast_nullable_to_non_nullable
                  as bool,
        allowRemix: null == allowRemix
            ? _value.allowRemix
            : allowRemix // ignore: cast_nullable_to_non_nullable
                  as bool,
        allowRepost: null == allowRepost
            ? _value.allowRepost
            : allowRepost // ignore: cast_nullable_to_non_nullable
                  as bool,
        allowShare: null == allowShare
            ? _value.allowShare
            : allowShare // ignore: cast_nullable_to_non_nullable
                  as bool,
        originalPostId: freezed == originalPostId
            ? _value.originalPostId
            : originalPostId // ignore: cast_nullable_to_non_nullable
                  as String?,
        originalUserId: freezed == originalUserId
            ? _value.originalUserId
            : originalUserId // ignore: cast_nullable_to_non_nullable
                  as String?,
        originalUsername: freezed == originalUsername
            ? _value.originalUsername
            : originalUsername // ignore: cast_nullable_to_non_nullable
                  as String?,
        commentsDisabled: null == commentsDisabled
            ? _value.commentsDisabled
            : commentsDisabled // ignore: cast_nullable_to_non_nullable
                  as bool,
        likesHidden: null == likesHidden
            ? _value.likesHidden
            : likesHidden // ignore: cast_nullable_to_non_nullable
                  as bool,
        hasAIContent: null == hasAIContent
            ? _value.hasAIContent
            : hasAIContent // ignore: cast_nullable_to_non_nullable
                  as bool,
        musicTrack: freezed == musicTrack
            ? _value.musicTrack
            : musicTrack // ignore: cast_nullable_to_non_nullable
                  as String?,
        musicUrl: freezed == musicUrl
            ? _value.musicUrl
            : musicUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        flaggedReasons: freezed == flaggedReasons
            ? _value._flaggedReasons
            : flaggedReasons // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        flaggedAt: freezed == flaggedAt
            ? _value.flaggedAt
            : flaggedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        flaggedBy: freezed == flaggedBy
            ? _value.flaggedBy
            : flaggedBy // ignore: cast_nullable_to_non_nullable
                  as String?,
        reportCount: freezed == reportCount
            ? _value.reportCount
            : reportCount // ignore: cast_nullable_to_non_nullable
                  as int?,
        reviewedAt: freezed == reviewedAt
            ? _value.reviewedAt
            : reviewedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        reviewedBy: freezed == reviewedBy
            ? _value.reviewedBy
            : reviewedBy // ignore: cast_nullable_to_non_nullable
                  as String?,
        moderationAction: freezed == moderationAction
            ? _value.moderationAction
            : moderationAction // ignore: cast_nullable_to_non_nullable
                  as String?,
        closeFriendsGroupIds: freezed == closeFriendsGroupIds
            ? _value._closeFriendsGroupIds
            : closeFriendsGroupIds // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        metadata: freezed == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
        contentWarnings: freezed == contentWarnings
            ? _value._contentWarnings
            : contentWarnings // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        ageRestriction: freezed == ageRestriction
            ? _value.ageRestriction
            : ageRestriction // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PostImpl implements _Post {
  const _$PostImpl({
    required this.id,
    required this.userId,
    required this.username,
    required this.userAvatarUrl,
    this.userRole,
    this.isVerified,
    this.isBillionaire,
    this.isCelebrity,
    this.verificationTier,
    required this.mediaType,
    required this.mediaUrl,
    final List<String>? mediaUrls,
    final List<MediaType>? mediaTypes,
    required this.caption,
    this.location,
    this.locationId,
    required this.likeCount,
    required this.commentCount,
    required this.timestamp,
    this.localCreatedAt,
    this.isLiked = false,
    this.isBookmarked = false,
    this.isReposted = false,
    final List<String>? coAuthorIds,
    final List<String>? coAuthorUsernames,
    final List<String>? coAuthorAvatars,
    final List<String>? mentionedUsers,
    final List<String>? hashtags,
    final List<MediaTag>? mediaTags,
    final List<String>? taggedUserIds,
    final List<String>? likedBy,
    final List<String>? bookmarkedBy,
    final List<String>? repostedBy,
    this.isPublic = true,
    this.visibility = 'public',
    this.isArchived = false,
    this.isReported = false,
    this.isPinned = false,
    this.isDeleted = false,
    this.isFlagged = false,
    this.status = 'active',
    this.viewCount,
    this.shareCount,
    this.repostCount,
    this.saveCount,
    this.trending = false,
    this.trendingScore,
    this.lastEditedAt,
    this.editedBy,
    this.isRemix = false,
    this.allowRemix = false,
    this.allowRepost = true,
    this.allowShare = true,
    this.originalPostId,
    this.originalUserId,
    this.originalUsername,
    this.commentsDisabled = false,
    this.likesHidden = false,
    this.hasAIContent = false,
    this.musicTrack,
    this.musicUrl,
    final List<String>? flaggedReasons,
    this.flaggedAt,
    this.flaggedBy,
    this.reportCount,
    this.reviewedAt,
    this.reviewedBy,
    this.moderationAction,
    final List<String>? closeFriendsGroupIds,
    final Map<String, dynamic>? metadata,
    final List<String>? contentWarnings,
    this.ageRestriction,
  }) : _mediaUrls = mediaUrls,
       _mediaTypes = mediaTypes,
       _coAuthorIds = coAuthorIds,
       _coAuthorUsernames = coAuthorUsernames,
       _coAuthorAvatars = coAuthorAvatars,
       _mentionedUsers = mentionedUsers,
       _hashtags = hashtags,
       _mediaTags = mediaTags,
       _taggedUserIds = taggedUserIds,
       _likedBy = likedBy,
       _bookmarkedBy = bookmarkedBy,
       _repostedBy = repostedBy,
       _flaggedReasons = flaggedReasons,
       _closeFriendsGroupIds = closeFriendsGroupIds,
       _metadata = metadata,
       _contentWarnings = contentWarnings;

  factory _$PostImpl.fromJson(Map<String, dynamic> json) =>
      _$$PostImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String username;
  @override
  final String userAvatarUrl;
  @override
  final String? userRole;
  // CEO, Plastic Surgeon, etc.
  @override
  final bool? isVerified;
  // General verification
  @override
  final bool? isBillionaire;
  // Billionaire verification
  @override
  final bool? isCelebrity;
  // Celebrity verification
  @override
  final String? verificationTier;
  // Verification tier as string
  @override
  final MediaType mediaType;
  @override
  final String mediaUrl;
  // Multiple media support
  final List<String>? _mediaUrls;
  // Multiple media support
  @override
  List<String>? get mediaUrls {
    final value = _mediaUrls;
    if (value == null) return null;
    if (_mediaUrls is EqualUnmodifiableListView) return _mediaUrls;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  // For multiple images/videos
  final List<MediaType>? _mediaTypes;
  // For multiple images/videos
  @override
  List<MediaType>? get mediaTypes {
    final value = _mediaTypes;
    if (value == null) return null;
    if (_mediaTypes is EqualUnmodifiableListView) return _mediaTypes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  // Corresponding media types
  @override
  final String caption;
  @override
  final String? location;
  @override
  final String? locationId;
  // For location navigation
  @override
  final int likeCount;
  @override
  final int commentCount;
  @override
  final DateTime timestamp;
  @override
  final DateTime? localCreatedAt;
  @override
  @JsonKey()
  final bool isLiked;
  @override
  @JsonKey()
  final bool isBookmarked;
  @override
  @JsonKey()
  final bool isReposted;
  // Co-author system (Remax feature)
  final List<String>? _coAuthorIds;
  // Co-author system (Remax feature)
  @override
  List<String>? get coAuthorIds {
    final value = _coAuthorIds;
    if (value == null) return null;
    if (_coAuthorIds is EqualUnmodifiableListView) return _coAuthorIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _coAuthorUsernames;
  @override
  List<String>? get coAuthorUsernames {
    final value = _coAuthorUsernames;
    if (value == null) return null;
    if (_coAuthorUsernames is EqualUnmodifiableListView)
      return _coAuthorUsernames;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _coAuthorAvatars;
  @override
  List<String>? get coAuthorAvatars {
    final value = _coAuthorAvatars;
    if (value == null) return null;
    if (_coAuthorAvatars is EqualUnmodifiableListView) return _coAuthorAvatars;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  // Tagging system
  final List<String>? _mentionedUsers;
  // Tagging system
  @override
  List<String>? get mentionedUsers {
    final value = _mentionedUsers;
    if (value == null) return null;
    if (_mentionedUsers is EqualUnmodifiableListView) return _mentionedUsers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  // @mentions in caption
  final List<String>? _hashtags;
  // @mentions in caption
  @override
  List<String>? get hashtags {
    final value = _hashtags;
    if (value == null) return null;
    if (_hashtags is EqualUnmodifiableListView) return _hashtags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  // #hashtags in caption
  final List<MediaTag>? _mediaTags;
  // #hashtags in caption
  @override
  List<MediaTag>? get mediaTags {
    final value = _mediaTags;
    if (value == null) return null;
    if (_mediaTags is EqualUnmodifiableListView) return _mediaTags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  // Tags on photos/videos
  final List<String>? _taggedUserIds;
  // Tags on photos/videos
  @override
  List<String>? get taggedUserIds {
    final value = _taggedUserIds;
    if (value == null) return null;
    if (_taggedUserIds is EqualUnmodifiableListView) return _taggedUserIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  // Users tagged in media
  // Interaction tracking
  final List<String>? _likedBy;
  // Users tagged in media
  // Interaction tracking
  @override
  List<String>? get likedBy {
    final value = _likedBy;
    if (value == null) return null;
    if (_likedBy is EqualUnmodifiableListView) return _likedBy;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _bookmarkedBy;
  @override
  List<String>? get bookmarkedBy {
    final value = _bookmarkedBy;
    if (value == null) return null;
    if (_bookmarkedBy is EqualUnmodifiableListView) return _bookmarkedBy;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _repostedBy;
  @override
  List<String>? get repostedBy {
    final value = _repostedBy;
    if (value == null) return null;
    if (_repostedBy is EqualUnmodifiableListView) return _repostedBy;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  // Enhanced permissions and visibility
  @override
  @JsonKey()
  final bool isPublic;
  @override
  @JsonKey()
  final String visibility;
  // 'public', 'followers', 'closeFriends', 'private'
  @override
  @JsonKey()
  final bool isArchived;
  @override
  @JsonKey()
  final bool isReported;
  @override
  @JsonKey()
  final bool isPinned;
  // For pinned posts on profile
  @override
  @JsonKey()
  final bool isDeleted;
  @override
  @JsonKey()
  final bool isFlagged;
  @override
  @JsonKey()
  final String status;
  // 'active', 'archived', 'deleted', 'flagged', 'review'
  // Analytics and engagement
  @override
  final int? viewCount;
  @override
  final int? shareCount;
  @override
  final int? repostCount;
  @override
  final int? saveCount;
  @override
  @JsonKey()
  final bool trending;
  // Trending badge
  @override
  final num? trendingScore;
  // Trending score for ranking
  @override
  final DateTime? lastEditedAt;
  @override
  final String? editedBy;
  // Remix and Repost features
  @override
  @JsonKey()
  final bool isRemix;
  @override
  @JsonKey()
  final bool allowRemix;
  @override
  @JsonKey()
  final bool allowRepost;
  @override
  @JsonKey()
  final bool allowShare;
  @override
  final String? originalPostId;
  // For remixes and reposts
  @override
  final String? originalUserId;
  @override
  final String? originalUsername;
  // Enhanced post features
  @override
  @JsonKey()
  final bool commentsDisabled;
  @override
  @JsonKey()
  final bool likesHidden;
  @override
  @JsonKey()
  final bool hasAIContent;
  @override
  final String? musicTrack;
  @override
  final String? musicUrl;
  // Admin features
  final List<String>? _flaggedReasons;
  // Admin features
  @override
  List<String>? get flaggedReasons {
    final value = _flaggedReasons;
    if (value == null) return null;
    if (_flaggedReasons is EqualUnmodifiableListView) return _flaggedReasons;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final DateTime? flaggedAt;
  @override
  final String? flaggedBy;
  @override
  final int? reportCount;
  @override
  final DateTime? reviewedAt;
  @override
  final String? reviewedBy;
  @override
  final String? moderationAction;
  // Close friends and groups
  final List<String>? _closeFriendsGroupIds;
  // Close friends and groups
  @override
  List<String>? get closeFriendsGroupIds {
    final value = _closeFriendsGroupIds;
    if (value == null) return null;
    if (_closeFriendsGroupIds is EqualUnmodifiableListView)
      return _closeFriendsGroupIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  // Content metadata
  final Map<String, dynamic>? _metadata;
  // Content metadata
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  final List<String>? _contentWarnings;
  @override
  List<String>? get contentWarnings {
    final value = _contentWarnings;
    if (value == null) return null;
    if (_contentWarnings is EqualUnmodifiableListView) return _contentWarnings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? ageRestriction;

  @override
  String toString() {
    return 'Post(id: $id, userId: $userId, username: $username, userAvatarUrl: $userAvatarUrl, userRole: $userRole, isVerified: $isVerified, isBillionaire: $isBillionaire, isCelebrity: $isCelebrity, verificationTier: $verificationTier, mediaType: $mediaType, mediaUrl: $mediaUrl, mediaUrls: $mediaUrls, mediaTypes: $mediaTypes, caption: $caption, location: $location, locationId: $locationId, likeCount: $likeCount, commentCount: $commentCount, timestamp: $timestamp, localCreatedAt: $localCreatedAt, isLiked: $isLiked, isBookmarked: $isBookmarked, isReposted: $isReposted, coAuthorIds: $coAuthorIds, coAuthorUsernames: $coAuthorUsernames, coAuthorAvatars: $coAuthorAvatars, mentionedUsers: $mentionedUsers, hashtags: $hashtags, mediaTags: $mediaTags, taggedUserIds: $taggedUserIds, likedBy: $likedBy, bookmarkedBy: $bookmarkedBy, repostedBy: $repostedBy, isPublic: $isPublic, visibility: $visibility, isArchived: $isArchived, isReported: $isReported, isPinned: $isPinned, isDeleted: $isDeleted, isFlagged: $isFlagged, status: $status, viewCount: $viewCount, shareCount: $shareCount, repostCount: $repostCount, saveCount: $saveCount, trending: $trending, trendingScore: $trendingScore, lastEditedAt: $lastEditedAt, editedBy: $editedBy, isRemix: $isRemix, allowRemix: $allowRemix, allowRepost: $allowRepost, allowShare: $allowShare, originalPostId: $originalPostId, originalUserId: $originalUserId, originalUsername: $originalUsername, commentsDisabled: $commentsDisabled, likesHidden: $likesHidden, hasAIContent: $hasAIContent, musicTrack: $musicTrack, musicUrl: $musicUrl, flaggedReasons: $flaggedReasons, flaggedAt: $flaggedAt, flaggedBy: $flaggedBy, reportCount: $reportCount, reviewedAt: $reviewedAt, reviewedBy: $reviewedBy, moderationAction: $moderationAction, closeFriendsGroupIds: $closeFriendsGroupIds, metadata: $metadata, contentWarnings: $contentWarnings, ageRestriction: $ageRestriction)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PostImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userAvatarUrl, userAvatarUrl) ||
                other.userAvatarUrl == userAvatarUrl) &&
            (identical(other.userRole, userRole) ||
                other.userRole == userRole) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.isBillionaire, isBillionaire) ||
                other.isBillionaire == isBillionaire) &&
            (identical(other.isCelebrity, isCelebrity) ||
                other.isCelebrity == isCelebrity) &&
            (identical(other.verificationTier, verificationTier) ||
                other.verificationTier == verificationTier) &&
            (identical(other.mediaType, mediaType) ||
                other.mediaType == mediaType) &&
            (identical(other.mediaUrl, mediaUrl) ||
                other.mediaUrl == mediaUrl) &&
            const DeepCollectionEquality().equals(
              other._mediaUrls,
              _mediaUrls,
            ) &&
            const DeepCollectionEquality().equals(
              other._mediaTypes,
              _mediaTypes,
            ) &&
            (identical(other.caption, caption) || other.caption == caption) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.locationId, locationId) ||
                other.locationId == locationId) &&
            (identical(other.likeCount, likeCount) ||
                other.likeCount == likeCount) &&
            (identical(other.commentCount, commentCount) ||
                other.commentCount == commentCount) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.localCreatedAt, localCreatedAt) ||
                other.localCreatedAt == localCreatedAt) &&
            (identical(other.isLiked, isLiked) || other.isLiked == isLiked) &&
            (identical(other.isBookmarked, isBookmarked) ||
                other.isBookmarked == isBookmarked) &&
            (identical(other.isReposted, isReposted) ||
                other.isReposted == isReposted) &&
            const DeepCollectionEquality().equals(
              other._coAuthorIds,
              _coAuthorIds,
            ) &&
            const DeepCollectionEquality().equals(
              other._coAuthorUsernames,
              _coAuthorUsernames,
            ) &&
            const DeepCollectionEquality().equals(
              other._coAuthorAvatars,
              _coAuthorAvatars,
            ) &&
            const DeepCollectionEquality().equals(
              other._mentionedUsers,
              _mentionedUsers,
            ) &&
            const DeepCollectionEquality().equals(other._hashtags, _hashtags) &&
            const DeepCollectionEquality().equals(
              other._mediaTags,
              _mediaTags,
            ) &&
            const DeepCollectionEquality().equals(
              other._taggedUserIds,
              _taggedUserIds,
            ) &&
            const DeepCollectionEquality().equals(other._likedBy, _likedBy) &&
            const DeepCollectionEquality().equals(
              other._bookmarkedBy,
              _bookmarkedBy,
            ) &&
            const DeepCollectionEquality().equals(
              other._repostedBy,
              _repostedBy,
            ) &&
            (identical(other.isPublic, isPublic) ||
                other.isPublic == isPublic) &&
            (identical(other.visibility, visibility) ||
                other.visibility == visibility) &&
            (identical(other.isArchived, isArchived) ||
                other.isArchived == isArchived) &&
            (identical(other.isReported, isReported) ||
                other.isReported == isReported) &&
            (identical(other.isPinned, isPinned) ||
                other.isPinned == isPinned) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted) &&
            (identical(other.isFlagged, isFlagged) ||
                other.isFlagged == isFlagged) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.viewCount, viewCount) ||
                other.viewCount == viewCount) &&
            (identical(other.shareCount, shareCount) ||
                other.shareCount == shareCount) &&
            (identical(other.repostCount, repostCount) ||
                other.repostCount == repostCount) &&
            (identical(other.saveCount, saveCount) ||
                other.saveCount == saveCount) &&
            (identical(other.trending, trending) ||
                other.trending == trending) &&
            (identical(other.trendingScore, trendingScore) ||
                other.trendingScore == trendingScore) &&
            (identical(other.lastEditedAt, lastEditedAt) ||
                other.lastEditedAt == lastEditedAt) &&
            (identical(other.editedBy, editedBy) ||
                other.editedBy == editedBy) &&
            (identical(other.isRemix, isRemix) || other.isRemix == isRemix) &&
            (identical(other.allowRemix, allowRemix) ||
                other.allowRemix == allowRemix) &&
            (identical(other.allowRepost, allowRepost) ||
                other.allowRepost == allowRepost) &&
            (identical(other.allowShare, allowShare) ||
                other.allowShare == allowShare) &&
            (identical(other.originalPostId, originalPostId) ||
                other.originalPostId == originalPostId) &&
            (identical(other.originalUserId, originalUserId) ||
                other.originalUserId == originalUserId) &&
            (identical(other.originalUsername, originalUsername) ||
                other.originalUsername == originalUsername) &&
            (identical(other.commentsDisabled, commentsDisabled) ||
                other.commentsDisabled == commentsDisabled) &&
            (identical(other.likesHidden, likesHidden) ||
                other.likesHidden == likesHidden) &&
            (identical(other.hasAIContent, hasAIContent) ||
                other.hasAIContent == hasAIContent) &&
            (identical(other.musicTrack, musicTrack) ||
                other.musicTrack == musicTrack) &&
            (identical(other.musicUrl, musicUrl) ||
                other.musicUrl == musicUrl) &&
            const DeepCollectionEquality().equals(
              other._flaggedReasons,
              _flaggedReasons,
            ) &&
            (identical(other.flaggedAt, flaggedAt) ||
                other.flaggedAt == flaggedAt) &&
            (identical(other.flaggedBy, flaggedBy) ||
                other.flaggedBy == flaggedBy) &&
            (identical(other.reportCount, reportCount) ||
                other.reportCount == reportCount) &&
            (identical(other.reviewedAt, reviewedAt) ||
                other.reviewedAt == reviewedAt) &&
            (identical(other.reviewedBy, reviewedBy) ||
                other.reviewedBy == reviewedBy) &&
            (identical(other.moderationAction, moderationAction) ||
                other.moderationAction == moderationAction) &&
            const DeepCollectionEquality().equals(
              other._closeFriendsGroupIds,
              _closeFriendsGroupIds,
            ) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            const DeepCollectionEquality().equals(
              other._contentWarnings,
              _contentWarnings,
            ) &&
            (identical(other.ageRestriction, ageRestriction) ||
                other.ageRestriction == ageRestriction));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    userId,
    username,
    userAvatarUrl,
    userRole,
    isVerified,
    isBillionaire,
    isCelebrity,
    verificationTier,
    mediaType,
    mediaUrl,
    const DeepCollectionEquality().hash(_mediaUrls),
    const DeepCollectionEquality().hash(_mediaTypes),
    caption,
    location,
    locationId,
    likeCount,
    commentCount,
    timestamp,
    localCreatedAt,
    isLiked,
    isBookmarked,
    isReposted,
    const DeepCollectionEquality().hash(_coAuthorIds),
    const DeepCollectionEquality().hash(_coAuthorUsernames),
    const DeepCollectionEquality().hash(_coAuthorAvatars),
    const DeepCollectionEquality().hash(_mentionedUsers),
    const DeepCollectionEquality().hash(_hashtags),
    const DeepCollectionEquality().hash(_mediaTags),
    const DeepCollectionEquality().hash(_taggedUserIds),
    const DeepCollectionEquality().hash(_likedBy),
    const DeepCollectionEquality().hash(_bookmarkedBy),
    const DeepCollectionEquality().hash(_repostedBy),
    isPublic,
    visibility,
    isArchived,
    isReported,
    isPinned,
    isDeleted,
    isFlagged,
    status,
    viewCount,
    shareCount,
    repostCount,
    saveCount,
    trending,
    trendingScore,
    lastEditedAt,
    editedBy,
    isRemix,
    allowRemix,
    allowRepost,
    allowShare,
    originalPostId,
    originalUserId,
    originalUsername,
    commentsDisabled,
    likesHidden,
    hasAIContent,
    musicTrack,
    musicUrl,
    const DeepCollectionEquality().hash(_flaggedReasons),
    flaggedAt,
    flaggedBy,
    reportCount,
    reviewedAt,
    reviewedBy,
    moderationAction,
    const DeepCollectionEquality().hash(_closeFriendsGroupIds),
    const DeepCollectionEquality().hash(_metadata),
    const DeepCollectionEquality().hash(_contentWarnings),
    ageRestriction,
  ]);

  /// Create a copy of Post
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PostImplCopyWith<_$PostImpl> get copyWith =>
      __$$PostImplCopyWithImpl<_$PostImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PostImplToJson(this);
  }
}

abstract class _Post implements Post {
  const factory _Post({
    required final String id,
    required final String userId,
    required final String username,
    required final String userAvatarUrl,
    final String? userRole,
    final bool? isVerified,
    final bool? isBillionaire,
    final bool? isCelebrity,
    final String? verificationTier,
    required final MediaType mediaType,
    required final String mediaUrl,
    final List<String>? mediaUrls,
    final List<MediaType>? mediaTypes,
    required final String caption,
    final String? location,
    final String? locationId,
    required final int likeCount,
    required final int commentCount,
    required final DateTime timestamp,
    final DateTime? localCreatedAt,
    final bool isLiked,
    final bool isBookmarked,
    final bool isReposted,
    final List<String>? coAuthorIds,
    final List<String>? coAuthorUsernames,
    final List<String>? coAuthorAvatars,
    final List<String>? mentionedUsers,
    final List<String>? hashtags,
    final List<MediaTag>? mediaTags,
    final List<String>? taggedUserIds,
    final List<String>? likedBy,
    final List<String>? bookmarkedBy,
    final List<String>? repostedBy,
    final bool isPublic,
    final String visibility,
    final bool isArchived,
    final bool isReported,
    final bool isPinned,
    final bool isDeleted,
    final bool isFlagged,
    final String status,
    final int? viewCount,
    final int? shareCount,
    final int? repostCount,
    final int? saveCount,
    final bool trending,
    final num? trendingScore,
    final DateTime? lastEditedAt,
    final String? editedBy,
    final bool isRemix,
    final bool allowRemix,
    final bool allowRepost,
    final bool allowShare,
    final String? originalPostId,
    final String? originalUserId,
    final String? originalUsername,
    final bool commentsDisabled,
    final bool likesHidden,
    final bool hasAIContent,
    final String? musicTrack,
    final String? musicUrl,
    final List<String>? flaggedReasons,
    final DateTime? flaggedAt,
    final String? flaggedBy,
    final int? reportCount,
    final DateTime? reviewedAt,
    final String? reviewedBy,
    final String? moderationAction,
    final List<String>? closeFriendsGroupIds,
    final Map<String, dynamic>? metadata,
    final List<String>? contentWarnings,
    final String? ageRestriction,
  }) = _$PostImpl;

  factory _Post.fromJson(Map<String, dynamic> json) = _$PostImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get username;
  @override
  String get userAvatarUrl;
  @override
  String? get userRole; // CEO, Plastic Surgeon, etc.
  @override
  bool? get isVerified; // General verification
  @override
  bool? get isBillionaire; // Billionaire verification
  @override
  bool? get isCelebrity; // Celebrity verification
  @override
  String? get verificationTier; // Verification tier as string
  @override
  MediaType get mediaType;
  @override
  String get mediaUrl; // Multiple media support
  @override
  List<String>? get mediaUrls; // For multiple images/videos
  @override
  List<MediaType>? get mediaTypes; // Corresponding media types
  @override
  String get caption;
  @override
  String? get location;
  @override
  String? get locationId; // For location navigation
  @override
  int get likeCount;
  @override
  int get commentCount;
  @override
  DateTime get timestamp;
  @override
  DateTime? get localCreatedAt;
  @override
  bool get isLiked;
  @override
  bool get isBookmarked;
  @override
  bool get isReposted; // Co-author system (Remax feature)
  @override
  List<String>? get coAuthorIds;
  @override
  List<String>? get coAuthorUsernames;
  @override
  List<String>? get coAuthorAvatars; // Tagging system
  @override
  List<String>? get mentionedUsers; // @mentions in caption
  @override
  List<String>? get hashtags; // #hashtags in caption
  @override
  List<MediaTag>? get mediaTags; // Tags on photos/videos
  @override
  List<String>? get taggedUserIds; // Users tagged in media
  // Interaction tracking
  @override
  List<String>? get likedBy;
  @override
  List<String>? get bookmarkedBy;
  @override
  List<String>? get repostedBy; // Enhanced permissions and visibility
  @override
  bool get isPublic;
  @override
  String get visibility; // 'public', 'followers', 'closeFriends', 'private'
  @override
  bool get isArchived;
  @override
  bool get isReported;
  @override
  bool get isPinned; // For pinned posts on profile
  @override
  bool get isDeleted;
  @override
  bool get isFlagged;
  @override
  String get status; // 'active', 'archived', 'deleted', 'flagged', 'review'
  // Analytics and engagement
  @override
  int? get viewCount;
  @override
  int? get shareCount;
  @override
  int? get repostCount;
  @override
  int? get saveCount;
  @override
  bool get trending; // Trending badge
  @override
  num? get trendingScore; // Trending score for ranking
  @override
  DateTime? get lastEditedAt;
  @override
  String? get editedBy; // Remix and Repost features
  @override
  bool get isRemix;
  @override
  bool get allowRemix;
  @override
  bool get allowRepost;
  @override
  bool get allowShare;
  @override
  String? get originalPostId; // For remixes and reposts
  @override
  String? get originalUserId;
  @override
  String? get originalUsername; // Enhanced post features
  @override
  bool get commentsDisabled;
  @override
  bool get likesHidden;
  @override
  bool get hasAIContent;
  @override
  String? get musicTrack;
  @override
  String? get musicUrl; // Admin features
  @override
  List<String>? get flaggedReasons;
  @override
  DateTime? get flaggedAt;
  @override
  String? get flaggedBy;
  @override
  int? get reportCount;
  @override
  DateTime? get reviewedAt;
  @override
  String? get reviewedBy;
  @override
  String? get moderationAction; // Close friends and groups
  @override
  List<String>? get closeFriendsGroupIds; // Content metadata
  @override
  Map<String, dynamic>? get metadata;
  @override
  List<String>? get contentWarnings;
  @override
  String? get ageRestriction;

  /// Create a copy of Post
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PostImplCopyWith<_$PostImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

MediaTag _$MediaTagFromJson(Map<String, dynamic> json) {
  return _MediaTag.fromJson(json);
}

/// @nodoc
mixin _$MediaTag {
  String get userId => throw _privateConstructorUsedError;
  String get username => throw _privateConstructorUsedError;
  double get x =>
      throw _privateConstructorUsedError; // X position on media (0-1)
  double get y => throw _privateConstructorUsedError;

  /// Serializes this MediaTag to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MediaTag
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MediaTagCopyWith<MediaTag> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MediaTagCopyWith<$Res> {
  factory $MediaTagCopyWith(MediaTag value, $Res Function(MediaTag) then) =
      _$MediaTagCopyWithImpl<$Res, MediaTag>;
  @useResult
  $Res call({String userId, String username, double x, double y});
}

/// @nodoc
class _$MediaTagCopyWithImpl<$Res, $Val extends MediaTag>
    implements $MediaTagCopyWith<$Res> {
  _$MediaTagCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MediaTag
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? username = null,
    Object? x = null,
    Object? y = null,
  }) {
    return _then(
      _value.copyWith(
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            username: null == username
                ? _value.username
                : username // ignore: cast_nullable_to_non_nullable
                      as String,
            x: null == x
                ? _value.x
                : x // ignore: cast_nullable_to_non_nullable
                      as double,
            y: null == y
                ? _value.y
                : y // ignore: cast_nullable_to_non_nullable
                      as double,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$MediaTagImplCopyWith<$Res>
    implements $MediaTagCopyWith<$Res> {
  factory _$$MediaTagImplCopyWith(
    _$MediaTagImpl value,
    $Res Function(_$MediaTagImpl) then,
  ) = __$$MediaTagImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String userId, String username, double x, double y});
}

/// @nodoc
class __$$MediaTagImplCopyWithImpl<$Res>
    extends _$MediaTagCopyWithImpl<$Res, _$MediaTagImpl>
    implements _$$MediaTagImplCopyWith<$Res> {
  __$$MediaTagImplCopyWithImpl(
    _$MediaTagImpl _value,
    $Res Function(_$MediaTagImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MediaTag
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? username = null,
    Object? x = null,
    Object? y = null,
  }) {
    return _then(
      _$MediaTagImpl(
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        username: null == username
            ? _value.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        x: null == x
            ? _value.x
            : x // ignore: cast_nullable_to_non_nullable
                  as double,
        y: null == y
            ? _value.y
            : y // ignore: cast_nullable_to_non_nullable
                  as double,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$MediaTagImpl implements _MediaTag {
  const _$MediaTagImpl({
    required this.userId,
    required this.username,
    required this.x,
    required this.y,
  });

  factory _$MediaTagImpl.fromJson(Map<String, dynamic> json) =>
      _$$MediaTagImplFromJson(json);

  @override
  final String userId;
  @override
  final String username;
  @override
  final double x;
  // X position on media (0-1)
  @override
  final double y;

  @override
  String toString() {
    return 'MediaTag(userId: $userId, username: $username, x: $x, y: $y)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MediaTagImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.x, x) || other.x == x) &&
            (identical(other.y, y) || other.y == y));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, userId, username, x, y);

  /// Create a copy of MediaTag
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MediaTagImplCopyWith<_$MediaTagImpl> get copyWith =>
      __$$MediaTagImplCopyWithImpl<_$MediaTagImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MediaTagImplToJson(this);
  }
}

abstract class _MediaTag implements MediaTag {
  const factory _MediaTag({
    required final String userId,
    required final String username,
    required final double x,
    required final double y,
  }) = _$MediaTagImpl;

  factory _MediaTag.fromJson(Map<String, dynamic> json) =
      _$MediaTagImpl.fromJson;

  @override
  String get userId;
  @override
  String get username;
  @override
  double get x; // X position on media (0-1)
  @override
  double get y;

  /// Create a copy of MediaTag
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MediaTagImplCopyWith<_$MediaTagImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PostInteraction _$PostInteractionFromJson(Map<String, dynamic> json) {
  return _PostInteraction.fromJson(json);
}

/// @nodoc
mixin _$PostInteraction {
  String get postId => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get username => throw _privateConstructorUsedError;
  String get userAvatarUrl => throw _privateConstructorUsedError;
  InteractionType get type => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;
  String? get comment => throw _privateConstructorUsedError;

  /// Serializes this PostInteraction to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PostInteraction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PostInteractionCopyWith<PostInteraction> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PostInteractionCopyWith<$Res> {
  factory $PostInteractionCopyWith(
    PostInteraction value,
    $Res Function(PostInteraction) then,
  ) = _$PostInteractionCopyWithImpl<$Res, PostInteraction>;
  @useResult
  $Res call({
    String postId,
    String userId,
    String username,
    String userAvatarUrl,
    InteractionType type,
    DateTime timestamp,
    String? comment,
  });
}

/// @nodoc
class _$PostInteractionCopyWithImpl<$Res, $Val extends PostInteraction>
    implements $PostInteractionCopyWith<$Res> {
  _$PostInteractionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PostInteraction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? postId = null,
    Object? userId = null,
    Object? username = null,
    Object? userAvatarUrl = null,
    Object? type = null,
    Object? timestamp = null,
    Object? comment = freezed,
  }) {
    return _then(
      _value.copyWith(
            postId: null == postId
                ? _value.postId
                : postId // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            username: null == username
                ? _value.username
                : username // ignore: cast_nullable_to_non_nullable
                      as String,
            userAvatarUrl: null == userAvatarUrl
                ? _value.userAvatarUrl
                : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as InteractionType,
            timestamp: null == timestamp
                ? _value.timestamp
                : timestamp // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            comment: freezed == comment
                ? _value.comment
                : comment // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PostInteractionImplCopyWith<$Res>
    implements $PostInteractionCopyWith<$Res> {
  factory _$$PostInteractionImplCopyWith(
    _$PostInteractionImpl value,
    $Res Function(_$PostInteractionImpl) then,
  ) = __$$PostInteractionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String postId,
    String userId,
    String username,
    String userAvatarUrl,
    InteractionType type,
    DateTime timestamp,
    String? comment,
  });
}

/// @nodoc
class __$$PostInteractionImplCopyWithImpl<$Res>
    extends _$PostInteractionCopyWithImpl<$Res, _$PostInteractionImpl>
    implements _$$PostInteractionImplCopyWith<$Res> {
  __$$PostInteractionImplCopyWithImpl(
    _$PostInteractionImpl _value,
    $Res Function(_$PostInteractionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PostInteraction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? postId = null,
    Object? userId = null,
    Object? username = null,
    Object? userAvatarUrl = null,
    Object? type = null,
    Object? timestamp = null,
    Object? comment = freezed,
  }) {
    return _then(
      _$PostInteractionImpl(
        postId: null == postId
            ? _value.postId
            : postId // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        username: null == username
            ? _value.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        userAvatarUrl: null == userAvatarUrl
            ? _value.userAvatarUrl
            : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as InteractionType,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        comment: freezed == comment
            ? _value.comment
            : comment // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PostInteractionImpl implements _PostInteraction {
  const _$PostInteractionImpl({
    required this.postId,
    required this.userId,
    required this.username,
    required this.userAvatarUrl,
    required this.type,
    required this.timestamp,
    this.comment,
  });

  factory _$PostInteractionImpl.fromJson(Map<String, dynamic> json) =>
      _$$PostInteractionImplFromJson(json);

  @override
  final String postId;
  @override
  final String userId;
  @override
  final String username;
  @override
  final String userAvatarUrl;
  @override
  final InteractionType type;
  @override
  final DateTime timestamp;
  @override
  final String? comment;

  @override
  String toString() {
    return 'PostInteraction(postId: $postId, userId: $userId, username: $username, userAvatarUrl: $userAvatarUrl, type: $type, timestamp: $timestamp, comment: $comment)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PostInteractionImpl &&
            (identical(other.postId, postId) || other.postId == postId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userAvatarUrl, userAvatarUrl) ||
                other.userAvatarUrl == userAvatarUrl) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.comment, comment) || other.comment == comment));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    postId,
    userId,
    username,
    userAvatarUrl,
    type,
    timestamp,
    comment,
  );

  /// Create a copy of PostInteraction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PostInteractionImplCopyWith<_$PostInteractionImpl> get copyWith =>
      __$$PostInteractionImplCopyWithImpl<_$PostInteractionImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$PostInteractionImplToJson(this);
  }
}

abstract class _PostInteraction implements PostInteraction {
  const factory _PostInteraction({
    required final String postId,
    required final String userId,
    required final String username,
    required final String userAvatarUrl,
    required final InteractionType type,
    required final DateTime timestamp,
    final String? comment,
  }) = _$PostInteractionImpl;

  factory _PostInteraction.fromJson(Map<String, dynamic> json) =
      _$PostInteractionImpl.fromJson;

  @override
  String get postId;
  @override
  String get userId;
  @override
  String get username;
  @override
  String get userAvatarUrl;
  @override
  InteractionType get type;
  @override
  DateTime get timestamp;
  @override
  String? get comment;

  /// Create a copy of PostInteraction
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PostInteractionImplCopyWith<_$PostInteractionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PostPermission _$PostPermissionFromJson(Map<String, dynamic> json) {
  return _PostPermission.fromJson(json);
}

/// @nodoc
mixin _$PostPermission {
  String get postId => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  bool get canEdit => throw _privateConstructorUsedError;
  bool get canDelete => throw _privateConstructorUsedError;
  bool get canModerateComments => throw _privateConstructorUsedError;
  bool get canArchive => throw _privateConstructorUsedError;
  bool get canReport => throw _privateConstructorUsedError;

  /// Serializes this PostPermission to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PostPermission
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PostPermissionCopyWith<PostPermission> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PostPermissionCopyWith<$Res> {
  factory $PostPermissionCopyWith(
    PostPermission value,
    $Res Function(PostPermission) then,
  ) = _$PostPermissionCopyWithImpl<$Res, PostPermission>;
  @useResult
  $Res call({
    String postId,
    String userId,
    bool canEdit,
    bool canDelete,
    bool canModerateComments,
    bool canArchive,
    bool canReport,
  });
}

/// @nodoc
class _$PostPermissionCopyWithImpl<$Res, $Val extends PostPermission>
    implements $PostPermissionCopyWith<$Res> {
  _$PostPermissionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PostPermission
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? postId = null,
    Object? userId = null,
    Object? canEdit = null,
    Object? canDelete = null,
    Object? canModerateComments = null,
    Object? canArchive = null,
    Object? canReport = null,
  }) {
    return _then(
      _value.copyWith(
            postId: null == postId
                ? _value.postId
                : postId // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            canEdit: null == canEdit
                ? _value.canEdit
                : canEdit // ignore: cast_nullable_to_non_nullable
                      as bool,
            canDelete: null == canDelete
                ? _value.canDelete
                : canDelete // ignore: cast_nullable_to_non_nullable
                      as bool,
            canModerateComments: null == canModerateComments
                ? _value.canModerateComments
                : canModerateComments // ignore: cast_nullable_to_non_nullable
                      as bool,
            canArchive: null == canArchive
                ? _value.canArchive
                : canArchive // ignore: cast_nullable_to_non_nullable
                      as bool,
            canReport: null == canReport
                ? _value.canReport
                : canReport // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PostPermissionImplCopyWith<$Res>
    implements $PostPermissionCopyWith<$Res> {
  factory _$$PostPermissionImplCopyWith(
    _$PostPermissionImpl value,
    $Res Function(_$PostPermissionImpl) then,
  ) = __$$PostPermissionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String postId,
    String userId,
    bool canEdit,
    bool canDelete,
    bool canModerateComments,
    bool canArchive,
    bool canReport,
  });
}

/// @nodoc
class __$$PostPermissionImplCopyWithImpl<$Res>
    extends _$PostPermissionCopyWithImpl<$Res, _$PostPermissionImpl>
    implements _$$PostPermissionImplCopyWith<$Res> {
  __$$PostPermissionImplCopyWithImpl(
    _$PostPermissionImpl _value,
    $Res Function(_$PostPermissionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PostPermission
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? postId = null,
    Object? userId = null,
    Object? canEdit = null,
    Object? canDelete = null,
    Object? canModerateComments = null,
    Object? canArchive = null,
    Object? canReport = null,
  }) {
    return _then(
      _$PostPermissionImpl(
        postId: null == postId
            ? _value.postId
            : postId // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        canEdit: null == canEdit
            ? _value.canEdit
            : canEdit // ignore: cast_nullable_to_non_nullable
                  as bool,
        canDelete: null == canDelete
            ? _value.canDelete
            : canDelete // ignore: cast_nullable_to_non_nullable
                  as bool,
        canModerateComments: null == canModerateComments
            ? _value.canModerateComments
            : canModerateComments // ignore: cast_nullable_to_non_nullable
                  as bool,
        canArchive: null == canArchive
            ? _value.canArchive
            : canArchive // ignore: cast_nullable_to_non_nullable
                  as bool,
        canReport: null == canReport
            ? _value.canReport
            : canReport // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PostPermissionImpl implements _PostPermission {
  const _$PostPermissionImpl({
    required this.postId,
    required this.userId,
    required this.canEdit,
    required this.canDelete,
    required this.canModerateComments,
    required this.canArchive,
    required this.canReport,
  });

  factory _$PostPermissionImpl.fromJson(Map<String, dynamic> json) =>
      _$$PostPermissionImplFromJson(json);

  @override
  final String postId;
  @override
  final String userId;
  @override
  final bool canEdit;
  @override
  final bool canDelete;
  @override
  final bool canModerateComments;
  @override
  final bool canArchive;
  @override
  final bool canReport;

  @override
  String toString() {
    return 'PostPermission(postId: $postId, userId: $userId, canEdit: $canEdit, canDelete: $canDelete, canModerateComments: $canModerateComments, canArchive: $canArchive, canReport: $canReport)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PostPermissionImpl &&
            (identical(other.postId, postId) || other.postId == postId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.canEdit, canEdit) || other.canEdit == canEdit) &&
            (identical(other.canDelete, canDelete) ||
                other.canDelete == canDelete) &&
            (identical(other.canModerateComments, canModerateComments) ||
                other.canModerateComments == canModerateComments) &&
            (identical(other.canArchive, canArchive) ||
                other.canArchive == canArchive) &&
            (identical(other.canReport, canReport) ||
                other.canReport == canReport));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    postId,
    userId,
    canEdit,
    canDelete,
    canModerateComments,
    canArchive,
    canReport,
  );

  /// Create a copy of PostPermission
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PostPermissionImplCopyWith<_$PostPermissionImpl> get copyWith =>
      __$$PostPermissionImplCopyWithImpl<_$PostPermissionImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$PostPermissionImplToJson(this);
  }
}

abstract class _PostPermission implements PostPermission {
  const factory _PostPermission({
    required final String postId,
    required final String userId,
    required final bool canEdit,
    required final bool canDelete,
    required final bool canModerateComments,
    required final bool canArchive,
    required final bool canReport,
  }) = _$PostPermissionImpl;

  factory _PostPermission.fromJson(Map<String, dynamic> json) =
      _$PostPermissionImpl.fromJson;

  @override
  String get postId;
  @override
  String get userId;
  @override
  bool get canEdit;
  @override
  bool get canDelete;
  @override
  bool get canModerateComments;
  @override
  bool get canArchive;
  @override
  bool get canReport;

  /// Create a copy of PostPermission
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PostPermissionImplCopyWith<_$PostPermissionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
