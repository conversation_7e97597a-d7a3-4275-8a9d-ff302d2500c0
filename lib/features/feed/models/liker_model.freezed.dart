// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'liker_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

LikerModel _$LikerModelFromJson(Map<String, dynamic> json) {
  return _LikerModel.fromJson(json);
}

/// @nodoc
mixin _$LikerModel {
  String get id => throw _privateConstructorUsedError;
  String get username => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get profilePictureUrl => throw _privateConstructorUsedError;
  bool get isVerified => throw _privateConstructorUsedError;
  DateTime get likedAt => throw _privateConstructorUsedError;

  /// Serializes this LikerModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LikerModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LikerModelCopyWith<LikerModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LikerModelCopyWith<$Res> {
  factory $LikerModelCopyWith(
    LikerModel value,
    $Res Function(LikerModel) then,
  ) = _$LikerModelCopyWithImpl<$Res, LikerModel>;
  @useResult
  $Res call({
    String id,
    String username,
    String name,
    String profilePictureUrl,
    bool isVerified,
    DateTime likedAt,
  });
}

/// @nodoc
class _$LikerModelCopyWithImpl<$Res, $Val extends LikerModel>
    implements $LikerModelCopyWith<$Res> {
  _$LikerModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LikerModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? username = null,
    Object? name = null,
    Object? profilePictureUrl = null,
    Object? isVerified = null,
    Object? likedAt = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            username: null == username
                ? _value.username
                : username // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            profilePictureUrl: null == profilePictureUrl
                ? _value.profilePictureUrl
                : profilePictureUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            isVerified: null == isVerified
                ? _value.isVerified
                : isVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            likedAt: null == likedAt
                ? _value.likedAt
                : likedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$LikerModelImplCopyWith<$Res>
    implements $LikerModelCopyWith<$Res> {
  factory _$$LikerModelImplCopyWith(
    _$LikerModelImpl value,
    $Res Function(_$LikerModelImpl) then,
  ) = __$$LikerModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String username,
    String name,
    String profilePictureUrl,
    bool isVerified,
    DateTime likedAt,
  });
}

/// @nodoc
class __$$LikerModelImplCopyWithImpl<$Res>
    extends _$LikerModelCopyWithImpl<$Res, _$LikerModelImpl>
    implements _$$LikerModelImplCopyWith<$Res> {
  __$$LikerModelImplCopyWithImpl(
    _$LikerModelImpl _value,
    $Res Function(_$LikerModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of LikerModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? username = null,
    Object? name = null,
    Object? profilePictureUrl = null,
    Object? isVerified = null,
    Object? likedAt = null,
  }) {
    return _then(
      _$LikerModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        username: null == username
            ? _value.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        profilePictureUrl: null == profilePictureUrl
            ? _value.profilePictureUrl
            : profilePictureUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        isVerified: null == isVerified
            ? _value.isVerified
            : isVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        likedAt: null == likedAt
            ? _value.likedAt
            : likedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$LikerModelImpl implements _LikerModel {
  const _$LikerModelImpl({
    required this.id,
    required this.username,
    required this.name,
    required this.profilePictureUrl,
    required this.isVerified,
    required this.likedAt,
  });

  factory _$LikerModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$LikerModelImplFromJson(json);

  @override
  final String id;
  @override
  final String username;
  @override
  final String name;
  @override
  final String profilePictureUrl;
  @override
  final bool isVerified;
  @override
  final DateTime likedAt;

  @override
  String toString() {
    return 'LikerModel(id: $id, username: $username, name: $name, profilePictureUrl: $profilePictureUrl, isVerified: $isVerified, likedAt: $likedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LikerModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.profilePictureUrl, profilePictureUrl) ||
                other.profilePictureUrl == profilePictureUrl) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.likedAt, likedAt) || other.likedAt == likedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    username,
    name,
    profilePictureUrl,
    isVerified,
    likedAt,
  );

  /// Create a copy of LikerModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LikerModelImplCopyWith<_$LikerModelImpl> get copyWith =>
      __$$LikerModelImplCopyWithImpl<_$LikerModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LikerModelImplToJson(this);
  }
}

abstract class _LikerModel implements LikerModel {
  const factory _LikerModel({
    required final String id,
    required final String username,
    required final String name,
    required final String profilePictureUrl,
    required final bool isVerified,
    required final DateTime likedAt,
  }) = _$LikerModelImpl;

  factory _LikerModel.fromJson(Map<String, dynamic> json) =
      _$LikerModelImpl.fromJson;

  @override
  String get id;
  @override
  String get username;
  @override
  String get name;
  @override
  String get profilePictureUrl;
  @override
  bool get isVerified;
  @override
  DateTime get likedAt;

  /// Create a copy of LikerModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LikerModelImplCopyWith<_$LikerModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
