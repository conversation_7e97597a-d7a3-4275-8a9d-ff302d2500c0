// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'reaction_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

PostReaction _$PostReactionFromJson(Map<String, dynamic> json) {
  return _PostReaction.fromJson(json);
}

/// @nodoc
mixin _$PostReaction {
  String get id => throw _privateConstructorUsedError;
  String get postId => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get username => throw _privateConstructorUsedError;
  String get userAvatarUrl => throw _privateConstructorUsedError;
  ReactionType get type => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;

  /// Serializes this PostReaction to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PostReaction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PostReactionCopyWith<PostReaction> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PostReactionCopyWith<$Res> {
  factory $PostReactionCopyWith(
    PostReaction value,
    $Res Function(PostReaction) then,
  ) = _$PostReactionCopyWithImpl<$Res, PostReaction>;
  @useResult
  $Res call({
    String id,
    String postId,
    String userId,
    String username,
    String userAvatarUrl,
    ReactionType type,
    DateTime timestamp,
  });
}

/// @nodoc
class _$PostReactionCopyWithImpl<$Res, $Val extends PostReaction>
    implements $PostReactionCopyWith<$Res> {
  _$PostReactionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PostReaction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? postId = null,
    Object? userId = null,
    Object? username = null,
    Object? userAvatarUrl = null,
    Object? type = null,
    Object? timestamp = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            postId: null == postId
                ? _value.postId
                : postId // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            username: null == username
                ? _value.username
                : username // ignore: cast_nullable_to_non_nullable
                      as String,
            userAvatarUrl: null == userAvatarUrl
                ? _value.userAvatarUrl
                : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as ReactionType,
            timestamp: null == timestamp
                ? _value.timestamp
                : timestamp // ignore: cast_nullable_to_non_nullable
                      as DateTime,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PostReactionImplCopyWith<$Res>
    implements $PostReactionCopyWith<$Res> {
  factory _$$PostReactionImplCopyWith(
    _$PostReactionImpl value,
    $Res Function(_$PostReactionImpl) then,
  ) = __$$PostReactionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String postId,
    String userId,
    String username,
    String userAvatarUrl,
    ReactionType type,
    DateTime timestamp,
  });
}

/// @nodoc
class __$$PostReactionImplCopyWithImpl<$Res>
    extends _$PostReactionCopyWithImpl<$Res, _$PostReactionImpl>
    implements _$$PostReactionImplCopyWith<$Res> {
  __$$PostReactionImplCopyWithImpl(
    _$PostReactionImpl _value,
    $Res Function(_$PostReactionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PostReaction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? postId = null,
    Object? userId = null,
    Object? username = null,
    Object? userAvatarUrl = null,
    Object? type = null,
    Object? timestamp = null,
  }) {
    return _then(
      _$PostReactionImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        postId: null == postId
            ? _value.postId
            : postId // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        username: null == username
            ? _value.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        userAvatarUrl: null == userAvatarUrl
            ? _value.userAvatarUrl
            : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as ReactionType,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PostReactionImpl implements _PostReaction {
  const _$PostReactionImpl({
    required this.id,
    required this.postId,
    required this.userId,
    required this.username,
    required this.userAvatarUrl,
    required this.type,
    required this.timestamp,
  });

  factory _$PostReactionImpl.fromJson(Map<String, dynamic> json) =>
      _$$PostReactionImplFromJson(json);

  @override
  final String id;
  @override
  final String postId;
  @override
  final String userId;
  @override
  final String username;
  @override
  final String userAvatarUrl;
  @override
  final ReactionType type;
  @override
  final DateTime timestamp;

  @override
  String toString() {
    return 'PostReaction(id: $id, postId: $postId, userId: $userId, username: $username, userAvatarUrl: $userAvatarUrl, type: $type, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PostReactionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.postId, postId) || other.postId == postId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userAvatarUrl, userAvatarUrl) ||
                other.userAvatarUrl == userAvatarUrl) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    postId,
    userId,
    username,
    userAvatarUrl,
    type,
    timestamp,
  );

  /// Create a copy of PostReaction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PostReactionImplCopyWith<_$PostReactionImpl> get copyWith =>
      __$$PostReactionImplCopyWithImpl<_$PostReactionImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PostReactionImplToJson(this);
  }
}

abstract class _PostReaction implements PostReaction {
  const factory _PostReaction({
    required final String id,
    required final String postId,
    required final String userId,
    required final String username,
    required final String userAvatarUrl,
    required final ReactionType type,
    required final DateTime timestamp,
  }) = _$PostReactionImpl;

  factory _PostReaction.fromJson(Map<String, dynamic> json) =
      _$PostReactionImpl.fromJson;

  @override
  String get id;
  @override
  String get postId;
  @override
  String get userId;
  @override
  String get username;
  @override
  String get userAvatarUrl;
  @override
  ReactionType get type;
  @override
  DateTime get timestamp;

  /// Create a copy of PostReaction
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PostReactionImplCopyWith<_$PostReactionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ReactionCount _$ReactionCountFromJson(Map<String, dynamic> json) {
  return _ReactionCount.fromJson(json);
}

/// @nodoc
mixin _$ReactionCount {
  ReactionType get type => throw _privateConstructorUsedError;
  int get count => throw _privateConstructorUsedError;
  bool get isUserReaction => throw _privateConstructorUsedError;

  /// Serializes this ReactionCount to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ReactionCount
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ReactionCountCopyWith<ReactionCount> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReactionCountCopyWith<$Res> {
  factory $ReactionCountCopyWith(
    ReactionCount value,
    $Res Function(ReactionCount) then,
  ) = _$ReactionCountCopyWithImpl<$Res, ReactionCount>;
  @useResult
  $Res call({ReactionType type, int count, bool isUserReaction});
}

/// @nodoc
class _$ReactionCountCopyWithImpl<$Res, $Val extends ReactionCount>
    implements $ReactionCountCopyWith<$Res> {
  _$ReactionCountCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ReactionCount
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? count = null,
    Object? isUserReaction = null,
  }) {
    return _then(
      _value.copyWith(
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as ReactionType,
            count: null == count
                ? _value.count
                : count // ignore: cast_nullable_to_non_nullable
                      as int,
            isUserReaction: null == isUserReaction
                ? _value.isUserReaction
                : isUserReaction // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ReactionCountImplCopyWith<$Res>
    implements $ReactionCountCopyWith<$Res> {
  factory _$$ReactionCountImplCopyWith(
    _$ReactionCountImpl value,
    $Res Function(_$ReactionCountImpl) then,
  ) = __$$ReactionCountImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({ReactionType type, int count, bool isUserReaction});
}

/// @nodoc
class __$$ReactionCountImplCopyWithImpl<$Res>
    extends _$ReactionCountCopyWithImpl<$Res, _$ReactionCountImpl>
    implements _$$ReactionCountImplCopyWith<$Res> {
  __$$ReactionCountImplCopyWithImpl(
    _$ReactionCountImpl _value,
    $Res Function(_$ReactionCountImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ReactionCount
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? count = null,
    Object? isUserReaction = null,
  }) {
    return _then(
      _$ReactionCountImpl(
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as ReactionType,
        count: null == count
            ? _value.count
            : count // ignore: cast_nullable_to_non_nullable
                  as int,
        isUserReaction: null == isUserReaction
            ? _value.isUserReaction
            : isUserReaction // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ReactionCountImpl implements _ReactionCount {
  const _$ReactionCountImpl({
    required this.type,
    required this.count,
    this.isUserReaction = false,
  });

  factory _$ReactionCountImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReactionCountImplFromJson(json);

  @override
  final ReactionType type;
  @override
  final int count;
  @override
  @JsonKey()
  final bool isUserReaction;

  @override
  String toString() {
    return 'ReactionCount(type: $type, count: $count, isUserReaction: $isUserReaction)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReactionCountImpl &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.count, count) || other.count == count) &&
            (identical(other.isUserReaction, isUserReaction) ||
                other.isUserReaction == isUserReaction));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, type, count, isUserReaction);

  /// Create a copy of ReactionCount
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReactionCountImplCopyWith<_$ReactionCountImpl> get copyWith =>
      __$$ReactionCountImplCopyWithImpl<_$ReactionCountImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReactionCountImplToJson(this);
  }
}

abstract class _ReactionCount implements ReactionCount {
  const factory _ReactionCount({
    required final ReactionType type,
    required final int count,
    final bool isUserReaction,
  }) = _$ReactionCountImpl;

  factory _ReactionCount.fromJson(Map<String, dynamic> json) =
      _$ReactionCountImpl.fromJson;

  @override
  ReactionType get type;
  @override
  int get count;
  @override
  bool get isUserReaction;

  /// Create a copy of ReactionCount
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReactionCountImplCopyWith<_$ReactionCountImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ReactionSummary _$ReactionSummaryFromJson(Map<String, dynamic> json) {
  return _ReactionSummary.fromJson(json);
}

/// @nodoc
mixin _$ReactionSummary {
  String get postId => throw _privateConstructorUsedError;
  int get totalReactions => throw _privateConstructorUsedError;
  List<ReactionCount> get reactionCounts => throw _privateConstructorUsedError;
  ReactionType? get userReaction => throw _privateConstructorUsedError;
  DateTime get lastUpdated => throw _privateConstructorUsedError;

  /// Serializes this ReactionSummary to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ReactionSummary
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ReactionSummaryCopyWith<ReactionSummary> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReactionSummaryCopyWith<$Res> {
  factory $ReactionSummaryCopyWith(
    ReactionSummary value,
    $Res Function(ReactionSummary) then,
  ) = _$ReactionSummaryCopyWithImpl<$Res, ReactionSummary>;
  @useResult
  $Res call({
    String postId,
    int totalReactions,
    List<ReactionCount> reactionCounts,
    ReactionType? userReaction,
    DateTime lastUpdated,
  });
}

/// @nodoc
class _$ReactionSummaryCopyWithImpl<$Res, $Val extends ReactionSummary>
    implements $ReactionSummaryCopyWith<$Res> {
  _$ReactionSummaryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ReactionSummary
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? postId = null,
    Object? totalReactions = null,
    Object? reactionCounts = null,
    Object? userReaction = freezed,
    Object? lastUpdated = null,
  }) {
    return _then(
      _value.copyWith(
            postId: null == postId
                ? _value.postId
                : postId // ignore: cast_nullable_to_non_nullable
                      as String,
            totalReactions: null == totalReactions
                ? _value.totalReactions
                : totalReactions // ignore: cast_nullable_to_non_nullable
                      as int,
            reactionCounts: null == reactionCounts
                ? _value.reactionCounts
                : reactionCounts // ignore: cast_nullable_to_non_nullable
                      as List<ReactionCount>,
            userReaction: freezed == userReaction
                ? _value.userReaction
                : userReaction // ignore: cast_nullable_to_non_nullable
                      as ReactionType?,
            lastUpdated: null == lastUpdated
                ? _value.lastUpdated
                : lastUpdated // ignore: cast_nullable_to_non_nullable
                      as DateTime,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ReactionSummaryImplCopyWith<$Res>
    implements $ReactionSummaryCopyWith<$Res> {
  factory _$$ReactionSummaryImplCopyWith(
    _$ReactionSummaryImpl value,
    $Res Function(_$ReactionSummaryImpl) then,
  ) = __$$ReactionSummaryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String postId,
    int totalReactions,
    List<ReactionCount> reactionCounts,
    ReactionType? userReaction,
    DateTime lastUpdated,
  });
}

/// @nodoc
class __$$ReactionSummaryImplCopyWithImpl<$Res>
    extends _$ReactionSummaryCopyWithImpl<$Res, _$ReactionSummaryImpl>
    implements _$$ReactionSummaryImplCopyWith<$Res> {
  __$$ReactionSummaryImplCopyWithImpl(
    _$ReactionSummaryImpl _value,
    $Res Function(_$ReactionSummaryImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ReactionSummary
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? postId = null,
    Object? totalReactions = null,
    Object? reactionCounts = null,
    Object? userReaction = freezed,
    Object? lastUpdated = null,
  }) {
    return _then(
      _$ReactionSummaryImpl(
        postId: null == postId
            ? _value.postId
            : postId // ignore: cast_nullable_to_non_nullable
                  as String,
        totalReactions: null == totalReactions
            ? _value.totalReactions
            : totalReactions // ignore: cast_nullable_to_non_nullable
                  as int,
        reactionCounts: null == reactionCounts
            ? _value._reactionCounts
            : reactionCounts // ignore: cast_nullable_to_non_nullable
                  as List<ReactionCount>,
        userReaction: freezed == userReaction
            ? _value.userReaction
            : userReaction // ignore: cast_nullable_to_non_nullable
                  as ReactionType?,
        lastUpdated: null == lastUpdated
            ? _value.lastUpdated
            : lastUpdated // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ReactionSummaryImpl implements _ReactionSummary {
  const _$ReactionSummaryImpl({
    required this.postId,
    required this.totalReactions,
    required final List<ReactionCount> reactionCounts,
    this.userReaction,
    required this.lastUpdated,
  }) : _reactionCounts = reactionCounts;

  factory _$ReactionSummaryImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReactionSummaryImplFromJson(json);

  @override
  final String postId;
  @override
  final int totalReactions;
  final List<ReactionCount> _reactionCounts;
  @override
  List<ReactionCount> get reactionCounts {
    if (_reactionCounts is EqualUnmodifiableListView) return _reactionCounts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_reactionCounts);
  }

  @override
  final ReactionType? userReaction;
  @override
  final DateTime lastUpdated;

  @override
  String toString() {
    return 'ReactionSummary(postId: $postId, totalReactions: $totalReactions, reactionCounts: $reactionCounts, userReaction: $userReaction, lastUpdated: $lastUpdated)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReactionSummaryImpl &&
            (identical(other.postId, postId) || other.postId == postId) &&
            (identical(other.totalReactions, totalReactions) ||
                other.totalReactions == totalReactions) &&
            const DeepCollectionEquality().equals(
              other._reactionCounts,
              _reactionCounts,
            ) &&
            (identical(other.userReaction, userReaction) ||
                other.userReaction == userReaction) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    postId,
    totalReactions,
    const DeepCollectionEquality().hash(_reactionCounts),
    userReaction,
    lastUpdated,
  );

  /// Create a copy of ReactionSummary
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReactionSummaryImplCopyWith<_$ReactionSummaryImpl> get copyWith =>
      __$$ReactionSummaryImplCopyWithImpl<_$ReactionSummaryImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ReactionSummaryImplToJson(this);
  }
}

abstract class _ReactionSummary implements ReactionSummary {
  const factory _ReactionSummary({
    required final String postId,
    required final int totalReactions,
    required final List<ReactionCount> reactionCounts,
    final ReactionType? userReaction,
    required final DateTime lastUpdated,
  }) = _$ReactionSummaryImpl;

  factory _ReactionSummary.fromJson(Map<String, dynamic> json) =
      _$ReactionSummaryImpl.fromJson;

  @override
  String get postId;
  @override
  int get totalReactions;
  @override
  List<ReactionCount> get reactionCounts;
  @override
  ReactionType? get userReaction;
  @override
  DateTime get lastUpdated;

  /// Create a copy of ReactionSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReactionSummaryImplCopyWith<_$ReactionSummaryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
