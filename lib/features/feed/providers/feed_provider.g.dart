// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'feed_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$feedHash() => r'bc79f61ee09aca029a231cf3316f80b1e000ecbf';

/// See also [Feed].
@ProviderFor(Feed)
final feedProvider =
    AutoDisposeAsyncNotifierProvider<Feed, List<Post>>.internal(
      Feed.new,
      name: r'feedProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$feedHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$Feed = AutoDisposeAsyncNotifier<List<Post>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
