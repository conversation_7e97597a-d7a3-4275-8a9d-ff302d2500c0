// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'discovery_feed_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$suggestedAccountsHash() => r'e9aeabd38a009fd808fd388cb52d08ce6b66ae48';

/// Provider for suggested accounts for new users
///
/// Copied from [SuggestedAccounts].
@ProviderFor(SuggestedAccounts)
final suggestedAccountsProvider =
    AsyncNotifierProvider<SuggestedAccounts, List<ProfileModel>>.internal(
      SuggestedAccounts.new,
      name: r'suggestedAccountsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$suggestedAccountsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SuggestedAccounts = AsyncNotifier<List<ProfileModel>>;
String _$discoveryContentHash() => r'58a51fc52d0940a990bd0b0d2400fe8a2ad4a0e5';

/// Provider for discovery content (posts from non-followed users)
///
/// Copied from [DiscoveryContent].
@ProviderFor(DiscoveryContent)
final discoveryContentProvider =
    AsyncNotifierProvider<DiscoveryContent, List<Post>>.internal(
      DiscoveryContent.new,
      name: r'discoveryContentProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$discoveryContentHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$DiscoveryContent = AsyncNotifier<List<Post>>;
String _$contentPreferencesHash() =>
    r'7ae7a5f5fc737845234fc0f6c3dcf3372d40c759';

/// Provider for content preferences
///
/// Copied from [ContentPreferences].
@ProviderFor(ContentPreferences)
final contentPreferencesProvider =
    AsyncNotifierProvider<ContentPreferences, Map<String, dynamic>>.internal(
      ContentPreferences.new,
      name: r'contentPreferencesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$contentPreferencesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$ContentPreferences = AsyncNotifier<Map<String, dynamic>>;
String _$hybridFeedHash() => r'0a0cef06dac8c7e0f29a309d440f65f8cc9a75a4';

/// Provider for hybrid feed (following + discovery content)
///
/// Copied from [HybridFeed].
@ProviderFor(HybridFeed)
final hybridFeedProvider =
    AsyncNotifierProvider<HybridFeed, List<Post>>.internal(
      HybridFeed.new,
      name: r'hybridFeedProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$hybridFeedHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$HybridFeed = AsyncNotifier<List<Post>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
