// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_settings_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

UserSettingsModel _$UserSettingsModelFromJson(Map<String, dynamic> json) {
  return _UserSettingsModel.fromJson(json);
}

/// @nodoc
mixin _$UserSettingsModel {
  String get userId => throw _privateConstructorUsedError;
  int get version => throw _privateConstructorUsedError;
  String get schemaVersion => throw _privateConstructorUsedError;
  DateTime get lastUpdated => throw _privateConstructorUsedError;
  Map<String, dynamic> get privacy => throw _privateConstructorUsedError;
  Map<String, dynamic> get notifications => throw _privateConstructorUsedError;
  Map<String, dynamic> get theme => throw _privateConstructorUsedError;
  Map<String, dynamic> get feed => throw _privateConstructorUsedError;
  Map<String, dynamic> get messaging => throw _privateConstructorUsedError;
  Map<String, dynamic> get verification => throw _privateConstructorUsedError;
  Map<String, dynamic> get security => throw _privateConstructorUsedError;
  Map<String, dynamic> get accessibility => throw _privateConstructorUsedError;
  Map<String, dynamic> get content => throw _privateConstructorUsedError;
  Map<String, dynamic> get localization =>
      throw _privateConstructorUsedError; // Metadata for tracking changes
  bool get needsSync => throw _privateConstructorUsedError;
  bool get hasCustomizations => throw _privateConstructorUsedError;
  DateTime? get lastMerged => throw _privateConstructorUsedError;
  List<String>? get customizedFields => throw _privateConstructorUsedError;

  /// Serializes this UserSettingsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserSettingsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserSettingsModelCopyWith<UserSettingsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserSettingsModelCopyWith<$Res> {
  factory $UserSettingsModelCopyWith(
    UserSettingsModel value,
    $Res Function(UserSettingsModel) then,
  ) = _$UserSettingsModelCopyWithImpl<$Res, UserSettingsModel>;
  @useResult
  $Res call({
    String userId,
    int version,
    String schemaVersion,
    DateTime lastUpdated,
    Map<String, dynamic> privacy,
    Map<String, dynamic> notifications,
    Map<String, dynamic> theme,
    Map<String, dynamic> feed,
    Map<String, dynamic> messaging,
    Map<String, dynamic> verification,
    Map<String, dynamic> security,
    Map<String, dynamic> accessibility,
    Map<String, dynamic> content,
    Map<String, dynamic> localization,
    bool needsSync,
    bool hasCustomizations,
    DateTime? lastMerged,
    List<String>? customizedFields,
  });
}

/// @nodoc
class _$UserSettingsModelCopyWithImpl<$Res, $Val extends UserSettingsModel>
    implements $UserSettingsModelCopyWith<$Res> {
  _$UserSettingsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserSettingsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? version = null,
    Object? schemaVersion = null,
    Object? lastUpdated = null,
    Object? privacy = null,
    Object? notifications = null,
    Object? theme = null,
    Object? feed = null,
    Object? messaging = null,
    Object? verification = null,
    Object? security = null,
    Object? accessibility = null,
    Object? content = null,
    Object? localization = null,
    Object? needsSync = null,
    Object? hasCustomizations = null,
    Object? lastMerged = freezed,
    Object? customizedFields = freezed,
  }) {
    return _then(
      _value.copyWith(
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            version: null == version
                ? _value.version
                : version // ignore: cast_nullable_to_non_nullable
                      as int,
            schemaVersion: null == schemaVersion
                ? _value.schemaVersion
                : schemaVersion // ignore: cast_nullable_to_non_nullable
                      as String,
            lastUpdated: null == lastUpdated
                ? _value.lastUpdated
                : lastUpdated // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            privacy: null == privacy
                ? _value.privacy
                : privacy // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            notifications: null == notifications
                ? _value.notifications
                : notifications // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            theme: null == theme
                ? _value.theme
                : theme // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            feed: null == feed
                ? _value.feed
                : feed // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            messaging: null == messaging
                ? _value.messaging
                : messaging // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            verification: null == verification
                ? _value.verification
                : verification // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            security: null == security
                ? _value.security
                : security // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            accessibility: null == accessibility
                ? _value.accessibility
                : accessibility // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            content: null == content
                ? _value.content
                : content // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            localization: null == localization
                ? _value.localization
                : localization // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            needsSync: null == needsSync
                ? _value.needsSync
                : needsSync // ignore: cast_nullable_to_non_nullable
                      as bool,
            hasCustomizations: null == hasCustomizations
                ? _value.hasCustomizations
                : hasCustomizations // ignore: cast_nullable_to_non_nullable
                      as bool,
            lastMerged: freezed == lastMerged
                ? _value.lastMerged
                : lastMerged // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            customizedFields: freezed == customizedFields
                ? _value.customizedFields
                : customizedFields // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$UserSettingsModelImplCopyWith<$Res>
    implements $UserSettingsModelCopyWith<$Res> {
  factory _$$UserSettingsModelImplCopyWith(
    _$UserSettingsModelImpl value,
    $Res Function(_$UserSettingsModelImpl) then,
  ) = __$$UserSettingsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String userId,
    int version,
    String schemaVersion,
    DateTime lastUpdated,
    Map<String, dynamic> privacy,
    Map<String, dynamic> notifications,
    Map<String, dynamic> theme,
    Map<String, dynamic> feed,
    Map<String, dynamic> messaging,
    Map<String, dynamic> verification,
    Map<String, dynamic> security,
    Map<String, dynamic> accessibility,
    Map<String, dynamic> content,
    Map<String, dynamic> localization,
    bool needsSync,
    bool hasCustomizations,
    DateTime? lastMerged,
    List<String>? customizedFields,
  });
}

/// @nodoc
class __$$UserSettingsModelImplCopyWithImpl<$Res>
    extends _$UserSettingsModelCopyWithImpl<$Res, _$UserSettingsModelImpl>
    implements _$$UserSettingsModelImplCopyWith<$Res> {
  __$$UserSettingsModelImplCopyWithImpl(
    _$UserSettingsModelImpl _value,
    $Res Function(_$UserSettingsModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of UserSettingsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? version = null,
    Object? schemaVersion = null,
    Object? lastUpdated = null,
    Object? privacy = null,
    Object? notifications = null,
    Object? theme = null,
    Object? feed = null,
    Object? messaging = null,
    Object? verification = null,
    Object? security = null,
    Object? accessibility = null,
    Object? content = null,
    Object? localization = null,
    Object? needsSync = null,
    Object? hasCustomizations = null,
    Object? lastMerged = freezed,
    Object? customizedFields = freezed,
  }) {
    return _then(
      _$UserSettingsModelImpl(
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        version: null == version
            ? _value.version
            : version // ignore: cast_nullable_to_non_nullable
                  as int,
        schemaVersion: null == schemaVersion
            ? _value.schemaVersion
            : schemaVersion // ignore: cast_nullable_to_non_nullable
                  as String,
        lastUpdated: null == lastUpdated
            ? _value.lastUpdated
            : lastUpdated // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        privacy: null == privacy
            ? _value._privacy
            : privacy // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        notifications: null == notifications
            ? _value._notifications
            : notifications // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        theme: null == theme
            ? _value._theme
            : theme // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        feed: null == feed
            ? _value._feed
            : feed // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        messaging: null == messaging
            ? _value._messaging
            : messaging // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        verification: null == verification
            ? _value._verification
            : verification // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        security: null == security
            ? _value._security
            : security // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        accessibility: null == accessibility
            ? _value._accessibility
            : accessibility // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        content: null == content
            ? _value._content
            : content // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        localization: null == localization
            ? _value._localization
            : localization // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        needsSync: null == needsSync
            ? _value.needsSync
            : needsSync // ignore: cast_nullable_to_non_nullable
                  as bool,
        hasCustomizations: null == hasCustomizations
            ? _value.hasCustomizations
            : hasCustomizations // ignore: cast_nullable_to_non_nullable
                  as bool,
        lastMerged: freezed == lastMerged
            ? _value.lastMerged
            : lastMerged // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        customizedFields: freezed == customizedFields
            ? _value._customizedFields
            : customizedFields // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$UserSettingsModelImpl implements _UserSettingsModel {
  const _$UserSettingsModelImpl({
    required this.userId,
    required this.version,
    required this.schemaVersion,
    required this.lastUpdated,
    required final Map<String, dynamic> privacy,
    required final Map<String, dynamic> notifications,
    required final Map<String, dynamic> theme,
    required final Map<String, dynamic> feed,
    required final Map<String, dynamic> messaging,
    required final Map<String, dynamic> verification,
    required final Map<String, dynamic> security,
    required final Map<String, dynamic> accessibility,
    required final Map<String, dynamic> content,
    required final Map<String, dynamic> localization,
    this.needsSync = false,
    this.hasCustomizations = false,
    this.lastMerged,
    final List<String>? customizedFields,
  }) : _privacy = privacy,
       _notifications = notifications,
       _theme = theme,
       _feed = feed,
       _messaging = messaging,
       _verification = verification,
       _security = security,
       _accessibility = accessibility,
       _content = content,
       _localization = localization,
       _customizedFields = customizedFields;

  factory _$UserSettingsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserSettingsModelImplFromJson(json);

  @override
  final String userId;
  @override
  final int version;
  @override
  final String schemaVersion;
  @override
  final DateTime lastUpdated;
  final Map<String, dynamic> _privacy;
  @override
  Map<String, dynamic> get privacy {
    if (_privacy is EqualUnmodifiableMapView) return _privacy;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_privacy);
  }

  final Map<String, dynamic> _notifications;
  @override
  Map<String, dynamic> get notifications {
    if (_notifications is EqualUnmodifiableMapView) return _notifications;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_notifications);
  }

  final Map<String, dynamic> _theme;
  @override
  Map<String, dynamic> get theme {
    if (_theme is EqualUnmodifiableMapView) return _theme;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_theme);
  }

  final Map<String, dynamic> _feed;
  @override
  Map<String, dynamic> get feed {
    if (_feed is EqualUnmodifiableMapView) return _feed;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_feed);
  }

  final Map<String, dynamic> _messaging;
  @override
  Map<String, dynamic> get messaging {
    if (_messaging is EqualUnmodifiableMapView) return _messaging;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_messaging);
  }

  final Map<String, dynamic> _verification;
  @override
  Map<String, dynamic> get verification {
    if (_verification is EqualUnmodifiableMapView) return _verification;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_verification);
  }

  final Map<String, dynamic> _security;
  @override
  Map<String, dynamic> get security {
    if (_security is EqualUnmodifiableMapView) return _security;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_security);
  }

  final Map<String, dynamic> _accessibility;
  @override
  Map<String, dynamic> get accessibility {
    if (_accessibility is EqualUnmodifiableMapView) return _accessibility;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_accessibility);
  }

  final Map<String, dynamic> _content;
  @override
  Map<String, dynamic> get content {
    if (_content is EqualUnmodifiableMapView) return _content;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_content);
  }

  final Map<String, dynamic> _localization;
  @override
  Map<String, dynamic> get localization {
    if (_localization is EqualUnmodifiableMapView) return _localization;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_localization);
  }

  // Metadata for tracking changes
  @override
  @JsonKey()
  final bool needsSync;
  @override
  @JsonKey()
  final bool hasCustomizations;
  @override
  final DateTime? lastMerged;
  final List<String>? _customizedFields;
  @override
  List<String>? get customizedFields {
    final value = _customizedFields;
    if (value == null) return null;
    if (_customizedFields is EqualUnmodifiableListView)
      return _customizedFields;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'UserSettingsModel(userId: $userId, version: $version, schemaVersion: $schemaVersion, lastUpdated: $lastUpdated, privacy: $privacy, notifications: $notifications, theme: $theme, feed: $feed, messaging: $messaging, verification: $verification, security: $security, accessibility: $accessibility, content: $content, localization: $localization, needsSync: $needsSync, hasCustomizations: $hasCustomizations, lastMerged: $lastMerged, customizedFields: $customizedFields)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserSettingsModelImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.version, version) || other.version == version) &&
            (identical(other.schemaVersion, schemaVersion) ||
                other.schemaVersion == schemaVersion) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated) &&
            const DeepCollectionEquality().equals(other._privacy, _privacy) &&
            const DeepCollectionEquality().equals(
              other._notifications,
              _notifications,
            ) &&
            const DeepCollectionEquality().equals(other._theme, _theme) &&
            const DeepCollectionEquality().equals(other._feed, _feed) &&
            const DeepCollectionEquality().equals(
              other._messaging,
              _messaging,
            ) &&
            const DeepCollectionEquality().equals(
              other._verification,
              _verification,
            ) &&
            const DeepCollectionEquality().equals(other._security, _security) &&
            const DeepCollectionEquality().equals(
              other._accessibility,
              _accessibility,
            ) &&
            const DeepCollectionEquality().equals(other._content, _content) &&
            const DeepCollectionEquality().equals(
              other._localization,
              _localization,
            ) &&
            (identical(other.needsSync, needsSync) ||
                other.needsSync == needsSync) &&
            (identical(other.hasCustomizations, hasCustomizations) ||
                other.hasCustomizations == hasCustomizations) &&
            (identical(other.lastMerged, lastMerged) ||
                other.lastMerged == lastMerged) &&
            const DeepCollectionEquality().equals(
              other._customizedFields,
              _customizedFields,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    userId,
    version,
    schemaVersion,
    lastUpdated,
    const DeepCollectionEquality().hash(_privacy),
    const DeepCollectionEquality().hash(_notifications),
    const DeepCollectionEquality().hash(_theme),
    const DeepCollectionEquality().hash(_feed),
    const DeepCollectionEquality().hash(_messaging),
    const DeepCollectionEquality().hash(_verification),
    const DeepCollectionEquality().hash(_security),
    const DeepCollectionEquality().hash(_accessibility),
    const DeepCollectionEquality().hash(_content),
    const DeepCollectionEquality().hash(_localization),
    needsSync,
    hasCustomizations,
    lastMerged,
    const DeepCollectionEquality().hash(_customizedFields),
  );

  /// Create a copy of UserSettingsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserSettingsModelImplCopyWith<_$UserSettingsModelImpl> get copyWith =>
      __$$UserSettingsModelImplCopyWithImpl<_$UserSettingsModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$UserSettingsModelImplToJson(this);
  }
}

abstract class _UserSettingsModel implements UserSettingsModel {
  const factory _UserSettingsModel({
    required final String userId,
    required final int version,
    required final String schemaVersion,
    required final DateTime lastUpdated,
    required final Map<String, dynamic> privacy,
    required final Map<String, dynamic> notifications,
    required final Map<String, dynamic> theme,
    required final Map<String, dynamic> feed,
    required final Map<String, dynamic> messaging,
    required final Map<String, dynamic> verification,
    required final Map<String, dynamic> security,
    required final Map<String, dynamic> accessibility,
    required final Map<String, dynamic> content,
    required final Map<String, dynamic> localization,
    final bool needsSync,
    final bool hasCustomizations,
    final DateTime? lastMerged,
    final List<String>? customizedFields,
  }) = _$UserSettingsModelImpl;

  factory _UserSettingsModel.fromJson(Map<String, dynamic> json) =
      _$UserSettingsModelImpl.fromJson;

  @override
  String get userId;
  @override
  int get version;
  @override
  String get schemaVersion;
  @override
  DateTime get lastUpdated;
  @override
  Map<String, dynamic> get privacy;
  @override
  Map<String, dynamic> get notifications;
  @override
  Map<String, dynamic> get theme;
  @override
  Map<String, dynamic> get feed;
  @override
  Map<String, dynamic> get messaging;
  @override
  Map<String, dynamic> get verification;
  @override
  Map<String, dynamic> get security;
  @override
  Map<String, dynamic> get accessibility;
  @override
  Map<String, dynamic> get content;
  @override
  Map<String, dynamic> get localization; // Metadata for tracking changes
  @override
  bool get needsSync;
  @override
  bool get hasCustomizations;
  @override
  DateTime? get lastMerged;
  @override
  List<String>? get customizedFields;

  /// Create a copy of UserSettingsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserSettingsModelImplCopyWith<_$UserSettingsModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
