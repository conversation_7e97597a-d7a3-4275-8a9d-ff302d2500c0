// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_settings_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserSettingsModelImpl _$$UserSettingsModelImplFromJson(
  Map<String, dynamic> json,
) => _$UserSettingsModelImpl(
  userId: json['userId'] as String,
  version: (json['version'] as num).toInt(),
  schemaVersion: json['schemaVersion'] as String,
  lastUpdated: DateTime.parse(json['lastUpdated'] as String),
  privacy: json['privacy'] as Map<String, dynamic>,
  notifications: json['notifications'] as Map<String, dynamic>,
  theme: json['theme'] as Map<String, dynamic>,
  feed: json['feed'] as Map<String, dynamic>,
  messaging: json['messaging'] as Map<String, dynamic>,
  verification: json['verification'] as Map<String, dynamic>,
  security: json['security'] as Map<String, dynamic>,
  accessibility: json['accessibility'] as Map<String, dynamic>,
  content: json['content'] as Map<String, dynamic>,
  localization: json['localization'] as Map<String, dynamic>,
  needsSync: json['needsSync'] as bool? ?? false,
  hasCustomizations: json['hasCustomizations'] as bool? ?? false,
  lastMerged: json['lastMerged'] == null
      ? null
      : DateTime.parse(json['lastMerged'] as String),
  customizedFields: (json['customizedFields'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
);

Map<String, dynamic> _$$UserSettingsModelImplToJson(
  _$UserSettingsModelImpl instance,
) => <String, dynamic>{
  'userId': instance.userId,
  'version': instance.version,
  'schemaVersion': instance.schemaVersion,
  'lastUpdated': instance.lastUpdated.toIso8601String(),
  'privacy': instance.privacy,
  'notifications': instance.notifications,
  'theme': instance.theme,
  'feed': instance.feed,
  'messaging': instance.messaging,
  'verification': instance.verification,
  'security': instance.security,
  'accessibility': instance.accessibility,
  'content': instance.content,
  'localization': instance.localization,
  'needsSync': instance.needsSync,
  'hasCustomizations': instance.hasCustomizations,
  'lastMerged': instance.lastMerged?.toIso8601String(),
  'customizedFields': instance.customizedFields,
};
