// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'chat_privacy_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

ChatPrivacySettings _$ChatPrivacySettingsFromJson(Map<String, dynamic> json) {
  return _ChatPrivacySettings.fromJson(json);
}

/// @nodoc
mixin _$ChatPrivacySettings {
  String get id => throw _privateConstructorUsedError;
  bool get blockNonFollowers => throw _privateConstructorUsedError;
  bool get autoDeleteAfterReading => throw _privateConstructorUsedError;
  List<String> get mutedConversationIds => throw _privateConstructorUsedError;
  List<String> get blockedUserIds => throw _privateConstructorUsedError;
  bool get allowGroupChats => throw _privateConstructorUsedError;
  bool get allowVoiceMessages => throw _privateConstructorUsedError;
  bool get allowVideoCalls => throw _privateConstructorUsedError;

  /// Serializes this ChatPrivacySettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ChatPrivacySettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ChatPrivacySettingsCopyWith<ChatPrivacySettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChatPrivacySettingsCopyWith<$Res> {
  factory $ChatPrivacySettingsCopyWith(
    ChatPrivacySettings value,
    $Res Function(ChatPrivacySettings) then,
  ) = _$ChatPrivacySettingsCopyWithImpl<$Res, ChatPrivacySettings>;
  @useResult
  $Res call({
    String id,
    bool blockNonFollowers,
    bool autoDeleteAfterReading,
    List<String> mutedConversationIds,
    List<String> blockedUserIds,
    bool allowGroupChats,
    bool allowVoiceMessages,
    bool allowVideoCalls,
  });
}

/// @nodoc
class _$ChatPrivacySettingsCopyWithImpl<$Res, $Val extends ChatPrivacySettings>
    implements $ChatPrivacySettingsCopyWith<$Res> {
  _$ChatPrivacySettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ChatPrivacySettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? blockNonFollowers = null,
    Object? autoDeleteAfterReading = null,
    Object? mutedConversationIds = null,
    Object? blockedUserIds = null,
    Object? allowGroupChats = null,
    Object? allowVoiceMessages = null,
    Object? allowVideoCalls = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            blockNonFollowers: null == blockNonFollowers
                ? _value.blockNonFollowers
                : blockNonFollowers // ignore: cast_nullable_to_non_nullable
                      as bool,
            autoDeleteAfterReading: null == autoDeleteAfterReading
                ? _value.autoDeleteAfterReading
                : autoDeleteAfterReading // ignore: cast_nullable_to_non_nullable
                      as bool,
            mutedConversationIds: null == mutedConversationIds
                ? _value.mutedConversationIds
                : mutedConversationIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            blockedUserIds: null == blockedUserIds
                ? _value.blockedUserIds
                : blockedUserIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            allowGroupChats: null == allowGroupChats
                ? _value.allowGroupChats
                : allowGroupChats // ignore: cast_nullable_to_non_nullable
                      as bool,
            allowVoiceMessages: null == allowVoiceMessages
                ? _value.allowVoiceMessages
                : allowVoiceMessages // ignore: cast_nullable_to_non_nullable
                      as bool,
            allowVideoCalls: null == allowVideoCalls
                ? _value.allowVideoCalls
                : allowVideoCalls // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ChatPrivacySettingsImplCopyWith<$Res>
    implements $ChatPrivacySettingsCopyWith<$Res> {
  factory _$$ChatPrivacySettingsImplCopyWith(
    _$ChatPrivacySettingsImpl value,
    $Res Function(_$ChatPrivacySettingsImpl) then,
  ) = __$$ChatPrivacySettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    bool blockNonFollowers,
    bool autoDeleteAfterReading,
    List<String> mutedConversationIds,
    List<String> blockedUserIds,
    bool allowGroupChats,
    bool allowVoiceMessages,
    bool allowVideoCalls,
  });
}

/// @nodoc
class __$$ChatPrivacySettingsImplCopyWithImpl<$Res>
    extends _$ChatPrivacySettingsCopyWithImpl<$Res, _$ChatPrivacySettingsImpl>
    implements _$$ChatPrivacySettingsImplCopyWith<$Res> {
  __$$ChatPrivacySettingsImplCopyWithImpl(
    _$ChatPrivacySettingsImpl _value,
    $Res Function(_$ChatPrivacySettingsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ChatPrivacySettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? blockNonFollowers = null,
    Object? autoDeleteAfterReading = null,
    Object? mutedConversationIds = null,
    Object? blockedUserIds = null,
    Object? allowGroupChats = null,
    Object? allowVoiceMessages = null,
    Object? allowVideoCalls = null,
  }) {
    return _then(
      _$ChatPrivacySettingsImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        blockNonFollowers: null == blockNonFollowers
            ? _value.blockNonFollowers
            : blockNonFollowers // ignore: cast_nullable_to_non_nullable
                  as bool,
        autoDeleteAfterReading: null == autoDeleteAfterReading
            ? _value.autoDeleteAfterReading
            : autoDeleteAfterReading // ignore: cast_nullable_to_non_nullable
                  as bool,
        mutedConversationIds: null == mutedConversationIds
            ? _value._mutedConversationIds
            : mutedConversationIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        blockedUserIds: null == blockedUserIds
            ? _value._blockedUserIds
            : blockedUserIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        allowGroupChats: null == allowGroupChats
            ? _value.allowGroupChats
            : allowGroupChats // ignore: cast_nullable_to_non_nullable
                  as bool,
        allowVoiceMessages: null == allowVoiceMessages
            ? _value.allowVoiceMessages
            : allowVoiceMessages // ignore: cast_nullable_to_non_nullable
                  as bool,
        allowVideoCalls: null == allowVideoCalls
            ? _value.allowVideoCalls
            : allowVideoCalls // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ChatPrivacySettingsImpl implements _ChatPrivacySettings {
  const _$ChatPrivacySettingsImpl({
    required this.id,
    required this.blockNonFollowers,
    required this.autoDeleteAfterReading,
    required final List<String> mutedConversationIds,
    required final List<String> blockedUserIds,
    required this.allowGroupChats,
    required this.allowVoiceMessages,
    required this.allowVideoCalls,
  }) : _mutedConversationIds = mutedConversationIds,
       _blockedUserIds = blockedUserIds;

  factory _$ChatPrivacySettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChatPrivacySettingsImplFromJson(json);

  @override
  final String id;
  @override
  final bool blockNonFollowers;
  @override
  final bool autoDeleteAfterReading;
  final List<String> _mutedConversationIds;
  @override
  List<String> get mutedConversationIds {
    if (_mutedConversationIds is EqualUnmodifiableListView)
      return _mutedConversationIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_mutedConversationIds);
  }

  final List<String> _blockedUserIds;
  @override
  List<String> get blockedUserIds {
    if (_blockedUserIds is EqualUnmodifiableListView) return _blockedUserIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_blockedUserIds);
  }

  @override
  final bool allowGroupChats;
  @override
  final bool allowVoiceMessages;
  @override
  final bool allowVideoCalls;

  @override
  String toString() {
    return 'ChatPrivacySettings(id: $id, blockNonFollowers: $blockNonFollowers, autoDeleteAfterReading: $autoDeleteAfterReading, mutedConversationIds: $mutedConversationIds, blockedUserIds: $blockedUserIds, allowGroupChats: $allowGroupChats, allowVoiceMessages: $allowVoiceMessages, allowVideoCalls: $allowVideoCalls)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChatPrivacySettingsImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.blockNonFollowers, blockNonFollowers) ||
                other.blockNonFollowers == blockNonFollowers) &&
            (identical(other.autoDeleteAfterReading, autoDeleteAfterReading) ||
                other.autoDeleteAfterReading == autoDeleteAfterReading) &&
            const DeepCollectionEquality().equals(
              other._mutedConversationIds,
              _mutedConversationIds,
            ) &&
            const DeepCollectionEquality().equals(
              other._blockedUserIds,
              _blockedUserIds,
            ) &&
            (identical(other.allowGroupChats, allowGroupChats) ||
                other.allowGroupChats == allowGroupChats) &&
            (identical(other.allowVoiceMessages, allowVoiceMessages) ||
                other.allowVoiceMessages == allowVoiceMessages) &&
            (identical(other.allowVideoCalls, allowVideoCalls) ||
                other.allowVideoCalls == allowVideoCalls));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    blockNonFollowers,
    autoDeleteAfterReading,
    const DeepCollectionEquality().hash(_mutedConversationIds),
    const DeepCollectionEquality().hash(_blockedUserIds),
    allowGroupChats,
    allowVoiceMessages,
    allowVideoCalls,
  );

  /// Create a copy of ChatPrivacySettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChatPrivacySettingsImplCopyWith<_$ChatPrivacySettingsImpl> get copyWith =>
      __$$ChatPrivacySettingsImplCopyWithImpl<_$ChatPrivacySettingsImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ChatPrivacySettingsImplToJson(this);
  }
}

abstract class _ChatPrivacySettings implements ChatPrivacySettings {
  const factory _ChatPrivacySettings({
    required final String id,
    required final bool blockNonFollowers,
    required final bool autoDeleteAfterReading,
    required final List<String> mutedConversationIds,
    required final List<String> blockedUserIds,
    required final bool allowGroupChats,
    required final bool allowVoiceMessages,
    required final bool allowVideoCalls,
  }) = _$ChatPrivacySettingsImpl;

  factory _ChatPrivacySettings.fromJson(Map<String, dynamic> json) =
      _$ChatPrivacySettingsImpl.fromJson;

  @override
  String get id;
  @override
  bool get blockNonFollowers;
  @override
  bool get autoDeleteAfterReading;
  @override
  List<String> get mutedConversationIds;
  @override
  List<String> get blockedUserIds;
  @override
  bool get allowGroupChats;
  @override
  bool get allowVoiceMessages;
  @override
  bool get allowVideoCalls;

  /// Create a copy of ChatPrivacySettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChatPrivacySettingsImplCopyWith<_$ChatPrivacySettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
