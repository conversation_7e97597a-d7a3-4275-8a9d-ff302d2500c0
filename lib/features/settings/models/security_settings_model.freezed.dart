// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'security_settings_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

SecuritySettings _$SecuritySettingsFromJson(Map<String, dynamic> json) {
  return _SecuritySettings.fromJson(json);
}

/// @nodoc
mixin _$SecuritySettings {
  bool get isTwoFactorEnabled => throw _privateConstructorUsedError;
  bool get isBiometricLoginEnabled => throw _privateConstructorUsedError;

  /// Serializes this SecuritySettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SecuritySettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SecuritySettingsCopyWith<SecuritySettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SecuritySettingsCopyWith<$Res> {
  factory $SecuritySettingsCopyWith(
    SecuritySettings value,
    $Res Function(SecuritySettings) then,
  ) = _$SecuritySettingsCopyWithImpl<$Res, SecuritySettings>;
  @useResult
  $Res call({bool isTwoFactorEnabled, bool isBiometricLoginEnabled});
}

/// @nodoc
class _$SecuritySettingsCopyWithImpl<$Res, $Val extends SecuritySettings>
    implements $SecuritySettingsCopyWith<$Res> {
  _$SecuritySettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SecuritySettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isTwoFactorEnabled = null,
    Object? isBiometricLoginEnabled = null,
  }) {
    return _then(
      _value.copyWith(
            isTwoFactorEnabled: null == isTwoFactorEnabled
                ? _value.isTwoFactorEnabled
                : isTwoFactorEnabled // ignore: cast_nullable_to_non_nullable
                      as bool,
            isBiometricLoginEnabled: null == isBiometricLoginEnabled
                ? _value.isBiometricLoginEnabled
                : isBiometricLoginEnabled // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$SecuritySettingsImplCopyWith<$Res>
    implements $SecuritySettingsCopyWith<$Res> {
  factory _$$SecuritySettingsImplCopyWith(
    _$SecuritySettingsImpl value,
    $Res Function(_$SecuritySettingsImpl) then,
  ) = __$$SecuritySettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isTwoFactorEnabled, bool isBiometricLoginEnabled});
}

/// @nodoc
class __$$SecuritySettingsImplCopyWithImpl<$Res>
    extends _$SecuritySettingsCopyWithImpl<$Res, _$SecuritySettingsImpl>
    implements _$$SecuritySettingsImplCopyWith<$Res> {
  __$$SecuritySettingsImplCopyWithImpl(
    _$SecuritySettingsImpl _value,
    $Res Function(_$SecuritySettingsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of SecuritySettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isTwoFactorEnabled = null,
    Object? isBiometricLoginEnabled = null,
  }) {
    return _then(
      _$SecuritySettingsImpl(
        isTwoFactorEnabled: null == isTwoFactorEnabled
            ? _value.isTwoFactorEnabled
            : isTwoFactorEnabled // ignore: cast_nullable_to_non_nullable
                  as bool,
        isBiometricLoginEnabled: null == isBiometricLoginEnabled
            ? _value.isBiometricLoginEnabled
            : isBiometricLoginEnabled // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$SecuritySettingsImpl implements _SecuritySettings {
  const _$SecuritySettingsImpl({
    required this.isTwoFactorEnabled,
    required this.isBiometricLoginEnabled,
  });

  factory _$SecuritySettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$SecuritySettingsImplFromJson(json);

  @override
  final bool isTwoFactorEnabled;
  @override
  final bool isBiometricLoginEnabled;

  @override
  String toString() {
    return 'SecuritySettings(isTwoFactorEnabled: $isTwoFactorEnabled, isBiometricLoginEnabled: $isBiometricLoginEnabled)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SecuritySettingsImpl &&
            (identical(other.isTwoFactorEnabled, isTwoFactorEnabled) ||
                other.isTwoFactorEnabled == isTwoFactorEnabled) &&
            (identical(
                  other.isBiometricLoginEnabled,
                  isBiometricLoginEnabled,
                ) ||
                other.isBiometricLoginEnabled == isBiometricLoginEnabled));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, isTwoFactorEnabled, isBiometricLoginEnabled);

  /// Create a copy of SecuritySettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SecuritySettingsImplCopyWith<_$SecuritySettingsImpl> get copyWith =>
      __$$SecuritySettingsImplCopyWithImpl<_$SecuritySettingsImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$SecuritySettingsImplToJson(this);
  }
}

abstract class _SecuritySettings implements SecuritySettings {
  const factory _SecuritySettings({
    required final bool isTwoFactorEnabled,
    required final bool isBiometricLoginEnabled,
  }) = _$SecuritySettingsImpl;

  factory _SecuritySettings.fromJson(Map<String, dynamic> json) =
      _$SecuritySettingsImpl.fromJson;

  @override
  bool get isTwoFactorEnabled;
  @override
  bool get isBiometricLoginEnabled;

  /// Create a copy of SecuritySettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SecuritySettingsImplCopyWith<_$SecuritySettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
