// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'privacy_settings_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

PrivacySettings _$PrivacySettingsFromJson(Map<String, dynamic> json) {
  return _PrivacySettings.fromJson(json);
}

/// @nodoc
mixin _$PrivacySettings {
  bool get isPrivate => throw _privateConstructorUsedError;
  List<String> get blockedAccountIds =>
      throw _privateConstructorUsedError; // Account Visibility Features
  bool get hideFollowersList => throw _privateConstructorUsedError;
  bool get hideFollowingList => throw _privateConstructorUsedError;
  bool get hideFromSearch => throw _privateConstructorUsedError;
  bool get hideFromSuggestions => throw _privateConstructorUsedError;
  bool get hideFromExplore => throw _privateConstructorUsedError;
  bool get allowExistingFollowersToSeeProfile =>
      throw _privateConstructorUsedError; // Controls whether the user can be found by photo search
  bool get discoverableByPhoto =>
      throw _privateConstructorUsedError; // Advanced privacy controls
  String get privacyLevel => throw _privateConstructorUsedError;
  bool get temporarilyHidden => throw _privateConstructorUsedError;
  bool get enableProfileViewHistory => throw _privateConstructorUsedError;

  /// Serializes this PrivacySettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PrivacySettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PrivacySettingsCopyWith<PrivacySettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PrivacySettingsCopyWith<$Res> {
  factory $PrivacySettingsCopyWith(
    PrivacySettings value,
    $Res Function(PrivacySettings) then,
  ) = _$PrivacySettingsCopyWithImpl<$Res, PrivacySettings>;
  @useResult
  $Res call({
    bool isPrivate,
    List<String> blockedAccountIds,
    bool hideFollowersList,
    bool hideFollowingList,
    bool hideFromSearch,
    bool hideFromSuggestions,
    bool hideFromExplore,
    bool allowExistingFollowersToSeeProfile,
    bool discoverableByPhoto,
    String privacyLevel,
    bool temporarilyHidden,
    bool enableProfileViewHistory,
  });
}

/// @nodoc
class _$PrivacySettingsCopyWithImpl<$Res, $Val extends PrivacySettings>
    implements $PrivacySettingsCopyWith<$Res> {
  _$PrivacySettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PrivacySettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isPrivate = null,
    Object? blockedAccountIds = null,
    Object? hideFollowersList = null,
    Object? hideFollowingList = null,
    Object? hideFromSearch = null,
    Object? hideFromSuggestions = null,
    Object? hideFromExplore = null,
    Object? allowExistingFollowersToSeeProfile = null,
    Object? discoverableByPhoto = null,
    Object? privacyLevel = null,
    Object? temporarilyHidden = null,
    Object? enableProfileViewHistory = null,
  }) {
    return _then(
      _value.copyWith(
            isPrivate: null == isPrivate
                ? _value.isPrivate
                : isPrivate // ignore: cast_nullable_to_non_nullable
                      as bool,
            blockedAccountIds: null == blockedAccountIds
                ? _value.blockedAccountIds
                : blockedAccountIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            hideFollowersList: null == hideFollowersList
                ? _value.hideFollowersList
                : hideFollowersList // ignore: cast_nullable_to_non_nullable
                      as bool,
            hideFollowingList: null == hideFollowingList
                ? _value.hideFollowingList
                : hideFollowingList // ignore: cast_nullable_to_non_nullable
                      as bool,
            hideFromSearch: null == hideFromSearch
                ? _value.hideFromSearch
                : hideFromSearch // ignore: cast_nullable_to_non_nullable
                      as bool,
            hideFromSuggestions: null == hideFromSuggestions
                ? _value.hideFromSuggestions
                : hideFromSuggestions // ignore: cast_nullable_to_non_nullable
                      as bool,
            hideFromExplore: null == hideFromExplore
                ? _value.hideFromExplore
                : hideFromExplore // ignore: cast_nullable_to_non_nullable
                      as bool,
            allowExistingFollowersToSeeProfile:
                null == allowExistingFollowersToSeeProfile
                ? _value.allowExistingFollowersToSeeProfile
                : allowExistingFollowersToSeeProfile // ignore: cast_nullable_to_non_nullable
                      as bool,
            discoverableByPhoto: null == discoverableByPhoto
                ? _value.discoverableByPhoto
                : discoverableByPhoto // ignore: cast_nullable_to_non_nullable
                      as bool,
            privacyLevel: null == privacyLevel
                ? _value.privacyLevel
                : privacyLevel // ignore: cast_nullable_to_non_nullable
                      as String,
            temporarilyHidden: null == temporarilyHidden
                ? _value.temporarilyHidden
                : temporarilyHidden // ignore: cast_nullable_to_non_nullable
                      as bool,
            enableProfileViewHistory: null == enableProfileViewHistory
                ? _value.enableProfileViewHistory
                : enableProfileViewHistory // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PrivacySettingsImplCopyWith<$Res>
    implements $PrivacySettingsCopyWith<$Res> {
  factory _$$PrivacySettingsImplCopyWith(
    _$PrivacySettingsImpl value,
    $Res Function(_$PrivacySettingsImpl) then,
  ) = __$$PrivacySettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    bool isPrivate,
    List<String> blockedAccountIds,
    bool hideFollowersList,
    bool hideFollowingList,
    bool hideFromSearch,
    bool hideFromSuggestions,
    bool hideFromExplore,
    bool allowExistingFollowersToSeeProfile,
    bool discoverableByPhoto,
    String privacyLevel,
    bool temporarilyHidden,
    bool enableProfileViewHistory,
  });
}

/// @nodoc
class __$$PrivacySettingsImplCopyWithImpl<$Res>
    extends _$PrivacySettingsCopyWithImpl<$Res, _$PrivacySettingsImpl>
    implements _$$PrivacySettingsImplCopyWith<$Res> {
  __$$PrivacySettingsImplCopyWithImpl(
    _$PrivacySettingsImpl _value,
    $Res Function(_$PrivacySettingsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PrivacySettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isPrivate = null,
    Object? blockedAccountIds = null,
    Object? hideFollowersList = null,
    Object? hideFollowingList = null,
    Object? hideFromSearch = null,
    Object? hideFromSuggestions = null,
    Object? hideFromExplore = null,
    Object? allowExistingFollowersToSeeProfile = null,
    Object? discoverableByPhoto = null,
    Object? privacyLevel = null,
    Object? temporarilyHidden = null,
    Object? enableProfileViewHistory = null,
  }) {
    return _then(
      _$PrivacySettingsImpl(
        isPrivate: null == isPrivate
            ? _value.isPrivate
            : isPrivate // ignore: cast_nullable_to_non_nullable
                  as bool,
        blockedAccountIds: null == blockedAccountIds
            ? _value._blockedAccountIds
            : blockedAccountIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        hideFollowersList: null == hideFollowersList
            ? _value.hideFollowersList
            : hideFollowersList // ignore: cast_nullable_to_non_nullable
                  as bool,
        hideFollowingList: null == hideFollowingList
            ? _value.hideFollowingList
            : hideFollowingList // ignore: cast_nullable_to_non_nullable
                  as bool,
        hideFromSearch: null == hideFromSearch
            ? _value.hideFromSearch
            : hideFromSearch // ignore: cast_nullable_to_non_nullable
                  as bool,
        hideFromSuggestions: null == hideFromSuggestions
            ? _value.hideFromSuggestions
            : hideFromSuggestions // ignore: cast_nullable_to_non_nullable
                  as bool,
        hideFromExplore: null == hideFromExplore
            ? _value.hideFromExplore
            : hideFromExplore // ignore: cast_nullable_to_non_nullable
                  as bool,
        allowExistingFollowersToSeeProfile:
            null == allowExistingFollowersToSeeProfile
            ? _value.allowExistingFollowersToSeeProfile
            : allowExistingFollowersToSeeProfile // ignore: cast_nullable_to_non_nullable
                  as bool,
        discoverableByPhoto: null == discoverableByPhoto
            ? _value.discoverableByPhoto
            : discoverableByPhoto // ignore: cast_nullable_to_non_nullable
                  as bool,
        privacyLevel: null == privacyLevel
            ? _value.privacyLevel
            : privacyLevel // ignore: cast_nullable_to_non_nullable
                  as String,
        temporarilyHidden: null == temporarilyHidden
            ? _value.temporarilyHidden
            : temporarilyHidden // ignore: cast_nullable_to_non_nullable
                  as bool,
        enableProfileViewHistory: null == enableProfileViewHistory
            ? _value.enableProfileViewHistory
            : enableProfileViewHistory // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PrivacySettingsImpl implements _PrivacySettings {
  const _$PrivacySettingsImpl({
    required this.isPrivate,
    required final List<String> blockedAccountIds,
    required this.hideFollowersList,
    required this.hideFollowingList,
    required this.hideFromSearch,
    required this.hideFromSuggestions,
    required this.hideFromExplore,
    required this.allowExistingFollowersToSeeProfile,
    required this.discoverableByPhoto,
    this.privacyLevel = 'public',
    this.temporarilyHidden = false,
    this.enableProfileViewHistory = false,
  }) : _blockedAccountIds = blockedAccountIds;

  factory _$PrivacySettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$PrivacySettingsImplFromJson(json);

  @override
  final bool isPrivate;
  final List<String> _blockedAccountIds;
  @override
  List<String> get blockedAccountIds {
    if (_blockedAccountIds is EqualUnmodifiableListView)
      return _blockedAccountIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_blockedAccountIds);
  }

  // Account Visibility Features
  @override
  final bool hideFollowersList;
  @override
  final bool hideFollowingList;
  @override
  final bool hideFromSearch;
  @override
  final bool hideFromSuggestions;
  @override
  final bool hideFromExplore;
  @override
  final bool allowExistingFollowersToSeeProfile;
  // Controls whether the user can be found by photo search
  @override
  final bool discoverableByPhoto;
  // Advanced privacy controls
  @override
  @JsonKey()
  final String privacyLevel;
  @override
  @JsonKey()
  final bool temporarilyHidden;
  @override
  @JsonKey()
  final bool enableProfileViewHistory;

  @override
  String toString() {
    return 'PrivacySettings(isPrivate: $isPrivate, blockedAccountIds: $blockedAccountIds, hideFollowersList: $hideFollowersList, hideFollowingList: $hideFollowingList, hideFromSearch: $hideFromSearch, hideFromSuggestions: $hideFromSuggestions, hideFromExplore: $hideFromExplore, allowExistingFollowersToSeeProfile: $allowExistingFollowersToSeeProfile, discoverableByPhoto: $discoverableByPhoto, privacyLevel: $privacyLevel, temporarilyHidden: $temporarilyHidden, enableProfileViewHistory: $enableProfileViewHistory)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PrivacySettingsImpl &&
            (identical(other.isPrivate, isPrivate) ||
                other.isPrivate == isPrivate) &&
            const DeepCollectionEquality().equals(
              other._blockedAccountIds,
              _blockedAccountIds,
            ) &&
            (identical(other.hideFollowersList, hideFollowersList) ||
                other.hideFollowersList == hideFollowersList) &&
            (identical(other.hideFollowingList, hideFollowingList) ||
                other.hideFollowingList == hideFollowingList) &&
            (identical(other.hideFromSearch, hideFromSearch) ||
                other.hideFromSearch == hideFromSearch) &&
            (identical(other.hideFromSuggestions, hideFromSuggestions) ||
                other.hideFromSuggestions == hideFromSuggestions) &&
            (identical(other.hideFromExplore, hideFromExplore) ||
                other.hideFromExplore == hideFromExplore) &&
            (identical(
                  other.allowExistingFollowersToSeeProfile,
                  allowExistingFollowersToSeeProfile,
                ) ||
                other.allowExistingFollowersToSeeProfile ==
                    allowExistingFollowersToSeeProfile) &&
            (identical(other.discoverableByPhoto, discoverableByPhoto) ||
                other.discoverableByPhoto == discoverableByPhoto) &&
            (identical(other.privacyLevel, privacyLevel) ||
                other.privacyLevel == privacyLevel) &&
            (identical(other.temporarilyHidden, temporarilyHidden) ||
                other.temporarilyHidden == temporarilyHidden) &&
            (identical(
                  other.enableProfileViewHistory,
                  enableProfileViewHistory,
                ) ||
                other.enableProfileViewHistory == enableProfileViewHistory));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    isPrivate,
    const DeepCollectionEquality().hash(_blockedAccountIds),
    hideFollowersList,
    hideFollowingList,
    hideFromSearch,
    hideFromSuggestions,
    hideFromExplore,
    allowExistingFollowersToSeeProfile,
    discoverableByPhoto,
    privacyLevel,
    temporarilyHidden,
    enableProfileViewHistory,
  );

  /// Create a copy of PrivacySettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PrivacySettingsImplCopyWith<_$PrivacySettingsImpl> get copyWith =>
      __$$PrivacySettingsImplCopyWithImpl<_$PrivacySettingsImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$PrivacySettingsImplToJson(this);
  }
}

abstract class _PrivacySettings implements PrivacySettings {
  const factory _PrivacySettings({
    required final bool isPrivate,
    required final List<String> blockedAccountIds,
    required final bool hideFollowersList,
    required final bool hideFollowingList,
    required final bool hideFromSearch,
    required final bool hideFromSuggestions,
    required final bool hideFromExplore,
    required final bool allowExistingFollowersToSeeProfile,
    required final bool discoverableByPhoto,
    final String privacyLevel,
    final bool temporarilyHidden,
    final bool enableProfileViewHistory,
  }) = _$PrivacySettingsImpl;

  factory _PrivacySettings.fromJson(Map<String, dynamic> json) =
      _$PrivacySettingsImpl.fromJson;

  @override
  bool get isPrivate;
  @override
  List<String> get blockedAccountIds; // Account Visibility Features
  @override
  bool get hideFollowersList;
  @override
  bool get hideFollowingList;
  @override
  bool get hideFromSearch;
  @override
  bool get hideFromSuggestions;
  @override
  bool get hideFromExplore;
  @override
  bool get allowExistingFollowersToSeeProfile; // Controls whether the user can be found by photo search
  @override
  bool get discoverableByPhoto; // Advanced privacy controls
  @override
  String get privacyLevel;
  @override
  bool get temporarilyHidden;
  @override
  bool get enableProfileViewHistory;

  /// Create a copy of PrivacySettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PrivacySettingsImplCopyWith<_$PrivacySettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
