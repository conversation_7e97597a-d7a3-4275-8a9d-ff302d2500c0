// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_settings_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userSettingsManagerHash() =>
    r'8ed54e1ee261c5d42f89151b916384a7a9441509';

/// Provider for UserSettingsManager service
///
/// Copied from [userSettingsManager].
@ProviderFor(userSettingsManager)
final userSettingsManagerProvider =
    AutoDisposeProvider<UserSettingsManager>.internal(
      userSettingsManager,
      name: r'userSettingsManagerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$userSettingsManagerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UserSettingsManagerRef = AutoDisposeProviderRef<UserSettingsManager>;
String _$userSettingsHash() => r'2dba94590787adc7011115763fd0dac0c7713638';

/// Provider for current user's settings
///
/// Copied from [userSettings].
@ProviderFor(userSettings)
final userSettingsProvider =
    AutoDisposeFutureProvider<UserSettingsModel>.internal(
      userSettings,
      name: r'userSettingsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$userSettingsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UserSettingsRef = AutoDisposeFutureProviderRef<UserSettingsModel>;
String _$userSettingsByIdHash() => r'f786a497b6fcfe9e6096a8aa275604c10c7e769d';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider for user settings by user ID (for viewing other users' public settings)
///
/// Copied from [userSettingsById].
@ProviderFor(userSettingsById)
const userSettingsByIdProvider = UserSettingsByIdFamily();

/// Provider for user settings by user ID (for viewing other users' public settings)
///
/// Copied from [userSettingsById].
class UserSettingsByIdFamily extends Family<AsyncValue<UserSettingsModel>> {
  /// Provider for user settings by user ID (for viewing other users' public settings)
  ///
  /// Copied from [userSettingsById].
  const UserSettingsByIdFamily();

  /// Provider for user settings by user ID (for viewing other users' public settings)
  ///
  /// Copied from [userSettingsById].
  UserSettingsByIdProvider call(String userId) {
    return UserSettingsByIdProvider(userId);
  }

  @override
  UserSettingsByIdProvider getProviderOverride(
    covariant UserSettingsByIdProvider provider,
  ) {
    return call(provider.userId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'userSettingsByIdProvider';
}

/// Provider for user settings by user ID (for viewing other users' public settings)
///
/// Copied from [userSettingsById].
class UserSettingsByIdProvider
    extends AutoDisposeFutureProvider<UserSettingsModel> {
  /// Provider for user settings by user ID (for viewing other users' public settings)
  ///
  /// Copied from [userSettingsById].
  UserSettingsByIdProvider(String userId)
    : this._internal(
        (ref) => userSettingsById(ref as UserSettingsByIdRef, userId),
        from: userSettingsByIdProvider,
        name: r'userSettingsByIdProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$userSettingsByIdHash,
        dependencies: UserSettingsByIdFamily._dependencies,
        allTransitiveDependencies:
            UserSettingsByIdFamily._allTransitiveDependencies,
        userId: userId,
      );

  UserSettingsByIdProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  Override overrideWith(
    FutureOr<UserSettingsModel> Function(UserSettingsByIdRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: UserSettingsByIdProvider._internal(
        (ref) => create(ref as UserSettingsByIdRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<UserSettingsModel> createElement() {
    return _UserSettingsByIdProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is UserSettingsByIdProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin UserSettingsByIdRef on AutoDisposeFutureProviderRef<UserSettingsModel> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _UserSettingsByIdProviderElement
    extends AutoDisposeFutureProviderElement<UserSettingsModel>
    with UserSettingsByIdRef {
  _UserSettingsByIdProviderElement(super.provider);

  @override
  String get userId => (origin as UserSettingsByIdProvider).userId;
}

String _$userSettingsStreamHash() =>
    r'cf975f256a874d996c088c040a3e9d6cf80b4069';

/// Provider for real-time user settings stream
///
/// Copied from [userSettingsStream].
@ProviderFor(userSettingsStream)
final userSettingsStreamProvider =
    AutoDisposeStreamProvider<UserSettingsModel>.internal(
      userSettingsStream,
      name: r'userSettingsStreamProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$userSettingsStreamHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UserSettingsStreamRef = AutoDisposeStreamProviderRef<UserSettingsModel>;
String _$privacySettingsHash() => r'4f6a921589def9ee0e363063b8ee4bee11fb975a';

/// Provider for specific setting categories
///
/// Copied from [privacySettings].
@ProviderFor(privacySettings)
final privacySettingsProvider =
    AutoDisposeFutureProvider<Map<String, dynamic>>.internal(
      privacySettings,
      name: r'privacySettingsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$privacySettingsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PrivacySettingsRef = AutoDisposeFutureProviderRef<Map<String, dynamic>>;
String _$notificationSettingsHash() =>
    r'5a1a596ede89f6a32a5748fca3364468496b5bfc';

/// See also [notificationSettings].
@ProviderFor(notificationSettings)
final notificationSettingsProvider =
    AutoDisposeFutureProvider<Map<String, dynamic>>.internal(
      notificationSettings,
      name: r'notificationSettingsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$notificationSettingsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef NotificationSettingsRef =
    AutoDisposeFutureProviderRef<Map<String, dynamic>>;
String _$themeSettingsHash() => r'0e120af181a87e599f7b7cb8beba980ecf3d17c1';

/// See also [themeSettings].
@ProviderFor(themeSettings)
final themeSettingsProvider =
    AutoDisposeFutureProvider<Map<String, dynamic>>.internal(
      themeSettings,
      name: r'themeSettingsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$themeSettingsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ThemeSettingsRef = AutoDisposeFutureProviderRef<Map<String, dynamic>>;
String _$feedSettingsHash() => r'cff733c7929bd09b15fd9c3048ccdf0d4872da1e';

/// See also [feedSettings].
@ProviderFor(feedSettings)
final feedSettingsProvider =
    AutoDisposeFutureProvider<Map<String, dynamic>>.internal(
      feedSettings,
      name: r'feedSettingsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$feedSettingsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FeedSettingsRef = AutoDisposeFutureProviderRef<Map<String, dynamic>>;
String _$messagingSettingsHash() => r'f04885b37a80c3f7f3a8d0406b7d4aa0d0c69347';

/// See also [messagingSettings].
@ProviderFor(messagingSettings)
final messagingSettingsProvider =
    AutoDisposeFutureProvider<Map<String, dynamic>>.internal(
      messagingSettings,
      name: r'messagingSettingsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$messagingSettingsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef MessagingSettingsRef =
    AutoDisposeFutureProviderRef<Map<String, dynamic>>;
String _$verificationSettingsHash() =>
    r'71cb59c43aeced90a3b8d031cb1c8f575862afae';

/// See also [verificationSettings].
@ProviderFor(verificationSettings)
final verificationSettingsProvider =
    AutoDisposeFutureProvider<Map<String, dynamic>>.internal(
      verificationSettings,
      name: r'verificationSettingsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$verificationSettingsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef VerificationSettingsRef =
    AutoDisposeFutureProviderRef<Map<String, dynamic>>;
String _$securitySettingsHash() => r'34fd0d8cda4e111f6fc7e720e52ee71cf9c37ff9';

/// See also [securitySettings].
@ProviderFor(securitySettings)
final securitySettingsProvider =
    AutoDisposeFutureProvider<Map<String, dynamic>>.internal(
      securitySettings,
      name: r'securitySettingsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$securitySettingsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SecuritySettingsRef =
    AutoDisposeFutureProviderRef<Map<String, dynamic>>;
String _$accessibilitySettingsHash() =>
    r'1f87a1c265040017880e7cebc235a5dd5fb1a9b9';

/// See also [accessibilitySettings].
@ProviderFor(accessibilitySettings)
final accessibilitySettingsProvider =
    AutoDisposeFutureProvider<Map<String, dynamic>>.internal(
      accessibilitySettings,
      name: r'accessibilitySettingsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$accessibilitySettingsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AccessibilitySettingsRef =
    AutoDisposeFutureProviderRef<Map<String, dynamic>>;
String _$contentSettingsHash() => r'0b9cc0fad9d5a7d1418ebf749879711334a7e3e6';

/// See also [contentSettings].
@ProviderFor(contentSettings)
final contentSettingsProvider =
    AutoDisposeFutureProvider<Map<String, dynamic>>.internal(
      contentSettings,
      name: r'contentSettingsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$contentSettingsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ContentSettingsRef = AutoDisposeFutureProviderRef<Map<String, dynamic>>;
String _$localizationSettingsHash() =>
    r'd57bdfe4b6ffd8f63a3badc90d294db6a5b5d2ca';

/// See also [localizationSettings].
@ProviderFor(localizationSettings)
final localizationSettingsProvider =
    AutoDisposeFutureProvider<Map<String, dynamic>>.internal(
      localizationSettings,
      name: r'localizationSettingsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$localizationSettingsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef LocalizationSettingsRef =
    AutoDisposeFutureProviderRef<Map<String, dynamic>>;
String _$isPrivateAccountHash() => r'63ab02b9f0b7f601700faf8e9a1e4693e6c7c25e';

/// Convenience providers for common settings
///
/// Copied from [isPrivateAccount].
@ProviderFor(isPrivateAccount)
final isPrivateAccountProvider = AutoDisposeFutureProvider<bool>.internal(
  isPrivateAccount,
  name: r'isPrivateAccountProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isPrivateAccountHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsPrivateAccountRef = AutoDisposeFutureProviderRef<bool>;
String _$pushNotificationsEnabledHash() =>
    r'a153a6c0c6c270db2a119bc57ae13203e225a9bf';

/// See also [pushNotificationsEnabled].
@ProviderFor(pushNotificationsEnabled)
final pushNotificationsEnabledProvider =
    AutoDisposeFutureProvider<bool>.internal(
      pushNotificationsEnabled,
      name: r'pushNotificationsEnabledProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$pushNotificationsEnabledHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PushNotificationsEnabledRef = AutoDisposeFutureProviderRef<bool>;
String _$currentThemeHash() => r'4c1d07dcb244b5462f90cf393dc54577933d0dad';

/// See also [currentTheme].
@ProviderFor(currentTheme)
final currentThemeProvider = AutoDisposeFutureProvider<String>.internal(
  currentTheme,
  name: r'currentThemeProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentThemeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentThemeRef = AutoDisposeFutureProviderRef<String>;
String _$showVerificationBadgesHash() =>
    r'59b0f1176dc4a1dc6e7efa329cda92d327ada489';

/// See also [showVerificationBadges].
@ProviderFor(showVerificationBadges)
final showVerificationBadgesProvider = AutoDisposeFutureProvider<bool>.internal(
  showVerificationBadges,
  name: r'showVerificationBadgesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$showVerificationBadgesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ShowVerificationBadgesRef = AutoDisposeFutureProviderRef<bool>;
String _$feedDefaultFilterHash() => r'5967db7727463fd9828f396e28d49f7a56ce250c';

/// See also [feedDefaultFilter].
@ProviderFor(feedDefaultFilter)
final feedDefaultFilterProvider = AutoDisposeFutureProvider<String>.internal(
  feedDefaultFilter,
  name: r'feedDefaultFilterProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$feedDefaultFilterHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FeedDefaultFilterRef = AutoDisposeFutureProviderRef<String>;
String _$allowMessagesFromNonFollowersHash() =>
    r'89b83621b2103e58c2cd44494d8808732d1fda24';

/// See also [allowMessagesFromNonFollowers].
@ProviderFor(allowMessagesFromNonFollowers)
final allowMessagesFromNonFollowersProvider =
    AutoDisposeFutureProvider<bool>.internal(
      allowMessagesFromNonFollowers,
      name: r'allowMessagesFromNonFollowersProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$allowMessagesFromNonFollowersHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AllowMessagesFromNonFollowersRef = AutoDisposeFutureProviderRef<bool>;
String _$hasCustomizationsHash() => r'0b22a9c6821647f3168307ad99710581ec932105';

/// Provider for checking if user has customizations
///
/// Copied from [hasCustomizations].
@ProviderFor(hasCustomizations)
final hasCustomizationsProvider = AutoDisposeFutureProvider<bool>.internal(
  hasCustomizations,
  name: r'hasCustomizationsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$hasCustomizationsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef HasCustomizationsRef = AutoDisposeFutureProviderRef<bool>;
String _$customizedFieldsHash() => r'29fd4f01a4f04ef20539d3793c0b145b5885e092';

/// Provider for getting customized fields list
///
/// Copied from [customizedFields].
@ProviderFor(customizedFields)
final customizedFieldsProvider =
    AutoDisposeFutureProvider<List<String>>.internal(
      customizedFields,
      name: r'customizedFieldsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$customizedFieldsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CustomizedFieldsRef = AutoDisposeFutureProviderRef<List<String>>;
String _$needsDefaultsUpdateHash() =>
    r'3e84e5a714a5439361aa22be33f60e91eb078969';

/// Provider for checking if settings need update
///
/// Copied from [needsDefaultsUpdate].
@ProviderFor(needsDefaultsUpdate)
final needsDefaultsUpdateProvider = AutoDisposeFutureProvider<bool>.internal(
  needsDefaultsUpdate,
  name: r'needsDefaultsUpdateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$needsDefaultsUpdateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef NeedsDefaultsUpdateRef = AutoDisposeFutureProviderRef<bool>;
String _$userSettingsNotifierHash() =>
    r'cb71ed6ca6d793850237062781731620967fb628';

/// Notifier for managing user settings updates
///
/// Copied from [UserSettingsNotifier].
@ProviderFor(UserSettingsNotifier)
final userSettingsNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      UserSettingsNotifier,
      UserSettingsModel
    >.internal(
      UserSettingsNotifier.new,
      name: r'userSettingsNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$userSettingsNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$UserSettingsNotifier = AutoDisposeAsyncNotifier<UserSettingsModel>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
