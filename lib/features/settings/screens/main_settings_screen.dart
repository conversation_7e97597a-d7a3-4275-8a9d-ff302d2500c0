import 'package:billionaires_social/features/notifications/screens/notification_settings_screen.dart';
import 'package:billionaires_social/features/profile/screens/close_friends_screen.dart';
import 'package:billionaires_social/features/profile/screens/saved_content_screen.dart';
import 'package:billionaires_social/features/settings/screens/privacy_settings_screen.dart';
import 'package:billionaires_social/features/settings/screens/security_settings_screen.dart';
import 'package:billionaires_social/features/stories/screens/story_settings_screen.dart';
import 'package:billionaires_social/features/settings/screens/chat_privacy_screen.dart';
import 'package:billionaires_social/features/messaging/screens/chat_settings_screen.dart';
import 'package:billionaires_social/features/auth/screens/n8n_integration_test_screen.dart';
import 'package:billionaires_social/features/settings/screens/app_appearance_screen.dart';
import 'package:billionaires_social/features/settings/screens/language_selection_screen.dart';
import 'package:billionaires_social/features/settings/providers/language_provider.dart';
import 'package:billionaires_social/core/localization/app_localizations.dart';
import 'package:billionaires_social/core/localization/translation_manager.dart';
import 'package:billionaires_social/features/verification/screens/owner_verification_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'package:billionaires_social/core/services/session_management_service.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/debug/translation_debug_widget.dart';

class MainSettingsScreen extends ConsumerWidget {
  const MainSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.settings),
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
      ),
      body: ListView(
        children: [
          _buildSectionHeader('Account'),
          _SettingsTile(
            icon: FontAwesomeIcons.crown,
            title: 'Owner Verification',
            subtitle: 'Quick verification for app owner',
            onTap: () => _navigateTo(context, const OwnerVerificationScreen()),
          ),
          _SettingsTile(
            icon: FontAwesomeIcons.userShield,
            title: 'Account Privacy',
            onTap: () => _navigateTo(context, const PrivacySettingsScreen()),
          ),
          _SettingsTile(
            icon: FontAwesomeIcons.lock,
            title: 'Security',
            onTap: () => _navigateTo(context, const SecuritySettingsScreen()),
          ),
          _SettingsTile(
            icon: FontAwesomeIcons.comments,
            title: 'Chat Privacy',
            onTap: () => _navigateTo(context, const ChatPrivacyScreen()),
          ),
          _SettingsTile(
            icon: FontAwesomeIcons.gear,
            title: 'Chat Settings',
            onTap: () => _navigateTo(context, const ChatSettingsScreen()),
          ),
          _buildSectionHeader('Automation & Integration'),
          _SettingsTile(
            icon: FontAwesomeIcons.robot,
            title: 'n8n Workflow Automation',
            subtitle: 'Configure automated workflows and integrations',
            onTap: () => _navigateTo(context, const N8nIntegrationTestScreen()),
          ),
          _buildSectionHeader('Content & Display'),
          _SettingsTile(
            icon: FontAwesomeIcons.solidBookmark,
            title: 'Saved',
            onTap: () => _navigateTo(context, const SavedContentScreen()),
          ),
          _SettingsTile(
            icon: FontAwesomeIcons.solidStar,
            title: 'Close Friends',
            onTap: () => _navigateTo(context, const CloseFriendsScreen()),
          ),
          _SettingsTile(
            icon: FontAwesomeIcons.solidCirclePlay,
            title: 'Story Settings',
            onTap: () => _navigateTo(context, const StorySettingsScreen()),
          ),
          _SettingsTile(
            icon: FontAwesomeIcons.solidBell,
            title: 'Notifications',
            onTap: () =>
                _navigateTo(context, const NotificationSettingsScreen()),
          ),
          _SettingsTile(
            icon: FontAwesomeIcons.palette,
            title: 'App Appearance',
            onTap: () => _navigateTo(context, const AppAppearanceScreen()),
          ),
          _buildLanguageTile(context, ref),
          // Debug tile for translation testing
          _SettingsTile(
            icon: FontAwesomeIcons.bug,
            title: '🔍 Translation Debug',
            subtitle: 'Test Arabic translations',
            onTap: () => showTranslationDebug(context),
          ),
          _buildSectionHeader('Login'),
          _SettingsTile(
            icon: FontAwesomeIcons.arrowRightFromBracket,
            title: 'Log Out',
            onTap: () => _showLogoutDialog(context, ref),
            isDestructive: true,
          ),
        ],
      ),
    );
  }

  void _navigateTo(BuildContext context, Widget screen) {
    Navigator.of(context).push(MaterialPageRoute(builder: (_) => screen));
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
      child: Text(
        title.toUpperCase(),
        style: TextStyle(
          color: Colors.grey.shade600,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildLanguageTile(BuildContext context, WidgetRef ref) {
    final currentLocale = ref.watch(languageProvider);
    final localizations = AppLocalizations.of(context)!;

    return _SettingsTile(
      icon: FontAwesomeIcons.language,
      title: localizations.changeLanguage,
      subtitle: TranslationManager.getLanguageDisplayName(
        currentLocale.languageCode,
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            TranslationManager.getCountryFlag(
              currentLocale.countryCode ?? 'US',
            ),
            style: const TextStyle(fontSize: 20),
          ),
          const SizedBox(width: 8),
          const Icon(Icons.chevron_right, color: Colors.grey),
        ],
      ),
      onTap: () => _navigateTo(context, const LanguageSelectionScreen()),
    );
  }

  void _showLogoutDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Log Out'),
          content: const Text(
            'Are you sure you want to log out? This will clear all your session data and you\'ll need to sign in again.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _performLogout(context, ref);
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Log Out'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _performLogout(BuildContext context, WidgetRef ref) async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      // Use the centralized session management service
      final sessionService = getIt<SessionManagementService>();
      await sessionService.performComprehensiveLogout(ref);

      // Close loading dialog
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // Show success message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Successfully logged out'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ Error during logout: $e');

      // Close loading dialog
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // Show error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Logout failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

class _SettingsTile extends StatelessWidget {
  final IconData icon;
  final String title;
  final String? subtitle;
  final Widget? trailing;
  final VoidCallback onTap;
  final bool isDestructive;

  const _SettingsTile({
    required this.icon,
    required this.title,
    required this.onTap,
    this.subtitle,
    this.trailing,
    this.isDestructive = false,
  });

  @override
  Widget build(BuildContext context) {
    final color = isDestructive
        ? Colors.red
        : Theme.of(context).textTheme.bodyLarge?.color;

    return ListTile(
      leading: FaIcon(icon, color: color, size: 20),
      title: Text(title, style: TextStyle(color: color)),
      subtitle: subtitle != null
          ? Text(
              subtitle!,
              style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
            )
          : null,
      trailing:
          trailing ??
          FaIcon(
            FontAwesomeIcons.chevronRight,
            size: 16,
            color: color?.withValues(alpha: 0.5),
          ),
      onTap: onTap,
    );
  }
}
