// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'profile_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

ProfileModel _$ProfileModelFromJson(Map<String, dynamic> json) {
  return _ProfileModel.fromJson(json);
}

/// @nodoc
mixin _$ProfileModel {
  String get id => throw _privateConstructorUsedError;
  String get username => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get profilePictureUrl => throw _privateConstructorUsedError;
  String get bio => throw _privateConstructorUsedError;
  int get postCount => throw _privateConstructorUsedError;
  int get followerCount => throw _privateConstructorUsedError;
  int get followingCount =>
      throw _privateConstructorUsedError; // Enhanced fields for complete profile
  String? get bannerImageUrl => throw _privateConstructorUsedError;
  String? get website => throw _privateConstructorUsedError;
  String? get location => throw _privateConstructorUsedError;
  String? get phone => throw _privateConstructorUsedError;
  String? get email => throw _privateConstructorUsedError;
  bool get isPrivate => throw _privateConstructorUsedError;
  bool get allowMessages => throw _privateConstructorUsedError;
  bool get showActivityStatus => throw _privateConstructorUsedError;
  bool get isVerified => throw _privateConstructorUsedError;
  bool get isBillionaire => throw _privateConstructorUsedError;
  bool get isCelebrity => throw _privateConstructorUsedError;
  VerificationTier? get verificationTier => throw _privateConstructorUsedError;
  DateTime? get verifiedAt => throw _privateConstructorUsedError;
  String? get verifiedBy =>
      throw _privateConstructorUsedError; // Admin ID who verified
  String get userType => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt =>
      throw _privateConstructorUsedError; // Business Account fields for sellers
  bool get isBusinessAccount => throw _privateConstructorUsedError;
  String? get businessName => throw _privateConstructorUsedError;
  String? get businessLogoUrl => throw _privateConstructorUsedError;
  String? get businessEmail => throw _privateConstructorUsedError;
  String? get businessPhone => throw _privateConstructorUsedError;
  String? get businessDescription => throw _privateConstructorUsedError;
  bool get businessVerified => throw _privateConstructorUsedError;
  bool get businessExclusive => throw _privateConstructorUsedError;
  String? get businessCategory => throw _privateConstructorUsedError;
  String? get businessWebsite => throw _privateConstructorUsedError;
  String? get businessAddress => throw _privateConstructorUsedError;
  DateTime? get businessCreatedAt => throw _privateConstructorUsedError;
  DateTime? get businessVerifiedAt => throw _privateConstructorUsedError;
  bool get isAdmin => throw _privateConstructorUsedError;

  /// Serializes this ProfileModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProfileModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProfileModelCopyWith<ProfileModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProfileModelCopyWith<$Res> {
  factory $ProfileModelCopyWith(
    ProfileModel value,
    $Res Function(ProfileModel) then,
  ) = _$ProfileModelCopyWithImpl<$Res, ProfileModel>;
  @useResult
  $Res call({
    String id,
    String username,
    String name,
    String profilePictureUrl,
    String bio,
    int postCount,
    int followerCount,
    int followingCount,
    String? bannerImageUrl,
    String? website,
    String? location,
    String? phone,
    String? email,
    bool isPrivate,
    bool allowMessages,
    bool showActivityStatus,
    bool isVerified,
    bool isBillionaire,
    bool isCelebrity,
    VerificationTier? verificationTier,
    DateTime? verifiedAt,
    String? verifiedBy,
    String userType,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool isBusinessAccount,
    String? businessName,
    String? businessLogoUrl,
    String? businessEmail,
    String? businessPhone,
    String? businessDescription,
    bool businessVerified,
    bool businessExclusive,
    String? businessCategory,
    String? businessWebsite,
    String? businessAddress,
    DateTime? businessCreatedAt,
    DateTime? businessVerifiedAt,
    bool isAdmin,
  });
}

/// @nodoc
class _$ProfileModelCopyWithImpl<$Res, $Val extends ProfileModel>
    implements $ProfileModelCopyWith<$Res> {
  _$ProfileModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProfileModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? username = null,
    Object? name = null,
    Object? profilePictureUrl = null,
    Object? bio = null,
    Object? postCount = null,
    Object? followerCount = null,
    Object? followingCount = null,
    Object? bannerImageUrl = freezed,
    Object? website = freezed,
    Object? location = freezed,
    Object? phone = freezed,
    Object? email = freezed,
    Object? isPrivate = null,
    Object? allowMessages = null,
    Object? showActivityStatus = null,
    Object? isVerified = null,
    Object? isBillionaire = null,
    Object? isCelebrity = null,
    Object? verificationTier = freezed,
    Object? verifiedAt = freezed,
    Object? verifiedBy = freezed,
    Object? userType = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? isBusinessAccount = null,
    Object? businessName = freezed,
    Object? businessLogoUrl = freezed,
    Object? businessEmail = freezed,
    Object? businessPhone = freezed,
    Object? businessDescription = freezed,
    Object? businessVerified = null,
    Object? businessExclusive = null,
    Object? businessCategory = freezed,
    Object? businessWebsite = freezed,
    Object? businessAddress = freezed,
    Object? businessCreatedAt = freezed,
    Object? businessVerifiedAt = freezed,
    Object? isAdmin = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            username: null == username
                ? _value.username
                : username // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            profilePictureUrl: null == profilePictureUrl
                ? _value.profilePictureUrl
                : profilePictureUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            bio: null == bio
                ? _value.bio
                : bio // ignore: cast_nullable_to_non_nullable
                      as String,
            postCount: null == postCount
                ? _value.postCount
                : postCount // ignore: cast_nullable_to_non_nullable
                      as int,
            followerCount: null == followerCount
                ? _value.followerCount
                : followerCount // ignore: cast_nullable_to_non_nullable
                      as int,
            followingCount: null == followingCount
                ? _value.followingCount
                : followingCount // ignore: cast_nullable_to_non_nullable
                      as int,
            bannerImageUrl: freezed == bannerImageUrl
                ? _value.bannerImageUrl
                : bannerImageUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            website: freezed == website
                ? _value.website
                : website // ignore: cast_nullable_to_non_nullable
                      as String?,
            location: freezed == location
                ? _value.location
                : location // ignore: cast_nullable_to_non_nullable
                      as String?,
            phone: freezed == phone
                ? _value.phone
                : phone // ignore: cast_nullable_to_non_nullable
                      as String?,
            email: freezed == email
                ? _value.email
                : email // ignore: cast_nullable_to_non_nullable
                      as String?,
            isPrivate: null == isPrivate
                ? _value.isPrivate
                : isPrivate // ignore: cast_nullable_to_non_nullable
                      as bool,
            allowMessages: null == allowMessages
                ? _value.allowMessages
                : allowMessages // ignore: cast_nullable_to_non_nullable
                      as bool,
            showActivityStatus: null == showActivityStatus
                ? _value.showActivityStatus
                : showActivityStatus // ignore: cast_nullable_to_non_nullable
                      as bool,
            isVerified: null == isVerified
                ? _value.isVerified
                : isVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            isBillionaire: null == isBillionaire
                ? _value.isBillionaire
                : isBillionaire // ignore: cast_nullable_to_non_nullable
                      as bool,
            isCelebrity: null == isCelebrity
                ? _value.isCelebrity
                : isCelebrity // ignore: cast_nullable_to_non_nullable
                      as bool,
            verificationTier: freezed == verificationTier
                ? _value.verificationTier
                : verificationTier // ignore: cast_nullable_to_non_nullable
                      as VerificationTier?,
            verifiedAt: freezed == verifiedAt
                ? _value.verifiedAt
                : verifiedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            verifiedBy: freezed == verifiedBy
                ? _value.verifiedBy
                : verifiedBy // ignore: cast_nullable_to_non_nullable
                      as String?,
            userType: null == userType
                ? _value.userType
                : userType // ignore: cast_nullable_to_non_nullable
                      as String,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            isBusinessAccount: null == isBusinessAccount
                ? _value.isBusinessAccount
                : isBusinessAccount // ignore: cast_nullable_to_non_nullable
                      as bool,
            businessName: freezed == businessName
                ? _value.businessName
                : businessName // ignore: cast_nullable_to_non_nullable
                      as String?,
            businessLogoUrl: freezed == businessLogoUrl
                ? _value.businessLogoUrl
                : businessLogoUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            businessEmail: freezed == businessEmail
                ? _value.businessEmail
                : businessEmail // ignore: cast_nullable_to_non_nullable
                      as String?,
            businessPhone: freezed == businessPhone
                ? _value.businessPhone
                : businessPhone // ignore: cast_nullable_to_non_nullable
                      as String?,
            businessDescription: freezed == businessDescription
                ? _value.businessDescription
                : businessDescription // ignore: cast_nullable_to_non_nullable
                      as String?,
            businessVerified: null == businessVerified
                ? _value.businessVerified
                : businessVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            businessExclusive: null == businessExclusive
                ? _value.businessExclusive
                : businessExclusive // ignore: cast_nullable_to_non_nullable
                      as bool,
            businessCategory: freezed == businessCategory
                ? _value.businessCategory
                : businessCategory // ignore: cast_nullable_to_non_nullable
                      as String?,
            businessWebsite: freezed == businessWebsite
                ? _value.businessWebsite
                : businessWebsite // ignore: cast_nullable_to_non_nullable
                      as String?,
            businessAddress: freezed == businessAddress
                ? _value.businessAddress
                : businessAddress // ignore: cast_nullable_to_non_nullable
                      as String?,
            businessCreatedAt: freezed == businessCreatedAt
                ? _value.businessCreatedAt
                : businessCreatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            businessVerifiedAt: freezed == businessVerifiedAt
                ? _value.businessVerifiedAt
                : businessVerifiedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            isAdmin: null == isAdmin
                ? _value.isAdmin
                : isAdmin // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ProfileModelImplCopyWith<$Res>
    implements $ProfileModelCopyWith<$Res> {
  factory _$$ProfileModelImplCopyWith(
    _$ProfileModelImpl value,
    $Res Function(_$ProfileModelImpl) then,
  ) = __$$ProfileModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String username,
    String name,
    String profilePictureUrl,
    String bio,
    int postCount,
    int followerCount,
    int followingCount,
    String? bannerImageUrl,
    String? website,
    String? location,
    String? phone,
    String? email,
    bool isPrivate,
    bool allowMessages,
    bool showActivityStatus,
    bool isVerified,
    bool isBillionaire,
    bool isCelebrity,
    VerificationTier? verificationTier,
    DateTime? verifiedAt,
    String? verifiedBy,
    String userType,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool isBusinessAccount,
    String? businessName,
    String? businessLogoUrl,
    String? businessEmail,
    String? businessPhone,
    String? businessDescription,
    bool businessVerified,
    bool businessExclusive,
    String? businessCategory,
    String? businessWebsite,
    String? businessAddress,
    DateTime? businessCreatedAt,
    DateTime? businessVerifiedAt,
    bool isAdmin,
  });
}

/// @nodoc
class __$$ProfileModelImplCopyWithImpl<$Res>
    extends _$ProfileModelCopyWithImpl<$Res, _$ProfileModelImpl>
    implements _$$ProfileModelImplCopyWith<$Res> {
  __$$ProfileModelImplCopyWithImpl(
    _$ProfileModelImpl _value,
    $Res Function(_$ProfileModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ProfileModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? username = null,
    Object? name = null,
    Object? profilePictureUrl = null,
    Object? bio = null,
    Object? postCount = null,
    Object? followerCount = null,
    Object? followingCount = null,
    Object? bannerImageUrl = freezed,
    Object? website = freezed,
    Object? location = freezed,
    Object? phone = freezed,
    Object? email = freezed,
    Object? isPrivate = null,
    Object? allowMessages = null,
    Object? showActivityStatus = null,
    Object? isVerified = null,
    Object? isBillionaire = null,
    Object? isCelebrity = null,
    Object? verificationTier = freezed,
    Object? verifiedAt = freezed,
    Object? verifiedBy = freezed,
    Object? userType = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? isBusinessAccount = null,
    Object? businessName = freezed,
    Object? businessLogoUrl = freezed,
    Object? businessEmail = freezed,
    Object? businessPhone = freezed,
    Object? businessDescription = freezed,
    Object? businessVerified = null,
    Object? businessExclusive = null,
    Object? businessCategory = freezed,
    Object? businessWebsite = freezed,
    Object? businessAddress = freezed,
    Object? businessCreatedAt = freezed,
    Object? businessVerifiedAt = freezed,
    Object? isAdmin = null,
  }) {
    return _then(
      _$ProfileModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        username: null == username
            ? _value.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        profilePictureUrl: null == profilePictureUrl
            ? _value.profilePictureUrl
            : profilePictureUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        bio: null == bio
            ? _value.bio
            : bio // ignore: cast_nullable_to_non_nullable
                  as String,
        postCount: null == postCount
            ? _value.postCount
            : postCount // ignore: cast_nullable_to_non_nullable
                  as int,
        followerCount: null == followerCount
            ? _value.followerCount
            : followerCount // ignore: cast_nullable_to_non_nullable
                  as int,
        followingCount: null == followingCount
            ? _value.followingCount
            : followingCount // ignore: cast_nullable_to_non_nullable
                  as int,
        bannerImageUrl: freezed == bannerImageUrl
            ? _value.bannerImageUrl
            : bannerImageUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        website: freezed == website
            ? _value.website
            : website // ignore: cast_nullable_to_non_nullable
                  as String?,
        location: freezed == location
            ? _value.location
            : location // ignore: cast_nullable_to_non_nullable
                  as String?,
        phone: freezed == phone
            ? _value.phone
            : phone // ignore: cast_nullable_to_non_nullable
                  as String?,
        email: freezed == email
            ? _value.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String?,
        isPrivate: null == isPrivate
            ? _value.isPrivate
            : isPrivate // ignore: cast_nullable_to_non_nullable
                  as bool,
        allowMessages: null == allowMessages
            ? _value.allowMessages
            : allowMessages // ignore: cast_nullable_to_non_nullable
                  as bool,
        showActivityStatus: null == showActivityStatus
            ? _value.showActivityStatus
            : showActivityStatus // ignore: cast_nullable_to_non_nullable
                  as bool,
        isVerified: null == isVerified
            ? _value.isVerified
            : isVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        isBillionaire: null == isBillionaire
            ? _value.isBillionaire
            : isBillionaire // ignore: cast_nullable_to_non_nullable
                  as bool,
        isCelebrity: null == isCelebrity
            ? _value.isCelebrity
            : isCelebrity // ignore: cast_nullable_to_non_nullable
                  as bool,
        verificationTier: freezed == verificationTier
            ? _value.verificationTier
            : verificationTier // ignore: cast_nullable_to_non_nullable
                  as VerificationTier?,
        verifiedAt: freezed == verifiedAt
            ? _value.verifiedAt
            : verifiedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        verifiedBy: freezed == verifiedBy
            ? _value.verifiedBy
            : verifiedBy // ignore: cast_nullable_to_non_nullable
                  as String?,
        userType: null == userType
            ? _value.userType
            : userType // ignore: cast_nullable_to_non_nullable
                  as String,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        isBusinessAccount: null == isBusinessAccount
            ? _value.isBusinessAccount
            : isBusinessAccount // ignore: cast_nullable_to_non_nullable
                  as bool,
        businessName: freezed == businessName
            ? _value.businessName
            : businessName // ignore: cast_nullable_to_non_nullable
                  as String?,
        businessLogoUrl: freezed == businessLogoUrl
            ? _value.businessLogoUrl
            : businessLogoUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        businessEmail: freezed == businessEmail
            ? _value.businessEmail
            : businessEmail // ignore: cast_nullable_to_non_nullable
                  as String?,
        businessPhone: freezed == businessPhone
            ? _value.businessPhone
            : businessPhone // ignore: cast_nullable_to_non_nullable
                  as String?,
        businessDescription: freezed == businessDescription
            ? _value.businessDescription
            : businessDescription // ignore: cast_nullable_to_non_nullable
                  as String?,
        businessVerified: null == businessVerified
            ? _value.businessVerified
            : businessVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        businessExclusive: null == businessExclusive
            ? _value.businessExclusive
            : businessExclusive // ignore: cast_nullable_to_non_nullable
                  as bool,
        businessCategory: freezed == businessCategory
            ? _value.businessCategory
            : businessCategory // ignore: cast_nullable_to_non_nullable
                  as String?,
        businessWebsite: freezed == businessWebsite
            ? _value.businessWebsite
            : businessWebsite // ignore: cast_nullable_to_non_nullable
                  as String?,
        businessAddress: freezed == businessAddress
            ? _value.businessAddress
            : businessAddress // ignore: cast_nullable_to_non_nullable
                  as String?,
        businessCreatedAt: freezed == businessCreatedAt
            ? _value.businessCreatedAt
            : businessCreatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        businessVerifiedAt: freezed == businessVerifiedAt
            ? _value.businessVerifiedAt
            : businessVerifiedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        isAdmin: null == isAdmin
            ? _value.isAdmin
            : isAdmin // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ProfileModelImpl implements _ProfileModel {
  const _$ProfileModelImpl({
    required this.id,
    required this.username,
    required this.name,
    required this.profilePictureUrl,
    required this.bio,
    required this.postCount,
    required this.followerCount,
    required this.followingCount,
    this.bannerImageUrl,
    this.website,
    this.location,
    this.phone,
    this.email,
    this.isPrivate = false,
    this.allowMessages = true,
    this.showActivityStatus = true,
    this.isVerified = false,
    this.isBillionaire = false,
    this.isCelebrity = false,
    this.verificationTier,
    this.verifiedAt,
    this.verifiedBy,
    this.userType = 'regular',
    this.createdAt,
    this.updatedAt,
    this.isBusinessAccount = false,
    this.businessName,
    this.businessLogoUrl,
    this.businessEmail,
    this.businessPhone,
    this.businessDescription,
    this.businessVerified = false,
    this.businessExclusive = false,
    this.businessCategory,
    this.businessWebsite,
    this.businessAddress,
    this.businessCreatedAt,
    this.businessVerifiedAt,
    this.isAdmin = false,
  });

  factory _$ProfileModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProfileModelImplFromJson(json);

  @override
  final String id;
  @override
  final String username;
  @override
  final String name;
  @override
  final String profilePictureUrl;
  @override
  final String bio;
  @override
  final int postCount;
  @override
  final int followerCount;
  @override
  final int followingCount;
  // Enhanced fields for complete profile
  @override
  final String? bannerImageUrl;
  @override
  final String? website;
  @override
  final String? location;
  @override
  final String? phone;
  @override
  final String? email;
  @override
  @JsonKey()
  final bool isPrivate;
  @override
  @JsonKey()
  final bool allowMessages;
  @override
  @JsonKey()
  final bool showActivityStatus;
  @override
  @JsonKey()
  final bool isVerified;
  @override
  @JsonKey()
  final bool isBillionaire;
  @override
  @JsonKey()
  final bool isCelebrity;
  @override
  final VerificationTier? verificationTier;
  @override
  final DateTime? verifiedAt;
  @override
  final String? verifiedBy;
  // Admin ID who verified
  @override
  @JsonKey()
  final String userType;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;
  // Business Account fields for sellers
  @override
  @JsonKey()
  final bool isBusinessAccount;
  @override
  final String? businessName;
  @override
  final String? businessLogoUrl;
  @override
  final String? businessEmail;
  @override
  final String? businessPhone;
  @override
  final String? businessDescription;
  @override
  @JsonKey()
  final bool businessVerified;
  @override
  @JsonKey()
  final bool businessExclusive;
  @override
  final String? businessCategory;
  @override
  final String? businessWebsite;
  @override
  final String? businessAddress;
  @override
  final DateTime? businessCreatedAt;
  @override
  final DateTime? businessVerifiedAt;
  @override
  @JsonKey()
  final bool isAdmin;

  @override
  String toString() {
    return 'ProfileModel(id: $id, username: $username, name: $name, profilePictureUrl: $profilePictureUrl, bio: $bio, postCount: $postCount, followerCount: $followerCount, followingCount: $followingCount, bannerImageUrl: $bannerImageUrl, website: $website, location: $location, phone: $phone, email: $email, isPrivate: $isPrivate, allowMessages: $allowMessages, showActivityStatus: $showActivityStatus, isVerified: $isVerified, isBillionaire: $isBillionaire, isCelebrity: $isCelebrity, verificationTier: $verificationTier, verifiedAt: $verifiedAt, verifiedBy: $verifiedBy, userType: $userType, createdAt: $createdAt, updatedAt: $updatedAt, isBusinessAccount: $isBusinessAccount, businessName: $businessName, businessLogoUrl: $businessLogoUrl, businessEmail: $businessEmail, businessPhone: $businessPhone, businessDescription: $businessDescription, businessVerified: $businessVerified, businessExclusive: $businessExclusive, businessCategory: $businessCategory, businessWebsite: $businessWebsite, businessAddress: $businessAddress, businessCreatedAt: $businessCreatedAt, businessVerifiedAt: $businessVerifiedAt, isAdmin: $isAdmin)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.profilePictureUrl, profilePictureUrl) ||
                other.profilePictureUrl == profilePictureUrl) &&
            (identical(other.bio, bio) || other.bio == bio) &&
            (identical(other.postCount, postCount) ||
                other.postCount == postCount) &&
            (identical(other.followerCount, followerCount) ||
                other.followerCount == followerCount) &&
            (identical(other.followingCount, followingCount) ||
                other.followingCount == followingCount) &&
            (identical(other.bannerImageUrl, bannerImageUrl) ||
                other.bannerImageUrl == bannerImageUrl) &&
            (identical(other.website, website) || other.website == website) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.isPrivate, isPrivate) ||
                other.isPrivate == isPrivate) &&
            (identical(other.allowMessages, allowMessages) ||
                other.allowMessages == allowMessages) &&
            (identical(other.showActivityStatus, showActivityStatus) ||
                other.showActivityStatus == showActivityStatus) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.isBillionaire, isBillionaire) ||
                other.isBillionaire == isBillionaire) &&
            (identical(other.isCelebrity, isCelebrity) ||
                other.isCelebrity == isCelebrity) &&
            (identical(other.verificationTier, verificationTier) ||
                other.verificationTier == verificationTier) &&
            (identical(other.verifiedAt, verifiedAt) ||
                other.verifiedAt == verifiedAt) &&
            (identical(other.verifiedBy, verifiedBy) ||
                other.verifiedBy == verifiedBy) &&
            (identical(other.userType, userType) ||
                other.userType == userType) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isBusinessAccount, isBusinessAccount) ||
                other.isBusinessAccount == isBusinessAccount) &&
            (identical(other.businessName, businessName) ||
                other.businessName == businessName) &&
            (identical(other.businessLogoUrl, businessLogoUrl) ||
                other.businessLogoUrl == businessLogoUrl) &&
            (identical(other.businessEmail, businessEmail) ||
                other.businessEmail == businessEmail) &&
            (identical(other.businessPhone, businessPhone) ||
                other.businessPhone == businessPhone) &&
            (identical(other.businessDescription, businessDescription) ||
                other.businessDescription == businessDescription) &&
            (identical(other.businessVerified, businessVerified) ||
                other.businessVerified == businessVerified) &&
            (identical(other.businessExclusive, businessExclusive) ||
                other.businessExclusive == businessExclusive) &&
            (identical(other.businessCategory, businessCategory) ||
                other.businessCategory == businessCategory) &&
            (identical(other.businessWebsite, businessWebsite) ||
                other.businessWebsite == businessWebsite) &&
            (identical(other.businessAddress, businessAddress) ||
                other.businessAddress == businessAddress) &&
            (identical(other.businessCreatedAt, businessCreatedAt) ||
                other.businessCreatedAt == businessCreatedAt) &&
            (identical(other.businessVerifiedAt, businessVerifiedAt) ||
                other.businessVerifiedAt == businessVerifiedAt) &&
            (identical(other.isAdmin, isAdmin) || other.isAdmin == isAdmin));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    username,
    name,
    profilePictureUrl,
    bio,
    postCount,
    followerCount,
    followingCount,
    bannerImageUrl,
    website,
    location,
    phone,
    email,
    isPrivate,
    allowMessages,
    showActivityStatus,
    isVerified,
    isBillionaire,
    isCelebrity,
    verificationTier,
    verifiedAt,
    verifiedBy,
    userType,
    createdAt,
    updatedAt,
    isBusinessAccount,
    businessName,
    businessLogoUrl,
    businessEmail,
    businessPhone,
    businessDescription,
    businessVerified,
    businessExclusive,
    businessCategory,
    businessWebsite,
    businessAddress,
    businessCreatedAt,
    businessVerifiedAt,
    isAdmin,
  ]);

  /// Create a copy of ProfileModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfileModelImplCopyWith<_$ProfileModelImpl> get copyWith =>
      __$$ProfileModelImplCopyWithImpl<_$ProfileModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProfileModelImplToJson(this);
  }
}

abstract class _ProfileModel implements ProfileModel {
  const factory _ProfileModel({
    required final String id,
    required final String username,
    required final String name,
    required final String profilePictureUrl,
    required final String bio,
    required final int postCount,
    required final int followerCount,
    required final int followingCount,
    final String? bannerImageUrl,
    final String? website,
    final String? location,
    final String? phone,
    final String? email,
    final bool isPrivate,
    final bool allowMessages,
    final bool showActivityStatus,
    final bool isVerified,
    final bool isBillionaire,
    final bool isCelebrity,
    final VerificationTier? verificationTier,
    final DateTime? verifiedAt,
    final String? verifiedBy,
    final String userType,
    final DateTime? createdAt,
    final DateTime? updatedAt,
    final bool isBusinessAccount,
    final String? businessName,
    final String? businessLogoUrl,
    final String? businessEmail,
    final String? businessPhone,
    final String? businessDescription,
    final bool businessVerified,
    final bool businessExclusive,
    final String? businessCategory,
    final String? businessWebsite,
    final String? businessAddress,
    final DateTime? businessCreatedAt,
    final DateTime? businessVerifiedAt,
    final bool isAdmin,
  }) = _$ProfileModelImpl;

  factory _ProfileModel.fromJson(Map<String, dynamic> json) =
      _$ProfileModelImpl.fromJson;

  @override
  String get id;
  @override
  String get username;
  @override
  String get name;
  @override
  String get profilePictureUrl;
  @override
  String get bio;
  @override
  int get postCount;
  @override
  int get followerCount;
  @override
  int get followingCount; // Enhanced fields for complete profile
  @override
  String? get bannerImageUrl;
  @override
  String? get website;
  @override
  String? get location;
  @override
  String? get phone;
  @override
  String? get email;
  @override
  bool get isPrivate;
  @override
  bool get allowMessages;
  @override
  bool get showActivityStatus;
  @override
  bool get isVerified;
  @override
  bool get isBillionaire;
  @override
  bool get isCelebrity;
  @override
  VerificationTier? get verificationTier;
  @override
  DateTime? get verifiedAt;
  @override
  String? get verifiedBy; // Admin ID who verified
  @override
  String get userType;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt; // Business Account fields for sellers
  @override
  bool get isBusinessAccount;
  @override
  String? get businessName;
  @override
  String? get businessLogoUrl;
  @override
  String? get businessEmail;
  @override
  String? get businessPhone;
  @override
  String? get businessDescription;
  @override
  bool get businessVerified;
  @override
  bool get businessExclusive;
  @override
  String? get businessCategory;
  @override
  String? get businessWebsite;
  @override
  String? get businessAddress;
  @override
  DateTime? get businessCreatedAt;
  @override
  DateTime? get businessVerifiedAt;
  @override
  bool get isAdmin;

  /// Create a copy of ProfileModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProfileModelImplCopyWith<_$ProfileModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
