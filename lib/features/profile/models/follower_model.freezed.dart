// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'follower_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

FollowerModel _$FollowerModelFromJson(Map<String, dynamic> json) {
  return _FollowerModel.fromJson(json);
}

/// @nodoc
mixin _$FollowerModel {
  String get id => throw _privateConstructorUsedError;
  String get username => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get profilePictureUrl => throw _privateConstructorUsedError;
  String? get bio => throw _privateConstructorUsedError;
  bool get isVerified => throw _privateConstructorUsedError;
  AccountType get accountType => throw _privateConstructorUsedError;
  Gender get gender => throw _privateConstructorUsedError;
  bool get isFollowingBack => throw _privateConstructorUsedError;
  bool get isMutual => throw _privateConstructorUsedError;
  DateTime get followedAt => throw _privateConstructorUsedError;
  bool get isMuted => throw _privateConstructorUsedError;
  bool get isSelected => throw _privateConstructorUsedError; // For bulk actions
  List<String> get tags => throw _privateConstructorUsedError;

  /// Serializes this FollowerModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FollowerModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FollowerModelCopyWith<FollowerModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FollowerModelCopyWith<$Res> {
  factory $FollowerModelCopyWith(
    FollowerModel value,
    $Res Function(FollowerModel) then,
  ) = _$FollowerModelCopyWithImpl<$Res, FollowerModel>;
  @useResult
  $Res call({
    String id,
    String username,
    String name,
    String profilePictureUrl,
    String? bio,
    bool isVerified,
    AccountType accountType,
    Gender gender,
    bool isFollowingBack,
    bool isMutual,
    DateTime followedAt,
    bool isMuted,
    bool isSelected,
    List<String> tags,
  });
}

/// @nodoc
class _$FollowerModelCopyWithImpl<$Res, $Val extends FollowerModel>
    implements $FollowerModelCopyWith<$Res> {
  _$FollowerModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FollowerModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? username = null,
    Object? name = null,
    Object? profilePictureUrl = null,
    Object? bio = freezed,
    Object? isVerified = null,
    Object? accountType = null,
    Object? gender = null,
    Object? isFollowingBack = null,
    Object? isMutual = null,
    Object? followedAt = null,
    Object? isMuted = null,
    Object? isSelected = null,
    Object? tags = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            username: null == username
                ? _value.username
                : username // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            profilePictureUrl: null == profilePictureUrl
                ? _value.profilePictureUrl
                : profilePictureUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            bio: freezed == bio
                ? _value.bio
                : bio // ignore: cast_nullable_to_non_nullable
                      as String?,
            isVerified: null == isVerified
                ? _value.isVerified
                : isVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            accountType: null == accountType
                ? _value.accountType
                : accountType // ignore: cast_nullable_to_non_nullable
                      as AccountType,
            gender: null == gender
                ? _value.gender
                : gender // ignore: cast_nullable_to_non_nullable
                      as Gender,
            isFollowingBack: null == isFollowingBack
                ? _value.isFollowingBack
                : isFollowingBack // ignore: cast_nullable_to_non_nullable
                      as bool,
            isMutual: null == isMutual
                ? _value.isMutual
                : isMutual // ignore: cast_nullable_to_non_nullable
                      as bool,
            followedAt: null == followedAt
                ? _value.followedAt
                : followedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isMuted: null == isMuted
                ? _value.isMuted
                : isMuted // ignore: cast_nullable_to_non_nullable
                      as bool,
            isSelected: null == isSelected
                ? _value.isSelected
                : isSelected // ignore: cast_nullable_to_non_nullable
                      as bool,
            tags: null == tags
                ? _value.tags
                : tags // ignore: cast_nullable_to_non_nullable
                      as List<String>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$FollowerModelImplCopyWith<$Res>
    implements $FollowerModelCopyWith<$Res> {
  factory _$$FollowerModelImplCopyWith(
    _$FollowerModelImpl value,
    $Res Function(_$FollowerModelImpl) then,
  ) = __$$FollowerModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String username,
    String name,
    String profilePictureUrl,
    String? bio,
    bool isVerified,
    AccountType accountType,
    Gender gender,
    bool isFollowingBack,
    bool isMutual,
    DateTime followedAt,
    bool isMuted,
    bool isSelected,
    List<String> tags,
  });
}

/// @nodoc
class __$$FollowerModelImplCopyWithImpl<$Res>
    extends _$FollowerModelCopyWithImpl<$Res, _$FollowerModelImpl>
    implements _$$FollowerModelImplCopyWith<$Res> {
  __$$FollowerModelImplCopyWithImpl(
    _$FollowerModelImpl _value,
    $Res Function(_$FollowerModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of FollowerModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? username = null,
    Object? name = null,
    Object? profilePictureUrl = null,
    Object? bio = freezed,
    Object? isVerified = null,
    Object? accountType = null,
    Object? gender = null,
    Object? isFollowingBack = null,
    Object? isMutual = null,
    Object? followedAt = null,
    Object? isMuted = null,
    Object? isSelected = null,
    Object? tags = null,
  }) {
    return _then(
      _$FollowerModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        username: null == username
            ? _value.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        profilePictureUrl: null == profilePictureUrl
            ? _value.profilePictureUrl
            : profilePictureUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        bio: freezed == bio
            ? _value.bio
            : bio // ignore: cast_nullable_to_non_nullable
                  as String?,
        isVerified: null == isVerified
            ? _value.isVerified
            : isVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        accountType: null == accountType
            ? _value.accountType
            : accountType // ignore: cast_nullable_to_non_nullable
                  as AccountType,
        gender: null == gender
            ? _value.gender
            : gender // ignore: cast_nullable_to_non_nullable
                  as Gender,
        isFollowingBack: null == isFollowingBack
            ? _value.isFollowingBack
            : isFollowingBack // ignore: cast_nullable_to_non_nullable
                  as bool,
        isMutual: null == isMutual
            ? _value.isMutual
            : isMutual // ignore: cast_nullable_to_non_nullable
                  as bool,
        followedAt: null == followedAt
            ? _value.followedAt
            : followedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isMuted: null == isMuted
            ? _value.isMuted
            : isMuted // ignore: cast_nullable_to_non_nullable
                  as bool,
        isSelected: null == isSelected
            ? _value.isSelected
            : isSelected // ignore: cast_nullable_to_non_nullable
                  as bool,
        tags: null == tags
            ? _value._tags
            : tags // ignore: cast_nullable_to_non_nullable
                  as List<String>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$FollowerModelImpl implements _FollowerModel {
  const _$FollowerModelImpl({
    required this.id,
    required this.username,
    required this.name,
    required this.profilePictureUrl,
    required this.bio,
    required this.isVerified,
    required this.accountType,
    required this.gender,
    required this.isFollowingBack,
    required this.isMutual,
    required this.followedAt,
    required this.isMuted,
    required this.isSelected,
    required final List<String> tags,
  }) : _tags = tags;

  factory _$FollowerModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$FollowerModelImplFromJson(json);

  @override
  final String id;
  @override
  final String username;
  @override
  final String name;
  @override
  final String profilePictureUrl;
  @override
  final String? bio;
  @override
  final bool isVerified;
  @override
  final AccountType accountType;
  @override
  final Gender gender;
  @override
  final bool isFollowingBack;
  @override
  final bool isMutual;
  @override
  final DateTime followedAt;
  @override
  final bool isMuted;
  @override
  final bool isSelected;
  // For bulk actions
  final List<String> _tags;
  // For bulk actions
  @override
  List<String> get tags {
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tags);
  }

  @override
  String toString() {
    return 'FollowerModel(id: $id, username: $username, name: $name, profilePictureUrl: $profilePictureUrl, bio: $bio, isVerified: $isVerified, accountType: $accountType, gender: $gender, isFollowingBack: $isFollowingBack, isMutual: $isMutual, followedAt: $followedAt, isMuted: $isMuted, isSelected: $isSelected, tags: $tags)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FollowerModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.profilePictureUrl, profilePictureUrl) ||
                other.profilePictureUrl == profilePictureUrl) &&
            (identical(other.bio, bio) || other.bio == bio) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.accountType, accountType) ||
                other.accountType == accountType) &&
            (identical(other.gender, gender) || other.gender == gender) &&
            (identical(other.isFollowingBack, isFollowingBack) ||
                other.isFollowingBack == isFollowingBack) &&
            (identical(other.isMutual, isMutual) ||
                other.isMutual == isMutual) &&
            (identical(other.followedAt, followedAt) ||
                other.followedAt == followedAt) &&
            (identical(other.isMuted, isMuted) || other.isMuted == isMuted) &&
            (identical(other.isSelected, isSelected) ||
                other.isSelected == isSelected) &&
            const DeepCollectionEquality().equals(other._tags, _tags));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    username,
    name,
    profilePictureUrl,
    bio,
    isVerified,
    accountType,
    gender,
    isFollowingBack,
    isMutual,
    followedAt,
    isMuted,
    isSelected,
    const DeepCollectionEquality().hash(_tags),
  );

  /// Create a copy of FollowerModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FollowerModelImplCopyWith<_$FollowerModelImpl> get copyWith =>
      __$$FollowerModelImplCopyWithImpl<_$FollowerModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FollowerModelImplToJson(this);
  }
}

abstract class _FollowerModel implements FollowerModel {
  const factory _FollowerModel({
    required final String id,
    required final String username,
    required final String name,
    required final String profilePictureUrl,
    required final String? bio,
    required final bool isVerified,
    required final AccountType accountType,
    required final Gender gender,
    required final bool isFollowingBack,
    required final bool isMutual,
    required final DateTime followedAt,
    required final bool isMuted,
    required final bool isSelected,
    required final List<String> tags,
  }) = _$FollowerModelImpl;

  factory _FollowerModel.fromJson(Map<String, dynamic> json) =
      _$FollowerModelImpl.fromJson;

  @override
  String get id;
  @override
  String get username;
  @override
  String get name;
  @override
  String get profilePictureUrl;
  @override
  String? get bio;
  @override
  bool get isVerified;
  @override
  AccountType get accountType;
  @override
  Gender get gender;
  @override
  bool get isFollowingBack;
  @override
  bool get isMutual;
  @override
  DateTime get followedAt;
  @override
  bool get isMuted;
  @override
  bool get isSelected; // For bulk actions
  @override
  List<String> get tags;

  /// Create a copy of FollowerModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FollowerModelImplCopyWith<_$FollowerModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

FollowingModel _$FollowingModelFromJson(Map<String, dynamic> json) {
  return _FollowingModel.fromJson(json);
}

/// @nodoc
mixin _$FollowingModel {
  String get id => throw _privateConstructorUsedError;
  String get username => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get profilePictureUrl => throw _privateConstructorUsedError;
  String? get bio => throw _privateConstructorUsedError;
  bool get isVerified => throw _privateConstructorUsedError;
  AccountType get accountType => throw _privateConstructorUsedError;
  Gender get gender => throw _privateConstructorUsedError;
  bool get isFollowingBack => throw _privateConstructorUsedError;
  bool get isMutual => throw _privateConstructorUsedError;
  DateTime get followedAt => throw _privateConstructorUsedError;
  bool get isMuted => throw _privateConstructorUsedError;
  bool get isSelected => throw _privateConstructorUsedError; // For bulk actions
  List<String> get tags => throw _privateConstructorUsedError;

  /// Serializes this FollowingModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FollowingModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FollowingModelCopyWith<FollowingModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FollowingModelCopyWith<$Res> {
  factory $FollowingModelCopyWith(
    FollowingModel value,
    $Res Function(FollowingModel) then,
  ) = _$FollowingModelCopyWithImpl<$Res, FollowingModel>;
  @useResult
  $Res call({
    String id,
    String username,
    String name,
    String profilePictureUrl,
    String? bio,
    bool isVerified,
    AccountType accountType,
    Gender gender,
    bool isFollowingBack,
    bool isMutual,
    DateTime followedAt,
    bool isMuted,
    bool isSelected,
    List<String> tags,
  });
}

/// @nodoc
class _$FollowingModelCopyWithImpl<$Res, $Val extends FollowingModel>
    implements $FollowingModelCopyWith<$Res> {
  _$FollowingModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FollowingModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? username = null,
    Object? name = null,
    Object? profilePictureUrl = null,
    Object? bio = freezed,
    Object? isVerified = null,
    Object? accountType = null,
    Object? gender = null,
    Object? isFollowingBack = null,
    Object? isMutual = null,
    Object? followedAt = null,
    Object? isMuted = null,
    Object? isSelected = null,
    Object? tags = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            username: null == username
                ? _value.username
                : username // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            profilePictureUrl: null == profilePictureUrl
                ? _value.profilePictureUrl
                : profilePictureUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            bio: freezed == bio
                ? _value.bio
                : bio // ignore: cast_nullable_to_non_nullable
                      as String?,
            isVerified: null == isVerified
                ? _value.isVerified
                : isVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            accountType: null == accountType
                ? _value.accountType
                : accountType // ignore: cast_nullable_to_non_nullable
                      as AccountType,
            gender: null == gender
                ? _value.gender
                : gender // ignore: cast_nullable_to_non_nullable
                      as Gender,
            isFollowingBack: null == isFollowingBack
                ? _value.isFollowingBack
                : isFollowingBack // ignore: cast_nullable_to_non_nullable
                      as bool,
            isMutual: null == isMutual
                ? _value.isMutual
                : isMutual // ignore: cast_nullable_to_non_nullable
                      as bool,
            followedAt: null == followedAt
                ? _value.followedAt
                : followedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isMuted: null == isMuted
                ? _value.isMuted
                : isMuted // ignore: cast_nullable_to_non_nullable
                      as bool,
            isSelected: null == isSelected
                ? _value.isSelected
                : isSelected // ignore: cast_nullable_to_non_nullable
                      as bool,
            tags: null == tags
                ? _value.tags
                : tags // ignore: cast_nullable_to_non_nullable
                      as List<String>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$FollowingModelImplCopyWith<$Res>
    implements $FollowingModelCopyWith<$Res> {
  factory _$$FollowingModelImplCopyWith(
    _$FollowingModelImpl value,
    $Res Function(_$FollowingModelImpl) then,
  ) = __$$FollowingModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String username,
    String name,
    String profilePictureUrl,
    String? bio,
    bool isVerified,
    AccountType accountType,
    Gender gender,
    bool isFollowingBack,
    bool isMutual,
    DateTime followedAt,
    bool isMuted,
    bool isSelected,
    List<String> tags,
  });
}

/// @nodoc
class __$$FollowingModelImplCopyWithImpl<$Res>
    extends _$FollowingModelCopyWithImpl<$Res, _$FollowingModelImpl>
    implements _$$FollowingModelImplCopyWith<$Res> {
  __$$FollowingModelImplCopyWithImpl(
    _$FollowingModelImpl _value,
    $Res Function(_$FollowingModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of FollowingModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? username = null,
    Object? name = null,
    Object? profilePictureUrl = null,
    Object? bio = freezed,
    Object? isVerified = null,
    Object? accountType = null,
    Object? gender = null,
    Object? isFollowingBack = null,
    Object? isMutual = null,
    Object? followedAt = null,
    Object? isMuted = null,
    Object? isSelected = null,
    Object? tags = null,
  }) {
    return _then(
      _$FollowingModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        username: null == username
            ? _value.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        profilePictureUrl: null == profilePictureUrl
            ? _value.profilePictureUrl
            : profilePictureUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        bio: freezed == bio
            ? _value.bio
            : bio // ignore: cast_nullable_to_non_nullable
                  as String?,
        isVerified: null == isVerified
            ? _value.isVerified
            : isVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        accountType: null == accountType
            ? _value.accountType
            : accountType // ignore: cast_nullable_to_non_nullable
                  as AccountType,
        gender: null == gender
            ? _value.gender
            : gender // ignore: cast_nullable_to_non_nullable
                  as Gender,
        isFollowingBack: null == isFollowingBack
            ? _value.isFollowingBack
            : isFollowingBack // ignore: cast_nullable_to_non_nullable
                  as bool,
        isMutual: null == isMutual
            ? _value.isMutual
            : isMutual // ignore: cast_nullable_to_non_nullable
                  as bool,
        followedAt: null == followedAt
            ? _value.followedAt
            : followedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isMuted: null == isMuted
            ? _value.isMuted
            : isMuted // ignore: cast_nullable_to_non_nullable
                  as bool,
        isSelected: null == isSelected
            ? _value.isSelected
            : isSelected // ignore: cast_nullable_to_non_nullable
                  as bool,
        tags: null == tags
            ? _value._tags
            : tags // ignore: cast_nullable_to_non_nullable
                  as List<String>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$FollowingModelImpl implements _FollowingModel {
  const _$FollowingModelImpl({
    required this.id,
    required this.username,
    required this.name,
    required this.profilePictureUrl,
    required this.bio,
    required this.isVerified,
    required this.accountType,
    required this.gender,
    required this.isFollowingBack,
    required this.isMutual,
    required this.followedAt,
    required this.isMuted,
    required this.isSelected,
    required final List<String> tags,
  }) : _tags = tags;

  factory _$FollowingModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$FollowingModelImplFromJson(json);

  @override
  final String id;
  @override
  final String username;
  @override
  final String name;
  @override
  final String profilePictureUrl;
  @override
  final String? bio;
  @override
  final bool isVerified;
  @override
  final AccountType accountType;
  @override
  final Gender gender;
  @override
  final bool isFollowingBack;
  @override
  final bool isMutual;
  @override
  final DateTime followedAt;
  @override
  final bool isMuted;
  @override
  final bool isSelected;
  // For bulk actions
  final List<String> _tags;
  // For bulk actions
  @override
  List<String> get tags {
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tags);
  }

  @override
  String toString() {
    return 'FollowingModel(id: $id, username: $username, name: $name, profilePictureUrl: $profilePictureUrl, bio: $bio, isVerified: $isVerified, accountType: $accountType, gender: $gender, isFollowingBack: $isFollowingBack, isMutual: $isMutual, followedAt: $followedAt, isMuted: $isMuted, isSelected: $isSelected, tags: $tags)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FollowingModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.profilePictureUrl, profilePictureUrl) ||
                other.profilePictureUrl == profilePictureUrl) &&
            (identical(other.bio, bio) || other.bio == bio) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.accountType, accountType) ||
                other.accountType == accountType) &&
            (identical(other.gender, gender) || other.gender == gender) &&
            (identical(other.isFollowingBack, isFollowingBack) ||
                other.isFollowingBack == isFollowingBack) &&
            (identical(other.isMutual, isMutual) ||
                other.isMutual == isMutual) &&
            (identical(other.followedAt, followedAt) ||
                other.followedAt == followedAt) &&
            (identical(other.isMuted, isMuted) || other.isMuted == isMuted) &&
            (identical(other.isSelected, isSelected) ||
                other.isSelected == isSelected) &&
            const DeepCollectionEquality().equals(other._tags, _tags));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    username,
    name,
    profilePictureUrl,
    bio,
    isVerified,
    accountType,
    gender,
    isFollowingBack,
    isMutual,
    followedAt,
    isMuted,
    isSelected,
    const DeepCollectionEquality().hash(_tags),
  );

  /// Create a copy of FollowingModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FollowingModelImplCopyWith<_$FollowingModelImpl> get copyWith =>
      __$$FollowingModelImplCopyWithImpl<_$FollowingModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$FollowingModelImplToJson(this);
  }
}

abstract class _FollowingModel implements FollowingModel {
  const factory _FollowingModel({
    required final String id,
    required final String username,
    required final String name,
    required final String profilePictureUrl,
    required final String? bio,
    required final bool isVerified,
    required final AccountType accountType,
    required final Gender gender,
    required final bool isFollowingBack,
    required final bool isMutual,
    required final DateTime followedAt,
    required final bool isMuted,
    required final bool isSelected,
    required final List<String> tags,
  }) = _$FollowingModelImpl;

  factory _FollowingModel.fromJson(Map<String, dynamic> json) =
      _$FollowingModelImpl.fromJson;

  @override
  String get id;
  @override
  String get username;
  @override
  String get name;
  @override
  String get profilePictureUrl;
  @override
  String? get bio;
  @override
  bool get isVerified;
  @override
  AccountType get accountType;
  @override
  Gender get gender;
  @override
  bool get isFollowingBack;
  @override
  bool get isMutual;
  @override
  DateTime get followedAt;
  @override
  bool get isMuted;
  @override
  bool get isSelected; // For bulk actions
  @override
  List<String> get tags;

  /// Create a copy of FollowingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FollowingModelImplCopyWith<_$FollowingModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

FollowersAnalytics _$FollowersAnalyticsFromJson(Map<String, dynamic> json) {
  return _FollowersAnalytics.fromJson(json);
}

/// @nodoc
mixin _$FollowersAnalytics {
  int get totalFollowers => throw _privateConstructorUsedError;
  int get totalFollowing => throw _privateConstructorUsedError;
  int get notFollowingBack => throw _privateConstructorUsedError;
  int get verifiedAccountsFollowed => throw _privateConstructorUsedError;
  int get billionairesFollowed => throw _privateConstructorUsedError;
  int get celebritiesFollowed => throw _privateConstructorUsedError;
  int get businessesFollowed => throw _privateConstructorUsedError;
  GenderSplit get genderSplit => throw _privateConstructorUsedError;
  AccountTypeSplit get accountTypeSplit => throw _privateConstructorUsedError;
  MutualStatusSplit get mutualStatusSplit => throw _privateConstructorUsedError;

  /// Serializes this FollowersAnalytics to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FollowersAnalytics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FollowersAnalyticsCopyWith<FollowersAnalytics> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FollowersAnalyticsCopyWith<$Res> {
  factory $FollowersAnalyticsCopyWith(
    FollowersAnalytics value,
    $Res Function(FollowersAnalytics) then,
  ) = _$FollowersAnalyticsCopyWithImpl<$Res, FollowersAnalytics>;
  @useResult
  $Res call({
    int totalFollowers,
    int totalFollowing,
    int notFollowingBack,
    int verifiedAccountsFollowed,
    int billionairesFollowed,
    int celebritiesFollowed,
    int businessesFollowed,
    GenderSplit genderSplit,
    AccountTypeSplit accountTypeSplit,
    MutualStatusSplit mutualStatusSplit,
  });

  $GenderSplitCopyWith<$Res> get genderSplit;
  $AccountTypeSplitCopyWith<$Res> get accountTypeSplit;
  $MutualStatusSplitCopyWith<$Res> get mutualStatusSplit;
}

/// @nodoc
class _$FollowersAnalyticsCopyWithImpl<$Res, $Val extends FollowersAnalytics>
    implements $FollowersAnalyticsCopyWith<$Res> {
  _$FollowersAnalyticsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FollowersAnalytics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalFollowers = null,
    Object? totalFollowing = null,
    Object? notFollowingBack = null,
    Object? verifiedAccountsFollowed = null,
    Object? billionairesFollowed = null,
    Object? celebritiesFollowed = null,
    Object? businessesFollowed = null,
    Object? genderSplit = null,
    Object? accountTypeSplit = null,
    Object? mutualStatusSplit = null,
  }) {
    return _then(
      _value.copyWith(
            totalFollowers: null == totalFollowers
                ? _value.totalFollowers
                : totalFollowers // ignore: cast_nullable_to_non_nullable
                      as int,
            totalFollowing: null == totalFollowing
                ? _value.totalFollowing
                : totalFollowing // ignore: cast_nullable_to_non_nullable
                      as int,
            notFollowingBack: null == notFollowingBack
                ? _value.notFollowingBack
                : notFollowingBack // ignore: cast_nullable_to_non_nullable
                      as int,
            verifiedAccountsFollowed: null == verifiedAccountsFollowed
                ? _value.verifiedAccountsFollowed
                : verifiedAccountsFollowed // ignore: cast_nullable_to_non_nullable
                      as int,
            billionairesFollowed: null == billionairesFollowed
                ? _value.billionairesFollowed
                : billionairesFollowed // ignore: cast_nullable_to_non_nullable
                      as int,
            celebritiesFollowed: null == celebritiesFollowed
                ? _value.celebritiesFollowed
                : celebritiesFollowed // ignore: cast_nullable_to_non_nullable
                      as int,
            businessesFollowed: null == businessesFollowed
                ? _value.businessesFollowed
                : businessesFollowed // ignore: cast_nullable_to_non_nullable
                      as int,
            genderSplit: null == genderSplit
                ? _value.genderSplit
                : genderSplit // ignore: cast_nullable_to_non_nullable
                      as GenderSplit,
            accountTypeSplit: null == accountTypeSplit
                ? _value.accountTypeSplit
                : accountTypeSplit // ignore: cast_nullable_to_non_nullable
                      as AccountTypeSplit,
            mutualStatusSplit: null == mutualStatusSplit
                ? _value.mutualStatusSplit
                : mutualStatusSplit // ignore: cast_nullable_to_non_nullable
                      as MutualStatusSplit,
          )
          as $Val,
    );
  }

  /// Create a copy of FollowersAnalytics
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GenderSplitCopyWith<$Res> get genderSplit {
    return $GenderSplitCopyWith<$Res>(_value.genderSplit, (value) {
      return _then(_value.copyWith(genderSplit: value) as $Val);
    });
  }

  /// Create a copy of FollowersAnalytics
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AccountTypeSplitCopyWith<$Res> get accountTypeSplit {
    return $AccountTypeSplitCopyWith<$Res>(_value.accountTypeSplit, (value) {
      return _then(_value.copyWith(accountTypeSplit: value) as $Val);
    });
  }

  /// Create a copy of FollowersAnalytics
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MutualStatusSplitCopyWith<$Res> get mutualStatusSplit {
    return $MutualStatusSplitCopyWith<$Res>(_value.mutualStatusSplit, (value) {
      return _then(_value.copyWith(mutualStatusSplit: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$FollowersAnalyticsImplCopyWith<$Res>
    implements $FollowersAnalyticsCopyWith<$Res> {
  factory _$$FollowersAnalyticsImplCopyWith(
    _$FollowersAnalyticsImpl value,
    $Res Function(_$FollowersAnalyticsImpl) then,
  ) = __$$FollowersAnalyticsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int totalFollowers,
    int totalFollowing,
    int notFollowingBack,
    int verifiedAccountsFollowed,
    int billionairesFollowed,
    int celebritiesFollowed,
    int businessesFollowed,
    GenderSplit genderSplit,
    AccountTypeSplit accountTypeSplit,
    MutualStatusSplit mutualStatusSplit,
  });

  @override
  $GenderSplitCopyWith<$Res> get genderSplit;
  @override
  $AccountTypeSplitCopyWith<$Res> get accountTypeSplit;
  @override
  $MutualStatusSplitCopyWith<$Res> get mutualStatusSplit;
}

/// @nodoc
class __$$FollowersAnalyticsImplCopyWithImpl<$Res>
    extends _$FollowersAnalyticsCopyWithImpl<$Res, _$FollowersAnalyticsImpl>
    implements _$$FollowersAnalyticsImplCopyWith<$Res> {
  __$$FollowersAnalyticsImplCopyWithImpl(
    _$FollowersAnalyticsImpl _value,
    $Res Function(_$FollowersAnalyticsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of FollowersAnalytics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalFollowers = null,
    Object? totalFollowing = null,
    Object? notFollowingBack = null,
    Object? verifiedAccountsFollowed = null,
    Object? billionairesFollowed = null,
    Object? celebritiesFollowed = null,
    Object? businessesFollowed = null,
    Object? genderSplit = null,
    Object? accountTypeSplit = null,
    Object? mutualStatusSplit = null,
  }) {
    return _then(
      _$FollowersAnalyticsImpl(
        totalFollowers: null == totalFollowers
            ? _value.totalFollowers
            : totalFollowers // ignore: cast_nullable_to_non_nullable
                  as int,
        totalFollowing: null == totalFollowing
            ? _value.totalFollowing
            : totalFollowing // ignore: cast_nullable_to_non_nullable
                  as int,
        notFollowingBack: null == notFollowingBack
            ? _value.notFollowingBack
            : notFollowingBack // ignore: cast_nullable_to_non_nullable
                  as int,
        verifiedAccountsFollowed: null == verifiedAccountsFollowed
            ? _value.verifiedAccountsFollowed
            : verifiedAccountsFollowed // ignore: cast_nullable_to_non_nullable
                  as int,
        billionairesFollowed: null == billionairesFollowed
            ? _value.billionairesFollowed
            : billionairesFollowed // ignore: cast_nullable_to_non_nullable
                  as int,
        celebritiesFollowed: null == celebritiesFollowed
            ? _value.celebritiesFollowed
            : celebritiesFollowed // ignore: cast_nullable_to_non_nullable
                  as int,
        businessesFollowed: null == businessesFollowed
            ? _value.businessesFollowed
            : businessesFollowed // ignore: cast_nullable_to_non_nullable
                  as int,
        genderSplit: null == genderSplit
            ? _value.genderSplit
            : genderSplit // ignore: cast_nullable_to_non_nullable
                  as GenderSplit,
        accountTypeSplit: null == accountTypeSplit
            ? _value.accountTypeSplit
            : accountTypeSplit // ignore: cast_nullable_to_non_nullable
                  as AccountTypeSplit,
        mutualStatusSplit: null == mutualStatusSplit
            ? _value.mutualStatusSplit
            : mutualStatusSplit // ignore: cast_nullable_to_non_nullable
                  as MutualStatusSplit,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$FollowersAnalyticsImpl implements _FollowersAnalytics {
  const _$FollowersAnalyticsImpl({
    required this.totalFollowers,
    required this.totalFollowing,
    required this.notFollowingBack,
    required this.verifiedAccountsFollowed,
    required this.billionairesFollowed,
    required this.celebritiesFollowed,
    required this.businessesFollowed,
    required this.genderSplit,
    required this.accountTypeSplit,
    required this.mutualStatusSplit,
  });

  factory _$FollowersAnalyticsImpl.fromJson(Map<String, dynamic> json) =>
      _$$FollowersAnalyticsImplFromJson(json);

  @override
  final int totalFollowers;
  @override
  final int totalFollowing;
  @override
  final int notFollowingBack;
  @override
  final int verifiedAccountsFollowed;
  @override
  final int billionairesFollowed;
  @override
  final int celebritiesFollowed;
  @override
  final int businessesFollowed;
  @override
  final GenderSplit genderSplit;
  @override
  final AccountTypeSplit accountTypeSplit;
  @override
  final MutualStatusSplit mutualStatusSplit;

  @override
  String toString() {
    return 'FollowersAnalytics(totalFollowers: $totalFollowers, totalFollowing: $totalFollowing, notFollowingBack: $notFollowingBack, verifiedAccountsFollowed: $verifiedAccountsFollowed, billionairesFollowed: $billionairesFollowed, celebritiesFollowed: $celebritiesFollowed, businessesFollowed: $businessesFollowed, genderSplit: $genderSplit, accountTypeSplit: $accountTypeSplit, mutualStatusSplit: $mutualStatusSplit)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FollowersAnalyticsImpl &&
            (identical(other.totalFollowers, totalFollowers) ||
                other.totalFollowers == totalFollowers) &&
            (identical(other.totalFollowing, totalFollowing) ||
                other.totalFollowing == totalFollowing) &&
            (identical(other.notFollowingBack, notFollowingBack) ||
                other.notFollowingBack == notFollowingBack) &&
            (identical(
                  other.verifiedAccountsFollowed,
                  verifiedAccountsFollowed,
                ) ||
                other.verifiedAccountsFollowed == verifiedAccountsFollowed) &&
            (identical(other.billionairesFollowed, billionairesFollowed) ||
                other.billionairesFollowed == billionairesFollowed) &&
            (identical(other.celebritiesFollowed, celebritiesFollowed) ||
                other.celebritiesFollowed == celebritiesFollowed) &&
            (identical(other.businessesFollowed, businessesFollowed) ||
                other.businessesFollowed == businessesFollowed) &&
            (identical(other.genderSplit, genderSplit) ||
                other.genderSplit == genderSplit) &&
            (identical(other.accountTypeSplit, accountTypeSplit) ||
                other.accountTypeSplit == accountTypeSplit) &&
            (identical(other.mutualStatusSplit, mutualStatusSplit) ||
                other.mutualStatusSplit == mutualStatusSplit));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    totalFollowers,
    totalFollowing,
    notFollowingBack,
    verifiedAccountsFollowed,
    billionairesFollowed,
    celebritiesFollowed,
    businessesFollowed,
    genderSplit,
    accountTypeSplit,
    mutualStatusSplit,
  );

  /// Create a copy of FollowersAnalytics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FollowersAnalyticsImplCopyWith<_$FollowersAnalyticsImpl> get copyWith =>
      __$$FollowersAnalyticsImplCopyWithImpl<_$FollowersAnalyticsImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$FollowersAnalyticsImplToJson(this);
  }
}

abstract class _FollowersAnalytics implements FollowersAnalytics {
  const factory _FollowersAnalytics({
    required final int totalFollowers,
    required final int totalFollowing,
    required final int notFollowingBack,
    required final int verifiedAccountsFollowed,
    required final int billionairesFollowed,
    required final int celebritiesFollowed,
    required final int businessesFollowed,
    required final GenderSplit genderSplit,
    required final AccountTypeSplit accountTypeSplit,
    required final MutualStatusSplit mutualStatusSplit,
  }) = _$FollowersAnalyticsImpl;

  factory _FollowersAnalytics.fromJson(Map<String, dynamic> json) =
      _$FollowersAnalyticsImpl.fromJson;

  @override
  int get totalFollowers;
  @override
  int get totalFollowing;
  @override
  int get notFollowingBack;
  @override
  int get verifiedAccountsFollowed;
  @override
  int get billionairesFollowed;
  @override
  int get celebritiesFollowed;
  @override
  int get businessesFollowed;
  @override
  GenderSplit get genderSplit;
  @override
  AccountTypeSplit get accountTypeSplit;
  @override
  MutualStatusSplit get mutualStatusSplit;

  /// Create a copy of FollowersAnalytics
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FollowersAnalyticsImplCopyWith<_$FollowersAnalyticsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

GenderSplit _$GenderSplitFromJson(Map<String, dynamic> json) {
  return _GenderSplit.fromJson(json);
}

/// @nodoc
mixin _$GenderSplit {
  int get male => throw _privateConstructorUsedError;
  int get female => throw _privateConstructorUsedError;
  int get other => throw _privateConstructorUsedError;
  double get malePercentage => throw _privateConstructorUsedError;
  double get femalePercentage => throw _privateConstructorUsedError;
  double get otherPercentage => throw _privateConstructorUsedError;

  /// Serializes this GenderSplit to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of GenderSplit
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $GenderSplitCopyWith<GenderSplit> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GenderSplitCopyWith<$Res> {
  factory $GenderSplitCopyWith(
    GenderSplit value,
    $Res Function(GenderSplit) then,
  ) = _$GenderSplitCopyWithImpl<$Res, GenderSplit>;
  @useResult
  $Res call({
    int male,
    int female,
    int other,
    double malePercentage,
    double femalePercentage,
    double otherPercentage,
  });
}

/// @nodoc
class _$GenderSplitCopyWithImpl<$Res, $Val extends GenderSplit>
    implements $GenderSplitCopyWith<$Res> {
  _$GenderSplitCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of GenderSplit
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? male = null,
    Object? female = null,
    Object? other = null,
    Object? malePercentage = null,
    Object? femalePercentage = null,
    Object? otherPercentage = null,
  }) {
    return _then(
      _value.copyWith(
            male: null == male
                ? _value.male
                : male // ignore: cast_nullable_to_non_nullable
                      as int,
            female: null == female
                ? _value.female
                : female // ignore: cast_nullable_to_non_nullable
                      as int,
            other: null == other
                ? _value.other
                : other // ignore: cast_nullable_to_non_nullable
                      as int,
            malePercentage: null == malePercentage
                ? _value.malePercentage
                : malePercentage // ignore: cast_nullable_to_non_nullable
                      as double,
            femalePercentage: null == femalePercentage
                ? _value.femalePercentage
                : femalePercentage // ignore: cast_nullable_to_non_nullable
                      as double,
            otherPercentage: null == otherPercentage
                ? _value.otherPercentage
                : otherPercentage // ignore: cast_nullable_to_non_nullable
                      as double,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$GenderSplitImplCopyWith<$Res>
    implements $GenderSplitCopyWith<$Res> {
  factory _$$GenderSplitImplCopyWith(
    _$GenderSplitImpl value,
    $Res Function(_$GenderSplitImpl) then,
  ) = __$$GenderSplitImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int male,
    int female,
    int other,
    double malePercentage,
    double femalePercentage,
    double otherPercentage,
  });
}

/// @nodoc
class __$$GenderSplitImplCopyWithImpl<$Res>
    extends _$GenderSplitCopyWithImpl<$Res, _$GenderSplitImpl>
    implements _$$GenderSplitImplCopyWith<$Res> {
  __$$GenderSplitImplCopyWithImpl(
    _$GenderSplitImpl _value,
    $Res Function(_$GenderSplitImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of GenderSplit
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? male = null,
    Object? female = null,
    Object? other = null,
    Object? malePercentage = null,
    Object? femalePercentage = null,
    Object? otherPercentage = null,
  }) {
    return _then(
      _$GenderSplitImpl(
        male: null == male
            ? _value.male
            : male // ignore: cast_nullable_to_non_nullable
                  as int,
        female: null == female
            ? _value.female
            : female // ignore: cast_nullable_to_non_nullable
                  as int,
        other: null == other
            ? _value.other
            : other // ignore: cast_nullable_to_non_nullable
                  as int,
        malePercentage: null == malePercentage
            ? _value.malePercentage
            : malePercentage // ignore: cast_nullable_to_non_nullable
                  as double,
        femalePercentage: null == femalePercentage
            ? _value.femalePercentage
            : femalePercentage // ignore: cast_nullable_to_non_nullable
                  as double,
        otherPercentage: null == otherPercentage
            ? _value.otherPercentage
            : otherPercentage // ignore: cast_nullable_to_non_nullable
                  as double,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$GenderSplitImpl implements _GenderSplit {
  const _$GenderSplitImpl({
    required this.male,
    required this.female,
    required this.other,
    required this.malePercentage,
    required this.femalePercentage,
    required this.otherPercentage,
  });

  factory _$GenderSplitImpl.fromJson(Map<String, dynamic> json) =>
      _$$GenderSplitImplFromJson(json);

  @override
  final int male;
  @override
  final int female;
  @override
  final int other;
  @override
  final double malePercentage;
  @override
  final double femalePercentage;
  @override
  final double otherPercentage;

  @override
  String toString() {
    return 'GenderSplit(male: $male, female: $female, other: $other, malePercentage: $malePercentage, femalePercentage: $femalePercentage, otherPercentage: $otherPercentage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GenderSplitImpl &&
            (identical(other.male, male) || other.male == male) &&
            (identical(other.female, female) || other.female == female) &&
            (identical(other.other, this.other) || other.other == this.other) &&
            (identical(other.malePercentage, malePercentage) ||
                other.malePercentage == malePercentage) &&
            (identical(other.femalePercentage, femalePercentage) ||
                other.femalePercentage == femalePercentage) &&
            (identical(other.otherPercentage, otherPercentage) ||
                other.otherPercentage == otherPercentage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    male,
    female,
    other,
    malePercentage,
    femalePercentage,
    otherPercentage,
  );

  /// Create a copy of GenderSplit
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GenderSplitImplCopyWith<_$GenderSplitImpl> get copyWith =>
      __$$GenderSplitImplCopyWithImpl<_$GenderSplitImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GenderSplitImplToJson(this);
  }
}

abstract class _GenderSplit implements GenderSplit {
  const factory _GenderSplit({
    required final int male,
    required final int female,
    required final int other,
    required final double malePercentage,
    required final double femalePercentage,
    required final double otherPercentage,
  }) = _$GenderSplitImpl;

  factory _GenderSplit.fromJson(Map<String, dynamic> json) =
      _$GenderSplitImpl.fromJson;

  @override
  int get male;
  @override
  int get female;
  @override
  int get other;
  @override
  double get malePercentage;
  @override
  double get femalePercentage;
  @override
  double get otherPercentage;

  /// Create a copy of GenderSplit
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GenderSplitImplCopyWith<_$GenderSplitImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AccountTypeSplit _$AccountTypeSplitFromJson(Map<String, dynamic> json) {
  return _AccountTypeSplit.fromJson(json);
}

/// @nodoc
mixin _$AccountTypeSplit {
  int get verified => throw _privateConstructorUsedError;
  int get celebrities => throw _privateConstructorUsedError;
  int get businesses => throw _privateConstructorUsedError;
  int get billionaires => throw _privateConstructorUsedError;
  int get regular => throw _privateConstructorUsedError;

  /// Serializes this AccountTypeSplit to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AccountTypeSplit
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AccountTypeSplitCopyWith<AccountTypeSplit> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountTypeSplitCopyWith<$Res> {
  factory $AccountTypeSplitCopyWith(
    AccountTypeSplit value,
    $Res Function(AccountTypeSplit) then,
  ) = _$AccountTypeSplitCopyWithImpl<$Res, AccountTypeSplit>;
  @useResult
  $Res call({
    int verified,
    int celebrities,
    int businesses,
    int billionaires,
    int regular,
  });
}

/// @nodoc
class _$AccountTypeSplitCopyWithImpl<$Res, $Val extends AccountTypeSplit>
    implements $AccountTypeSplitCopyWith<$Res> {
  _$AccountTypeSplitCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AccountTypeSplit
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? verified = null,
    Object? celebrities = null,
    Object? businesses = null,
    Object? billionaires = null,
    Object? regular = null,
  }) {
    return _then(
      _value.copyWith(
            verified: null == verified
                ? _value.verified
                : verified // ignore: cast_nullable_to_non_nullable
                      as int,
            celebrities: null == celebrities
                ? _value.celebrities
                : celebrities // ignore: cast_nullable_to_non_nullable
                      as int,
            businesses: null == businesses
                ? _value.businesses
                : businesses // ignore: cast_nullable_to_non_nullable
                      as int,
            billionaires: null == billionaires
                ? _value.billionaires
                : billionaires // ignore: cast_nullable_to_non_nullable
                      as int,
            regular: null == regular
                ? _value.regular
                : regular // ignore: cast_nullable_to_non_nullable
                      as int,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$AccountTypeSplitImplCopyWith<$Res>
    implements $AccountTypeSplitCopyWith<$Res> {
  factory _$$AccountTypeSplitImplCopyWith(
    _$AccountTypeSplitImpl value,
    $Res Function(_$AccountTypeSplitImpl) then,
  ) = __$$AccountTypeSplitImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int verified,
    int celebrities,
    int businesses,
    int billionaires,
    int regular,
  });
}

/// @nodoc
class __$$AccountTypeSplitImplCopyWithImpl<$Res>
    extends _$AccountTypeSplitCopyWithImpl<$Res, _$AccountTypeSplitImpl>
    implements _$$AccountTypeSplitImplCopyWith<$Res> {
  __$$AccountTypeSplitImplCopyWithImpl(
    _$AccountTypeSplitImpl _value,
    $Res Function(_$AccountTypeSplitImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AccountTypeSplit
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? verified = null,
    Object? celebrities = null,
    Object? businesses = null,
    Object? billionaires = null,
    Object? regular = null,
  }) {
    return _then(
      _$AccountTypeSplitImpl(
        verified: null == verified
            ? _value.verified
            : verified // ignore: cast_nullable_to_non_nullable
                  as int,
        celebrities: null == celebrities
            ? _value.celebrities
            : celebrities // ignore: cast_nullable_to_non_nullable
                  as int,
        businesses: null == businesses
            ? _value.businesses
            : businesses // ignore: cast_nullable_to_non_nullable
                  as int,
        billionaires: null == billionaires
            ? _value.billionaires
            : billionaires // ignore: cast_nullable_to_non_nullable
                  as int,
        regular: null == regular
            ? _value.regular
            : regular // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$AccountTypeSplitImpl implements _AccountTypeSplit {
  const _$AccountTypeSplitImpl({
    required this.verified,
    required this.celebrities,
    required this.businesses,
    required this.billionaires,
    required this.regular,
  });

  factory _$AccountTypeSplitImpl.fromJson(Map<String, dynamic> json) =>
      _$$AccountTypeSplitImplFromJson(json);

  @override
  final int verified;
  @override
  final int celebrities;
  @override
  final int businesses;
  @override
  final int billionaires;
  @override
  final int regular;

  @override
  String toString() {
    return 'AccountTypeSplit(verified: $verified, celebrities: $celebrities, businesses: $businesses, billionaires: $billionaires, regular: $regular)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountTypeSplitImpl &&
            (identical(other.verified, verified) ||
                other.verified == verified) &&
            (identical(other.celebrities, celebrities) ||
                other.celebrities == celebrities) &&
            (identical(other.businesses, businesses) ||
                other.businesses == businesses) &&
            (identical(other.billionaires, billionaires) ||
                other.billionaires == billionaires) &&
            (identical(other.regular, regular) || other.regular == regular));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    verified,
    celebrities,
    businesses,
    billionaires,
    regular,
  );

  /// Create a copy of AccountTypeSplit
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountTypeSplitImplCopyWith<_$AccountTypeSplitImpl> get copyWith =>
      __$$AccountTypeSplitImplCopyWithImpl<_$AccountTypeSplitImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$AccountTypeSplitImplToJson(this);
  }
}

abstract class _AccountTypeSplit implements AccountTypeSplit {
  const factory _AccountTypeSplit({
    required final int verified,
    required final int celebrities,
    required final int businesses,
    required final int billionaires,
    required final int regular,
  }) = _$AccountTypeSplitImpl;

  factory _AccountTypeSplit.fromJson(Map<String, dynamic> json) =
      _$AccountTypeSplitImpl.fromJson;

  @override
  int get verified;
  @override
  int get celebrities;
  @override
  int get businesses;
  @override
  int get billionaires;
  @override
  int get regular;

  /// Create a copy of AccountTypeSplit
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountTypeSplitImplCopyWith<_$AccountTypeSplitImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

MutualStatusSplit _$MutualStatusSplitFromJson(Map<String, dynamic> json) {
  return _MutualStatusSplit.fromJson(json);
}

/// @nodoc
mixin _$MutualStatusSplit {
  int get mutual => throw _privateConstructorUsedError;
  int get notFollowingBack => throw _privateConstructorUsedError;
  int get notFollowedBack => throw _privateConstructorUsedError;

  /// Serializes this MutualStatusSplit to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MutualStatusSplit
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MutualStatusSplitCopyWith<MutualStatusSplit> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MutualStatusSplitCopyWith<$Res> {
  factory $MutualStatusSplitCopyWith(
    MutualStatusSplit value,
    $Res Function(MutualStatusSplit) then,
  ) = _$MutualStatusSplitCopyWithImpl<$Res, MutualStatusSplit>;
  @useResult
  $Res call({int mutual, int notFollowingBack, int notFollowedBack});
}

/// @nodoc
class _$MutualStatusSplitCopyWithImpl<$Res, $Val extends MutualStatusSplit>
    implements $MutualStatusSplitCopyWith<$Res> {
  _$MutualStatusSplitCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MutualStatusSplit
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mutual = null,
    Object? notFollowingBack = null,
    Object? notFollowedBack = null,
  }) {
    return _then(
      _value.copyWith(
            mutual: null == mutual
                ? _value.mutual
                : mutual // ignore: cast_nullable_to_non_nullable
                      as int,
            notFollowingBack: null == notFollowingBack
                ? _value.notFollowingBack
                : notFollowingBack // ignore: cast_nullable_to_non_nullable
                      as int,
            notFollowedBack: null == notFollowedBack
                ? _value.notFollowedBack
                : notFollowedBack // ignore: cast_nullable_to_non_nullable
                      as int,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$MutualStatusSplitImplCopyWith<$Res>
    implements $MutualStatusSplitCopyWith<$Res> {
  factory _$$MutualStatusSplitImplCopyWith(
    _$MutualStatusSplitImpl value,
    $Res Function(_$MutualStatusSplitImpl) then,
  ) = __$$MutualStatusSplitImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int mutual, int notFollowingBack, int notFollowedBack});
}

/// @nodoc
class __$$MutualStatusSplitImplCopyWithImpl<$Res>
    extends _$MutualStatusSplitCopyWithImpl<$Res, _$MutualStatusSplitImpl>
    implements _$$MutualStatusSplitImplCopyWith<$Res> {
  __$$MutualStatusSplitImplCopyWithImpl(
    _$MutualStatusSplitImpl _value,
    $Res Function(_$MutualStatusSplitImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MutualStatusSplit
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mutual = null,
    Object? notFollowingBack = null,
    Object? notFollowedBack = null,
  }) {
    return _then(
      _$MutualStatusSplitImpl(
        mutual: null == mutual
            ? _value.mutual
            : mutual // ignore: cast_nullable_to_non_nullable
                  as int,
        notFollowingBack: null == notFollowingBack
            ? _value.notFollowingBack
            : notFollowingBack // ignore: cast_nullable_to_non_nullable
                  as int,
        notFollowedBack: null == notFollowedBack
            ? _value.notFollowedBack
            : notFollowedBack // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$MutualStatusSplitImpl implements _MutualStatusSplit {
  const _$MutualStatusSplitImpl({
    required this.mutual,
    required this.notFollowingBack,
    required this.notFollowedBack,
  });

  factory _$MutualStatusSplitImpl.fromJson(Map<String, dynamic> json) =>
      _$$MutualStatusSplitImplFromJson(json);

  @override
  final int mutual;
  @override
  final int notFollowingBack;
  @override
  final int notFollowedBack;

  @override
  String toString() {
    return 'MutualStatusSplit(mutual: $mutual, notFollowingBack: $notFollowingBack, notFollowedBack: $notFollowedBack)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MutualStatusSplitImpl &&
            (identical(other.mutual, mutual) || other.mutual == mutual) &&
            (identical(other.notFollowingBack, notFollowingBack) ||
                other.notFollowingBack == notFollowingBack) &&
            (identical(other.notFollowedBack, notFollowedBack) ||
                other.notFollowedBack == notFollowedBack));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, mutual, notFollowingBack, notFollowedBack);

  /// Create a copy of MutualStatusSplit
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MutualStatusSplitImplCopyWith<_$MutualStatusSplitImpl> get copyWith =>
      __$$MutualStatusSplitImplCopyWithImpl<_$MutualStatusSplitImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$MutualStatusSplitImplToJson(this);
  }
}

abstract class _MutualStatusSplit implements MutualStatusSplit {
  const factory _MutualStatusSplit({
    required final int mutual,
    required final int notFollowingBack,
    required final int notFollowedBack,
  }) = _$MutualStatusSplitImpl;

  factory _MutualStatusSplit.fromJson(Map<String, dynamic> json) =
      _$MutualStatusSplitImpl.fromJson;

  @override
  int get mutual;
  @override
  int get notFollowingBack;
  @override
  int get notFollowedBack;

  /// Create a copy of MutualStatusSplit
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MutualStatusSplitImplCopyWith<_$MutualStatusSplitImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

FollowersFilter _$FollowersFilterFromJson(Map<String, dynamic> json) {
  return _FollowersFilter.fromJson(json);
}

/// @nodoc
mixin _$FollowersFilter {
  FilterType get type => throw _privateConstructorUsedError;
  Gender? get gender => throw _privateConstructorUsedError;
  AccountType? get accountType => throw _privateConstructorUsedError;
  MutualStatus? get mutualStatus => throw _privateConstructorUsedError;
  bool? get isVerified => throw _privateConstructorUsedError;
  bool? get isMuted => throw _privateConstructorUsedError;
  String? get searchQuery => throw _privateConstructorUsedError;

  /// Serializes this FollowersFilter to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FollowersFilter
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FollowersFilterCopyWith<FollowersFilter> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FollowersFilterCopyWith<$Res> {
  factory $FollowersFilterCopyWith(
    FollowersFilter value,
    $Res Function(FollowersFilter) then,
  ) = _$FollowersFilterCopyWithImpl<$Res, FollowersFilter>;
  @useResult
  $Res call({
    FilterType type,
    Gender? gender,
    AccountType? accountType,
    MutualStatus? mutualStatus,
    bool? isVerified,
    bool? isMuted,
    String? searchQuery,
  });
}

/// @nodoc
class _$FollowersFilterCopyWithImpl<$Res, $Val extends FollowersFilter>
    implements $FollowersFilterCopyWith<$Res> {
  _$FollowersFilterCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FollowersFilter
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? gender = freezed,
    Object? accountType = freezed,
    Object? mutualStatus = freezed,
    Object? isVerified = freezed,
    Object? isMuted = freezed,
    Object? searchQuery = freezed,
  }) {
    return _then(
      _value.copyWith(
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as FilterType,
            gender: freezed == gender
                ? _value.gender
                : gender // ignore: cast_nullable_to_non_nullable
                      as Gender?,
            accountType: freezed == accountType
                ? _value.accountType
                : accountType // ignore: cast_nullable_to_non_nullable
                      as AccountType?,
            mutualStatus: freezed == mutualStatus
                ? _value.mutualStatus
                : mutualStatus // ignore: cast_nullable_to_non_nullable
                      as MutualStatus?,
            isVerified: freezed == isVerified
                ? _value.isVerified
                : isVerified // ignore: cast_nullable_to_non_nullable
                      as bool?,
            isMuted: freezed == isMuted
                ? _value.isMuted
                : isMuted // ignore: cast_nullable_to_non_nullable
                      as bool?,
            searchQuery: freezed == searchQuery
                ? _value.searchQuery
                : searchQuery // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$FollowersFilterImplCopyWith<$Res>
    implements $FollowersFilterCopyWith<$Res> {
  factory _$$FollowersFilterImplCopyWith(
    _$FollowersFilterImpl value,
    $Res Function(_$FollowersFilterImpl) then,
  ) = __$$FollowersFilterImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    FilterType type,
    Gender? gender,
    AccountType? accountType,
    MutualStatus? mutualStatus,
    bool? isVerified,
    bool? isMuted,
    String? searchQuery,
  });
}

/// @nodoc
class __$$FollowersFilterImplCopyWithImpl<$Res>
    extends _$FollowersFilterCopyWithImpl<$Res, _$FollowersFilterImpl>
    implements _$$FollowersFilterImplCopyWith<$Res> {
  __$$FollowersFilterImplCopyWithImpl(
    _$FollowersFilterImpl _value,
    $Res Function(_$FollowersFilterImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of FollowersFilter
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? gender = freezed,
    Object? accountType = freezed,
    Object? mutualStatus = freezed,
    Object? isVerified = freezed,
    Object? isMuted = freezed,
    Object? searchQuery = freezed,
  }) {
    return _then(
      _$FollowersFilterImpl(
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as FilterType,
        gender: freezed == gender
            ? _value.gender
            : gender // ignore: cast_nullable_to_non_nullable
                  as Gender?,
        accountType: freezed == accountType
            ? _value.accountType
            : accountType // ignore: cast_nullable_to_non_nullable
                  as AccountType?,
        mutualStatus: freezed == mutualStatus
            ? _value.mutualStatus
            : mutualStatus // ignore: cast_nullable_to_non_nullable
                  as MutualStatus?,
        isVerified: freezed == isVerified
            ? _value.isVerified
            : isVerified // ignore: cast_nullable_to_non_nullable
                  as bool?,
        isMuted: freezed == isMuted
            ? _value.isMuted
            : isMuted // ignore: cast_nullable_to_non_nullable
                  as bool?,
        searchQuery: freezed == searchQuery
            ? _value.searchQuery
            : searchQuery // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$FollowersFilterImpl implements _FollowersFilter {
  const _$FollowersFilterImpl({
    required this.type,
    required this.gender,
    required this.accountType,
    required this.mutualStatus,
    required this.isVerified,
    required this.isMuted,
    required this.searchQuery,
  });

  factory _$FollowersFilterImpl.fromJson(Map<String, dynamic> json) =>
      _$$FollowersFilterImplFromJson(json);

  @override
  final FilterType type;
  @override
  final Gender? gender;
  @override
  final AccountType? accountType;
  @override
  final MutualStatus? mutualStatus;
  @override
  final bool? isVerified;
  @override
  final bool? isMuted;
  @override
  final String? searchQuery;

  @override
  String toString() {
    return 'FollowersFilter(type: $type, gender: $gender, accountType: $accountType, mutualStatus: $mutualStatus, isVerified: $isVerified, isMuted: $isMuted, searchQuery: $searchQuery)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FollowersFilterImpl &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.gender, gender) || other.gender == gender) &&
            (identical(other.accountType, accountType) ||
                other.accountType == accountType) &&
            (identical(other.mutualStatus, mutualStatus) ||
                other.mutualStatus == mutualStatus) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.isMuted, isMuted) || other.isMuted == isMuted) &&
            (identical(other.searchQuery, searchQuery) ||
                other.searchQuery == searchQuery));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    type,
    gender,
    accountType,
    mutualStatus,
    isVerified,
    isMuted,
    searchQuery,
  );

  /// Create a copy of FollowersFilter
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FollowersFilterImplCopyWith<_$FollowersFilterImpl> get copyWith =>
      __$$FollowersFilterImplCopyWithImpl<_$FollowersFilterImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$FollowersFilterImplToJson(this);
  }
}

abstract class _FollowersFilter implements FollowersFilter {
  const factory _FollowersFilter({
    required final FilterType type,
    required final Gender? gender,
    required final AccountType? accountType,
    required final MutualStatus? mutualStatus,
    required final bool? isVerified,
    required final bool? isMuted,
    required final String? searchQuery,
  }) = _$FollowersFilterImpl;

  factory _FollowersFilter.fromJson(Map<String, dynamic> json) =
      _$FollowersFilterImpl.fromJson;

  @override
  FilterType get type;
  @override
  Gender? get gender;
  @override
  AccountType? get accountType;
  @override
  MutualStatus? get mutualStatus;
  @override
  bool? get isVerified;
  @override
  bool? get isMuted;
  @override
  String? get searchQuery;

  /// Create a copy of FollowersFilter
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FollowersFilterImplCopyWith<_$FollowersFilterImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
