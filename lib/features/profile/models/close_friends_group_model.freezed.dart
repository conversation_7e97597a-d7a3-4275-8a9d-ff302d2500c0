// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'close_friends_group_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

CloseFriendsGroup _$CloseFriendsGroupFromJson(Map<String, dynamic> json) {
  return _CloseFriendsGroup.fromJson(json);
}

/// @nodoc
mixin _$CloseFriendsGroup {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get emoji => throw _privateConstructorUsedError;
  List<String> get memberIds => throw _privateConstructorUsedError;
  bool get isStoryVisible => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this CloseFriendsGroup to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CloseFriendsGroup
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CloseFriendsGroupCopyWith<CloseFriendsGroup> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CloseFriendsGroupCopyWith<$Res> {
  factory $CloseFriendsGroupCopyWith(
    CloseFriendsGroup value,
    $Res Function(CloseFriendsGroup) then,
  ) = _$CloseFriendsGroupCopyWithImpl<$Res, CloseFriendsGroup>;
  @useResult
  $Res call({
    String id,
    String name,
    String emoji,
    List<String> memberIds,
    bool isStoryVisible,
    DateTime createdAt,
    DateTime updatedAt,
  });
}

/// @nodoc
class _$CloseFriendsGroupCopyWithImpl<$Res, $Val extends CloseFriendsGroup>
    implements $CloseFriendsGroupCopyWith<$Res> {
  _$CloseFriendsGroupCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CloseFriendsGroup
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? emoji = null,
    Object? memberIds = null,
    Object? isStoryVisible = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            emoji: null == emoji
                ? _value.emoji
                : emoji // ignore: cast_nullable_to_non_nullable
                      as String,
            memberIds: null == memberIds
                ? _value.memberIds
                : memberIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            isStoryVisible: null == isStoryVisible
                ? _value.isStoryVisible
                : isStoryVisible // ignore: cast_nullable_to_non_nullable
                      as bool,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$CloseFriendsGroupImplCopyWith<$Res>
    implements $CloseFriendsGroupCopyWith<$Res> {
  factory _$$CloseFriendsGroupImplCopyWith(
    _$CloseFriendsGroupImpl value,
    $Res Function(_$CloseFriendsGroupImpl) then,
  ) = __$$CloseFriendsGroupImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String emoji,
    List<String> memberIds,
    bool isStoryVisible,
    DateTime createdAt,
    DateTime updatedAt,
  });
}

/// @nodoc
class __$$CloseFriendsGroupImplCopyWithImpl<$Res>
    extends _$CloseFriendsGroupCopyWithImpl<$Res, _$CloseFriendsGroupImpl>
    implements _$$CloseFriendsGroupImplCopyWith<$Res> {
  __$$CloseFriendsGroupImplCopyWithImpl(
    _$CloseFriendsGroupImpl _value,
    $Res Function(_$CloseFriendsGroupImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of CloseFriendsGroup
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? emoji = null,
    Object? memberIds = null,
    Object? isStoryVisible = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(
      _$CloseFriendsGroupImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        emoji: null == emoji
            ? _value.emoji
            : emoji // ignore: cast_nullable_to_non_nullable
                  as String,
        memberIds: null == memberIds
            ? _value._memberIds
            : memberIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        isStoryVisible: null == isStoryVisible
            ? _value.isStoryVisible
            : isStoryVisible // ignore: cast_nullable_to_non_nullable
                  as bool,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CloseFriendsGroupImpl implements _CloseFriendsGroup {
  const _$CloseFriendsGroupImpl({
    required this.id,
    required this.name,
    required this.emoji,
    required final List<String> memberIds,
    required this.isStoryVisible,
    required this.createdAt,
    required this.updatedAt,
  }) : _memberIds = memberIds;

  factory _$CloseFriendsGroupImpl.fromJson(Map<String, dynamic> json) =>
      _$$CloseFriendsGroupImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String emoji;
  final List<String> _memberIds;
  @override
  List<String> get memberIds {
    if (_memberIds is EqualUnmodifiableListView) return _memberIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_memberIds);
  }

  @override
  final bool isStoryVisible;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;

  @override
  String toString() {
    return 'CloseFriendsGroup(id: $id, name: $name, emoji: $emoji, memberIds: $memberIds, isStoryVisible: $isStoryVisible, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CloseFriendsGroupImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.emoji, emoji) || other.emoji == emoji) &&
            const DeepCollectionEquality().equals(
              other._memberIds,
              _memberIds,
            ) &&
            (identical(other.isStoryVisible, isStoryVisible) ||
                other.isStoryVisible == isStoryVisible) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    emoji,
    const DeepCollectionEquality().hash(_memberIds),
    isStoryVisible,
    createdAt,
    updatedAt,
  );

  /// Create a copy of CloseFriendsGroup
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CloseFriendsGroupImplCopyWith<_$CloseFriendsGroupImpl> get copyWith =>
      __$$CloseFriendsGroupImplCopyWithImpl<_$CloseFriendsGroupImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$CloseFriendsGroupImplToJson(this);
  }
}

abstract class _CloseFriendsGroup implements CloseFriendsGroup {
  const factory _CloseFriendsGroup({
    required final String id,
    required final String name,
    required final String emoji,
    required final List<String> memberIds,
    required final bool isStoryVisible,
    required final DateTime createdAt,
    required final DateTime updatedAt,
  }) = _$CloseFriendsGroupImpl;

  factory _CloseFriendsGroup.fromJson(Map<String, dynamic> json) =
      _$CloseFriendsGroupImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get emoji;
  @override
  List<String> get memberIds;
  @override
  bool get isStoryVisible;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;

  /// Create a copy of CloseFriendsGroup
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CloseFriendsGroupImplCopyWith<_$CloseFriendsGroupImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
