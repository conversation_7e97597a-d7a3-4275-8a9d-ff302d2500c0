// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'follower_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$FollowerModelImpl _$$FollowerModelImplFromJson(Map<String, dynamic> json) =>
    _$FollowerModelImpl(
      id: json['id'] as String,
      username: json['username'] as String,
      name: json['name'] as String,
      profilePictureUrl: json['profilePictureUrl'] as String,
      bio: json['bio'] as String?,
      isVerified: json['isVerified'] as bool,
      accountType: $enumDecode(_$AccountTypeEnumMap, json['accountType']),
      gender: $enumDecode(_$GenderEnumMap, json['gender']),
      isFollowingBack: json['isFollowingBack'] as bool,
      isMutual: json['isMutual'] as bool,
      followedAt: DateTime.parse(json['followedAt'] as String),
      isMuted: json['isMuted'] as bool,
      isSelected: json['isSelected'] as bool,
      tags: (json['tags'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$$FollowerModelImplToJson(_$FollowerModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'username': instance.username,
      'name': instance.name,
      'profilePictureUrl': instance.profilePictureUrl,
      'bio': instance.bio,
      'isVerified': instance.isVerified,
      'accountType': _$AccountTypeEnumMap[instance.accountType]!,
      'gender': _$GenderEnumMap[instance.gender]!,
      'isFollowingBack': instance.isFollowingBack,
      'isMutual': instance.isMutual,
      'followedAt': instance.followedAt.toIso8601String(),
      'isMuted': instance.isMuted,
      'isSelected': instance.isSelected,
      'tags': instance.tags,
    };

const _$AccountTypeEnumMap = {
  AccountType.regular: 'regular',
  AccountType.verified: 'verified',
  AccountType.celebrity: 'celebrity',
  AccountType.business: 'business',
  AccountType.billionaire: 'billionaire',
};

const _$GenderEnumMap = {
  Gender.male: 'male',
  Gender.female: 'female',
  Gender.other: 'other',
};

_$FollowingModelImpl _$$FollowingModelImplFromJson(Map<String, dynamic> json) =>
    _$FollowingModelImpl(
      id: json['id'] as String,
      username: json['username'] as String,
      name: json['name'] as String,
      profilePictureUrl: json['profilePictureUrl'] as String,
      bio: json['bio'] as String?,
      isVerified: json['isVerified'] as bool,
      accountType: $enumDecode(_$AccountTypeEnumMap, json['accountType']),
      gender: $enumDecode(_$GenderEnumMap, json['gender']),
      isFollowingBack: json['isFollowingBack'] as bool,
      isMutual: json['isMutual'] as bool,
      followedAt: DateTime.parse(json['followedAt'] as String),
      isMuted: json['isMuted'] as bool,
      isSelected: json['isSelected'] as bool,
      tags: (json['tags'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$$FollowingModelImplToJson(
  _$FollowingModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'username': instance.username,
  'name': instance.name,
  'profilePictureUrl': instance.profilePictureUrl,
  'bio': instance.bio,
  'isVerified': instance.isVerified,
  'accountType': _$AccountTypeEnumMap[instance.accountType]!,
  'gender': _$GenderEnumMap[instance.gender]!,
  'isFollowingBack': instance.isFollowingBack,
  'isMutual': instance.isMutual,
  'followedAt': instance.followedAt.toIso8601String(),
  'isMuted': instance.isMuted,
  'isSelected': instance.isSelected,
  'tags': instance.tags,
};

_$FollowersAnalyticsImpl _$$FollowersAnalyticsImplFromJson(
  Map<String, dynamic> json,
) => _$FollowersAnalyticsImpl(
  totalFollowers: (json['totalFollowers'] as num).toInt(),
  totalFollowing: (json['totalFollowing'] as num).toInt(),
  notFollowingBack: (json['notFollowingBack'] as num).toInt(),
  verifiedAccountsFollowed: (json['verifiedAccountsFollowed'] as num).toInt(),
  billionairesFollowed: (json['billionairesFollowed'] as num).toInt(),
  celebritiesFollowed: (json['celebritiesFollowed'] as num).toInt(),
  businessesFollowed: (json['businessesFollowed'] as num).toInt(),
  genderSplit: GenderSplit.fromJson(
    json['genderSplit'] as Map<String, dynamic>,
  ),
  accountTypeSplit: AccountTypeSplit.fromJson(
    json['accountTypeSplit'] as Map<String, dynamic>,
  ),
  mutualStatusSplit: MutualStatusSplit.fromJson(
    json['mutualStatusSplit'] as Map<String, dynamic>,
  ),
);

Map<String, dynamic> _$$FollowersAnalyticsImplToJson(
  _$FollowersAnalyticsImpl instance,
) => <String, dynamic>{
  'totalFollowers': instance.totalFollowers,
  'totalFollowing': instance.totalFollowing,
  'notFollowingBack': instance.notFollowingBack,
  'verifiedAccountsFollowed': instance.verifiedAccountsFollowed,
  'billionairesFollowed': instance.billionairesFollowed,
  'celebritiesFollowed': instance.celebritiesFollowed,
  'businessesFollowed': instance.businessesFollowed,
  'genderSplit': instance.genderSplit,
  'accountTypeSplit': instance.accountTypeSplit,
  'mutualStatusSplit': instance.mutualStatusSplit,
};

_$GenderSplitImpl _$$GenderSplitImplFromJson(Map<String, dynamic> json) =>
    _$GenderSplitImpl(
      male: (json['male'] as num).toInt(),
      female: (json['female'] as num).toInt(),
      other: (json['other'] as num).toInt(),
      malePercentage: (json['malePercentage'] as num).toDouble(),
      femalePercentage: (json['femalePercentage'] as num).toDouble(),
      otherPercentage: (json['otherPercentage'] as num).toDouble(),
    );

Map<String, dynamic> _$$GenderSplitImplToJson(_$GenderSplitImpl instance) =>
    <String, dynamic>{
      'male': instance.male,
      'female': instance.female,
      'other': instance.other,
      'malePercentage': instance.malePercentage,
      'femalePercentage': instance.femalePercentage,
      'otherPercentage': instance.otherPercentage,
    };

_$AccountTypeSplitImpl _$$AccountTypeSplitImplFromJson(
  Map<String, dynamic> json,
) => _$AccountTypeSplitImpl(
  verified: (json['verified'] as num).toInt(),
  celebrities: (json['celebrities'] as num).toInt(),
  businesses: (json['businesses'] as num).toInt(),
  billionaires: (json['billionaires'] as num).toInt(),
  regular: (json['regular'] as num).toInt(),
);

Map<String, dynamic> _$$AccountTypeSplitImplToJson(
  _$AccountTypeSplitImpl instance,
) => <String, dynamic>{
  'verified': instance.verified,
  'celebrities': instance.celebrities,
  'businesses': instance.businesses,
  'billionaires': instance.billionaires,
  'regular': instance.regular,
};

_$MutualStatusSplitImpl _$$MutualStatusSplitImplFromJson(
  Map<String, dynamic> json,
) => _$MutualStatusSplitImpl(
  mutual: (json['mutual'] as num).toInt(),
  notFollowingBack: (json['notFollowingBack'] as num).toInt(),
  notFollowedBack: (json['notFollowedBack'] as num).toInt(),
);

Map<String, dynamic> _$$MutualStatusSplitImplToJson(
  _$MutualStatusSplitImpl instance,
) => <String, dynamic>{
  'mutual': instance.mutual,
  'notFollowingBack': instance.notFollowingBack,
  'notFollowedBack': instance.notFollowedBack,
};

_$FollowersFilterImpl _$$FollowersFilterImplFromJson(
  Map<String, dynamic> json,
) => _$FollowersFilterImpl(
  type: $enumDecode(_$FilterTypeEnumMap, json['type']),
  gender: $enumDecodeNullable(_$GenderEnumMap, json['gender']),
  accountType: $enumDecodeNullable(_$AccountTypeEnumMap, json['accountType']),
  mutualStatus: $enumDecodeNullable(
    _$MutualStatusEnumMap,
    json['mutualStatus'],
  ),
  isVerified: json['isVerified'] as bool?,
  isMuted: json['isMuted'] as bool?,
  searchQuery: json['searchQuery'] as String?,
);

Map<String, dynamic> _$$FollowersFilterImplToJson(
  _$FollowersFilterImpl instance,
) => <String, dynamic>{
  'type': _$FilterTypeEnumMap[instance.type]!,
  'gender': _$GenderEnumMap[instance.gender],
  'accountType': _$AccountTypeEnumMap[instance.accountType],
  'mutualStatus': _$MutualStatusEnumMap[instance.mutualStatus],
  'isVerified': instance.isVerified,
  'isMuted': instance.isMuted,
  'searchQuery': instance.searchQuery,
};

const _$FilterTypeEnumMap = {
  FilterType.followers: 'followers',
  FilterType.following: 'following',
  FilterType.all: 'all',
};

const _$MutualStatusEnumMap = {
  MutualStatus.mutual: 'mutual',
  MutualStatus.notFollowingBack: 'notFollowingBack',
  MutualStatus.notFollowedBack: 'notFollowedBack',
};
