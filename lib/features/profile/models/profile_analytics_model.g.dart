// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'profile_analytics_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ProfileAnalyticsModelImpl _$$ProfileAnalyticsModelImplFromJson(
  Map<String, dynamic> json,
) => _$ProfileAnalyticsModelImpl(
  id: json['id'] as String,
  userId: json['userId'] as String,
  date: DateTime.parse(json['date'] as String),
  profileViews: ProfileViewsData.fromJson(
    json['profileViews'] as Map<String, dynamic>,
  ),
  followerGrowth: FollowerGrowthData.fromJson(
    json['followerGrowth'] as Map<String, dynamic>,
  ),
  engagement: EngagementData.fromJson(
    json['engagement'] as Map<String, dynamic>,
  ),
  contentPerformance: ContentPerformanceData.fromJson(
    json['contentPerformance'] as Map<String, dynamic>,
  ),
  audience: AudienceData.fromJson(json['audience'] as Map<String, dynamic>),
  metadata: json['metadata'] as Map<String, dynamic>,
);

Map<String, dynamic> _$$ProfileAnalyticsModelImplToJson(
  _$ProfileAnalyticsModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'date': instance.date.toIso8601String(),
  'profileViews': instance.profileViews,
  'followerGrowth': instance.followerGrowth,
  'engagement': instance.engagement,
  'contentPerformance': instance.contentPerformance,
  'audience': instance.audience,
  'metadata': instance.metadata,
};

_$ProfileViewsDataImpl _$$ProfileViewsDataImplFromJson(
  Map<String, dynamic> json,
) => _$ProfileViewsDataImpl(
  totalViews: (json['totalViews'] as num).toInt(),
  uniqueViews: (json['uniqueViews'] as num).toInt(),
  profileVisits: (json['profileVisits'] as num).toInt(),
  profileShares: (json['profileShares'] as num).toInt(),
  profileBookmarks: (json['profileBookmarks'] as num).toInt(),
  viewSources: (json['viewSources'] as List<dynamic>)
      .map((e) => ProfileViewSource.fromJson(e as Map<String, dynamic>))
      .toList(),
  viewTimes: (json['viewTimes'] as List<dynamic>)
      .map((e) => ProfileViewTime.fromJson(e as Map<String, dynamic>))
      .toList(),
  viewsByCountry: Map<String, int>.from(json['viewsByCountry'] as Map),
  viewsByDevice: Map<String, int>.from(json['viewsByDevice'] as Map),
);

Map<String, dynamic> _$$ProfileViewsDataImplToJson(
  _$ProfileViewsDataImpl instance,
) => <String, dynamic>{
  'totalViews': instance.totalViews,
  'uniqueViews': instance.uniqueViews,
  'profileVisits': instance.profileVisits,
  'profileShares': instance.profileShares,
  'profileBookmarks': instance.profileBookmarks,
  'viewSources': instance.viewSources,
  'viewTimes': instance.viewTimes,
  'viewsByCountry': instance.viewsByCountry,
  'viewsByDevice': instance.viewsByDevice,
};

_$ProfileViewSourceImpl _$$ProfileViewSourceImplFromJson(
  Map<String, dynamic> json,
) => _$ProfileViewSourceImpl(
  source: json['source'] as String,
  count: (json['count'] as num).toInt(),
  percentage: (json['percentage'] as num).toDouble(),
);

Map<String, dynamic> _$$ProfileViewSourceImplToJson(
  _$ProfileViewSourceImpl instance,
) => <String, dynamic>{
  'source': instance.source,
  'count': instance.count,
  'percentage': instance.percentage,
};

_$ProfileViewTimeImpl _$$ProfileViewTimeImplFromJson(
  Map<String, dynamic> json,
) => _$ProfileViewTimeImpl(
  timeSlot: json['timeSlot'] as String,
  count: (json['count'] as num).toInt(),
);

Map<String, dynamic> _$$ProfileViewTimeImplToJson(
  _$ProfileViewTimeImpl instance,
) => <String, dynamic>{'timeSlot': instance.timeSlot, 'count': instance.count};

_$FollowerGrowthDataImpl _$$FollowerGrowthDataImplFromJson(
  Map<String, dynamic> json,
) => _$FollowerGrowthDataImpl(
  totalFollowers: (json['totalFollowers'] as num).toInt(),
  newFollowers: (json['newFollowers'] as num).toInt(),
  lostFollowers: (json['lostFollowers'] as num).toInt(),
  growthRate: (json['growthRate'] as num).toDouble(),
  growthHistory: (json['growthHistory'] as List<dynamic>)
      .map((e) => FollowerGrowthPoint.fromJson(e as Map<String, dynamic>))
      .toList(),
  followersBySource: Map<String, int>.from(json['followersBySource'] as Map),
  followersByCountry: Map<String, int>.from(json['followersByCountry'] as Map),
  followersByAge: Map<String, int>.from(json['followersByAge'] as Map),
  followersByGender: Map<String, int>.from(json['followersByGender'] as Map),
);

Map<String, dynamic> _$$FollowerGrowthDataImplToJson(
  _$FollowerGrowthDataImpl instance,
) => <String, dynamic>{
  'totalFollowers': instance.totalFollowers,
  'newFollowers': instance.newFollowers,
  'lostFollowers': instance.lostFollowers,
  'growthRate': instance.growthRate,
  'growthHistory': instance.growthHistory,
  'followersBySource': instance.followersBySource,
  'followersByCountry': instance.followersByCountry,
  'followersByAge': instance.followersByAge,
  'followersByGender': instance.followersByGender,
};

_$FollowerGrowthPointImpl _$$FollowerGrowthPointImplFromJson(
  Map<String, dynamic> json,
) => _$FollowerGrowthPointImpl(
  date: DateTime.parse(json['date'] as String),
  followers: (json['followers'] as num).toInt(),
  newFollowers: (json['newFollowers'] as num).toInt(),
  lostFollowers: (json['lostFollowers'] as num).toInt(),
);

Map<String, dynamic> _$$FollowerGrowthPointImplToJson(
  _$FollowerGrowthPointImpl instance,
) => <String, dynamic>{
  'date': instance.date.toIso8601String(),
  'followers': instance.followers,
  'newFollowers': instance.newFollowers,
  'lostFollowers': instance.lostFollowers,
};

_$EngagementDataImpl _$$EngagementDataImplFromJson(
  Map<String, dynamic> json,
) => _$EngagementDataImpl(
  overallEngagementRate: (json['overallEngagementRate'] as num).toDouble(),
  totalEngagements: (json['totalEngagements'] as num).toInt(),
  likes: (json['likes'] as num).toInt(),
  comments: (json['comments'] as num).toInt(),
  shares: (json['shares'] as num).toInt(),
  saves: (json['saves'] as num).toInt(),
  engagementTrends: (json['engagementTrends'] as List<dynamic>)
      .map((e) => EngagementTrend.fromJson(e as Map<String, dynamic>))
      .toList(),
  engagementByContentType:
      (json['engagementByContentType'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
  engagementByTimeOfDay: (json['engagementByTimeOfDay'] as Map<String, dynamic>)
      .map((k, e) => MapEntry(k, (e as num).toDouble())),
  engagementByDayOfWeek: (json['engagementByDayOfWeek'] as Map<String, dynamic>)
      .map((k, e) => MapEntry(k, (e as num).toDouble())),
);

Map<String, dynamic> _$$EngagementDataImplToJson(
  _$EngagementDataImpl instance,
) => <String, dynamic>{
  'overallEngagementRate': instance.overallEngagementRate,
  'totalEngagements': instance.totalEngagements,
  'likes': instance.likes,
  'comments': instance.comments,
  'shares': instance.shares,
  'saves': instance.saves,
  'engagementTrends': instance.engagementTrends,
  'engagementByContentType': instance.engagementByContentType,
  'engagementByTimeOfDay': instance.engagementByTimeOfDay,
  'engagementByDayOfWeek': instance.engagementByDayOfWeek,
};

_$EngagementTrendImpl _$$EngagementTrendImplFromJson(
  Map<String, dynamic> json,
) => _$EngagementTrendImpl(
  date: DateTime.parse(json['date'] as String),
  engagementRate: (json['engagementRate'] as num).toDouble(),
  totalEngagements: (json['totalEngagements'] as num).toInt(),
);

Map<String, dynamic> _$$EngagementTrendImplToJson(
  _$EngagementTrendImpl instance,
) => <String, dynamic>{
  'date': instance.date.toIso8601String(),
  'engagementRate': instance.engagementRate,
  'totalEngagements': instance.totalEngagements,
};

_$ContentPerformanceDataImpl _$$ContentPerformanceDataImplFromJson(
  Map<String, dynamic> json,
) => _$ContentPerformanceDataImpl(
  totalPosts: (json['totalPosts'] as num).toInt(),
  averageLikes: (json['averageLikes'] as num).toDouble(),
  averageComments: (json['averageComments'] as num).toDouble(),
  averageShares: (json['averageShares'] as num).toDouble(),
  averageSaves: (json['averageSaves'] as num).toDouble(),
  topPosts: (json['topPosts'] as List<dynamic>)
      .map((e) => TopPerformingPost.fromJson(e as Map<String, dynamic>))
      .toList(),
  postsByType: Map<String, int>.from(json['postsByType'] as Map),
  performanceByType: (json['performanceByType'] as Map<String, dynamic>).map(
    (k, e) => MapEntry(k, (e as num).toDouble()),
  ),
);

Map<String, dynamic> _$$ContentPerformanceDataImplToJson(
  _$ContentPerformanceDataImpl instance,
) => <String, dynamic>{
  'totalPosts': instance.totalPosts,
  'averageLikes': instance.averageLikes,
  'averageComments': instance.averageComments,
  'averageShares': instance.averageShares,
  'averageSaves': instance.averageSaves,
  'topPosts': instance.topPosts,
  'postsByType': instance.postsByType,
  'performanceByType': instance.performanceByType,
};

_$TopPerformingPostImpl _$$TopPerformingPostImplFromJson(
  Map<String, dynamic> json,
) => _$TopPerformingPostImpl(
  postId: json['postId'] as String,
  postType: json['postType'] as String,
  likes: (json['likes'] as num).toInt(),
  comments: (json['comments'] as num).toInt(),
  shares: (json['shares'] as num).toInt(),
  saves: (json['saves'] as num).toInt(),
  engagementRate: (json['engagementRate'] as num).toDouble(),
  postedAt: DateTime.parse(json['postedAt'] as String),
);

Map<String, dynamic> _$$TopPerformingPostImplToJson(
  _$TopPerformingPostImpl instance,
) => <String, dynamic>{
  'postId': instance.postId,
  'postType': instance.postType,
  'likes': instance.likes,
  'comments': instance.comments,
  'shares': instance.shares,
  'saves': instance.saves,
  'engagementRate': instance.engagementRate,
  'postedAt': instance.postedAt.toIso8601String(),
};

_$AudienceDataImpl _$$AudienceDataImplFromJson(
  Map<String, dynamic> json,
) => _$AudienceDataImpl(
  totalAudience: (json['totalAudience'] as num).toInt(),
  audienceByCountry: Map<String, int>.from(json['audienceByCountry'] as Map),
  audienceByAge: Map<String, int>.from(json['audienceByAge'] as Map),
  audienceByGender: Map<String, int>.from(json['audienceByGender'] as Map),
  audienceByInterest: Map<String, int>.from(json['audienceByInterest'] as Map),
  topCountries: (json['topCountries'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  topInterests: (json['topInterests'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  averageAge: (json['averageAge'] as num).toDouble(),
  dominantGender: json['dominantGender'] as String,
);

Map<String, dynamic> _$$AudienceDataImplToJson(_$AudienceDataImpl instance) =>
    <String, dynamic>{
      'totalAudience': instance.totalAudience,
      'audienceByCountry': instance.audienceByCountry,
      'audienceByAge': instance.audienceByAge,
      'audienceByGender': instance.audienceByGender,
      'audienceByInterest': instance.audienceByInterest,
      'topCountries': instance.topCountries,
      'topInterests': instance.topInterests,
      'averageAge': instance.averageAge,
      'dominantGender': instance.dominantGender,
    };

_$ProfileAnalyticsSummaryImpl _$$ProfileAnalyticsSummaryImplFromJson(
  Map<String, dynamic> json,
) => _$ProfileAnalyticsSummaryImpl(
  userId: json['userId'] as String,
  periodStart: DateTime.parse(json['periodStart'] as String),
  periodEnd: DateTime.parse(json['periodEnd'] as String),
  totalProfileViews: (json['totalProfileViews'] as num).toInt(),
  totalFollowers: (json['totalFollowers'] as num).toInt(),
  engagementRate: (json['engagementRate'] as num).toDouble(),
  totalPosts: (json['totalPosts'] as num).toInt(),
  followerGrowthRate: (json['followerGrowthRate'] as num).toDouble(),
  topPerformingContent: (json['topPerformingContent'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  insights: json['insights'] as Map<String, dynamic>,
);

Map<String, dynamic> _$$ProfileAnalyticsSummaryImplToJson(
  _$ProfileAnalyticsSummaryImpl instance,
) => <String, dynamic>{
  'userId': instance.userId,
  'periodStart': instance.periodStart.toIso8601String(),
  'periodEnd': instance.periodEnd.toIso8601String(),
  'totalProfileViews': instance.totalProfileViews,
  'totalFollowers': instance.totalFollowers,
  'engagementRate': instance.engagementRate,
  'totalPosts': instance.totalPosts,
  'followerGrowthRate': instance.followerGrowthRate,
  'topPerformingContent': instance.topPerformingContent,
  'insights': instance.insights,
};
