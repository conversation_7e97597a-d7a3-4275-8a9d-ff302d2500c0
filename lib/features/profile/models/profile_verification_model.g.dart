// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'profile_verification_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ProfileVerificationModelImpl _$$ProfileVerificationModelImplFromJson(
  Map<String, dynamic> json,
) => _$ProfileVerificationModelImpl(
  id: json['id'] as String,
  userId: json['userId'] as String,
  type: $enumDecode(_$VerificationTypeEnumMap, json['type']),
  status: $enumDecode(_$VerificationStatusEnumMap, json['status']),
  createdAt: DateTime.parse(json['createdAt'] as String),
  completedAt: json['completedAt'] == null
      ? null
      : DateTime.parse(json['completedAt'] as String),
  expiresAt: json['expiresAt'] == null
      ? null
      : DateTime.parse(json['expiresAt'] as String),
  verificationCode: json['verificationCode'] as String?,
  documentUrl: json['documentUrl'] as String?,
  documentType: json['documentType'] as String?,
  verificationNotes: json['verificationNotes'] as String?,
  verifiedBy: json['verifiedBy'] as String?,
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$$ProfileVerificationModelImplToJson(
  _$ProfileVerificationModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'type': _$VerificationTypeEnumMap[instance.type]!,
  'status': _$VerificationStatusEnumMap[instance.status]!,
  'createdAt': instance.createdAt.toIso8601String(),
  'completedAt': instance.completedAt?.toIso8601String(),
  'expiresAt': instance.expiresAt?.toIso8601String(),
  'verificationCode': instance.verificationCode,
  'documentUrl': instance.documentUrl,
  'documentType': instance.documentType,
  'verificationNotes': instance.verificationNotes,
  'verifiedBy': instance.verifiedBy,
  'metadata': instance.metadata,
};

const _$VerificationTypeEnumMap = {
  VerificationType.email: 'email',
  VerificationType.phone: 'phone',
  VerificationType.identity: 'identity',
  VerificationType.business: 'business',
  VerificationType.celebrity: 'celebrity',
  VerificationType.billionaire: 'billionaire',
};

const _$VerificationStatusEnumMap = {
  VerificationStatus.pending: 'pending',
  VerificationStatus.submitted: 'submitted',
  VerificationStatus.underReview: 'underReview',
  VerificationStatus.approved: 'approved',
  VerificationStatus.rejected: 'rejected',
  VerificationStatus.expired: 'expired',
};

_$EmailVerificationModelImpl _$$EmailVerificationModelImplFromJson(
  Map<String, dynamic> json,
) => _$EmailVerificationModelImpl(
  email: json['email'] as String,
  verificationCode: json['verificationCode'] as String,
  expiresAt: DateTime.parse(json['expiresAt'] as String),
  isVerified: json['isVerified'] as bool,
  verifiedAt: json['verifiedAt'] == null
      ? null
      : DateTime.parse(json['verifiedAt'] as String),
);

Map<String, dynamic> _$$EmailVerificationModelImplToJson(
  _$EmailVerificationModelImpl instance,
) => <String, dynamic>{
  'email': instance.email,
  'verificationCode': instance.verificationCode,
  'expiresAt': instance.expiresAt.toIso8601String(),
  'isVerified': instance.isVerified,
  'verifiedAt': instance.verifiedAt?.toIso8601String(),
};

_$PhoneVerificationModelImpl _$$PhoneVerificationModelImplFromJson(
  Map<String, dynamic> json,
) => _$PhoneVerificationModelImpl(
  phone: json['phone'] as String,
  verificationCode: json['verificationCode'] as String,
  expiresAt: DateTime.parse(json['expiresAt'] as String),
  isVerified: json['isVerified'] as bool,
  verifiedAt: json['verifiedAt'] == null
      ? null
      : DateTime.parse(json['verifiedAt'] as String),
);

Map<String, dynamic> _$$PhoneVerificationModelImplToJson(
  _$PhoneVerificationModelImpl instance,
) => <String, dynamic>{
  'phone': instance.phone,
  'verificationCode': instance.verificationCode,
  'expiresAt': instance.expiresAt.toIso8601String(),
  'isVerified': instance.isVerified,
  'verifiedAt': instance.verifiedAt?.toIso8601String(),
};

_$IdentityVerificationModelImpl _$$IdentityVerificationModelImplFromJson(
  Map<String, dynamic> json,
) => _$IdentityVerificationModelImpl(
  id: json['id'] as String,
  userId: json['userId'] as String,
  documentType: json['documentType'] as String,
  documentUrl: json['documentUrl'] as String,
  fullName: json['fullName'] as String,
  dateOfBirth: DateTime.parse(json['dateOfBirth'] as String),
  nationality: json['nationality'] as String,
  status: $enumDecode(_$VerificationStatusEnumMap, json['status']),
  submittedAt: DateTime.parse(json['submittedAt'] as String),
  reviewedAt: json['reviewedAt'] == null
      ? null
      : DateTime.parse(json['reviewedAt'] as String),
  reviewedBy: json['reviewedBy'] as String?,
  rejectionReason: json['rejectionReason'] as String?,
  documentData: json['documentData'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$$IdentityVerificationModelImplToJson(
  _$IdentityVerificationModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'documentType': instance.documentType,
  'documentUrl': instance.documentUrl,
  'fullName': instance.fullName,
  'dateOfBirth': instance.dateOfBirth.toIso8601String(),
  'nationality': instance.nationality,
  'status': _$VerificationStatusEnumMap[instance.status]!,
  'submittedAt': instance.submittedAt.toIso8601String(),
  'reviewedAt': instance.reviewedAt?.toIso8601String(),
  'reviewedBy': instance.reviewedBy,
  'rejectionReason': instance.rejectionReason,
  'documentData': instance.documentData,
};

_$BusinessVerificationModelImpl _$$BusinessVerificationModelImplFromJson(
  Map<String, dynamic> json,
) => _$BusinessVerificationModelImpl(
  id: json['id'] as String,
  userId: json['userId'] as String,
  businessName: json['businessName'] as String,
  businessRegistrationNumber: json['businessRegistrationNumber'] as String,
  businessAddress: json['businessAddress'] as String,
  businessCategory: json['businessCategory'] as String,
  documentUrl: json['documentUrl'] as String,
  contactEmail: json['contactEmail'] as String,
  contactPhone: json['contactPhone'] as String,
  status: $enumDecode(_$VerificationStatusEnumMap, json['status']),
  submittedAt: DateTime.parse(json['submittedAt'] as String),
  reviewedAt: json['reviewedAt'] == null
      ? null
      : DateTime.parse(json['reviewedAt'] as String),
  reviewedBy: json['reviewedBy'] as String?,
  rejectionReason: json['rejectionReason'] as String?,
  verifiedBusinessName: json['verifiedBusinessName'] as String?,
  verifiedBusinessLogo: json['verifiedBusinessLogo'] as String?,
  businessData: json['businessData'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$$BusinessVerificationModelImplToJson(
  _$BusinessVerificationModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'businessName': instance.businessName,
  'businessRegistrationNumber': instance.businessRegistrationNumber,
  'businessAddress': instance.businessAddress,
  'businessCategory': instance.businessCategory,
  'documentUrl': instance.documentUrl,
  'contactEmail': instance.contactEmail,
  'contactPhone': instance.contactPhone,
  'status': _$VerificationStatusEnumMap[instance.status]!,
  'submittedAt': instance.submittedAt.toIso8601String(),
  'reviewedAt': instance.reviewedAt?.toIso8601String(),
  'reviewedBy': instance.reviewedBy,
  'rejectionReason': instance.rejectionReason,
  'verifiedBusinessName': instance.verifiedBusinessName,
  'verifiedBusinessLogo': instance.verifiedBusinessLogo,
  'businessData': instance.businessData,
};
