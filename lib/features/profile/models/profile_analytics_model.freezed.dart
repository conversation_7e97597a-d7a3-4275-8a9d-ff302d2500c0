// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'profile_analytics_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

ProfileAnalyticsModel _$ProfileAnalyticsModelFromJson(
  Map<String, dynamic> json,
) {
  return _ProfileAnalyticsModel.fromJson(json);
}

/// @nodoc
mixin _$ProfileAnalyticsModel {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  DateTime get date => throw _privateConstructorUsedError;
  ProfileViewsData get profileViews => throw _privateConstructorUsedError;
  FollowerGrowthData get followerGrowth => throw _privateConstructorUsedError;
  EngagementData get engagement => throw _privateConstructorUsedError;
  ContentPerformanceData get contentPerformance =>
      throw _privateConstructorUsedError;
  AudienceData get audience => throw _privateConstructorUsedError;
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;

  /// Serializes this ProfileAnalyticsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProfileAnalyticsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProfileAnalyticsModelCopyWith<ProfileAnalyticsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProfileAnalyticsModelCopyWith<$Res> {
  factory $ProfileAnalyticsModelCopyWith(
    ProfileAnalyticsModel value,
    $Res Function(ProfileAnalyticsModel) then,
  ) = _$ProfileAnalyticsModelCopyWithImpl<$Res, ProfileAnalyticsModel>;
  @useResult
  $Res call({
    String id,
    String userId,
    DateTime date,
    ProfileViewsData profileViews,
    FollowerGrowthData followerGrowth,
    EngagementData engagement,
    ContentPerformanceData contentPerformance,
    AudienceData audience,
    Map<String, dynamic> metadata,
  });

  $ProfileViewsDataCopyWith<$Res> get profileViews;
  $FollowerGrowthDataCopyWith<$Res> get followerGrowth;
  $EngagementDataCopyWith<$Res> get engagement;
  $ContentPerformanceDataCopyWith<$Res> get contentPerformance;
  $AudienceDataCopyWith<$Res> get audience;
}

/// @nodoc
class _$ProfileAnalyticsModelCopyWithImpl<
  $Res,
  $Val extends ProfileAnalyticsModel
>
    implements $ProfileAnalyticsModelCopyWith<$Res> {
  _$ProfileAnalyticsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProfileAnalyticsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? date = null,
    Object? profileViews = null,
    Object? followerGrowth = null,
    Object? engagement = null,
    Object? contentPerformance = null,
    Object? audience = null,
    Object? metadata = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            date: null == date
                ? _value.date
                : date // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            profileViews: null == profileViews
                ? _value.profileViews
                : profileViews // ignore: cast_nullable_to_non_nullable
                      as ProfileViewsData,
            followerGrowth: null == followerGrowth
                ? _value.followerGrowth
                : followerGrowth // ignore: cast_nullable_to_non_nullable
                      as FollowerGrowthData,
            engagement: null == engagement
                ? _value.engagement
                : engagement // ignore: cast_nullable_to_non_nullable
                      as EngagementData,
            contentPerformance: null == contentPerformance
                ? _value.contentPerformance
                : contentPerformance // ignore: cast_nullable_to_non_nullable
                      as ContentPerformanceData,
            audience: null == audience
                ? _value.audience
                : audience // ignore: cast_nullable_to_non_nullable
                      as AudienceData,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }

  /// Create a copy of ProfileAnalyticsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProfileViewsDataCopyWith<$Res> get profileViews {
    return $ProfileViewsDataCopyWith<$Res>(_value.profileViews, (value) {
      return _then(_value.copyWith(profileViews: value) as $Val);
    });
  }

  /// Create a copy of ProfileAnalyticsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FollowerGrowthDataCopyWith<$Res> get followerGrowth {
    return $FollowerGrowthDataCopyWith<$Res>(_value.followerGrowth, (value) {
      return _then(_value.copyWith(followerGrowth: value) as $Val);
    });
  }

  /// Create a copy of ProfileAnalyticsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $EngagementDataCopyWith<$Res> get engagement {
    return $EngagementDataCopyWith<$Res>(_value.engagement, (value) {
      return _then(_value.copyWith(engagement: value) as $Val);
    });
  }

  /// Create a copy of ProfileAnalyticsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ContentPerformanceDataCopyWith<$Res> get contentPerformance {
    return $ContentPerformanceDataCopyWith<$Res>(_value.contentPerformance, (
      value,
    ) {
      return _then(_value.copyWith(contentPerformance: value) as $Val);
    });
  }

  /// Create a copy of ProfileAnalyticsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AudienceDataCopyWith<$Res> get audience {
    return $AudienceDataCopyWith<$Res>(_value.audience, (value) {
      return _then(_value.copyWith(audience: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ProfileAnalyticsModelImplCopyWith<$Res>
    implements $ProfileAnalyticsModelCopyWith<$Res> {
  factory _$$ProfileAnalyticsModelImplCopyWith(
    _$ProfileAnalyticsModelImpl value,
    $Res Function(_$ProfileAnalyticsModelImpl) then,
  ) = __$$ProfileAnalyticsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    DateTime date,
    ProfileViewsData profileViews,
    FollowerGrowthData followerGrowth,
    EngagementData engagement,
    ContentPerformanceData contentPerformance,
    AudienceData audience,
    Map<String, dynamic> metadata,
  });

  @override
  $ProfileViewsDataCopyWith<$Res> get profileViews;
  @override
  $FollowerGrowthDataCopyWith<$Res> get followerGrowth;
  @override
  $EngagementDataCopyWith<$Res> get engagement;
  @override
  $ContentPerformanceDataCopyWith<$Res> get contentPerformance;
  @override
  $AudienceDataCopyWith<$Res> get audience;
}

/// @nodoc
class __$$ProfileAnalyticsModelImplCopyWithImpl<$Res>
    extends
        _$ProfileAnalyticsModelCopyWithImpl<$Res, _$ProfileAnalyticsModelImpl>
    implements _$$ProfileAnalyticsModelImplCopyWith<$Res> {
  __$$ProfileAnalyticsModelImplCopyWithImpl(
    _$ProfileAnalyticsModelImpl _value,
    $Res Function(_$ProfileAnalyticsModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ProfileAnalyticsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? date = null,
    Object? profileViews = null,
    Object? followerGrowth = null,
    Object? engagement = null,
    Object? contentPerformance = null,
    Object? audience = null,
    Object? metadata = null,
  }) {
    return _then(
      _$ProfileAnalyticsModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        date: null == date
            ? _value.date
            : date // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        profileViews: null == profileViews
            ? _value.profileViews
            : profileViews // ignore: cast_nullable_to_non_nullable
                  as ProfileViewsData,
        followerGrowth: null == followerGrowth
            ? _value.followerGrowth
            : followerGrowth // ignore: cast_nullable_to_non_nullable
                  as FollowerGrowthData,
        engagement: null == engagement
            ? _value.engagement
            : engagement // ignore: cast_nullable_to_non_nullable
                  as EngagementData,
        contentPerformance: null == contentPerformance
            ? _value.contentPerformance
            : contentPerformance // ignore: cast_nullable_to_non_nullable
                  as ContentPerformanceData,
        audience: null == audience
            ? _value.audience
            : audience // ignore: cast_nullable_to_non_nullable
                  as AudienceData,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ProfileAnalyticsModelImpl implements _ProfileAnalyticsModel {
  const _$ProfileAnalyticsModelImpl({
    required this.id,
    required this.userId,
    required this.date,
    required this.profileViews,
    required this.followerGrowth,
    required this.engagement,
    required this.contentPerformance,
    required this.audience,
    required final Map<String, dynamic> metadata,
  }) : _metadata = metadata;

  factory _$ProfileAnalyticsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProfileAnalyticsModelImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final DateTime date;
  @override
  final ProfileViewsData profileViews;
  @override
  final FollowerGrowthData followerGrowth;
  @override
  final EngagementData engagement;
  @override
  final ContentPerformanceData contentPerformance;
  @override
  final AudienceData audience;
  final Map<String, dynamic> _metadata;
  @override
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  String toString() {
    return 'ProfileAnalyticsModel(id: $id, userId: $userId, date: $date, profileViews: $profileViews, followerGrowth: $followerGrowth, engagement: $engagement, contentPerformance: $contentPerformance, audience: $audience, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileAnalyticsModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.profileViews, profileViews) ||
                other.profileViews == profileViews) &&
            (identical(other.followerGrowth, followerGrowth) ||
                other.followerGrowth == followerGrowth) &&
            (identical(other.engagement, engagement) ||
                other.engagement == engagement) &&
            (identical(other.contentPerformance, contentPerformance) ||
                other.contentPerformance == contentPerformance) &&
            (identical(other.audience, audience) ||
                other.audience == audience) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    userId,
    date,
    profileViews,
    followerGrowth,
    engagement,
    contentPerformance,
    audience,
    const DeepCollectionEquality().hash(_metadata),
  );

  /// Create a copy of ProfileAnalyticsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfileAnalyticsModelImplCopyWith<_$ProfileAnalyticsModelImpl>
  get copyWith =>
      __$$ProfileAnalyticsModelImplCopyWithImpl<_$ProfileAnalyticsModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ProfileAnalyticsModelImplToJson(this);
  }
}

abstract class _ProfileAnalyticsModel implements ProfileAnalyticsModel {
  const factory _ProfileAnalyticsModel({
    required final String id,
    required final String userId,
    required final DateTime date,
    required final ProfileViewsData profileViews,
    required final FollowerGrowthData followerGrowth,
    required final EngagementData engagement,
    required final ContentPerformanceData contentPerformance,
    required final AudienceData audience,
    required final Map<String, dynamic> metadata,
  }) = _$ProfileAnalyticsModelImpl;

  factory _ProfileAnalyticsModel.fromJson(Map<String, dynamic> json) =
      _$ProfileAnalyticsModelImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  DateTime get date;
  @override
  ProfileViewsData get profileViews;
  @override
  FollowerGrowthData get followerGrowth;
  @override
  EngagementData get engagement;
  @override
  ContentPerformanceData get contentPerformance;
  @override
  AudienceData get audience;
  @override
  Map<String, dynamic> get metadata;

  /// Create a copy of ProfileAnalyticsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProfileAnalyticsModelImplCopyWith<_$ProfileAnalyticsModelImpl>
  get copyWith => throw _privateConstructorUsedError;
}

ProfileViewsData _$ProfileViewsDataFromJson(Map<String, dynamic> json) {
  return _ProfileViewsData.fromJson(json);
}

/// @nodoc
mixin _$ProfileViewsData {
  int get totalViews => throw _privateConstructorUsedError;
  int get uniqueViews => throw _privateConstructorUsedError;
  int get profileVisits => throw _privateConstructorUsedError;
  int get profileShares => throw _privateConstructorUsedError;
  int get profileBookmarks => throw _privateConstructorUsedError;
  List<ProfileViewSource> get viewSources => throw _privateConstructorUsedError;
  List<ProfileViewTime> get viewTimes => throw _privateConstructorUsedError;
  Map<String, int> get viewsByCountry => throw _privateConstructorUsedError;
  Map<String, int> get viewsByDevice => throw _privateConstructorUsedError;

  /// Serializes this ProfileViewsData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProfileViewsData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProfileViewsDataCopyWith<ProfileViewsData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProfileViewsDataCopyWith<$Res> {
  factory $ProfileViewsDataCopyWith(
    ProfileViewsData value,
    $Res Function(ProfileViewsData) then,
  ) = _$ProfileViewsDataCopyWithImpl<$Res, ProfileViewsData>;
  @useResult
  $Res call({
    int totalViews,
    int uniqueViews,
    int profileVisits,
    int profileShares,
    int profileBookmarks,
    List<ProfileViewSource> viewSources,
    List<ProfileViewTime> viewTimes,
    Map<String, int> viewsByCountry,
    Map<String, int> viewsByDevice,
  });
}

/// @nodoc
class _$ProfileViewsDataCopyWithImpl<$Res, $Val extends ProfileViewsData>
    implements $ProfileViewsDataCopyWith<$Res> {
  _$ProfileViewsDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProfileViewsData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalViews = null,
    Object? uniqueViews = null,
    Object? profileVisits = null,
    Object? profileShares = null,
    Object? profileBookmarks = null,
    Object? viewSources = null,
    Object? viewTimes = null,
    Object? viewsByCountry = null,
    Object? viewsByDevice = null,
  }) {
    return _then(
      _value.copyWith(
            totalViews: null == totalViews
                ? _value.totalViews
                : totalViews // ignore: cast_nullable_to_non_nullable
                      as int,
            uniqueViews: null == uniqueViews
                ? _value.uniqueViews
                : uniqueViews // ignore: cast_nullable_to_non_nullable
                      as int,
            profileVisits: null == profileVisits
                ? _value.profileVisits
                : profileVisits // ignore: cast_nullable_to_non_nullable
                      as int,
            profileShares: null == profileShares
                ? _value.profileShares
                : profileShares // ignore: cast_nullable_to_non_nullable
                      as int,
            profileBookmarks: null == profileBookmarks
                ? _value.profileBookmarks
                : profileBookmarks // ignore: cast_nullable_to_non_nullable
                      as int,
            viewSources: null == viewSources
                ? _value.viewSources
                : viewSources // ignore: cast_nullable_to_non_nullable
                      as List<ProfileViewSource>,
            viewTimes: null == viewTimes
                ? _value.viewTimes
                : viewTimes // ignore: cast_nullable_to_non_nullable
                      as List<ProfileViewTime>,
            viewsByCountry: null == viewsByCountry
                ? _value.viewsByCountry
                : viewsByCountry // ignore: cast_nullable_to_non_nullable
                      as Map<String, int>,
            viewsByDevice: null == viewsByDevice
                ? _value.viewsByDevice
                : viewsByDevice // ignore: cast_nullable_to_non_nullable
                      as Map<String, int>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ProfileViewsDataImplCopyWith<$Res>
    implements $ProfileViewsDataCopyWith<$Res> {
  factory _$$ProfileViewsDataImplCopyWith(
    _$ProfileViewsDataImpl value,
    $Res Function(_$ProfileViewsDataImpl) then,
  ) = __$$ProfileViewsDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int totalViews,
    int uniqueViews,
    int profileVisits,
    int profileShares,
    int profileBookmarks,
    List<ProfileViewSource> viewSources,
    List<ProfileViewTime> viewTimes,
    Map<String, int> viewsByCountry,
    Map<String, int> viewsByDevice,
  });
}

/// @nodoc
class __$$ProfileViewsDataImplCopyWithImpl<$Res>
    extends _$ProfileViewsDataCopyWithImpl<$Res, _$ProfileViewsDataImpl>
    implements _$$ProfileViewsDataImplCopyWith<$Res> {
  __$$ProfileViewsDataImplCopyWithImpl(
    _$ProfileViewsDataImpl _value,
    $Res Function(_$ProfileViewsDataImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ProfileViewsData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalViews = null,
    Object? uniqueViews = null,
    Object? profileVisits = null,
    Object? profileShares = null,
    Object? profileBookmarks = null,
    Object? viewSources = null,
    Object? viewTimes = null,
    Object? viewsByCountry = null,
    Object? viewsByDevice = null,
  }) {
    return _then(
      _$ProfileViewsDataImpl(
        totalViews: null == totalViews
            ? _value.totalViews
            : totalViews // ignore: cast_nullable_to_non_nullable
                  as int,
        uniqueViews: null == uniqueViews
            ? _value.uniqueViews
            : uniqueViews // ignore: cast_nullable_to_non_nullable
                  as int,
        profileVisits: null == profileVisits
            ? _value.profileVisits
            : profileVisits // ignore: cast_nullable_to_non_nullable
                  as int,
        profileShares: null == profileShares
            ? _value.profileShares
            : profileShares // ignore: cast_nullable_to_non_nullable
                  as int,
        profileBookmarks: null == profileBookmarks
            ? _value.profileBookmarks
            : profileBookmarks // ignore: cast_nullable_to_non_nullable
                  as int,
        viewSources: null == viewSources
            ? _value._viewSources
            : viewSources // ignore: cast_nullable_to_non_nullable
                  as List<ProfileViewSource>,
        viewTimes: null == viewTimes
            ? _value._viewTimes
            : viewTimes // ignore: cast_nullable_to_non_nullable
                  as List<ProfileViewTime>,
        viewsByCountry: null == viewsByCountry
            ? _value._viewsByCountry
            : viewsByCountry // ignore: cast_nullable_to_non_nullable
                  as Map<String, int>,
        viewsByDevice: null == viewsByDevice
            ? _value._viewsByDevice
            : viewsByDevice // ignore: cast_nullable_to_non_nullable
                  as Map<String, int>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ProfileViewsDataImpl implements _ProfileViewsData {
  const _$ProfileViewsDataImpl({
    required this.totalViews,
    required this.uniqueViews,
    required this.profileVisits,
    required this.profileShares,
    required this.profileBookmarks,
    required final List<ProfileViewSource> viewSources,
    required final List<ProfileViewTime> viewTimes,
    required final Map<String, int> viewsByCountry,
    required final Map<String, int> viewsByDevice,
  }) : _viewSources = viewSources,
       _viewTimes = viewTimes,
       _viewsByCountry = viewsByCountry,
       _viewsByDevice = viewsByDevice;

  factory _$ProfileViewsDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProfileViewsDataImplFromJson(json);

  @override
  final int totalViews;
  @override
  final int uniqueViews;
  @override
  final int profileVisits;
  @override
  final int profileShares;
  @override
  final int profileBookmarks;
  final List<ProfileViewSource> _viewSources;
  @override
  List<ProfileViewSource> get viewSources {
    if (_viewSources is EqualUnmodifiableListView) return _viewSources;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_viewSources);
  }

  final List<ProfileViewTime> _viewTimes;
  @override
  List<ProfileViewTime> get viewTimes {
    if (_viewTimes is EqualUnmodifiableListView) return _viewTimes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_viewTimes);
  }

  final Map<String, int> _viewsByCountry;
  @override
  Map<String, int> get viewsByCountry {
    if (_viewsByCountry is EqualUnmodifiableMapView) return _viewsByCountry;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_viewsByCountry);
  }

  final Map<String, int> _viewsByDevice;
  @override
  Map<String, int> get viewsByDevice {
    if (_viewsByDevice is EqualUnmodifiableMapView) return _viewsByDevice;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_viewsByDevice);
  }

  @override
  String toString() {
    return 'ProfileViewsData(totalViews: $totalViews, uniqueViews: $uniqueViews, profileVisits: $profileVisits, profileShares: $profileShares, profileBookmarks: $profileBookmarks, viewSources: $viewSources, viewTimes: $viewTimes, viewsByCountry: $viewsByCountry, viewsByDevice: $viewsByDevice)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileViewsDataImpl &&
            (identical(other.totalViews, totalViews) ||
                other.totalViews == totalViews) &&
            (identical(other.uniqueViews, uniqueViews) ||
                other.uniqueViews == uniqueViews) &&
            (identical(other.profileVisits, profileVisits) ||
                other.profileVisits == profileVisits) &&
            (identical(other.profileShares, profileShares) ||
                other.profileShares == profileShares) &&
            (identical(other.profileBookmarks, profileBookmarks) ||
                other.profileBookmarks == profileBookmarks) &&
            const DeepCollectionEquality().equals(
              other._viewSources,
              _viewSources,
            ) &&
            const DeepCollectionEquality().equals(
              other._viewTimes,
              _viewTimes,
            ) &&
            const DeepCollectionEquality().equals(
              other._viewsByCountry,
              _viewsByCountry,
            ) &&
            const DeepCollectionEquality().equals(
              other._viewsByDevice,
              _viewsByDevice,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    totalViews,
    uniqueViews,
    profileVisits,
    profileShares,
    profileBookmarks,
    const DeepCollectionEquality().hash(_viewSources),
    const DeepCollectionEquality().hash(_viewTimes),
    const DeepCollectionEquality().hash(_viewsByCountry),
    const DeepCollectionEquality().hash(_viewsByDevice),
  );

  /// Create a copy of ProfileViewsData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfileViewsDataImplCopyWith<_$ProfileViewsDataImpl> get copyWith =>
      __$$ProfileViewsDataImplCopyWithImpl<_$ProfileViewsDataImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ProfileViewsDataImplToJson(this);
  }
}

abstract class _ProfileViewsData implements ProfileViewsData {
  const factory _ProfileViewsData({
    required final int totalViews,
    required final int uniqueViews,
    required final int profileVisits,
    required final int profileShares,
    required final int profileBookmarks,
    required final List<ProfileViewSource> viewSources,
    required final List<ProfileViewTime> viewTimes,
    required final Map<String, int> viewsByCountry,
    required final Map<String, int> viewsByDevice,
  }) = _$ProfileViewsDataImpl;

  factory _ProfileViewsData.fromJson(Map<String, dynamic> json) =
      _$ProfileViewsDataImpl.fromJson;

  @override
  int get totalViews;
  @override
  int get uniqueViews;
  @override
  int get profileVisits;
  @override
  int get profileShares;
  @override
  int get profileBookmarks;
  @override
  List<ProfileViewSource> get viewSources;
  @override
  List<ProfileViewTime> get viewTimes;
  @override
  Map<String, int> get viewsByCountry;
  @override
  Map<String, int> get viewsByDevice;

  /// Create a copy of ProfileViewsData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProfileViewsDataImplCopyWith<_$ProfileViewsDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProfileViewSource _$ProfileViewSourceFromJson(Map<String, dynamic> json) {
  return _ProfileViewSource.fromJson(json);
}

/// @nodoc
mixin _$ProfileViewSource {
  String get source => throw _privateConstructorUsedError;
  int get count => throw _privateConstructorUsedError;
  double get percentage => throw _privateConstructorUsedError;

  /// Serializes this ProfileViewSource to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProfileViewSource
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProfileViewSourceCopyWith<ProfileViewSource> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProfileViewSourceCopyWith<$Res> {
  factory $ProfileViewSourceCopyWith(
    ProfileViewSource value,
    $Res Function(ProfileViewSource) then,
  ) = _$ProfileViewSourceCopyWithImpl<$Res, ProfileViewSource>;
  @useResult
  $Res call({String source, int count, double percentage});
}

/// @nodoc
class _$ProfileViewSourceCopyWithImpl<$Res, $Val extends ProfileViewSource>
    implements $ProfileViewSourceCopyWith<$Res> {
  _$ProfileViewSourceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProfileViewSource
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? source = null,
    Object? count = null,
    Object? percentage = null,
  }) {
    return _then(
      _value.copyWith(
            source: null == source
                ? _value.source
                : source // ignore: cast_nullable_to_non_nullable
                      as String,
            count: null == count
                ? _value.count
                : count // ignore: cast_nullable_to_non_nullable
                      as int,
            percentage: null == percentage
                ? _value.percentage
                : percentage // ignore: cast_nullable_to_non_nullable
                      as double,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ProfileViewSourceImplCopyWith<$Res>
    implements $ProfileViewSourceCopyWith<$Res> {
  factory _$$ProfileViewSourceImplCopyWith(
    _$ProfileViewSourceImpl value,
    $Res Function(_$ProfileViewSourceImpl) then,
  ) = __$$ProfileViewSourceImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String source, int count, double percentage});
}

/// @nodoc
class __$$ProfileViewSourceImplCopyWithImpl<$Res>
    extends _$ProfileViewSourceCopyWithImpl<$Res, _$ProfileViewSourceImpl>
    implements _$$ProfileViewSourceImplCopyWith<$Res> {
  __$$ProfileViewSourceImplCopyWithImpl(
    _$ProfileViewSourceImpl _value,
    $Res Function(_$ProfileViewSourceImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ProfileViewSource
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? source = null,
    Object? count = null,
    Object? percentage = null,
  }) {
    return _then(
      _$ProfileViewSourceImpl(
        source: null == source
            ? _value.source
            : source // ignore: cast_nullable_to_non_nullable
                  as String,
        count: null == count
            ? _value.count
            : count // ignore: cast_nullable_to_non_nullable
                  as int,
        percentage: null == percentage
            ? _value.percentage
            : percentage // ignore: cast_nullable_to_non_nullable
                  as double,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ProfileViewSourceImpl implements _ProfileViewSource {
  const _$ProfileViewSourceImpl({
    required this.source,
    required this.count,
    required this.percentage,
  });

  factory _$ProfileViewSourceImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProfileViewSourceImplFromJson(json);

  @override
  final String source;
  @override
  final int count;
  @override
  final double percentage;

  @override
  String toString() {
    return 'ProfileViewSource(source: $source, count: $count, percentage: $percentage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileViewSourceImpl &&
            (identical(other.source, source) || other.source == source) &&
            (identical(other.count, count) || other.count == count) &&
            (identical(other.percentage, percentage) ||
                other.percentage == percentage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, source, count, percentage);

  /// Create a copy of ProfileViewSource
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfileViewSourceImplCopyWith<_$ProfileViewSourceImpl> get copyWith =>
      __$$ProfileViewSourceImplCopyWithImpl<_$ProfileViewSourceImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ProfileViewSourceImplToJson(this);
  }
}

abstract class _ProfileViewSource implements ProfileViewSource {
  const factory _ProfileViewSource({
    required final String source,
    required final int count,
    required final double percentage,
  }) = _$ProfileViewSourceImpl;

  factory _ProfileViewSource.fromJson(Map<String, dynamic> json) =
      _$ProfileViewSourceImpl.fromJson;

  @override
  String get source;
  @override
  int get count;
  @override
  double get percentage;

  /// Create a copy of ProfileViewSource
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProfileViewSourceImplCopyWith<_$ProfileViewSourceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProfileViewTime _$ProfileViewTimeFromJson(Map<String, dynamic> json) {
  return _ProfileViewTime.fromJson(json);
}

/// @nodoc
mixin _$ProfileViewTime {
  String get timeSlot => throw _privateConstructorUsedError;
  int get count => throw _privateConstructorUsedError;

  /// Serializes this ProfileViewTime to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProfileViewTime
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProfileViewTimeCopyWith<ProfileViewTime> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProfileViewTimeCopyWith<$Res> {
  factory $ProfileViewTimeCopyWith(
    ProfileViewTime value,
    $Res Function(ProfileViewTime) then,
  ) = _$ProfileViewTimeCopyWithImpl<$Res, ProfileViewTime>;
  @useResult
  $Res call({String timeSlot, int count});
}

/// @nodoc
class _$ProfileViewTimeCopyWithImpl<$Res, $Val extends ProfileViewTime>
    implements $ProfileViewTimeCopyWith<$Res> {
  _$ProfileViewTimeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProfileViewTime
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? timeSlot = null, Object? count = null}) {
    return _then(
      _value.copyWith(
            timeSlot: null == timeSlot
                ? _value.timeSlot
                : timeSlot // ignore: cast_nullable_to_non_nullable
                      as String,
            count: null == count
                ? _value.count
                : count // ignore: cast_nullable_to_non_nullable
                      as int,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ProfileViewTimeImplCopyWith<$Res>
    implements $ProfileViewTimeCopyWith<$Res> {
  factory _$$ProfileViewTimeImplCopyWith(
    _$ProfileViewTimeImpl value,
    $Res Function(_$ProfileViewTimeImpl) then,
  ) = __$$ProfileViewTimeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String timeSlot, int count});
}

/// @nodoc
class __$$ProfileViewTimeImplCopyWithImpl<$Res>
    extends _$ProfileViewTimeCopyWithImpl<$Res, _$ProfileViewTimeImpl>
    implements _$$ProfileViewTimeImplCopyWith<$Res> {
  __$$ProfileViewTimeImplCopyWithImpl(
    _$ProfileViewTimeImpl _value,
    $Res Function(_$ProfileViewTimeImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ProfileViewTime
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? timeSlot = null, Object? count = null}) {
    return _then(
      _$ProfileViewTimeImpl(
        timeSlot: null == timeSlot
            ? _value.timeSlot
            : timeSlot // ignore: cast_nullable_to_non_nullable
                  as String,
        count: null == count
            ? _value.count
            : count // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ProfileViewTimeImpl implements _ProfileViewTime {
  const _$ProfileViewTimeImpl({required this.timeSlot, required this.count});

  factory _$ProfileViewTimeImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProfileViewTimeImplFromJson(json);

  @override
  final String timeSlot;
  @override
  final int count;

  @override
  String toString() {
    return 'ProfileViewTime(timeSlot: $timeSlot, count: $count)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileViewTimeImpl &&
            (identical(other.timeSlot, timeSlot) ||
                other.timeSlot == timeSlot) &&
            (identical(other.count, count) || other.count == count));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, timeSlot, count);

  /// Create a copy of ProfileViewTime
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfileViewTimeImplCopyWith<_$ProfileViewTimeImpl> get copyWith =>
      __$$ProfileViewTimeImplCopyWithImpl<_$ProfileViewTimeImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ProfileViewTimeImplToJson(this);
  }
}

abstract class _ProfileViewTime implements ProfileViewTime {
  const factory _ProfileViewTime({
    required final String timeSlot,
    required final int count,
  }) = _$ProfileViewTimeImpl;

  factory _ProfileViewTime.fromJson(Map<String, dynamic> json) =
      _$ProfileViewTimeImpl.fromJson;

  @override
  String get timeSlot;
  @override
  int get count;

  /// Create a copy of ProfileViewTime
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProfileViewTimeImplCopyWith<_$ProfileViewTimeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

FollowerGrowthData _$FollowerGrowthDataFromJson(Map<String, dynamic> json) {
  return _FollowerGrowthData.fromJson(json);
}

/// @nodoc
mixin _$FollowerGrowthData {
  int get totalFollowers => throw _privateConstructorUsedError;
  int get newFollowers => throw _privateConstructorUsedError;
  int get lostFollowers => throw _privateConstructorUsedError;
  double get growthRate => throw _privateConstructorUsedError;
  List<FollowerGrowthPoint> get growthHistory =>
      throw _privateConstructorUsedError;
  Map<String, int> get followersBySource => throw _privateConstructorUsedError;
  Map<String, int> get followersByCountry => throw _privateConstructorUsedError;
  Map<String, int> get followersByAge => throw _privateConstructorUsedError;
  Map<String, int> get followersByGender => throw _privateConstructorUsedError;

  /// Serializes this FollowerGrowthData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FollowerGrowthData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FollowerGrowthDataCopyWith<FollowerGrowthData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FollowerGrowthDataCopyWith<$Res> {
  factory $FollowerGrowthDataCopyWith(
    FollowerGrowthData value,
    $Res Function(FollowerGrowthData) then,
  ) = _$FollowerGrowthDataCopyWithImpl<$Res, FollowerGrowthData>;
  @useResult
  $Res call({
    int totalFollowers,
    int newFollowers,
    int lostFollowers,
    double growthRate,
    List<FollowerGrowthPoint> growthHistory,
    Map<String, int> followersBySource,
    Map<String, int> followersByCountry,
    Map<String, int> followersByAge,
    Map<String, int> followersByGender,
  });
}

/// @nodoc
class _$FollowerGrowthDataCopyWithImpl<$Res, $Val extends FollowerGrowthData>
    implements $FollowerGrowthDataCopyWith<$Res> {
  _$FollowerGrowthDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FollowerGrowthData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalFollowers = null,
    Object? newFollowers = null,
    Object? lostFollowers = null,
    Object? growthRate = null,
    Object? growthHistory = null,
    Object? followersBySource = null,
    Object? followersByCountry = null,
    Object? followersByAge = null,
    Object? followersByGender = null,
  }) {
    return _then(
      _value.copyWith(
            totalFollowers: null == totalFollowers
                ? _value.totalFollowers
                : totalFollowers // ignore: cast_nullable_to_non_nullable
                      as int,
            newFollowers: null == newFollowers
                ? _value.newFollowers
                : newFollowers // ignore: cast_nullable_to_non_nullable
                      as int,
            lostFollowers: null == lostFollowers
                ? _value.lostFollowers
                : lostFollowers // ignore: cast_nullable_to_non_nullable
                      as int,
            growthRate: null == growthRate
                ? _value.growthRate
                : growthRate // ignore: cast_nullable_to_non_nullable
                      as double,
            growthHistory: null == growthHistory
                ? _value.growthHistory
                : growthHistory // ignore: cast_nullable_to_non_nullable
                      as List<FollowerGrowthPoint>,
            followersBySource: null == followersBySource
                ? _value.followersBySource
                : followersBySource // ignore: cast_nullable_to_non_nullable
                      as Map<String, int>,
            followersByCountry: null == followersByCountry
                ? _value.followersByCountry
                : followersByCountry // ignore: cast_nullable_to_non_nullable
                      as Map<String, int>,
            followersByAge: null == followersByAge
                ? _value.followersByAge
                : followersByAge // ignore: cast_nullable_to_non_nullable
                      as Map<String, int>,
            followersByGender: null == followersByGender
                ? _value.followersByGender
                : followersByGender // ignore: cast_nullable_to_non_nullable
                      as Map<String, int>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$FollowerGrowthDataImplCopyWith<$Res>
    implements $FollowerGrowthDataCopyWith<$Res> {
  factory _$$FollowerGrowthDataImplCopyWith(
    _$FollowerGrowthDataImpl value,
    $Res Function(_$FollowerGrowthDataImpl) then,
  ) = __$$FollowerGrowthDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int totalFollowers,
    int newFollowers,
    int lostFollowers,
    double growthRate,
    List<FollowerGrowthPoint> growthHistory,
    Map<String, int> followersBySource,
    Map<String, int> followersByCountry,
    Map<String, int> followersByAge,
    Map<String, int> followersByGender,
  });
}

/// @nodoc
class __$$FollowerGrowthDataImplCopyWithImpl<$Res>
    extends _$FollowerGrowthDataCopyWithImpl<$Res, _$FollowerGrowthDataImpl>
    implements _$$FollowerGrowthDataImplCopyWith<$Res> {
  __$$FollowerGrowthDataImplCopyWithImpl(
    _$FollowerGrowthDataImpl _value,
    $Res Function(_$FollowerGrowthDataImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of FollowerGrowthData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalFollowers = null,
    Object? newFollowers = null,
    Object? lostFollowers = null,
    Object? growthRate = null,
    Object? growthHistory = null,
    Object? followersBySource = null,
    Object? followersByCountry = null,
    Object? followersByAge = null,
    Object? followersByGender = null,
  }) {
    return _then(
      _$FollowerGrowthDataImpl(
        totalFollowers: null == totalFollowers
            ? _value.totalFollowers
            : totalFollowers // ignore: cast_nullable_to_non_nullable
                  as int,
        newFollowers: null == newFollowers
            ? _value.newFollowers
            : newFollowers // ignore: cast_nullable_to_non_nullable
                  as int,
        lostFollowers: null == lostFollowers
            ? _value.lostFollowers
            : lostFollowers // ignore: cast_nullable_to_non_nullable
                  as int,
        growthRate: null == growthRate
            ? _value.growthRate
            : growthRate // ignore: cast_nullable_to_non_nullable
                  as double,
        growthHistory: null == growthHistory
            ? _value._growthHistory
            : growthHistory // ignore: cast_nullable_to_non_nullable
                  as List<FollowerGrowthPoint>,
        followersBySource: null == followersBySource
            ? _value._followersBySource
            : followersBySource // ignore: cast_nullable_to_non_nullable
                  as Map<String, int>,
        followersByCountry: null == followersByCountry
            ? _value._followersByCountry
            : followersByCountry // ignore: cast_nullable_to_non_nullable
                  as Map<String, int>,
        followersByAge: null == followersByAge
            ? _value._followersByAge
            : followersByAge // ignore: cast_nullable_to_non_nullable
                  as Map<String, int>,
        followersByGender: null == followersByGender
            ? _value._followersByGender
            : followersByGender // ignore: cast_nullable_to_non_nullable
                  as Map<String, int>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$FollowerGrowthDataImpl implements _FollowerGrowthData {
  const _$FollowerGrowthDataImpl({
    required this.totalFollowers,
    required this.newFollowers,
    required this.lostFollowers,
    required this.growthRate,
    required final List<FollowerGrowthPoint> growthHistory,
    required final Map<String, int> followersBySource,
    required final Map<String, int> followersByCountry,
    required final Map<String, int> followersByAge,
    required final Map<String, int> followersByGender,
  }) : _growthHistory = growthHistory,
       _followersBySource = followersBySource,
       _followersByCountry = followersByCountry,
       _followersByAge = followersByAge,
       _followersByGender = followersByGender;

  factory _$FollowerGrowthDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$FollowerGrowthDataImplFromJson(json);

  @override
  final int totalFollowers;
  @override
  final int newFollowers;
  @override
  final int lostFollowers;
  @override
  final double growthRate;
  final List<FollowerGrowthPoint> _growthHistory;
  @override
  List<FollowerGrowthPoint> get growthHistory {
    if (_growthHistory is EqualUnmodifiableListView) return _growthHistory;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_growthHistory);
  }

  final Map<String, int> _followersBySource;
  @override
  Map<String, int> get followersBySource {
    if (_followersBySource is EqualUnmodifiableMapView)
      return _followersBySource;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_followersBySource);
  }

  final Map<String, int> _followersByCountry;
  @override
  Map<String, int> get followersByCountry {
    if (_followersByCountry is EqualUnmodifiableMapView)
      return _followersByCountry;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_followersByCountry);
  }

  final Map<String, int> _followersByAge;
  @override
  Map<String, int> get followersByAge {
    if (_followersByAge is EqualUnmodifiableMapView) return _followersByAge;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_followersByAge);
  }

  final Map<String, int> _followersByGender;
  @override
  Map<String, int> get followersByGender {
    if (_followersByGender is EqualUnmodifiableMapView)
      return _followersByGender;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_followersByGender);
  }

  @override
  String toString() {
    return 'FollowerGrowthData(totalFollowers: $totalFollowers, newFollowers: $newFollowers, lostFollowers: $lostFollowers, growthRate: $growthRate, growthHistory: $growthHistory, followersBySource: $followersBySource, followersByCountry: $followersByCountry, followersByAge: $followersByAge, followersByGender: $followersByGender)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FollowerGrowthDataImpl &&
            (identical(other.totalFollowers, totalFollowers) ||
                other.totalFollowers == totalFollowers) &&
            (identical(other.newFollowers, newFollowers) ||
                other.newFollowers == newFollowers) &&
            (identical(other.lostFollowers, lostFollowers) ||
                other.lostFollowers == lostFollowers) &&
            (identical(other.growthRate, growthRate) ||
                other.growthRate == growthRate) &&
            const DeepCollectionEquality().equals(
              other._growthHistory,
              _growthHistory,
            ) &&
            const DeepCollectionEquality().equals(
              other._followersBySource,
              _followersBySource,
            ) &&
            const DeepCollectionEquality().equals(
              other._followersByCountry,
              _followersByCountry,
            ) &&
            const DeepCollectionEquality().equals(
              other._followersByAge,
              _followersByAge,
            ) &&
            const DeepCollectionEquality().equals(
              other._followersByGender,
              _followersByGender,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    totalFollowers,
    newFollowers,
    lostFollowers,
    growthRate,
    const DeepCollectionEquality().hash(_growthHistory),
    const DeepCollectionEquality().hash(_followersBySource),
    const DeepCollectionEquality().hash(_followersByCountry),
    const DeepCollectionEquality().hash(_followersByAge),
    const DeepCollectionEquality().hash(_followersByGender),
  );

  /// Create a copy of FollowerGrowthData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FollowerGrowthDataImplCopyWith<_$FollowerGrowthDataImpl> get copyWith =>
      __$$FollowerGrowthDataImplCopyWithImpl<_$FollowerGrowthDataImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$FollowerGrowthDataImplToJson(this);
  }
}

abstract class _FollowerGrowthData implements FollowerGrowthData {
  const factory _FollowerGrowthData({
    required final int totalFollowers,
    required final int newFollowers,
    required final int lostFollowers,
    required final double growthRate,
    required final List<FollowerGrowthPoint> growthHistory,
    required final Map<String, int> followersBySource,
    required final Map<String, int> followersByCountry,
    required final Map<String, int> followersByAge,
    required final Map<String, int> followersByGender,
  }) = _$FollowerGrowthDataImpl;

  factory _FollowerGrowthData.fromJson(Map<String, dynamic> json) =
      _$FollowerGrowthDataImpl.fromJson;

  @override
  int get totalFollowers;
  @override
  int get newFollowers;
  @override
  int get lostFollowers;
  @override
  double get growthRate;
  @override
  List<FollowerGrowthPoint> get growthHistory;
  @override
  Map<String, int> get followersBySource;
  @override
  Map<String, int> get followersByCountry;
  @override
  Map<String, int> get followersByAge;
  @override
  Map<String, int> get followersByGender;

  /// Create a copy of FollowerGrowthData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FollowerGrowthDataImplCopyWith<_$FollowerGrowthDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

FollowerGrowthPoint _$FollowerGrowthPointFromJson(Map<String, dynamic> json) {
  return _FollowerGrowthPoint.fromJson(json);
}

/// @nodoc
mixin _$FollowerGrowthPoint {
  DateTime get date => throw _privateConstructorUsedError;
  int get followers => throw _privateConstructorUsedError;
  int get newFollowers => throw _privateConstructorUsedError;
  int get lostFollowers => throw _privateConstructorUsedError;

  /// Serializes this FollowerGrowthPoint to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FollowerGrowthPoint
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FollowerGrowthPointCopyWith<FollowerGrowthPoint> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FollowerGrowthPointCopyWith<$Res> {
  factory $FollowerGrowthPointCopyWith(
    FollowerGrowthPoint value,
    $Res Function(FollowerGrowthPoint) then,
  ) = _$FollowerGrowthPointCopyWithImpl<$Res, FollowerGrowthPoint>;
  @useResult
  $Res call({
    DateTime date,
    int followers,
    int newFollowers,
    int lostFollowers,
  });
}

/// @nodoc
class _$FollowerGrowthPointCopyWithImpl<$Res, $Val extends FollowerGrowthPoint>
    implements $FollowerGrowthPointCopyWith<$Res> {
  _$FollowerGrowthPointCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FollowerGrowthPoint
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
    Object? followers = null,
    Object? newFollowers = null,
    Object? lostFollowers = null,
  }) {
    return _then(
      _value.copyWith(
            date: null == date
                ? _value.date
                : date // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            followers: null == followers
                ? _value.followers
                : followers // ignore: cast_nullable_to_non_nullable
                      as int,
            newFollowers: null == newFollowers
                ? _value.newFollowers
                : newFollowers // ignore: cast_nullable_to_non_nullable
                      as int,
            lostFollowers: null == lostFollowers
                ? _value.lostFollowers
                : lostFollowers // ignore: cast_nullable_to_non_nullable
                      as int,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$FollowerGrowthPointImplCopyWith<$Res>
    implements $FollowerGrowthPointCopyWith<$Res> {
  factory _$$FollowerGrowthPointImplCopyWith(
    _$FollowerGrowthPointImpl value,
    $Res Function(_$FollowerGrowthPointImpl) then,
  ) = __$$FollowerGrowthPointImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    DateTime date,
    int followers,
    int newFollowers,
    int lostFollowers,
  });
}

/// @nodoc
class __$$FollowerGrowthPointImplCopyWithImpl<$Res>
    extends _$FollowerGrowthPointCopyWithImpl<$Res, _$FollowerGrowthPointImpl>
    implements _$$FollowerGrowthPointImplCopyWith<$Res> {
  __$$FollowerGrowthPointImplCopyWithImpl(
    _$FollowerGrowthPointImpl _value,
    $Res Function(_$FollowerGrowthPointImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of FollowerGrowthPoint
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
    Object? followers = null,
    Object? newFollowers = null,
    Object? lostFollowers = null,
  }) {
    return _then(
      _$FollowerGrowthPointImpl(
        date: null == date
            ? _value.date
            : date // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        followers: null == followers
            ? _value.followers
            : followers // ignore: cast_nullable_to_non_nullable
                  as int,
        newFollowers: null == newFollowers
            ? _value.newFollowers
            : newFollowers // ignore: cast_nullable_to_non_nullable
                  as int,
        lostFollowers: null == lostFollowers
            ? _value.lostFollowers
            : lostFollowers // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$FollowerGrowthPointImpl implements _FollowerGrowthPoint {
  const _$FollowerGrowthPointImpl({
    required this.date,
    required this.followers,
    required this.newFollowers,
    required this.lostFollowers,
  });

  factory _$FollowerGrowthPointImpl.fromJson(Map<String, dynamic> json) =>
      _$$FollowerGrowthPointImplFromJson(json);

  @override
  final DateTime date;
  @override
  final int followers;
  @override
  final int newFollowers;
  @override
  final int lostFollowers;

  @override
  String toString() {
    return 'FollowerGrowthPoint(date: $date, followers: $followers, newFollowers: $newFollowers, lostFollowers: $lostFollowers)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FollowerGrowthPointImpl &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.followers, followers) ||
                other.followers == followers) &&
            (identical(other.newFollowers, newFollowers) ||
                other.newFollowers == newFollowers) &&
            (identical(other.lostFollowers, lostFollowers) ||
                other.lostFollowers == lostFollowers));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, date, followers, newFollowers, lostFollowers);

  /// Create a copy of FollowerGrowthPoint
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FollowerGrowthPointImplCopyWith<_$FollowerGrowthPointImpl> get copyWith =>
      __$$FollowerGrowthPointImplCopyWithImpl<_$FollowerGrowthPointImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$FollowerGrowthPointImplToJson(this);
  }
}

abstract class _FollowerGrowthPoint implements FollowerGrowthPoint {
  const factory _FollowerGrowthPoint({
    required final DateTime date,
    required final int followers,
    required final int newFollowers,
    required final int lostFollowers,
  }) = _$FollowerGrowthPointImpl;

  factory _FollowerGrowthPoint.fromJson(Map<String, dynamic> json) =
      _$FollowerGrowthPointImpl.fromJson;

  @override
  DateTime get date;
  @override
  int get followers;
  @override
  int get newFollowers;
  @override
  int get lostFollowers;

  /// Create a copy of FollowerGrowthPoint
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FollowerGrowthPointImplCopyWith<_$FollowerGrowthPointImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

EngagementData _$EngagementDataFromJson(Map<String, dynamic> json) {
  return _EngagementData.fromJson(json);
}

/// @nodoc
mixin _$EngagementData {
  double get overallEngagementRate => throw _privateConstructorUsedError;
  int get totalEngagements => throw _privateConstructorUsedError;
  int get likes => throw _privateConstructorUsedError;
  int get comments => throw _privateConstructorUsedError;
  int get shares => throw _privateConstructorUsedError;
  int get saves => throw _privateConstructorUsedError;
  List<EngagementTrend> get engagementTrends =>
      throw _privateConstructorUsedError;
  Map<String, double> get engagementByContentType =>
      throw _privateConstructorUsedError;
  Map<String, double> get engagementByTimeOfDay =>
      throw _privateConstructorUsedError;
  Map<String, double> get engagementByDayOfWeek =>
      throw _privateConstructorUsedError;

  /// Serializes this EngagementData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of EngagementData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EngagementDataCopyWith<EngagementData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EngagementDataCopyWith<$Res> {
  factory $EngagementDataCopyWith(
    EngagementData value,
    $Res Function(EngagementData) then,
  ) = _$EngagementDataCopyWithImpl<$Res, EngagementData>;
  @useResult
  $Res call({
    double overallEngagementRate,
    int totalEngagements,
    int likes,
    int comments,
    int shares,
    int saves,
    List<EngagementTrend> engagementTrends,
    Map<String, double> engagementByContentType,
    Map<String, double> engagementByTimeOfDay,
    Map<String, double> engagementByDayOfWeek,
  });
}

/// @nodoc
class _$EngagementDataCopyWithImpl<$Res, $Val extends EngagementData>
    implements $EngagementDataCopyWith<$Res> {
  _$EngagementDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EngagementData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? overallEngagementRate = null,
    Object? totalEngagements = null,
    Object? likes = null,
    Object? comments = null,
    Object? shares = null,
    Object? saves = null,
    Object? engagementTrends = null,
    Object? engagementByContentType = null,
    Object? engagementByTimeOfDay = null,
    Object? engagementByDayOfWeek = null,
  }) {
    return _then(
      _value.copyWith(
            overallEngagementRate: null == overallEngagementRate
                ? _value.overallEngagementRate
                : overallEngagementRate // ignore: cast_nullable_to_non_nullable
                      as double,
            totalEngagements: null == totalEngagements
                ? _value.totalEngagements
                : totalEngagements // ignore: cast_nullable_to_non_nullable
                      as int,
            likes: null == likes
                ? _value.likes
                : likes // ignore: cast_nullable_to_non_nullable
                      as int,
            comments: null == comments
                ? _value.comments
                : comments // ignore: cast_nullable_to_non_nullable
                      as int,
            shares: null == shares
                ? _value.shares
                : shares // ignore: cast_nullable_to_non_nullable
                      as int,
            saves: null == saves
                ? _value.saves
                : saves // ignore: cast_nullable_to_non_nullable
                      as int,
            engagementTrends: null == engagementTrends
                ? _value.engagementTrends
                : engagementTrends // ignore: cast_nullable_to_non_nullable
                      as List<EngagementTrend>,
            engagementByContentType: null == engagementByContentType
                ? _value.engagementByContentType
                : engagementByContentType // ignore: cast_nullable_to_non_nullable
                      as Map<String, double>,
            engagementByTimeOfDay: null == engagementByTimeOfDay
                ? _value.engagementByTimeOfDay
                : engagementByTimeOfDay // ignore: cast_nullable_to_non_nullable
                      as Map<String, double>,
            engagementByDayOfWeek: null == engagementByDayOfWeek
                ? _value.engagementByDayOfWeek
                : engagementByDayOfWeek // ignore: cast_nullable_to_non_nullable
                      as Map<String, double>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$EngagementDataImplCopyWith<$Res>
    implements $EngagementDataCopyWith<$Res> {
  factory _$$EngagementDataImplCopyWith(
    _$EngagementDataImpl value,
    $Res Function(_$EngagementDataImpl) then,
  ) = __$$EngagementDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    double overallEngagementRate,
    int totalEngagements,
    int likes,
    int comments,
    int shares,
    int saves,
    List<EngagementTrend> engagementTrends,
    Map<String, double> engagementByContentType,
    Map<String, double> engagementByTimeOfDay,
    Map<String, double> engagementByDayOfWeek,
  });
}

/// @nodoc
class __$$EngagementDataImplCopyWithImpl<$Res>
    extends _$EngagementDataCopyWithImpl<$Res, _$EngagementDataImpl>
    implements _$$EngagementDataImplCopyWith<$Res> {
  __$$EngagementDataImplCopyWithImpl(
    _$EngagementDataImpl _value,
    $Res Function(_$EngagementDataImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EngagementData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? overallEngagementRate = null,
    Object? totalEngagements = null,
    Object? likes = null,
    Object? comments = null,
    Object? shares = null,
    Object? saves = null,
    Object? engagementTrends = null,
    Object? engagementByContentType = null,
    Object? engagementByTimeOfDay = null,
    Object? engagementByDayOfWeek = null,
  }) {
    return _then(
      _$EngagementDataImpl(
        overallEngagementRate: null == overallEngagementRate
            ? _value.overallEngagementRate
            : overallEngagementRate // ignore: cast_nullable_to_non_nullable
                  as double,
        totalEngagements: null == totalEngagements
            ? _value.totalEngagements
            : totalEngagements // ignore: cast_nullable_to_non_nullable
                  as int,
        likes: null == likes
            ? _value.likes
            : likes // ignore: cast_nullable_to_non_nullable
                  as int,
        comments: null == comments
            ? _value.comments
            : comments // ignore: cast_nullable_to_non_nullable
                  as int,
        shares: null == shares
            ? _value.shares
            : shares // ignore: cast_nullable_to_non_nullable
                  as int,
        saves: null == saves
            ? _value.saves
            : saves // ignore: cast_nullable_to_non_nullable
                  as int,
        engagementTrends: null == engagementTrends
            ? _value._engagementTrends
            : engagementTrends // ignore: cast_nullable_to_non_nullable
                  as List<EngagementTrend>,
        engagementByContentType: null == engagementByContentType
            ? _value._engagementByContentType
            : engagementByContentType // ignore: cast_nullable_to_non_nullable
                  as Map<String, double>,
        engagementByTimeOfDay: null == engagementByTimeOfDay
            ? _value._engagementByTimeOfDay
            : engagementByTimeOfDay // ignore: cast_nullable_to_non_nullable
                  as Map<String, double>,
        engagementByDayOfWeek: null == engagementByDayOfWeek
            ? _value._engagementByDayOfWeek
            : engagementByDayOfWeek // ignore: cast_nullable_to_non_nullable
                  as Map<String, double>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$EngagementDataImpl implements _EngagementData {
  const _$EngagementDataImpl({
    required this.overallEngagementRate,
    required this.totalEngagements,
    required this.likes,
    required this.comments,
    required this.shares,
    required this.saves,
    required final List<EngagementTrend> engagementTrends,
    required final Map<String, double> engagementByContentType,
    required final Map<String, double> engagementByTimeOfDay,
    required final Map<String, double> engagementByDayOfWeek,
  }) : _engagementTrends = engagementTrends,
       _engagementByContentType = engagementByContentType,
       _engagementByTimeOfDay = engagementByTimeOfDay,
       _engagementByDayOfWeek = engagementByDayOfWeek;

  factory _$EngagementDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$EngagementDataImplFromJson(json);

  @override
  final double overallEngagementRate;
  @override
  final int totalEngagements;
  @override
  final int likes;
  @override
  final int comments;
  @override
  final int shares;
  @override
  final int saves;
  final List<EngagementTrend> _engagementTrends;
  @override
  List<EngagementTrend> get engagementTrends {
    if (_engagementTrends is EqualUnmodifiableListView)
      return _engagementTrends;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_engagementTrends);
  }

  final Map<String, double> _engagementByContentType;
  @override
  Map<String, double> get engagementByContentType {
    if (_engagementByContentType is EqualUnmodifiableMapView)
      return _engagementByContentType;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_engagementByContentType);
  }

  final Map<String, double> _engagementByTimeOfDay;
  @override
  Map<String, double> get engagementByTimeOfDay {
    if (_engagementByTimeOfDay is EqualUnmodifiableMapView)
      return _engagementByTimeOfDay;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_engagementByTimeOfDay);
  }

  final Map<String, double> _engagementByDayOfWeek;
  @override
  Map<String, double> get engagementByDayOfWeek {
    if (_engagementByDayOfWeek is EqualUnmodifiableMapView)
      return _engagementByDayOfWeek;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_engagementByDayOfWeek);
  }

  @override
  String toString() {
    return 'EngagementData(overallEngagementRate: $overallEngagementRate, totalEngagements: $totalEngagements, likes: $likes, comments: $comments, shares: $shares, saves: $saves, engagementTrends: $engagementTrends, engagementByContentType: $engagementByContentType, engagementByTimeOfDay: $engagementByTimeOfDay, engagementByDayOfWeek: $engagementByDayOfWeek)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EngagementDataImpl &&
            (identical(other.overallEngagementRate, overallEngagementRate) ||
                other.overallEngagementRate == overallEngagementRate) &&
            (identical(other.totalEngagements, totalEngagements) ||
                other.totalEngagements == totalEngagements) &&
            (identical(other.likes, likes) || other.likes == likes) &&
            (identical(other.comments, comments) ||
                other.comments == comments) &&
            (identical(other.shares, shares) || other.shares == shares) &&
            (identical(other.saves, saves) || other.saves == saves) &&
            const DeepCollectionEquality().equals(
              other._engagementTrends,
              _engagementTrends,
            ) &&
            const DeepCollectionEquality().equals(
              other._engagementByContentType,
              _engagementByContentType,
            ) &&
            const DeepCollectionEquality().equals(
              other._engagementByTimeOfDay,
              _engagementByTimeOfDay,
            ) &&
            const DeepCollectionEquality().equals(
              other._engagementByDayOfWeek,
              _engagementByDayOfWeek,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    overallEngagementRate,
    totalEngagements,
    likes,
    comments,
    shares,
    saves,
    const DeepCollectionEquality().hash(_engagementTrends),
    const DeepCollectionEquality().hash(_engagementByContentType),
    const DeepCollectionEquality().hash(_engagementByTimeOfDay),
    const DeepCollectionEquality().hash(_engagementByDayOfWeek),
  );

  /// Create a copy of EngagementData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EngagementDataImplCopyWith<_$EngagementDataImpl> get copyWith =>
      __$$EngagementDataImplCopyWithImpl<_$EngagementDataImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$EngagementDataImplToJson(this);
  }
}

abstract class _EngagementData implements EngagementData {
  const factory _EngagementData({
    required final double overallEngagementRate,
    required final int totalEngagements,
    required final int likes,
    required final int comments,
    required final int shares,
    required final int saves,
    required final List<EngagementTrend> engagementTrends,
    required final Map<String, double> engagementByContentType,
    required final Map<String, double> engagementByTimeOfDay,
    required final Map<String, double> engagementByDayOfWeek,
  }) = _$EngagementDataImpl;

  factory _EngagementData.fromJson(Map<String, dynamic> json) =
      _$EngagementDataImpl.fromJson;

  @override
  double get overallEngagementRate;
  @override
  int get totalEngagements;
  @override
  int get likes;
  @override
  int get comments;
  @override
  int get shares;
  @override
  int get saves;
  @override
  List<EngagementTrend> get engagementTrends;
  @override
  Map<String, double> get engagementByContentType;
  @override
  Map<String, double> get engagementByTimeOfDay;
  @override
  Map<String, double> get engagementByDayOfWeek;

  /// Create a copy of EngagementData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EngagementDataImplCopyWith<_$EngagementDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

EngagementTrend _$EngagementTrendFromJson(Map<String, dynamic> json) {
  return _EngagementTrend.fromJson(json);
}

/// @nodoc
mixin _$EngagementTrend {
  DateTime get date => throw _privateConstructorUsedError;
  double get engagementRate => throw _privateConstructorUsedError;
  int get totalEngagements => throw _privateConstructorUsedError;

  /// Serializes this EngagementTrend to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of EngagementTrend
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EngagementTrendCopyWith<EngagementTrend> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EngagementTrendCopyWith<$Res> {
  factory $EngagementTrendCopyWith(
    EngagementTrend value,
    $Res Function(EngagementTrend) then,
  ) = _$EngagementTrendCopyWithImpl<$Res, EngagementTrend>;
  @useResult
  $Res call({DateTime date, double engagementRate, int totalEngagements});
}

/// @nodoc
class _$EngagementTrendCopyWithImpl<$Res, $Val extends EngagementTrend>
    implements $EngagementTrendCopyWith<$Res> {
  _$EngagementTrendCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EngagementTrend
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
    Object? engagementRate = null,
    Object? totalEngagements = null,
  }) {
    return _then(
      _value.copyWith(
            date: null == date
                ? _value.date
                : date // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            engagementRate: null == engagementRate
                ? _value.engagementRate
                : engagementRate // ignore: cast_nullable_to_non_nullable
                      as double,
            totalEngagements: null == totalEngagements
                ? _value.totalEngagements
                : totalEngagements // ignore: cast_nullable_to_non_nullable
                      as int,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$EngagementTrendImplCopyWith<$Res>
    implements $EngagementTrendCopyWith<$Res> {
  factory _$$EngagementTrendImplCopyWith(
    _$EngagementTrendImpl value,
    $Res Function(_$EngagementTrendImpl) then,
  ) = __$$EngagementTrendImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({DateTime date, double engagementRate, int totalEngagements});
}

/// @nodoc
class __$$EngagementTrendImplCopyWithImpl<$Res>
    extends _$EngagementTrendCopyWithImpl<$Res, _$EngagementTrendImpl>
    implements _$$EngagementTrendImplCopyWith<$Res> {
  __$$EngagementTrendImplCopyWithImpl(
    _$EngagementTrendImpl _value,
    $Res Function(_$EngagementTrendImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EngagementTrend
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
    Object? engagementRate = null,
    Object? totalEngagements = null,
  }) {
    return _then(
      _$EngagementTrendImpl(
        date: null == date
            ? _value.date
            : date // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        engagementRate: null == engagementRate
            ? _value.engagementRate
            : engagementRate // ignore: cast_nullable_to_non_nullable
                  as double,
        totalEngagements: null == totalEngagements
            ? _value.totalEngagements
            : totalEngagements // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$EngagementTrendImpl implements _EngagementTrend {
  const _$EngagementTrendImpl({
    required this.date,
    required this.engagementRate,
    required this.totalEngagements,
  });

  factory _$EngagementTrendImpl.fromJson(Map<String, dynamic> json) =>
      _$$EngagementTrendImplFromJson(json);

  @override
  final DateTime date;
  @override
  final double engagementRate;
  @override
  final int totalEngagements;

  @override
  String toString() {
    return 'EngagementTrend(date: $date, engagementRate: $engagementRate, totalEngagements: $totalEngagements)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EngagementTrendImpl &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.engagementRate, engagementRate) ||
                other.engagementRate == engagementRate) &&
            (identical(other.totalEngagements, totalEngagements) ||
                other.totalEngagements == totalEngagements));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, date, engagementRate, totalEngagements);

  /// Create a copy of EngagementTrend
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EngagementTrendImplCopyWith<_$EngagementTrendImpl> get copyWith =>
      __$$EngagementTrendImplCopyWithImpl<_$EngagementTrendImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$EngagementTrendImplToJson(this);
  }
}

abstract class _EngagementTrend implements EngagementTrend {
  const factory _EngagementTrend({
    required final DateTime date,
    required final double engagementRate,
    required final int totalEngagements,
  }) = _$EngagementTrendImpl;

  factory _EngagementTrend.fromJson(Map<String, dynamic> json) =
      _$EngagementTrendImpl.fromJson;

  @override
  DateTime get date;
  @override
  double get engagementRate;
  @override
  int get totalEngagements;

  /// Create a copy of EngagementTrend
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EngagementTrendImplCopyWith<_$EngagementTrendImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ContentPerformanceData _$ContentPerformanceDataFromJson(
  Map<String, dynamic> json,
) {
  return _ContentPerformanceData.fromJson(json);
}

/// @nodoc
mixin _$ContentPerformanceData {
  int get totalPosts => throw _privateConstructorUsedError;
  double get averageLikes => throw _privateConstructorUsedError;
  double get averageComments => throw _privateConstructorUsedError;
  double get averageShares => throw _privateConstructorUsedError;
  double get averageSaves => throw _privateConstructorUsedError;
  List<TopPerformingPost> get topPosts => throw _privateConstructorUsedError;
  Map<String, int> get postsByType => throw _privateConstructorUsedError;
  Map<String, double> get performanceByType =>
      throw _privateConstructorUsedError;

  /// Serializes this ContentPerformanceData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ContentPerformanceData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ContentPerformanceDataCopyWith<ContentPerformanceData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContentPerformanceDataCopyWith<$Res> {
  factory $ContentPerformanceDataCopyWith(
    ContentPerformanceData value,
    $Res Function(ContentPerformanceData) then,
  ) = _$ContentPerformanceDataCopyWithImpl<$Res, ContentPerformanceData>;
  @useResult
  $Res call({
    int totalPosts,
    double averageLikes,
    double averageComments,
    double averageShares,
    double averageSaves,
    List<TopPerformingPost> topPosts,
    Map<String, int> postsByType,
    Map<String, double> performanceByType,
  });
}

/// @nodoc
class _$ContentPerformanceDataCopyWithImpl<
  $Res,
  $Val extends ContentPerformanceData
>
    implements $ContentPerformanceDataCopyWith<$Res> {
  _$ContentPerformanceDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ContentPerformanceData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalPosts = null,
    Object? averageLikes = null,
    Object? averageComments = null,
    Object? averageShares = null,
    Object? averageSaves = null,
    Object? topPosts = null,
    Object? postsByType = null,
    Object? performanceByType = null,
  }) {
    return _then(
      _value.copyWith(
            totalPosts: null == totalPosts
                ? _value.totalPosts
                : totalPosts // ignore: cast_nullable_to_non_nullable
                      as int,
            averageLikes: null == averageLikes
                ? _value.averageLikes
                : averageLikes // ignore: cast_nullable_to_non_nullable
                      as double,
            averageComments: null == averageComments
                ? _value.averageComments
                : averageComments // ignore: cast_nullable_to_non_nullable
                      as double,
            averageShares: null == averageShares
                ? _value.averageShares
                : averageShares // ignore: cast_nullable_to_non_nullable
                      as double,
            averageSaves: null == averageSaves
                ? _value.averageSaves
                : averageSaves // ignore: cast_nullable_to_non_nullable
                      as double,
            topPosts: null == topPosts
                ? _value.topPosts
                : topPosts // ignore: cast_nullable_to_non_nullable
                      as List<TopPerformingPost>,
            postsByType: null == postsByType
                ? _value.postsByType
                : postsByType // ignore: cast_nullable_to_non_nullable
                      as Map<String, int>,
            performanceByType: null == performanceByType
                ? _value.performanceByType
                : performanceByType // ignore: cast_nullable_to_non_nullable
                      as Map<String, double>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ContentPerformanceDataImplCopyWith<$Res>
    implements $ContentPerformanceDataCopyWith<$Res> {
  factory _$$ContentPerformanceDataImplCopyWith(
    _$ContentPerformanceDataImpl value,
    $Res Function(_$ContentPerformanceDataImpl) then,
  ) = __$$ContentPerformanceDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int totalPosts,
    double averageLikes,
    double averageComments,
    double averageShares,
    double averageSaves,
    List<TopPerformingPost> topPosts,
    Map<String, int> postsByType,
    Map<String, double> performanceByType,
  });
}

/// @nodoc
class __$$ContentPerformanceDataImplCopyWithImpl<$Res>
    extends
        _$ContentPerformanceDataCopyWithImpl<$Res, _$ContentPerformanceDataImpl>
    implements _$$ContentPerformanceDataImplCopyWith<$Res> {
  __$$ContentPerformanceDataImplCopyWithImpl(
    _$ContentPerformanceDataImpl _value,
    $Res Function(_$ContentPerformanceDataImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ContentPerformanceData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalPosts = null,
    Object? averageLikes = null,
    Object? averageComments = null,
    Object? averageShares = null,
    Object? averageSaves = null,
    Object? topPosts = null,
    Object? postsByType = null,
    Object? performanceByType = null,
  }) {
    return _then(
      _$ContentPerformanceDataImpl(
        totalPosts: null == totalPosts
            ? _value.totalPosts
            : totalPosts // ignore: cast_nullable_to_non_nullable
                  as int,
        averageLikes: null == averageLikes
            ? _value.averageLikes
            : averageLikes // ignore: cast_nullable_to_non_nullable
                  as double,
        averageComments: null == averageComments
            ? _value.averageComments
            : averageComments // ignore: cast_nullable_to_non_nullable
                  as double,
        averageShares: null == averageShares
            ? _value.averageShares
            : averageShares // ignore: cast_nullable_to_non_nullable
                  as double,
        averageSaves: null == averageSaves
            ? _value.averageSaves
            : averageSaves // ignore: cast_nullable_to_non_nullable
                  as double,
        topPosts: null == topPosts
            ? _value._topPosts
            : topPosts // ignore: cast_nullable_to_non_nullable
                  as List<TopPerformingPost>,
        postsByType: null == postsByType
            ? _value._postsByType
            : postsByType // ignore: cast_nullable_to_non_nullable
                  as Map<String, int>,
        performanceByType: null == performanceByType
            ? _value._performanceByType
            : performanceByType // ignore: cast_nullable_to_non_nullable
                  as Map<String, double>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ContentPerformanceDataImpl implements _ContentPerformanceData {
  const _$ContentPerformanceDataImpl({
    required this.totalPosts,
    required this.averageLikes,
    required this.averageComments,
    required this.averageShares,
    required this.averageSaves,
    required final List<TopPerformingPost> topPosts,
    required final Map<String, int> postsByType,
    required final Map<String, double> performanceByType,
  }) : _topPosts = topPosts,
       _postsByType = postsByType,
       _performanceByType = performanceByType;

  factory _$ContentPerformanceDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$ContentPerformanceDataImplFromJson(json);

  @override
  final int totalPosts;
  @override
  final double averageLikes;
  @override
  final double averageComments;
  @override
  final double averageShares;
  @override
  final double averageSaves;
  final List<TopPerformingPost> _topPosts;
  @override
  List<TopPerformingPost> get topPosts {
    if (_topPosts is EqualUnmodifiableListView) return _topPosts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_topPosts);
  }

  final Map<String, int> _postsByType;
  @override
  Map<String, int> get postsByType {
    if (_postsByType is EqualUnmodifiableMapView) return _postsByType;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_postsByType);
  }

  final Map<String, double> _performanceByType;
  @override
  Map<String, double> get performanceByType {
    if (_performanceByType is EqualUnmodifiableMapView)
      return _performanceByType;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_performanceByType);
  }

  @override
  String toString() {
    return 'ContentPerformanceData(totalPosts: $totalPosts, averageLikes: $averageLikes, averageComments: $averageComments, averageShares: $averageShares, averageSaves: $averageSaves, topPosts: $topPosts, postsByType: $postsByType, performanceByType: $performanceByType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContentPerformanceDataImpl &&
            (identical(other.totalPosts, totalPosts) ||
                other.totalPosts == totalPosts) &&
            (identical(other.averageLikes, averageLikes) ||
                other.averageLikes == averageLikes) &&
            (identical(other.averageComments, averageComments) ||
                other.averageComments == averageComments) &&
            (identical(other.averageShares, averageShares) ||
                other.averageShares == averageShares) &&
            (identical(other.averageSaves, averageSaves) ||
                other.averageSaves == averageSaves) &&
            const DeepCollectionEquality().equals(other._topPosts, _topPosts) &&
            const DeepCollectionEquality().equals(
              other._postsByType,
              _postsByType,
            ) &&
            const DeepCollectionEquality().equals(
              other._performanceByType,
              _performanceByType,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    totalPosts,
    averageLikes,
    averageComments,
    averageShares,
    averageSaves,
    const DeepCollectionEquality().hash(_topPosts),
    const DeepCollectionEquality().hash(_postsByType),
    const DeepCollectionEquality().hash(_performanceByType),
  );

  /// Create a copy of ContentPerformanceData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ContentPerformanceDataImplCopyWith<_$ContentPerformanceDataImpl>
  get copyWith =>
      __$$ContentPerformanceDataImplCopyWithImpl<_$ContentPerformanceDataImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ContentPerformanceDataImplToJson(this);
  }
}

abstract class _ContentPerformanceData implements ContentPerformanceData {
  const factory _ContentPerformanceData({
    required final int totalPosts,
    required final double averageLikes,
    required final double averageComments,
    required final double averageShares,
    required final double averageSaves,
    required final List<TopPerformingPost> topPosts,
    required final Map<String, int> postsByType,
    required final Map<String, double> performanceByType,
  }) = _$ContentPerformanceDataImpl;

  factory _ContentPerformanceData.fromJson(Map<String, dynamic> json) =
      _$ContentPerformanceDataImpl.fromJson;

  @override
  int get totalPosts;
  @override
  double get averageLikes;
  @override
  double get averageComments;
  @override
  double get averageShares;
  @override
  double get averageSaves;
  @override
  List<TopPerformingPost> get topPosts;
  @override
  Map<String, int> get postsByType;
  @override
  Map<String, double> get performanceByType;

  /// Create a copy of ContentPerformanceData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ContentPerformanceDataImplCopyWith<_$ContentPerformanceDataImpl>
  get copyWith => throw _privateConstructorUsedError;
}

TopPerformingPost _$TopPerformingPostFromJson(Map<String, dynamic> json) {
  return _TopPerformingPost.fromJson(json);
}

/// @nodoc
mixin _$TopPerformingPost {
  String get postId => throw _privateConstructorUsedError;
  String get postType => throw _privateConstructorUsedError;
  int get likes => throw _privateConstructorUsedError;
  int get comments => throw _privateConstructorUsedError;
  int get shares => throw _privateConstructorUsedError;
  int get saves => throw _privateConstructorUsedError;
  double get engagementRate => throw _privateConstructorUsedError;
  DateTime get postedAt => throw _privateConstructorUsedError;

  /// Serializes this TopPerformingPost to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TopPerformingPost
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TopPerformingPostCopyWith<TopPerformingPost> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TopPerformingPostCopyWith<$Res> {
  factory $TopPerformingPostCopyWith(
    TopPerformingPost value,
    $Res Function(TopPerformingPost) then,
  ) = _$TopPerformingPostCopyWithImpl<$Res, TopPerformingPost>;
  @useResult
  $Res call({
    String postId,
    String postType,
    int likes,
    int comments,
    int shares,
    int saves,
    double engagementRate,
    DateTime postedAt,
  });
}

/// @nodoc
class _$TopPerformingPostCopyWithImpl<$Res, $Val extends TopPerformingPost>
    implements $TopPerformingPostCopyWith<$Res> {
  _$TopPerformingPostCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TopPerformingPost
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? postId = null,
    Object? postType = null,
    Object? likes = null,
    Object? comments = null,
    Object? shares = null,
    Object? saves = null,
    Object? engagementRate = null,
    Object? postedAt = null,
  }) {
    return _then(
      _value.copyWith(
            postId: null == postId
                ? _value.postId
                : postId // ignore: cast_nullable_to_non_nullable
                      as String,
            postType: null == postType
                ? _value.postType
                : postType // ignore: cast_nullable_to_non_nullable
                      as String,
            likes: null == likes
                ? _value.likes
                : likes // ignore: cast_nullable_to_non_nullable
                      as int,
            comments: null == comments
                ? _value.comments
                : comments // ignore: cast_nullable_to_non_nullable
                      as int,
            shares: null == shares
                ? _value.shares
                : shares // ignore: cast_nullable_to_non_nullable
                      as int,
            saves: null == saves
                ? _value.saves
                : saves // ignore: cast_nullable_to_non_nullable
                      as int,
            engagementRate: null == engagementRate
                ? _value.engagementRate
                : engagementRate // ignore: cast_nullable_to_non_nullable
                      as double,
            postedAt: null == postedAt
                ? _value.postedAt
                : postedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$TopPerformingPostImplCopyWith<$Res>
    implements $TopPerformingPostCopyWith<$Res> {
  factory _$$TopPerformingPostImplCopyWith(
    _$TopPerformingPostImpl value,
    $Res Function(_$TopPerformingPostImpl) then,
  ) = __$$TopPerformingPostImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String postId,
    String postType,
    int likes,
    int comments,
    int shares,
    int saves,
    double engagementRate,
    DateTime postedAt,
  });
}

/// @nodoc
class __$$TopPerformingPostImplCopyWithImpl<$Res>
    extends _$TopPerformingPostCopyWithImpl<$Res, _$TopPerformingPostImpl>
    implements _$$TopPerformingPostImplCopyWith<$Res> {
  __$$TopPerformingPostImplCopyWithImpl(
    _$TopPerformingPostImpl _value,
    $Res Function(_$TopPerformingPostImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TopPerformingPost
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? postId = null,
    Object? postType = null,
    Object? likes = null,
    Object? comments = null,
    Object? shares = null,
    Object? saves = null,
    Object? engagementRate = null,
    Object? postedAt = null,
  }) {
    return _then(
      _$TopPerformingPostImpl(
        postId: null == postId
            ? _value.postId
            : postId // ignore: cast_nullable_to_non_nullable
                  as String,
        postType: null == postType
            ? _value.postType
            : postType // ignore: cast_nullable_to_non_nullable
                  as String,
        likes: null == likes
            ? _value.likes
            : likes // ignore: cast_nullable_to_non_nullable
                  as int,
        comments: null == comments
            ? _value.comments
            : comments // ignore: cast_nullable_to_non_nullable
                  as int,
        shares: null == shares
            ? _value.shares
            : shares // ignore: cast_nullable_to_non_nullable
                  as int,
        saves: null == saves
            ? _value.saves
            : saves // ignore: cast_nullable_to_non_nullable
                  as int,
        engagementRate: null == engagementRate
            ? _value.engagementRate
            : engagementRate // ignore: cast_nullable_to_non_nullable
                  as double,
        postedAt: null == postedAt
            ? _value.postedAt
            : postedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$TopPerformingPostImpl implements _TopPerformingPost {
  const _$TopPerformingPostImpl({
    required this.postId,
    required this.postType,
    required this.likes,
    required this.comments,
    required this.shares,
    required this.saves,
    required this.engagementRate,
    required this.postedAt,
  });

  factory _$TopPerformingPostImpl.fromJson(Map<String, dynamic> json) =>
      _$$TopPerformingPostImplFromJson(json);

  @override
  final String postId;
  @override
  final String postType;
  @override
  final int likes;
  @override
  final int comments;
  @override
  final int shares;
  @override
  final int saves;
  @override
  final double engagementRate;
  @override
  final DateTime postedAt;

  @override
  String toString() {
    return 'TopPerformingPost(postId: $postId, postType: $postType, likes: $likes, comments: $comments, shares: $shares, saves: $saves, engagementRate: $engagementRate, postedAt: $postedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TopPerformingPostImpl &&
            (identical(other.postId, postId) || other.postId == postId) &&
            (identical(other.postType, postType) ||
                other.postType == postType) &&
            (identical(other.likes, likes) || other.likes == likes) &&
            (identical(other.comments, comments) ||
                other.comments == comments) &&
            (identical(other.shares, shares) || other.shares == shares) &&
            (identical(other.saves, saves) || other.saves == saves) &&
            (identical(other.engagementRate, engagementRate) ||
                other.engagementRate == engagementRate) &&
            (identical(other.postedAt, postedAt) ||
                other.postedAt == postedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    postId,
    postType,
    likes,
    comments,
    shares,
    saves,
    engagementRate,
    postedAt,
  );

  /// Create a copy of TopPerformingPost
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TopPerformingPostImplCopyWith<_$TopPerformingPostImpl> get copyWith =>
      __$$TopPerformingPostImplCopyWithImpl<_$TopPerformingPostImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$TopPerformingPostImplToJson(this);
  }
}

abstract class _TopPerformingPost implements TopPerformingPost {
  const factory _TopPerformingPost({
    required final String postId,
    required final String postType,
    required final int likes,
    required final int comments,
    required final int shares,
    required final int saves,
    required final double engagementRate,
    required final DateTime postedAt,
  }) = _$TopPerformingPostImpl;

  factory _TopPerformingPost.fromJson(Map<String, dynamic> json) =
      _$TopPerformingPostImpl.fromJson;

  @override
  String get postId;
  @override
  String get postType;
  @override
  int get likes;
  @override
  int get comments;
  @override
  int get shares;
  @override
  int get saves;
  @override
  double get engagementRate;
  @override
  DateTime get postedAt;

  /// Create a copy of TopPerformingPost
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TopPerformingPostImplCopyWith<_$TopPerformingPostImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AudienceData _$AudienceDataFromJson(Map<String, dynamic> json) {
  return _AudienceData.fromJson(json);
}

/// @nodoc
mixin _$AudienceData {
  int get totalAudience => throw _privateConstructorUsedError;
  Map<String, int> get audienceByCountry => throw _privateConstructorUsedError;
  Map<String, int> get audienceByAge => throw _privateConstructorUsedError;
  Map<String, int> get audienceByGender => throw _privateConstructorUsedError;
  Map<String, int> get audienceByInterest => throw _privateConstructorUsedError;
  List<String> get topCountries => throw _privateConstructorUsedError;
  List<String> get topInterests => throw _privateConstructorUsedError;
  double get averageAge => throw _privateConstructorUsedError;
  String get dominantGender => throw _privateConstructorUsedError;

  /// Serializes this AudienceData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AudienceData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AudienceDataCopyWith<AudienceData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AudienceDataCopyWith<$Res> {
  factory $AudienceDataCopyWith(
    AudienceData value,
    $Res Function(AudienceData) then,
  ) = _$AudienceDataCopyWithImpl<$Res, AudienceData>;
  @useResult
  $Res call({
    int totalAudience,
    Map<String, int> audienceByCountry,
    Map<String, int> audienceByAge,
    Map<String, int> audienceByGender,
    Map<String, int> audienceByInterest,
    List<String> topCountries,
    List<String> topInterests,
    double averageAge,
    String dominantGender,
  });
}

/// @nodoc
class _$AudienceDataCopyWithImpl<$Res, $Val extends AudienceData>
    implements $AudienceDataCopyWith<$Res> {
  _$AudienceDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AudienceData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalAudience = null,
    Object? audienceByCountry = null,
    Object? audienceByAge = null,
    Object? audienceByGender = null,
    Object? audienceByInterest = null,
    Object? topCountries = null,
    Object? topInterests = null,
    Object? averageAge = null,
    Object? dominantGender = null,
  }) {
    return _then(
      _value.copyWith(
            totalAudience: null == totalAudience
                ? _value.totalAudience
                : totalAudience // ignore: cast_nullable_to_non_nullable
                      as int,
            audienceByCountry: null == audienceByCountry
                ? _value.audienceByCountry
                : audienceByCountry // ignore: cast_nullable_to_non_nullable
                      as Map<String, int>,
            audienceByAge: null == audienceByAge
                ? _value.audienceByAge
                : audienceByAge // ignore: cast_nullable_to_non_nullable
                      as Map<String, int>,
            audienceByGender: null == audienceByGender
                ? _value.audienceByGender
                : audienceByGender // ignore: cast_nullable_to_non_nullable
                      as Map<String, int>,
            audienceByInterest: null == audienceByInterest
                ? _value.audienceByInterest
                : audienceByInterest // ignore: cast_nullable_to_non_nullable
                      as Map<String, int>,
            topCountries: null == topCountries
                ? _value.topCountries
                : topCountries // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            topInterests: null == topInterests
                ? _value.topInterests
                : topInterests // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            averageAge: null == averageAge
                ? _value.averageAge
                : averageAge // ignore: cast_nullable_to_non_nullable
                      as double,
            dominantGender: null == dominantGender
                ? _value.dominantGender
                : dominantGender // ignore: cast_nullable_to_non_nullable
                      as String,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$AudienceDataImplCopyWith<$Res>
    implements $AudienceDataCopyWith<$Res> {
  factory _$$AudienceDataImplCopyWith(
    _$AudienceDataImpl value,
    $Res Function(_$AudienceDataImpl) then,
  ) = __$$AudienceDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int totalAudience,
    Map<String, int> audienceByCountry,
    Map<String, int> audienceByAge,
    Map<String, int> audienceByGender,
    Map<String, int> audienceByInterest,
    List<String> topCountries,
    List<String> topInterests,
    double averageAge,
    String dominantGender,
  });
}

/// @nodoc
class __$$AudienceDataImplCopyWithImpl<$Res>
    extends _$AudienceDataCopyWithImpl<$Res, _$AudienceDataImpl>
    implements _$$AudienceDataImplCopyWith<$Res> {
  __$$AudienceDataImplCopyWithImpl(
    _$AudienceDataImpl _value,
    $Res Function(_$AudienceDataImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AudienceData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalAudience = null,
    Object? audienceByCountry = null,
    Object? audienceByAge = null,
    Object? audienceByGender = null,
    Object? audienceByInterest = null,
    Object? topCountries = null,
    Object? topInterests = null,
    Object? averageAge = null,
    Object? dominantGender = null,
  }) {
    return _then(
      _$AudienceDataImpl(
        totalAudience: null == totalAudience
            ? _value.totalAudience
            : totalAudience // ignore: cast_nullable_to_non_nullable
                  as int,
        audienceByCountry: null == audienceByCountry
            ? _value._audienceByCountry
            : audienceByCountry // ignore: cast_nullable_to_non_nullable
                  as Map<String, int>,
        audienceByAge: null == audienceByAge
            ? _value._audienceByAge
            : audienceByAge // ignore: cast_nullable_to_non_nullable
                  as Map<String, int>,
        audienceByGender: null == audienceByGender
            ? _value._audienceByGender
            : audienceByGender // ignore: cast_nullable_to_non_nullable
                  as Map<String, int>,
        audienceByInterest: null == audienceByInterest
            ? _value._audienceByInterest
            : audienceByInterest // ignore: cast_nullable_to_non_nullable
                  as Map<String, int>,
        topCountries: null == topCountries
            ? _value._topCountries
            : topCountries // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        topInterests: null == topInterests
            ? _value._topInterests
            : topInterests // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        averageAge: null == averageAge
            ? _value.averageAge
            : averageAge // ignore: cast_nullable_to_non_nullable
                  as double,
        dominantGender: null == dominantGender
            ? _value.dominantGender
            : dominantGender // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$AudienceDataImpl implements _AudienceData {
  const _$AudienceDataImpl({
    required this.totalAudience,
    required final Map<String, int> audienceByCountry,
    required final Map<String, int> audienceByAge,
    required final Map<String, int> audienceByGender,
    required final Map<String, int> audienceByInterest,
    required final List<String> topCountries,
    required final List<String> topInterests,
    required this.averageAge,
    required this.dominantGender,
  }) : _audienceByCountry = audienceByCountry,
       _audienceByAge = audienceByAge,
       _audienceByGender = audienceByGender,
       _audienceByInterest = audienceByInterest,
       _topCountries = topCountries,
       _topInterests = topInterests;

  factory _$AudienceDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$AudienceDataImplFromJson(json);

  @override
  final int totalAudience;
  final Map<String, int> _audienceByCountry;
  @override
  Map<String, int> get audienceByCountry {
    if (_audienceByCountry is EqualUnmodifiableMapView)
      return _audienceByCountry;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_audienceByCountry);
  }

  final Map<String, int> _audienceByAge;
  @override
  Map<String, int> get audienceByAge {
    if (_audienceByAge is EqualUnmodifiableMapView) return _audienceByAge;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_audienceByAge);
  }

  final Map<String, int> _audienceByGender;
  @override
  Map<String, int> get audienceByGender {
    if (_audienceByGender is EqualUnmodifiableMapView) return _audienceByGender;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_audienceByGender);
  }

  final Map<String, int> _audienceByInterest;
  @override
  Map<String, int> get audienceByInterest {
    if (_audienceByInterest is EqualUnmodifiableMapView)
      return _audienceByInterest;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_audienceByInterest);
  }

  final List<String> _topCountries;
  @override
  List<String> get topCountries {
    if (_topCountries is EqualUnmodifiableListView) return _topCountries;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_topCountries);
  }

  final List<String> _topInterests;
  @override
  List<String> get topInterests {
    if (_topInterests is EqualUnmodifiableListView) return _topInterests;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_topInterests);
  }

  @override
  final double averageAge;
  @override
  final String dominantGender;

  @override
  String toString() {
    return 'AudienceData(totalAudience: $totalAudience, audienceByCountry: $audienceByCountry, audienceByAge: $audienceByAge, audienceByGender: $audienceByGender, audienceByInterest: $audienceByInterest, topCountries: $topCountries, topInterests: $topInterests, averageAge: $averageAge, dominantGender: $dominantGender)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AudienceDataImpl &&
            (identical(other.totalAudience, totalAudience) ||
                other.totalAudience == totalAudience) &&
            const DeepCollectionEquality().equals(
              other._audienceByCountry,
              _audienceByCountry,
            ) &&
            const DeepCollectionEquality().equals(
              other._audienceByAge,
              _audienceByAge,
            ) &&
            const DeepCollectionEquality().equals(
              other._audienceByGender,
              _audienceByGender,
            ) &&
            const DeepCollectionEquality().equals(
              other._audienceByInterest,
              _audienceByInterest,
            ) &&
            const DeepCollectionEquality().equals(
              other._topCountries,
              _topCountries,
            ) &&
            const DeepCollectionEquality().equals(
              other._topInterests,
              _topInterests,
            ) &&
            (identical(other.averageAge, averageAge) ||
                other.averageAge == averageAge) &&
            (identical(other.dominantGender, dominantGender) ||
                other.dominantGender == dominantGender));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    totalAudience,
    const DeepCollectionEquality().hash(_audienceByCountry),
    const DeepCollectionEquality().hash(_audienceByAge),
    const DeepCollectionEquality().hash(_audienceByGender),
    const DeepCollectionEquality().hash(_audienceByInterest),
    const DeepCollectionEquality().hash(_topCountries),
    const DeepCollectionEquality().hash(_topInterests),
    averageAge,
    dominantGender,
  );

  /// Create a copy of AudienceData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AudienceDataImplCopyWith<_$AudienceDataImpl> get copyWith =>
      __$$AudienceDataImplCopyWithImpl<_$AudienceDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AudienceDataImplToJson(this);
  }
}

abstract class _AudienceData implements AudienceData {
  const factory _AudienceData({
    required final int totalAudience,
    required final Map<String, int> audienceByCountry,
    required final Map<String, int> audienceByAge,
    required final Map<String, int> audienceByGender,
    required final Map<String, int> audienceByInterest,
    required final List<String> topCountries,
    required final List<String> topInterests,
    required final double averageAge,
    required final String dominantGender,
  }) = _$AudienceDataImpl;

  factory _AudienceData.fromJson(Map<String, dynamic> json) =
      _$AudienceDataImpl.fromJson;

  @override
  int get totalAudience;
  @override
  Map<String, int> get audienceByCountry;
  @override
  Map<String, int> get audienceByAge;
  @override
  Map<String, int> get audienceByGender;
  @override
  Map<String, int> get audienceByInterest;
  @override
  List<String> get topCountries;
  @override
  List<String> get topInterests;
  @override
  double get averageAge;
  @override
  String get dominantGender;

  /// Create a copy of AudienceData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AudienceDataImplCopyWith<_$AudienceDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProfileAnalyticsSummary _$ProfileAnalyticsSummaryFromJson(
  Map<String, dynamic> json,
) {
  return _ProfileAnalyticsSummary.fromJson(json);
}

/// @nodoc
mixin _$ProfileAnalyticsSummary {
  String get userId => throw _privateConstructorUsedError;
  DateTime get periodStart => throw _privateConstructorUsedError;
  DateTime get periodEnd => throw _privateConstructorUsedError;
  int get totalProfileViews => throw _privateConstructorUsedError;
  int get totalFollowers => throw _privateConstructorUsedError;
  double get engagementRate => throw _privateConstructorUsedError;
  int get totalPosts => throw _privateConstructorUsedError;
  double get followerGrowthRate => throw _privateConstructorUsedError;
  List<String> get topPerformingContent => throw _privateConstructorUsedError;
  Map<String, dynamic> get insights => throw _privateConstructorUsedError;

  /// Serializes this ProfileAnalyticsSummary to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProfileAnalyticsSummary
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProfileAnalyticsSummaryCopyWith<ProfileAnalyticsSummary> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProfileAnalyticsSummaryCopyWith<$Res> {
  factory $ProfileAnalyticsSummaryCopyWith(
    ProfileAnalyticsSummary value,
    $Res Function(ProfileAnalyticsSummary) then,
  ) = _$ProfileAnalyticsSummaryCopyWithImpl<$Res, ProfileAnalyticsSummary>;
  @useResult
  $Res call({
    String userId,
    DateTime periodStart,
    DateTime periodEnd,
    int totalProfileViews,
    int totalFollowers,
    double engagementRate,
    int totalPosts,
    double followerGrowthRate,
    List<String> topPerformingContent,
    Map<String, dynamic> insights,
  });
}

/// @nodoc
class _$ProfileAnalyticsSummaryCopyWithImpl<
  $Res,
  $Val extends ProfileAnalyticsSummary
>
    implements $ProfileAnalyticsSummaryCopyWith<$Res> {
  _$ProfileAnalyticsSummaryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProfileAnalyticsSummary
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? periodStart = null,
    Object? periodEnd = null,
    Object? totalProfileViews = null,
    Object? totalFollowers = null,
    Object? engagementRate = null,
    Object? totalPosts = null,
    Object? followerGrowthRate = null,
    Object? topPerformingContent = null,
    Object? insights = null,
  }) {
    return _then(
      _value.copyWith(
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            periodStart: null == periodStart
                ? _value.periodStart
                : periodStart // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            periodEnd: null == periodEnd
                ? _value.periodEnd
                : periodEnd // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            totalProfileViews: null == totalProfileViews
                ? _value.totalProfileViews
                : totalProfileViews // ignore: cast_nullable_to_non_nullable
                      as int,
            totalFollowers: null == totalFollowers
                ? _value.totalFollowers
                : totalFollowers // ignore: cast_nullable_to_non_nullable
                      as int,
            engagementRate: null == engagementRate
                ? _value.engagementRate
                : engagementRate // ignore: cast_nullable_to_non_nullable
                      as double,
            totalPosts: null == totalPosts
                ? _value.totalPosts
                : totalPosts // ignore: cast_nullable_to_non_nullable
                      as int,
            followerGrowthRate: null == followerGrowthRate
                ? _value.followerGrowthRate
                : followerGrowthRate // ignore: cast_nullable_to_non_nullable
                      as double,
            topPerformingContent: null == topPerformingContent
                ? _value.topPerformingContent
                : topPerformingContent // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            insights: null == insights
                ? _value.insights
                : insights // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ProfileAnalyticsSummaryImplCopyWith<$Res>
    implements $ProfileAnalyticsSummaryCopyWith<$Res> {
  factory _$$ProfileAnalyticsSummaryImplCopyWith(
    _$ProfileAnalyticsSummaryImpl value,
    $Res Function(_$ProfileAnalyticsSummaryImpl) then,
  ) = __$$ProfileAnalyticsSummaryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String userId,
    DateTime periodStart,
    DateTime periodEnd,
    int totalProfileViews,
    int totalFollowers,
    double engagementRate,
    int totalPosts,
    double followerGrowthRate,
    List<String> topPerformingContent,
    Map<String, dynamic> insights,
  });
}

/// @nodoc
class __$$ProfileAnalyticsSummaryImplCopyWithImpl<$Res>
    extends
        _$ProfileAnalyticsSummaryCopyWithImpl<
          $Res,
          _$ProfileAnalyticsSummaryImpl
        >
    implements _$$ProfileAnalyticsSummaryImplCopyWith<$Res> {
  __$$ProfileAnalyticsSummaryImplCopyWithImpl(
    _$ProfileAnalyticsSummaryImpl _value,
    $Res Function(_$ProfileAnalyticsSummaryImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ProfileAnalyticsSummary
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? periodStart = null,
    Object? periodEnd = null,
    Object? totalProfileViews = null,
    Object? totalFollowers = null,
    Object? engagementRate = null,
    Object? totalPosts = null,
    Object? followerGrowthRate = null,
    Object? topPerformingContent = null,
    Object? insights = null,
  }) {
    return _then(
      _$ProfileAnalyticsSummaryImpl(
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        periodStart: null == periodStart
            ? _value.periodStart
            : periodStart // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        periodEnd: null == periodEnd
            ? _value.periodEnd
            : periodEnd // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        totalProfileViews: null == totalProfileViews
            ? _value.totalProfileViews
            : totalProfileViews // ignore: cast_nullable_to_non_nullable
                  as int,
        totalFollowers: null == totalFollowers
            ? _value.totalFollowers
            : totalFollowers // ignore: cast_nullable_to_non_nullable
                  as int,
        engagementRate: null == engagementRate
            ? _value.engagementRate
            : engagementRate // ignore: cast_nullable_to_non_nullable
                  as double,
        totalPosts: null == totalPosts
            ? _value.totalPosts
            : totalPosts // ignore: cast_nullable_to_non_nullable
                  as int,
        followerGrowthRate: null == followerGrowthRate
            ? _value.followerGrowthRate
            : followerGrowthRate // ignore: cast_nullable_to_non_nullable
                  as double,
        topPerformingContent: null == topPerformingContent
            ? _value._topPerformingContent
            : topPerformingContent // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        insights: null == insights
            ? _value._insights
            : insights // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ProfileAnalyticsSummaryImpl implements _ProfileAnalyticsSummary {
  const _$ProfileAnalyticsSummaryImpl({
    required this.userId,
    required this.periodStart,
    required this.periodEnd,
    required this.totalProfileViews,
    required this.totalFollowers,
    required this.engagementRate,
    required this.totalPosts,
    required this.followerGrowthRate,
    required final List<String> topPerformingContent,
    required final Map<String, dynamic> insights,
  }) : _topPerformingContent = topPerformingContent,
       _insights = insights;

  factory _$ProfileAnalyticsSummaryImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProfileAnalyticsSummaryImplFromJson(json);

  @override
  final String userId;
  @override
  final DateTime periodStart;
  @override
  final DateTime periodEnd;
  @override
  final int totalProfileViews;
  @override
  final int totalFollowers;
  @override
  final double engagementRate;
  @override
  final int totalPosts;
  @override
  final double followerGrowthRate;
  final List<String> _topPerformingContent;
  @override
  List<String> get topPerformingContent {
    if (_topPerformingContent is EqualUnmodifiableListView)
      return _topPerformingContent;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_topPerformingContent);
  }

  final Map<String, dynamic> _insights;
  @override
  Map<String, dynamic> get insights {
    if (_insights is EqualUnmodifiableMapView) return _insights;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_insights);
  }

  @override
  String toString() {
    return 'ProfileAnalyticsSummary(userId: $userId, periodStart: $periodStart, periodEnd: $periodEnd, totalProfileViews: $totalProfileViews, totalFollowers: $totalFollowers, engagementRate: $engagementRate, totalPosts: $totalPosts, followerGrowthRate: $followerGrowthRate, topPerformingContent: $topPerformingContent, insights: $insights)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileAnalyticsSummaryImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.periodStart, periodStart) ||
                other.periodStart == periodStart) &&
            (identical(other.periodEnd, periodEnd) ||
                other.periodEnd == periodEnd) &&
            (identical(other.totalProfileViews, totalProfileViews) ||
                other.totalProfileViews == totalProfileViews) &&
            (identical(other.totalFollowers, totalFollowers) ||
                other.totalFollowers == totalFollowers) &&
            (identical(other.engagementRate, engagementRate) ||
                other.engagementRate == engagementRate) &&
            (identical(other.totalPosts, totalPosts) ||
                other.totalPosts == totalPosts) &&
            (identical(other.followerGrowthRate, followerGrowthRate) ||
                other.followerGrowthRate == followerGrowthRate) &&
            const DeepCollectionEquality().equals(
              other._topPerformingContent,
              _topPerformingContent,
            ) &&
            const DeepCollectionEquality().equals(other._insights, _insights));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    userId,
    periodStart,
    periodEnd,
    totalProfileViews,
    totalFollowers,
    engagementRate,
    totalPosts,
    followerGrowthRate,
    const DeepCollectionEquality().hash(_topPerformingContent),
    const DeepCollectionEquality().hash(_insights),
  );

  /// Create a copy of ProfileAnalyticsSummary
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfileAnalyticsSummaryImplCopyWith<_$ProfileAnalyticsSummaryImpl>
  get copyWith =>
      __$$ProfileAnalyticsSummaryImplCopyWithImpl<
        _$ProfileAnalyticsSummaryImpl
      >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProfileAnalyticsSummaryImplToJson(this);
  }
}

abstract class _ProfileAnalyticsSummary implements ProfileAnalyticsSummary {
  const factory _ProfileAnalyticsSummary({
    required final String userId,
    required final DateTime periodStart,
    required final DateTime periodEnd,
    required final int totalProfileViews,
    required final int totalFollowers,
    required final double engagementRate,
    required final int totalPosts,
    required final double followerGrowthRate,
    required final List<String> topPerformingContent,
    required final Map<String, dynamic> insights,
  }) = _$ProfileAnalyticsSummaryImpl;

  factory _ProfileAnalyticsSummary.fromJson(Map<String, dynamic> json) =
      _$ProfileAnalyticsSummaryImpl.fromJson;

  @override
  String get userId;
  @override
  DateTime get periodStart;
  @override
  DateTime get periodEnd;
  @override
  int get totalProfileViews;
  @override
  int get totalFollowers;
  @override
  double get engagementRate;
  @override
  int get totalPosts;
  @override
  double get followerGrowthRate;
  @override
  List<String> get topPerformingContent;
  @override
  Map<String, dynamic> get insights;

  /// Create a copy of ProfileAnalyticsSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProfileAnalyticsSummaryImplCopyWith<_$ProfileAnalyticsSummaryImpl>
  get copyWith => throw _privateConstructorUsedError;
}
