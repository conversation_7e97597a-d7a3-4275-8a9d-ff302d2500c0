// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'profile_verification_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

ProfileVerificationModel _$ProfileVerificationModelFromJson(
  Map<String, dynamic> json,
) {
  return _ProfileVerificationModel.fromJson(json);
}

/// @nodoc
mixin _$ProfileVerificationModel {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  VerificationType get type => throw _privateConstructorUsedError;
  VerificationStatus get status => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime? get completedAt => throw _privateConstructorUsedError;
  DateTime? get expiresAt => throw _privateConstructorUsedError;
  String? get verificationCode => throw _privateConstructorUsedError;
  String? get documentUrl => throw _privateConstructorUsedError;
  String? get documentType => throw _privateConstructorUsedError;
  String? get verificationNotes => throw _privateConstructorUsedError;
  String? get verifiedBy => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this ProfileVerificationModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProfileVerificationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProfileVerificationModelCopyWith<ProfileVerificationModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProfileVerificationModelCopyWith<$Res> {
  factory $ProfileVerificationModelCopyWith(
    ProfileVerificationModel value,
    $Res Function(ProfileVerificationModel) then,
  ) = _$ProfileVerificationModelCopyWithImpl<$Res, ProfileVerificationModel>;
  @useResult
  $Res call({
    String id,
    String userId,
    VerificationType type,
    VerificationStatus status,
    DateTime createdAt,
    DateTime? completedAt,
    DateTime? expiresAt,
    String? verificationCode,
    String? documentUrl,
    String? documentType,
    String? verificationNotes,
    String? verifiedBy,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class _$ProfileVerificationModelCopyWithImpl<
  $Res,
  $Val extends ProfileVerificationModel
>
    implements $ProfileVerificationModelCopyWith<$Res> {
  _$ProfileVerificationModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProfileVerificationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? type = null,
    Object? status = null,
    Object? createdAt = null,
    Object? completedAt = freezed,
    Object? expiresAt = freezed,
    Object? verificationCode = freezed,
    Object? documentUrl = freezed,
    Object? documentType = freezed,
    Object? verificationNotes = freezed,
    Object? verifiedBy = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as VerificationType,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as VerificationStatus,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            completedAt: freezed == completedAt
                ? _value.completedAt
                : completedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            expiresAt: freezed == expiresAt
                ? _value.expiresAt
                : expiresAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            verificationCode: freezed == verificationCode
                ? _value.verificationCode
                : verificationCode // ignore: cast_nullable_to_non_nullable
                      as String?,
            documentUrl: freezed == documentUrl
                ? _value.documentUrl
                : documentUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            documentType: freezed == documentType
                ? _value.documentType
                : documentType // ignore: cast_nullable_to_non_nullable
                      as String?,
            verificationNotes: freezed == verificationNotes
                ? _value.verificationNotes
                : verificationNotes // ignore: cast_nullable_to_non_nullable
                      as String?,
            verifiedBy: freezed == verifiedBy
                ? _value.verifiedBy
                : verifiedBy // ignore: cast_nullable_to_non_nullable
                      as String?,
            metadata: freezed == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ProfileVerificationModelImplCopyWith<$Res>
    implements $ProfileVerificationModelCopyWith<$Res> {
  factory _$$ProfileVerificationModelImplCopyWith(
    _$ProfileVerificationModelImpl value,
    $Res Function(_$ProfileVerificationModelImpl) then,
  ) = __$$ProfileVerificationModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    VerificationType type,
    VerificationStatus status,
    DateTime createdAt,
    DateTime? completedAt,
    DateTime? expiresAt,
    String? verificationCode,
    String? documentUrl,
    String? documentType,
    String? verificationNotes,
    String? verifiedBy,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class __$$ProfileVerificationModelImplCopyWithImpl<$Res>
    extends
        _$ProfileVerificationModelCopyWithImpl<
          $Res,
          _$ProfileVerificationModelImpl
        >
    implements _$$ProfileVerificationModelImplCopyWith<$Res> {
  __$$ProfileVerificationModelImplCopyWithImpl(
    _$ProfileVerificationModelImpl _value,
    $Res Function(_$ProfileVerificationModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ProfileVerificationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? type = null,
    Object? status = null,
    Object? createdAt = null,
    Object? completedAt = freezed,
    Object? expiresAt = freezed,
    Object? verificationCode = freezed,
    Object? documentUrl = freezed,
    Object? documentType = freezed,
    Object? verificationNotes = freezed,
    Object? verifiedBy = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _$ProfileVerificationModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as VerificationType,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as VerificationStatus,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        completedAt: freezed == completedAt
            ? _value.completedAt
            : completedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        expiresAt: freezed == expiresAt
            ? _value.expiresAt
            : expiresAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        verificationCode: freezed == verificationCode
            ? _value.verificationCode
            : verificationCode // ignore: cast_nullable_to_non_nullable
                  as String?,
        documentUrl: freezed == documentUrl
            ? _value.documentUrl
            : documentUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        documentType: freezed == documentType
            ? _value.documentType
            : documentType // ignore: cast_nullable_to_non_nullable
                  as String?,
        verificationNotes: freezed == verificationNotes
            ? _value.verificationNotes
            : verificationNotes // ignore: cast_nullable_to_non_nullable
                  as String?,
        verifiedBy: freezed == verifiedBy
            ? _value.verifiedBy
            : verifiedBy // ignore: cast_nullable_to_non_nullable
                  as String?,
        metadata: freezed == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ProfileVerificationModelImpl implements _ProfileVerificationModel {
  const _$ProfileVerificationModelImpl({
    required this.id,
    required this.userId,
    required this.type,
    required this.status,
    required this.createdAt,
    this.completedAt,
    this.expiresAt,
    this.verificationCode,
    this.documentUrl,
    this.documentType,
    this.verificationNotes,
    this.verifiedBy,
    final Map<String, dynamic>? metadata,
  }) : _metadata = metadata;

  factory _$ProfileVerificationModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProfileVerificationModelImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final VerificationType type;
  @override
  final VerificationStatus status;
  @override
  final DateTime createdAt;
  @override
  final DateTime? completedAt;
  @override
  final DateTime? expiresAt;
  @override
  final String? verificationCode;
  @override
  final String? documentUrl;
  @override
  final String? documentType;
  @override
  final String? verificationNotes;
  @override
  final String? verifiedBy;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'ProfileVerificationModel(id: $id, userId: $userId, type: $type, status: $status, createdAt: $createdAt, completedAt: $completedAt, expiresAt: $expiresAt, verificationCode: $verificationCode, documentUrl: $documentUrl, documentType: $documentType, verificationNotes: $verificationNotes, verifiedBy: $verifiedBy, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileVerificationModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.completedAt, completedAt) ||
                other.completedAt == completedAt) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt) &&
            (identical(other.verificationCode, verificationCode) ||
                other.verificationCode == verificationCode) &&
            (identical(other.documentUrl, documentUrl) ||
                other.documentUrl == documentUrl) &&
            (identical(other.documentType, documentType) ||
                other.documentType == documentType) &&
            (identical(other.verificationNotes, verificationNotes) ||
                other.verificationNotes == verificationNotes) &&
            (identical(other.verifiedBy, verifiedBy) ||
                other.verifiedBy == verifiedBy) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    userId,
    type,
    status,
    createdAt,
    completedAt,
    expiresAt,
    verificationCode,
    documentUrl,
    documentType,
    verificationNotes,
    verifiedBy,
    const DeepCollectionEquality().hash(_metadata),
  );

  /// Create a copy of ProfileVerificationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfileVerificationModelImplCopyWith<_$ProfileVerificationModelImpl>
  get copyWith =>
      __$$ProfileVerificationModelImplCopyWithImpl<
        _$ProfileVerificationModelImpl
      >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProfileVerificationModelImplToJson(this);
  }
}

abstract class _ProfileVerificationModel implements ProfileVerificationModel {
  const factory _ProfileVerificationModel({
    required final String id,
    required final String userId,
    required final VerificationType type,
    required final VerificationStatus status,
    required final DateTime createdAt,
    final DateTime? completedAt,
    final DateTime? expiresAt,
    final String? verificationCode,
    final String? documentUrl,
    final String? documentType,
    final String? verificationNotes,
    final String? verifiedBy,
    final Map<String, dynamic>? metadata,
  }) = _$ProfileVerificationModelImpl;

  factory _ProfileVerificationModel.fromJson(Map<String, dynamic> json) =
      _$ProfileVerificationModelImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  VerificationType get type;
  @override
  VerificationStatus get status;
  @override
  DateTime get createdAt;
  @override
  DateTime? get completedAt;
  @override
  DateTime? get expiresAt;
  @override
  String? get verificationCode;
  @override
  String? get documentUrl;
  @override
  String? get documentType;
  @override
  String? get verificationNotes;
  @override
  String? get verifiedBy;
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of ProfileVerificationModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProfileVerificationModelImplCopyWith<_$ProfileVerificationModelImpl>
  get copyWith => throw _privateConstructorUsedError;
}

EmailVerificationModel _$EmailVerificationModelFromJson(
  Map<String, dynamic> json,
) {
  return _EmailVerificationModel.fromJson(json);
}

/// @nodoc
mixin _$EmailVerificationModel {
  String get email => throw _privateConstructorUsedError;
  String get verificationCode => throw _privateConstructorUsedError;
  DateTime get expiresAt => throw _privateConstructorUsedError;
  bool get isVerified => throw _privateConstructorUsedError;
  DateTime? get verifiedAt => throw _privateConstructorUsedError;

  /// Serializes this EmailVerificationModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of EmailVerificationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EmailVerificationModelCopyWith<EmailVerificationModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EmailVerificationModelCopyWith<$Res> {
  factory $EmailVerificationModelCopyWith(
    EmailVerificationModel value,
    $Res Function(EmailVerificationModel) then,
  ) = _$EmailVerificationModelCopyWithImpl<$Res, EmailVerificationModel>;
  @useResult
  $Res call({
    String email,
    String verificationCode,
    DateTime expiresAt,
    bool isVerified,
    DateTime? verifiedAt,
  });
}

/// @nodoc
class _$EmailVerificationModelCopyWithImpl<
  $Res,
  $Val extends EmailVerificationModel
>
    implements $EmailVerificationModelCopyWith<$Res> {
  _$EmailVerificationModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EmailVerificationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = null,
    Object? verificationCode = null,
    Object? expiresAt = null,
    Object? isVerified = null,
    Object? verifiedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            email: null == email
                ? _value.email
                : email // ignore: cast_nullable_to_non_nullable
                      as String,
            verificationCode: null == verificationCode
                ? _value.verificationCode
                : verificationCode // ignore: cast_nullable_to_non_nullable
                      as String,
            expiresAt: null == expiresAt
                ? _value.expiresAt
                : expiresAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isVerified: null == isVerified
                ? _value.isVerified
                : isVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            verifiedAt: freezed == verifiedAt
                ? _value.verifiedAt
                : verifiedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$EmailVerificationModelImplCopyWith<$Res>
    implements $EmailVerificationModelCopyWith<$Res> {
  factory _$$EmailVerificationModelImplCopyWith(
    _$EmailVerificationModelImpl value,
    $Res Function(_$EmailVerificationModelImpl) then,
  ) = __$$EmailVerificationModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String email,
    String verificationCode,
    DateTime expiresAt,
    bool isVerified,
    DateTime? verifiedAt,
  });
}

/// @nodoc
class __$$EmailVerificationModelImplCopyWithImpl<$Res>
    extends
        _$EmailVerificationModelCopyWithImpl<$Res, _$EmailVerificationModelImpl>
    implements _$$EmailVerificationModelImplCopyWith<$Res> {
  __$$EmailVerificationModelImplCopyWithImpl(
    _$EmailVerificationModelImpl _value,
    $Res Function(_$EmailVerificationModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EmailVerificationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = null,
    Object? verificationCode = null,
    Object? expiresAt = null,
    Object? isVerified = null,
    Object? verifiedAt = freezed,
  }) {
    return _then(
      _$EmailVerificationModelImpl(
        email: null == email
            ? _value.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String,
        verificationCode: null == verificationCode
            ? _value.verificationCode
            : verificationCode // ignore: cast_nullable_to_non_nullable
                  as String,
        expiresAt: null == expiresAt
            ? _value.expiresAt
            : expiresAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isVerified: null == isVerified
            ? _value.isVerified
            : isVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        verifiedAt: freezed == verifiedAt
            ? _value.verifiedAt
            : verifiedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$EmailVerificationModelImpl implements _EmailVerificationModel {
  const _$EmailVerificationModelImpl({
    required this.email,
    required this.verificationCode,
    required this.expiresAt,
    required this.isVerified,
    this.verifiedAt,
  });

  factory _$EmailVerificationModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$EmailVerificationModelImplFromJson(json);

  @override
  final String email;
  @override
  final String verificationCode;
  @override
  final DateTime expiresAt;
  @override
  final bool isVerified;
  @override
  final DateTime? verifiedAt;

  @override
  String toString() {
    return 'EmailVerificationModel(email: $email, verificationCode: $verificationCode, expiresAt: $expiresAt, isVerified: $isVerified, verifiedAt: $verifiedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EmailVerificationModelImpl &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.verificationCode, verificationCode) ||
                other.verificationCode == verificationCode) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.verifiedAt, verifiedAt) ||
                other.verifiedAt == verifiedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    email,
    verificationCode,
    expiresAt,
    isVerified,
    verifiedAt,
  );

  /// Create a copy of EmailVerificationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EmailVerificationModelImplCopyWith<_$EmailVerificationModelImpl>
  get copyWith =>
      __$$EmailVerificationModelImplCopyWithImpl<_$EmailVerificationModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$EmailVerificationModelImplToJson(this);
  }
}

abstract class _EmailVerificationModel implements EmailVerificationModel {
  const factory _EmailVerificationModel({
    required final String email,
    required final String verificationCode,
    required final DateTime expiresAt,
    required final bool isVerified,
    final DateTime? verifiedAt,
  }) = _$EmailVerificationModelImpl;

  factory _EmailVerificationModel.fromJson(Map<String, dynamic> json) =
      _$EmailVerificationModelImpl.fromJson;

  @override
  String get email;
  @override
  String get verificationCode;
  @override
  DateTime get expiresAt;
  @override
  bool get isVerified;
  @override
  DateTime? get verifiedAt;

  /// Create a copy of EmailVerificationModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EmailVerificationModelImplCopyWith<_$EmailVerificationModelImpl>
  get copyWith => throw _privateConstructorUsedError;
}

PhoneVerificationModel _$PhoneVerificationModelFromJson(
  Map<String, dynamic> json,
) {
  return _PhoneVerificationModel.fromJson(json);
}

/// @nodoc
mixin _$PhoneVerificationModel {
  String get phone => throw _privateConstructorUsedError;
  String get verificationCode => throw _privateConstructorUsedError;
  DateTime get expiresAt => throw _privateConstructorUsedError;
  bool get isVerified => throw _privateConstructorUsedError;
  DateTime? get verifiedAt => throw _privateConstructorUsedError;

  /// Serializes this PhoneVerificationModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PhoneVerificationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PhoneVerificationModelCopyWith<PhoneVerificationModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PhoneVerificationModelCopyWith<$Res> {
  factory $PhoneVerificationModelCopyWith(
    PhoneVerificationModel value,
    $Res Function(PhoneVerificationModel) then,
  ) = _$PhoneVerificationModelCopyWithImpl<$Res, PhoneVerificationModel>;
  @useResult
  $Res call({
    String phone,
    String verificationCode,
    DateTime expiresAt,
    bool isVerified,
    DateTime? verifiedAt,
  });
}

/// @nodoc
class _$PhoneVerificationModelCopyWithImpl<
  $Res,
  $Val extends PhoneVerificationModel
>
    implements $PhoneVerificationModelCopyWith<$Res> {
  _$PhoneVerificationModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PhoneVerificationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phone = null,
    Object? verificationCode = null,
    Object? expiresAt = null,
    Object? isVerified = null,
    Object? verifiedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            phone: null == phone
                ? _value.phone
                : phone // ignore: cast_nullable_to_non_nullable
                      as String,
            verificationCode: null == verificationCode
                ? _value.verificationCode
                : verificationCode // ignore: cast_nullable_to_non_nullable
                      as String,
            expiresAt: null == expiresAt
                ? _value.expiresAt
                : expiresAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isVerified: null == isVerified
                ? _value.isVerified
                : isVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            verifiedAt: freezed == verifiedAt
                ? _value.verifiedAt
                : verifiedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PhoneVerificationModelImplCopyWith<$Res>
    implements $PhoneVerificationModelCopyWith<$Res> {
  factory _$$PhoneVerificationModelImplCopyWith(
    _$PhoneVerificationModelImpl value,
    $Res Function(_$PhoneVerificationModelImpl) then,
  ) = __$$PhoneVerificationModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String phone,
    String verificationCode,
    DateTime expiresAt,
    bool isVerified,
    DateTime? verifiedAt,
  });
}

/// @nodoc
class __$$PhoneVerificationModelImplCopyWithImpl<$Res>
    extends
        _$PhoneVerificationModelCopyWithImpl<$Res, _$PhoneVerificationModelImpl>
    implements _$$PhoneVerificationModelImplCopyWith<$Res> {
  __$$PhoneVerificationModelImplCopyWithImpl(
    _$PhoneVerificationModelImpl _value,
    $Res Function(_$PhoneVerificationModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PhoneVerificationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phone = null,
    Object? verificationCode = null,
    Object? expiresAt = null,
    Object? isVerified = null,
    Object? verifiedAt = freezed,
  }) {
    return _then(
      _$PhoneVerificationModelImpl(
        phone: null == phone
            ? _value.phone
            : phone // ignore: cast_nullable_to_non_nullable
                  as String,
        verificationCode: null == verificationCode
            ? _value.verificationCode
            : verificationCode // ignore: cast_nullable_to_non_nullable
                  as String,
        expiresAt: null == expiresAt
            ? _value.expiresAt
            : expiresAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isVerified: null == isVerified
            ? _value.isVerified
            : isVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        verifiedAt: freezed == verifiedAt
            ? _value.verifiedAt
            : verifiedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PhoneVerificationModelImpl implements _PhoneVerificationModel {
  const _$PhoneVerificationModelImpl({
    required this.phone,
    required this.verificationCode,
    required this.expiresAt,
    required this.isVerified,
    this.verifiedAt,
  });

  factory _$PhoneVerificationModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$PhoneVerificationModelImplFromJson(json);

  @override
  final String phone;
  @override
  final String verificationCode;
  @override
  final DateTime expiresAt;
  @override
  final bool isVerified;
  @override
  final DateTime? verifiedAt;

  @override
  String toString() {
    return 'PhoneVerificationModel(phone: $phone, verificationCode: $verificationCode, expiresAt: $expiresAt, isVerified: $isVerified, verifiedAt: $verifiedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PhoneVerificationModelImpl &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.verificationCode, verificationCode) ||
                other.verificationCode == verificationCode) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.verifiedAt, verifiedAt) ||
                other.verifiedAt == verifiedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    phone,
    verificationCode,
    expiresAt,
    isVerified,
    verifiedAt,
  );

  /// Create a copy of PhoneVerificationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PhoneVerificationModelImplCopyWith<_$PhoneVerificationModelImpl>
  get copyWith =>
      __$$PhoneVerificationModelImplCopyWithImpl<_$PhoneVerificationModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$PhoneVerificationModelImplToJson(this);
  }
}

abstract class _PhoneVerificationModel implements PhoneVerificationModel {
  const factory _PhoneVerificationModel({
    required final String phone,
    required final String verificationCode,
    required final DateTime expiresAt,
    required final bool isVerified,
    final DateTime? verifiedAt,
  }) = _$PhoneVerificationModelImpl;

  factory _PhoneVerificationModel.fromJson(Map<String, dynamic> json) =
      _$PhoneVerificationModelImpl.fromJson;

  @override
  String get phone;
  @override
  String get verificationCode;
  @override
  DateTime get expiresAt;
  @override
  bool get isVerified;
  @override
  DateTime? get verifiedAt;

  /// Create a copy of PhoneVerificationModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PhoneVerificationModelImplCopyWith<_$PhoneVerificationModelImpl>
  get copyWith => throw _privateConstructorUsedError;
}

IdentityVerificationModel _$IdentityVerificationModelFromJson(
  Map<String, dynamic> json,
) {
  return _IdentityVerificationModel.fromJson(json);
}

/// @nodoc
mixin _$IdentityVerificationModel {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get documentType => throw _privateConstructorUsedError;
  String get documentUrl => throw _privateConstructorUsedError;
  String get fullName => throw _privateConstructorUsedError;
  DateTime get dateOfBirth => throw _privateConstructorUsedError;
  String get nationality => throw _privateConstructorUsedError;
  VerificationStatus get status => throw _privateConstructorUsedError;
  DateTime get submittedAt => throw _privateConstructorUsedError;
  DateTime? get reviewedAt => throw _privateConstructorUsedError;
  String? get reviewedBy => throw _privateConstructorUsedError;
  String? get rejectionReason => throw _privateConstructorUsedError;
  Map<String, dynamic>? get documentData => throw _privateConstructorUsedError;

  /// Serializes this IdentityVerificationModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of IdentityVerificationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $IdentityVerificationModelCopyWith<IdentityVerificationModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IdentityVerificationModelCopyWith<$Res> {
  factory $IdentityVerificationModelCopyWith(
    IdentityVerificationModel value,
    $Res Function(IdentityVerificationModel) then,
  ) = _$IdentityVerificationModelCopyWithImpl<$Res, IdentityVerificationModel>;
  @useResult
  $Res call({
    String id,
    String userId,
    String documentType,
    String documentUrl,
    String fullName,
    DateTime dateOfBirth,
    String nationality,
    VerificationStatus status,
    DateTime submittedAt,
    DateTime? reviewedAt,
    String? reviewedBy,
    String? rejectionReason,
    Map<String, dynamic>? documentData,
  });
}

/// @nodoc
class _$IdentityVerificationModelCopyWithImpl<
  $Res,
  $Val extends IdentityVerificationModel
>
    implements $IdentityVerificationModelCopyWith<$Res> {
  _$IdentityVerificationModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of IdentityVerificationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? documentType = null,
    Object? documentUrl = null,
    Object? fullName = null,
    Object? dateOfBirth = null,
    Object? nationality = null,
    Object? status = null,
    Object? submittedAt = null,
    Object? reviewedAt = freezed,
    Object? reviewedBy = freezed,
    Object? rejectionReason = freezed,
    Object? documentData = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            documentType: null == documentType
                ? _value.documentType
                : documentType // ignore: cast_nullable_to_non_nullable
                      as String,
            documentUrl: null == documentUrl
                ? _value.documentUrl
                : documentUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            fullName: null == fullName
                ? _value.fullName
                : fullName // ignore: cast_nullable_to_non_nullable
                      as String,
            dateOfBirth: null == dateOfBirth
                ? _value.dateOfBirth
                : dateOfBirth // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            nationality: null == nationality
                ? _value.nationality
                : nationality // ignore: cast_nullable_to_non_nullable
                      as String,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as VerificationStatus,
            submittedAt: null == submittedAt
                ? _value.submittedAt
                : submittedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            reviewedAt: freezed == reviewedAt
                ? _value.reviewedAt
                : reviewedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            reviewedBy: freezed == reviewedBy
                ? _value.reviewedBy
                : reviewedBy // ignore: cast_nullable_to_non_nullable
                      as String?,
            rejectionReason: freezed == rejectionReason
                ? _value.rejectionReason
                : rejectionReason // ignore: cast_nullable_to_non_nullable
                      as String?,
            documentData: freezed == documentData
                ? _value.documentData
                : documentData // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$IdentityVerificationModelImplCopyWith<$Res>
    implements $IdentityVerificationModelCopyWith<$Res> {
  factory _$$IdentityVerificationModelImplCopyWith(
    _$IdentityVerificationModelImpl value,
    $Res Function(_$IdentityVerificationModelImpl) then,
  ) = __$$IdentityVerificationModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    String documentType,
    String documentUrl,
    String fullName,
    DateTime dateOfBirth,
    String nationality,
    VerificationStatus status,
    DateTime submittedAt,
    DateTime? reviewedAt,
    String? reviewedBy,
    String? rejectionReason,
    Map<String, dynamic>? documentData,
  });
}

/// @nodoc
class __$$IdentityVerificationModelImplCopyWithImpl<$Res>
    extends
        _$IdentityVerificationModelCopyWithImpl<
          $Res,
          _$IdentityVerificationModelImpl
        >
    implements _$$IdentityVerificationModelImplCopyWith<$Res> {
  __$$IdentityVerificationModelImplCopyWithImpl(
    _$IdentityVerificationModelImpl _value,
    $Res Function(_$IdentityVerificationModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of IdentityVerificationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? documentType = null,
    Object? documentUrl = null,
    Object? fullName = null,
    Object? dateOfBirth = null,
    Object? nationality = null,
    Object? status = null,
    Object? submittedAt = null,
    Object? reviewedAt = freezed,
    Object? reviewedBy = freezed,
    Object? rejectionReason = freezed,
    Object? documentData = freezed,
  }) {
    return _then(
      _$IdentityVerificationModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        documentType: null == documentType
            ? _value.documentType
            : documentType // ignore: cast_nullable_to_non_nullable
                  as String,
        documentUrl: null == documentUrl
            ? _value.documentUrl
            : documentUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        fullName: null == fullName
            ? _value.fullName
            : fullName // ignore: cast_nullable_to_non_nullable
                  as String,
        dateOfBirth: null == dateOfBirth
            ? _value.dateOfBirth
            : dateOfBirth // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        nationality: null == nationality
            ? _value.nationality
            : nationality // ignore: cast_nullable_to_non_nullable
                  as String,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as VerificationStatus,
        submittedAt: null == submittedAt
            ? _value.submittedAt
            : submittedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        reviewedAt: freezed == reviewedAt
            ? _value.reviewedAt
            : reviewedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        reviewedBy: freezed == reviewedBy
            ? _value.reviewedBy
            : reviewedBy // ignore: cast_nullable_to_non_nullable
                  as String?,
        rejectionReason: freezed == rejectionReason
            ? _value.rejectionReason
            : rejectionReason // ignore: cast_nullable_to_non_nullable
                  as String?,
        documentData: freezed == documentData
            ? _value._documentData
            : documentData // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$IdentityVerificationModelImpl implements _IdentityVerificationModel {
  const _$IdentityVerificationModelImpl({
    required this.id,
    required this.userId,
    required this.documentType,
    required this.documentUrl,
    required this.fullName,
    required this.dateOfBirth,
    required this.nationality,
    required this.status,
    required this.submittedAt,
    this.reviewedAt,
    this.reviewedBy,
    this.rejectionReason,
    final Map<String, dynamic>? documentData,
  }) : _documentData = documentData;

  factory _$IdentityVerificationModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$IdentityVerificationModelImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String documentType;
  @override
  final String documentUrl;
  @override
  final String fullName;
  @override
  final DateTime dateOfBirth;
  @override
  final String nationality;
  @override
  final VerificationStatus status;
  @override
  final DateTime submittedAt;
  @override
  final DateTime? reviewedAt;
  @override
  final String? reviewedBy;
  @override
  final String? rejectionReason;
  final Map<String, dynamic>? _documentData;
  @override
  Map<String, dynamic>? get documentData {
    final value = _documentData;
    if (value == null) return null;
    if (_documentData is EqualUnmodifiableMapView) return _documentData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'IdentityVerificationModel(id: $id, userId: $userId, documentType: $documentType, documentUrl: $documentUrl, fullName: $fullName, dateOfBirth: $dateOfBirth, nationality: $nationality, status: $status, submittedAt: $submittedAt, reviewedAt: $reviewedAt, reviewedBy: $reviewedBy, rejectionReason: $rejectionReason, documentData: $documentData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IdentityVerificationModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.documentType, documentType) ||
                other.documentType == documentType) &&
            (identical(other.documentUrl, documentUrl) ||
                other.documentUrl == documentUrl) &&
            (identical(other.fullName, fullName) ||
                other.fullName == fullName) &&
            (identical(other.dateOfBirth, dateOfBirth) ||
                other.dateOfBirth == dateOfBirth) &&
            (identical(other.nationality, nationality) ||
                other.nationality == nationality) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.submittedAt, submittedAt) ||
                other.submittedAt == submittedAt) &&
            (identical(other.reviewedAt, reviewedAt) ||
                other.reviewedAt == reviewedAt) &&
            (identical(other.reviewedBy, reviewedBy) ||
                other.reviewedBy == reviewedBy) &&
            (identical(other.rejectionReason, rejectionReason) ||
                other.rejectionReason == rejectionReason) &&
            const DeepCollectionEquality().equals(
              other._documentData,
              _documentData,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    userId,
    documentType,
    documentUrl,
    fullName,
    dateOfBirth,
    nationality,
    status,
    submittedAt,
    reviewedAt,
    reviewedBy,
    rejectionReason,
    const DeepCollectionEquality().hash(_documentData),
  );

  /// Create a copy of IdentityVerificationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$IdentityVerificationModelImplCopyWith<_$IdentityVerificationModelImpl>
  get copyWith =>
      __$$IdentityVerificationModelImplCopyWithImpl<
        _$IdentityVerificationModelImpl
      >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$IdentityVerificationModelImplToJson(this);
  }
}

abstract class _IdentityVerificationModel implements IdentityVerificationModel {
  const factory _IdentityVerificationModel({
    required final String id,
    required final String userId,
    required final String documentType,
    required final String documentUrl,
    required final String fullName,
    required final DateTime dateOfBirth,
    required final String nationality,
    required final VerificationStatus status,
    required final DateTime submittedAt,
    final DateTime? reviewedAt,
    final String? reviewedBy,
    final String? rejectionReason,
    final Map<String, dynamic>? documentData,
  }) = _$IdentityVerificationModelImpl;

  factory _IdentityVerificationModel.fromJson(Map<String, dynamic> json) =
      _$IdentityVerificationModelImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get documentType;
  @override
  String get documentUrl;
  @override
  String get fullName;
  @override
  DateTime get dateOfBirth;
  @override
  String get nationality;
  @override
  VerificationStatus get status;
  @override
  DateTime get submittedAt;
  @override
  DateTime? get reviewedAt;
  @override
  String? get reviewedBy;
  @override
  String? get rejectionReason;
  @override
  Map<String, dynamic>? get documentData;

  /// Create a copy of IdentityVerificationModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$IdentityVerificationModelImplCopyWith<_$IdentityVerificationModelImpl>
  get copyWith => throw _privateConstructorUsedError;
}

BusinessVerificationModel _$BusinessVerificationModelFromJson(
  Map<String, dynamic> json,
) {
  return _BusinessVerificationModel.fromJson(json);
}

/// @nodoc
mixin _$BusinessVerificationModel {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get businessName => throw _privateConstructorUsedError;
  String get businessRegistrationNumber => throw _privateConstructorUsedError;
  String get businessAddress => throw _privateConstructorUsedError;
  String get businessCategory => throw _privateConstructorUsedError;
  String get documentUrl => throw _privateConstructorUsedError;
  String get contactEmail => throw _privateConstructorUsedError;
  String get contactPhone => throw _privateConstructorUsedError;
  VerificationStatus get status => throw _privateConstructorUsedError;
  DateTime get submittedAt => throw _privateConstructorUsedError;
  DateTime? get reviewedAt => throw _privateConstructorUsedError;
  String? get reviewedBy => throw _privateConstructorUsedError;
  String? get rejectionReason => throw _privateConstructorUsedError;
  String? get verifiedBusinessName => throw _privateConstructorUsedError;
  String? get verifiedBusinessLogo => throw _privateConstructorUsedError;
  Map<String, dynamic>? get businessData => throw _privateConstructorUsedError;

  /// Serializes this BusinessVerificationModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BusinessVerificationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BusinessVerificationModelCopyWith<BusinessVerificationModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BusinessVerificationModelCopyWith<$Res> {
  factory $BusinessVerificationModelCopyWith(
    BusinessVerificationModel value,
    $Res Function(BusinessVerificationModel) then,
  ) = _$BusinessVerificationModelCopyWithImpl<$Res, BusinessVerificationModel>;
  @useResult
  $Res call({
    String id,
    String userId,
    String businessName,
    String businessRegistrationNumber,
    String businessAddress,
    String businessCategory,
    String documentUrl,
    String contactEmail,
    String contactPhone,
    VerificationStatus status,
    DateTime submittedAt,
    DateTime? reviewedAt,
    String? reviewedBy,
    String? rejectionReason,
    String? verifiedBusinessName,
    String? verifiedBusinessLogo,
    Map<String, dynamic>? businessData,
  });
}

/// @nodoc
class _$BusinessVerificationModelCopyWithImpl<
  $Res,
  $Val extends BusinessVerificationModel
>
    implements $BusinessVerificationModelCopyWith<$Res> {
  _$BusinessVerificationModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BusinessVerificationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? businessName = null,
    Object? businessRegistrationNumber = null,
    Object? businessAddress = null,
    Object? businessCategory = null,
    Object? documentUrl = null,
    Object? contactEmail = null,
    Object? contactPhone = null,
    Object? status = null,
    Object? submittedAt = null,
    Object? reviewedAt = freezed,
    Object? reviewedBy = freezed,
    Object? rejectionReason = freezed,
    Object? verifiedBusinessName = freezed,
    Object? verifiedBusinessLogo = freezed,
    Object? businessData = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            businessName: null == businessName
                ? _value.businessName
                : businessName // ignore: cast_nullable_to_non_nullable
                      as String,
            businessRegistrationNumber: null == businessRegistrationNumber
                ? _value.businessRegistrationNumber
                : businessRegistrationNumber // ignore: cast_nullable_to_non_nullable
                      as String,
            businessAddress: null == businessAddress
                ? _value.businessAddress
                : businessAddress // ignore: cast_nullable_to_non_nullable
                      as String,
            businessCategory: null == businessCategory
                ? _value.businessCategory
                : businessCategory // ignore: cast_nullable_to_non_nullable
                      as String,
            documentUrl: null == documentUrl
                ? _value.documentUrl
                : documentUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            contactEmail: null == contactEmail
                ? _value.contactEmail
                : contactEmail // ignore: cast_nullable_to_non_nullable
                      as String,
            contactPhone: null == contactPhone
                ? _value.contactPhone
                : contactPhone // ignore: cast_nullable_to_non_nullable
                      as String,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as VerificationStatus,
            submittedAt: null == submittedAt
                ? _value.submittedAt
                : submittedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            reviewedAt: freezed == reviewedAt
                ? _value.reviewedAt
                : reviewedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            reviewedBy: freezed == reviewedBy
                ? _value.reviewedBy
                : reviewedBy // ignore: cast_nullable_to_non_nullable
                      as String?,
            rejectionReason: freezed == rejectionReason
                ? _value.rejectionReason
                : rejectionReason // ignore: cast_nullable_to_non_nullable
                      as String?,
            verifiedBusinessName: freezed == verifiedBusinessName
                ? _value.verifiedBusinessName
                : verifiedBusinessName // ignore: cast_nullable_to_non_nullable
                      as String?,
            verifiedBusinessLogo: freezed == verifiedBusinessLogo
                ? _value.verifiedBusinessLogo
                : verifiedBusinessLogo // ignore: cast_nullable_to_non_nullable
                      as String?,
            businessData: freezed == businessData
                ? _value.businessData
                : businessData // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$BusinessVerificationModelImplCopyWith<$Res>
    implements $BusinessVerificationModelCopyWith<$Res> {
  factory _$$BusinessVerificationModelImplCopyWith(
    _$BusinessVerificationModelImpl value,
    $Res Function(_$BusinessVerificationModelImpl) then,
  ) = __$$BusinessVerificationModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    String businessName,
    String businessRegistrationNumber,
    String businessAddress,
    String businessCategory,
    String documentUrl,
    String contactEmail,
    String contactPhone,
    VerificationStatus status,
    DateTime submittedAt,
    DateTime? reviewedAt,
    String? reviewedBy,
    String? rejectionReason,
    String? verifiedBusinessName,
    String? verifiedBusinessLogo,
    Map<String, dynamic>? businessData,
  });
}

/// @nodoc
class __$$BusinessVerificationModelImplCopyWithImpl<$Res>
    extends
        _$BusinessVerificationModelCopyWithImpl<
          $Res,
          _$BusinessVerificationModelImpl
        >
    implements _$$BusinessVerificationModelImplCopyWith<$Res> {
  __$$BusinessVerificationModelImplCopyWithImpl(
    _$BusinessVerificationModelImpl _value,
    $Res Function(_$BusinessVerificationModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of BusinessVerificationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? businessName = null,
    Object? businessRegistrationNumber = null,
    Object? businessAddress = null,
    Object? businessCategory = null,
    Object? documentUrl = null,
    Object? contactEmail = null,
    Object? contactPhone = null,
    Object? status = null,
    Object? submittedAt = null,
    Object? reviewedAt = freezed,
    Object? reviewedBy = freezed,
    Object? rejectionReason = freezed,
    Object? verifiedBusinessName = freezed,
    Object? verifiedBusinessLogo = freezed,
    Object? businessData = freezed,
  }) {
    return _then(
      _$BusinessVerificationModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        businessName: null == businessName
            ? _value.businessName
            : businessName // ignore: cast_nullable_to_non_nullable
                  as String,
        businessRegistrationNumber: null == businessRegistrationNumber
            ? _value.businessRegistrationNumber
            : businessRegistrationNumber // ignore: cast_nullable_to_non_nullable
                  as String,
        businessAddress: null == businessAddress
            ? _value.businessAddress
            : businessAddress // ignore: cast_nullable_to_non_nullable
                  as String,
        businessCategory: null == businessCategory
            ? _value.businessCategory
            : businessCategory // ignore: cast_nullable_to_non_nullable
                  as String,
        documentUrl: null == documentUrl
            ? _value.documentUrl
            : documentUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        contactEmail: null == contactEmail
            ? _value.contactEmail
            : contactEmail // ignore: cast_nullable_to_non_nullable
                  as String,
        contactPhone: null == contactPhone
            ? _value.contactPhone
            : contactPhone // ignore: cast_nullable_to_non_nullable
                  as String,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as VerificationStatus,
        submittedAt: null == submittedAt
            ? _value.submittedAt
            : submittedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        reviewedAt: freezed == reviewedAt
            ? _value.reviewedAt
            : reviewedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        reviewedBy: freezed == reviewedBy
            ? _value.reviewedBy
            : reviewedBy // ignore: cast_nullable_to_non_nullable
                  as String?,
        rejectionReason: freezed == rejectionReason
            ? _value.rejectionReason
            : rejectionReason // ignore: cast_nullable_to_non_nullable
                  as String?,
        verifiedBusinessName: freezed == verifiedBusinessName
            ? _value.verifiedBusinessName
            : verifiedBusinessName // ignore: cast_nullable_to_non_nullable
                  as String?,
        verifiedBusinessLogo: freezed == verifiedBusinessLogo
            ? _value.verifiedBusinessLogo
            : verifiedBusinessLogo // ignore: cast_nullable_to_non_nullable
                  as String?,
        businessData: freezed == businessData
            ? _value._businessData
            : businessData // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$BusinessVerificationModelImpl implements _BusinessVerificationModel {
  const _$BusinessVerificationModelImpl({
    required this.id,
    required this.userId,
    required this.businessName,
    required this.businessRegistrationNumber,
    required this.businessAddress,
    required this.businessCategory,
    required this.documentUrl,
    required this.contactEmail,
    required this.contactPhone,
    required this.status,
    required this.submittedAt,
    this.reviewedAt,
    this.reviewedBy,
    this.rejectionReason,
    this.verifiedBusinessName,
    this.verifiedBusinessLogo,
    final Map<String, dynamic>? businessData,
  }) : _businessData = businessData;

  factory _$BusinessVerificationModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$BusinessVerificationModelImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String businessName;
  @override
  final String businessRegistrationNumber;
  @override
  final String businessAddress;
  @override
  final String businessCategory;
  @override
  final String documentUrl;
  @override
  final String contactEmail;
  @override
  final String contactPhone;
  @override
  final VerificationStatus status;
  @override
  final DateTime submittedAt;
  @override
  final DateTime? reviewedAt;
  @override
  final String? reviewedBy;
  @override
  final String? rejectionReason;
  @override
  final String? verifiedBusinessName;
  @override
  final String? verifiedBusinessLogo;
  final Map<String, dynamic>? _businessData;
  @override
  Map<String, dynamic>? get businessData {
    final value = _businessData;
    if (value == null) return null;
    if (_businessData is EqualUnmodifiableMapView) return _businessData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'BusinessVerificationModel(id: $id, userId: $userId, businessName: $businessName, businessRegistrationNumber: $businessRegistrationNumber, businessAddress: $businessAddress, businessCategory: $businessCategory, documentUrl: $documentUrl, contactEmail: $contactEmail, contactPhone: $contactPhone, status: $status, submittedAt: $submittedAt, reviewedAt: $reviewedAt, reviewedBy: $reviewedBy, rejectionReason: $rejectionReason, verifiedBusinessName: $verifiedBusinessName, verifiedBusinessLogo: $verifiedBusinessLogo, businessData: $businessData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BusinessVerificationModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.businessName, businessName) ||
                other.businessName == businessName) &&
            (identical(
                  other.businessRegistrationNumber,
                  businessRegistrationNumber,
                ) ||
                other.businessRegistrationNumber ==
                    businessRegistrationNumber) &&
            (identical(other.businessAddress, businessAddress) ||
                other.businessAddress == businessAddress) &&
            (identical(other.businessCategory, businessCategory) ||
                other.businessCategory == businessCategory) &&
            (identical(other.documentUrl, documentUrl) ||
                other.documentUrl == documentUrl) &&
            (identical(other.contactEmail, contactEmail) ||
                other.contactEmail == contactEmail) &&
            (identical(other.contactPhone, contactPhone) ||
                other.contactPhone == contactPhone) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.submittedAt, submittedAt) ||
                other.submittedAt == submittedAt) &&
            (identical(other.reviewedAt, reviewedAt) ||
                other.reviewedAt == reviewedAt) &&
            (identical(other.reviewedBy, reviewedBy) ||
                other.reviewedBy == reviewedBy) &&
            (identical(other.rejectionReason, rejectionReason) ||
                other.rejectionReason == rejectionReason) &&
            (identical(other.verifiedBusinessName, verifiedBusinessName) ||
                other.verifiedBusinessName == verifiedBusinessName) &&
            (identical(other.verifiedBusinessLogo, verifiedBusinessLogo) ||
                other.verifiedBusinessLogo == verifiedBusinessLogo) &&
            const DeepCollectionEquality().equals(
              other._businessData,
              _businessData,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    userId,
    businessName,
    businessRegistrationNumber,
    businessAddress,
    businessCategory,
    documentUrl,
    contactEmail,
    contactPhone,
    status,
    submittedAt,
    reviewedAt,
    reviewedBy,
    rejectionReason,
    verifiedBusinessName,
    verifiedBusinessLogo,
    const DeepCollectionEquality().hash(_businessData),
  );

  /// Create a copy of BusinessVerificationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BusinessVerificationModelImplCopyWith<_$BusinessVerificationModelImpl>
  get copyWith =>
      __$$BusinessVerificationModelImplCopyWithImpl<
        _$BusinessVerificationModelImpl
      >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BusinessVerificationModelImplToJson(this);
  }
}

abstract class _BusinessVerificationModel implements BusinessVerificationModel {
  const factory _BusinessVerificationModel({
    required final String id,
    required final String userId,
    required final String businessName,
    required final String businessRegistrationNumber,
    required final String businessAddress,
    required final String businessCategory,
    required final String documentUrl,
    required final String contactEmail,
    required final String contactPhone,
    required final VerificationStatus status,
    required final DateTime submittedAt,
    final DateTime? reviewedAt,
    final String? reviewedBy,
    final String? rejectionReason,
    final String? verifiedBusinessName,
    final String? verifiedBusinessLogo,
    final Map<String, dynamic>? businessData,
  }) = _$BusinessVerificationModelImpl;

  factory _BusinessVerificationModel.fromJson(Map<String, dynamic> json) =
      _$BusinessVerificationModelImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get businessName;
  @override
  String get businessRegistrationNumber;
  @override
  String get businessAddress;
  @override
  String get businessCategory;
  @override
  String get documentUrl;
  @override
  String get contactEmail;
  @override
  String get contactPhone;
  @override
  VerificationStatus get status;
  @override
  DateTime get submittedAt;
  @override
  DateTime? get reviewedAt;
  @override
  String? get reviewedBy;
  @override
  String? get rejectionReason;
  @override
  String? get verifiedBusinessName;
  @override
  String? get verifiedBusinessLogo;
  @override
  Map<String, dynamic>? get businessData;

  /// Create a copy of BusinessVerificationModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BusinessVerificationModelImplCopyWith<_$BusinessVerificationModelImpl>
  get copyWith => throw _privateConstructorUsedError;
}
