// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'current_user_profile_picture_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$currentUserProfilePictureHash() =>
    r'5233de82f604c0112b93a46617b727d2c4bc03ad';

/// Provider for current user's profile picture with real-time updates
///
/// Copied from [currentUserProfilePicture].
@ProviderFor(currentUserProfilePicture)
final currentUserProfilePictureProvider =
    AutoDisposeStreamProvider<ProfilePictureData?>.internal(
      currentUserProfilePicture,
      name: r'currentUserProfilePictureProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$currentUserProfilePictureHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentUserProfilePictureRef =
    AutoDisposeStreamProviderRef<ProfilePictureData?>;
String _$userProfilePictureHash() =>
    r'49218422c9272322b6bc7d0f34f5b868a89d2dc2';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider for any user's profile picture (not just current user)
///
/// Copied from [userProfilePicture].
@ProviderFor(userProfilePicture)
const userProfilePictureProvider = UserProfilePictureFamily();

/// Provider for any user's profile picture (not just current user)
///
/// Copied from [userProfilePicture].
class UserProfilePictureFamily extends Family<AsyncValue<ProfilePictureData?>> {
  /// Provider for any user's profile picture (not just current user)
  ///
  /// Copied from [userProfilePicture].
  const UserProfilePictureFamily();

  /// Provider for any user's profile picture (not just current user)
  ///
  /// Copied from [userProfilePicture].
  UserProfilePictureProvider call(String userId) {
    return UserProfilePictureProvider(userId);
  }

  @override
  UserProfilePictureProvider getProviderOverride(
    covariant UserProfilePictureProvider provider,
  ) {
    return call(provider.userId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'userProfilePictureProvider';
}

/// Provider for any user's profile picture (not just current user)
///
/// Copied from [userProfilePicture].
class UserProfilePictureProvider
    extends AutoDisposeStreamProvider<ProfilePictureData?> {
  /// Provider for any user's profile picture (not just current user)
  ///
  /// Copied from [userProfilePicture].
  UserProfilePictureProvider(String userId)
    : this._internal(
        (ref) => userProfilePicture(ref as UserProfilePictureRef, userId),
        from: userProfilePictureProvider,
        name: r'userProfilePictureProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$userProfilePictureHash,
        dependencies: UserProfilePictureFamily._dependencies,
        allTransitiveDependencies:
            UserProfilePictureFamily._allTransitiveDependencies,
        userId: userId,
      );

  UserProfilePictureProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  Override overrideWith(
    Stream<ProfilePictureData?> Function(UserProfilePictureRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: UserProfilePictureProvider._internal(
        (ref) => create(ref as UserProfilePictureRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeStreamProviderElement<ProfilePictureData?> createElement() {
    return _UserProfilePictureProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is UserProfilePictureProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin UserProfilePictureRef
    on AutoDisposeStreamProviderRef<ProfilePictureData?> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _UserProfilePictureProviderElement
    extends AutoDisposeStreamProviderElement<ProfilePictureData?>
    with UserProfilePictureRef {
  _UserProfilePictureProviderElement(super.provider);

  @override
  String get userId => (origin as UserProfilePictureProvider).userId;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
