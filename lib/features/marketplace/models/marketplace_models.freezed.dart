// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'marketplace_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

Seller _$SellerFromJson(Map<String, dynamic> json) {
  return _Seller.fromJson(json);
}

/// @nodoc
mixin _$Seller {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get avatarUrl => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  double get rating => throw _privateConstructorUsedError;
  int get reviewCount => throw _privateConstructorUsedError;
  int get totalSales => throw _privateConstructorUsedError;
  bool get isVerified => throw _privateConstructorUsedError;
  bool get isPremium => throw _privateConstructorUsedError;
  String? get location => throw _privateConstructorUsedError;
  DateTime? get joinedDate => throw _privateConstructorUsedError;
  List<String> get specializations => throw _privateConstructorUsedError;

  /// Serializes this Seller to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Seller
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SellerCopyWith<Seller> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SellerCopyWith<$Res> {
  factory $SellerCopyWith(Seller value, $Res Function(Seller) then) =
      _$SellerCopyWithImpl<$Res, Seller>;
  @useResult
  $Res call({
    String id,
    String name,
    String avatarUrl,
    String? description,
    double rating,
    int reviewCount,
    int totalSales,
    bool isVerified,
    bool isPremium,
    String? location,
    DateTime? joinedDate,
    List<String> specializations,
  });
}

/// @nodoc
class _$SellerCopyWithImpl<$Res, $Val extends Seller>
    implements $SellerCopyWith<$Res> {
  _$SellerCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Seller
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? avatarUrl = null,
    Object? description = freezed,
    Object? rating = null,
    Object? reviewCount = null,
    Object? totalSales = null,
    Object? isVerified = null,
    Object? isPremium = null,
    Object? location = freezed,
    Object? joinedDate = freezed,
    Object? specializations = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            avatarUrl: null == avatarUrl
                ? _value.avatarUrl
                : avatarUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            rating: null == rating
                ? _value.rating
                : rating // ignore: cast_nullable_to_non_nullable
                      as double,
            reviewCount: null == reviewCount
                ? _value.reviewCount
                : reviewCount // ignore: cast_nullable_to_non_nullable
                      as int,
            totalSales: null == totalSales
                ? _value.totalSales
                : totalSales // ignore: cast_nullable_to_non_nullable
                      as int,
            isVerified: null == isVerified
                ? _value.isVerified
                : isVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            isPremium: null == isPremium
                ? _value.isPremium
                : isPremium // ignore: cast_nullable_to_non_nullable
                      as bool,
            location: freezed == location
                ? _value.location
                : location // ignore: cast_nullable_to_non_nullable
                      as String?,
            joinedDate: freezed == joinedDate
                ? _value.joinedDate
                : joinedDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            specializations: null == specializations
                ? _value.specializations
                : specializations // ignore: cast_nullable_to_non_nullable
                      as List<String>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$SellerImplCopyWith<$Res> implements $SellerCopyWith<$Res> {
  factory _$$SellerImplCopyWith(
    _$SellerImpl value,
    $Res Function(_$SellerImpl) then,
  ) = __$$SellerImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String avatarUrl,
    String? description,
    double rating,
    int reviewCount,
    int totalSales,
    bool isVerified,
    bool isPremium,
    String? location,
    DateTime? joinedDate,
    List<String> specializations,
  });
}

/// @nodoc
class __$$SellerImplCopyWithImpl<$Res>
    extends _$SellerCopyWithImpl<$Res, _$SellerImpl>
    implements _$$SellerImplCopyWith<$Res> {
  __$$SellerImplCopyWithImpl(
    _$SellerImpl _value,
    $Res Function(_$SellerImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Seller
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? avatarUrl = null,
    Object? description = freezed,
    Object? rating = null,
    Object? reviewCount = null,
    Object? totalSales = null,
    Object? isVerified = null,
    Object? isPremium = null,
    Object? location = freezed,
    Object? joinedDate = freezed,
    Object? specializations = null,
  }) {
    return _then(
      _$SellerImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        avatarUrl: null == avatarUrl
            ? _value.avatarUrl
            : avatarUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        rating: null == rating
            ? _value.rating
            : rating // ignore: cast_nullable_to_non_nullable
                  as double,
        reviewCount: null == reviewCount
            ? _value.reviewCount
            : reviewCount // ignore: cast_nullable_to_non_nullable
                  as int,
        totalSales: null == totalSales
            ? _value.totalSales
            : totalSales // ignore: cast_nullable_to_non_nullable
                  as int,
        isVerified: null == isVerified
            ? _value.isVerified
            : isVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        isPremium: null == isPremium
            ? _value.isPremium
            : isPremium // ignore: cast_nullable_to_non_nullable
                  as bool,
        location: freezed == location
            ? _value.location
            : location // ignore: cast_nullable_to_non_nullable
                  as String?,
        joinedDate: freezed == joinedDate
            ? _value.joinedDate
            : joinedDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        specializations: null == specializations
            ? _value._specializations
            : specializations // ignore: cast_nullable_to_non_nullable
                  as List<String>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$SellerImpl implements _Seller {
  const _$SellerImpl({
    required this.id,
    required this.name,
    required this.avatarUrl,
    this.description,
    this.rating = 0.0,
    this.reviewCount = 0,
    this.totalSales = 0,
    this.isVerified = false,
    this.isPremium = false,
    this.location,
    this.joinedDate,
    final List<String> specializations = const [],
  }) : _specializations = specializations;

  factory _$SellerImpl.fromJson(Map<String, dynamic> json) =>
      _$$SellerImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String avatarUrl;
  @override
  final String? description;
  @override
  @JsonKey()
  final double rating;
  @override
  @JsonKey()
  final int reviewCount;
  @override
  @JsonKey()
  final int totalSales;
  @override
  @JsonKey()
  final bool isVerified;
  @override
  @JsonKey()
  final bool isPremium;
  @override
  final String? location;
  @override
  final DateTime? joinedDate;
  final List<String> _specializations;
  @override
  @JsonKey()
  List<String> get specializations {
    if (_specializations is EqualUnmodifiableListView) return _specializations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_specializations);
  }

  @override
  String toString() {
    return 'Seller(id: $id, name: $name, avatarUrl: $avatarUrl, description: $description, rating: $rating, reviewCount: $reviewCount, totalSales: $totalSales, isVerified: $isVerified, isPremium: $isPremium, location: $location, joinedDate: $joinedDate, specializations: $specializations)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SellerImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.avatarUrl, avatarUrl) ||
                other.avatarUrl == avatarUrl) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            (identical(other.reviewCount, reviewCount) ||
                other.reviewCount == reviewCount) &&
            (identical(other.totalSales, totalSales) ||
                other.totalSales == totalSales) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.isPremium, isPremium) ||
                other.isPremium == isPremium) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.joinedDate, joinedDate) ||
                other.joinedDate == joinedDate) &&
            const DeepCollectionEquality().equals(
              other._specializations,
              _specializations,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    avatarUrl,
    description,
    rating,
    reviewCount,
    totalSales,
    isVerified,
    isPremium,
    location,
    joinedDate,
    const DeepCollectionEquality().hash(_specializations),
  );

  /// Create a copy of Seller
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SellerImplCopyWith<_$SellerImpl> get copyWith =>
      __$$SellerImplCopyWithImpl<_$SellerImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SellerImplToJson(this);
  }
}

abstract class _Seller implements Seller {
  const factory _Seller({
    required final String id,
    required final String name,
    required final String avatarUrl,
    final String? description,
    final double rating,
    final int reviewCount,
    final int totalSales,
    final bool isVerified,
    final bool isPremium,
    final String? location,
    final DateTime? joinedDate,
    final List<String> specializations,
  }) = _$SellerImpl;

  factory _Seller.fromJson(Map<String, dynamic> json) = _$SellerImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get avatarUrl;
  @override
  String? get description;
  @override
  double get rating;
  @override
  int get reviewCount;
  @override
  int get totalSales;
  @override
  bool get isVerified;
  @override
  bool get isPremium;
  @override
  String? get location;
  @override
  DateTime? get joinedDate;
  @override
  List<String> get specializations;

  /// Create a copy of Seller
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SellerImplCopyWith<_$SellerImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

MarketplaceItem _$MarketplaceItemFromJson(Map<String, dynamic> json) {
  return _MarketplaceItem.fromJson(json);
}

/// @nodoc
mixin _$MarketplaceItem {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  double get price => throw _privateConstructorUsedError;
  ProductCategory get category => throw _privateConstructorUsedError;
  List<String> get images => throw _privateConstructorUsedError;
  String get sellerId => throw _privateConstructorUsedError;
  Seller get seller => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  bool get isVerified => throw _privateConstructorUsedError;
  bool get isExclusive => throw _privateConstructorUsedError;
  bool get isFeatured => throw _privateConstructorUsedError;
  ProductCondition get condition => throw _privateConstructorUsedError;
  int get availableQuantity => throw _privateConstructorUsedError;
  int get soldQuantity => throw _privateConstructorUsedError;
  double get rating => throw _privateConstructorUsedError;
  int get reviewCount => throw _privateConstructorUsedError;
  String? get brand => throw _privateConstructorUsedError;
  String? get model => throw _privateConstructorUsedError;
  String? get year => throw _privateConstructorUsedError;
  String? get location => throw _privateConstructorUsedError;
  Map<String, dynamic>? get specifications =>
      throw _privateConstructorUsedError;
  List<String>? get tags => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this MarketplaceItem to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MarketplaceItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MarketplaceItemCopyWith<MarketplaceItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MarketplaceItemCopyWith<$Res> {
  factory $MarketplaceItemCopyWith(
    MarketplaceItem value,
    $Res Function(MarketplaceItem) then,
  ) = _$MarketplaceItemCopyWithImpl<$Res, MarketplaceItem>;
  @useResult
  $Res call({
    String id,
    String name,
    double price,
    ProductCategory category,
    List<String> images,
    String sellerId,
    Seller seller,
    String? description,
    bool isVerified,
    bool isExclusive,
    bool isFeatured,
    ProductCondition condition,
    int availableQuantity,
    int soldQuantity,
    double rating,
    int reviewCount,
    String? brand,
    String? model,
    String? year,
    String? location,
    Map<String, dynamic>? specifications,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
  });

  $SellerCopyWith<$Res> get seller;
}

/// @nodoc
class _$MarketplaceItemCopyWithImpl<$Res, $Val extends MarketplaceItem>
    implements $MarketplaceItemCopyWith<$Res> {
  _$MarketplaceItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MarketplaceItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? price = null,
    Object? category = null,
    Object? images = null,
    Object? sellerId = null,
    Object? seller = null,
    Object? description = freezed,
    Object? isVerified = null,
    Object? isExclusive = null,
    Object? isFeatured = null,
    Object? condition = null,
    Object? availableQuantity = null,
    Object? soldQuantity = null,
    Object? rating = null,
    Object? reviewCount = null,
    Object? brand = freezed,
    Object? model = freezed,
    Object? year = freezed,
    Object? location = freezed,
    Object? specifications = freezed,
    Object? tags = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            price: null == price
                ? _value.price
                : price // ignore: cast_nullable_to_non_nullable
                      as double,
            category: null == category
                ? _value.category
                : category // ignore: cast_nullable_to_non_nullable
                      as ProductCategory,
            images: null == images
                ? _value.images
                : images // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            sellerId: null == sellerId
                ? _value.sellerId
                : sellerId // ignore: cast_nullable_to_non_nullable
                      as String,
            seller: null == seller
                ? _value.seller
                : seller // ignore: cast_nullable_to_non_nullable
                      as Seller,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            isVerified: null == isVerified
                ? _value.isVerified
                : isVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            isExclusive: null == isExclusive
                ? _value.isExclusive
                : isExclusive // ignore: cast_nullable_to_non_nullable
                      as bool,
            isFeatured: null == isFeatured
                ? _value.isFeatured
                : isFeatured // ignore: cast_nullable_to_non_nullable
                      as bool,
            condition: null == condition
                ? _value.condition
                : condition // ignore: cast_nullable_to_non_nullable
                      as ProductCondition,
            availableQuantity: null == availableQuantity
                ? _value.availableQuantity
                : availableQuantity // ignore: cast_nullable_to_non_nullable
                      as int,
            soldQuantity: null == soldQuantity
                ? _value.soldQuantity
                : soldQuantity // ignore: cast_nullable_to_non_nullable
                      as int,
            rating: null == rating
                ? _value.rating
                : rating // ignore: cast_nullable_to_non_nullable
                      as double,
            reviewCount: null == reviewCount
                ? _value.reviewCount
                : reviewCount // ignore: cast_nullable_to_non_nullable
                      as int,
            brand: freezed == brand
                ? _value.brand
                : brand // ignore: cast_nullable_to_non_nullable
                      as String?,
            model: freezed == model
                ? _value.model
                : model // ignore: cast_nullable_to_non_nullable
                      as String?,
            year: freezed == year
                ? _value.year
                : year // ignore: cast_nullable_to_non_nullable
                      as String?,
            location: freezed == location
                ? _value.location
                : location // ignore: cast_nullable_to_non_nullable
                      as String?,
            specifications: freezed == specifications
                ? _value.specifications
                : specifications // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
            tags: freezed == tags
                ? _value.tags
                : tags // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }

  /// Create a copy of MarketplaceItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SellerCopyWith<$Res> get seller {
    return $SellerCopyWith<$Res>(_value.seller, (value) {
      return _then(_value.copyWith(seller: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$MarketplaceItemImplCopyWith<$Res>
    implements $MarketplaceItemCopyWith<$Res> {
  factory _$$MarketplaceItemImplCopyWith(
    _$MarketplaceItemImpl value,
    $Res Function(_$MarketplaceItemImpl) then,
  ) = __$$MarketplaceItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    double price,
    ProductCategory category,
    List<String> images,
    String sellerId,
    Seller seller,
    String? description,
    bool isVerified,
    bool isExclusive,
    bool isFeatured,
    ProductCondition condition,
    int availableQuantity,
    int soldQuantity,
    double rating,
    int reviewCount,
    String? brand,
    String? model,
    String? year,
    String? location,
    Map<String, dynamic>? specifications,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
  });

  @override
  $SellerCopyWith<$Res> get seller;
}

/// @nodoc
class __$$MarketplaceItemImplCopyWithImpl<$Res>
    extends _$MarketplaceItemCopyWithImpl<$Res, _$MarketplaceItemImpl>
    implements _$$MarketplaceItemImplCopyWith<$Res> {
  __$$MarketplaceItemImplCopyWithImpl(
    _$MarketplaceItemImpl _value,
    $Res Function(_$MarketplaceItemImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MarketplaceItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? price = null,
    Object? category = null,
    Object? images = null,
    Object? sellerId = null,
    Object? seller = null,
    Object? description = freezed,
    Object? isVerified = null,
    Object? isExclusive = null,
    Object? isFeatured = null,
    Object? condition = null,
    Object? availableQuantity = null,
    Object? soldQuantity = null,
    Object? rating = null,
    Object? reviewCount = null,
    Object? brand = freezed,
    Object? model = freezed,
    Object? year = freezed,
    Object? location = freezed,
    Object? specifications = freezed,
    Object? tags = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$MarketplaceItemImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        price: null == price
            ? _value.price
            : price // ignore: cast_nullable_to_non_nullable
                  as double,
        category: null == category
            ? _value.category
            : category // ignore: cast_nullable_to_non_nullable
                  as ProductCategory,
        images: null == images
            ? _value._images
            : images // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        sellerId: null == sellerId
            ? _value.sellerId
            : sellerId // ignore: cast_nullable_to_non_nullable
                  as String,
        seller: null == seller
            ? _value.seller
            : seller // ignore: cast_nullable_to_non_nullable
                  as Seller,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        isVerified: null == isVerified
            ? _value.isVerified
            : isVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        isExclusive: null == isExclusive
            ? _value.isExclusive
            : isExclusive // ignore: cast_nullable_to_non_nullable
                  as bool,
        isFeatured: null == isFeatured
            ? _value.isFeatured
            : isFeatured // ignore: cast_nullable_to_non_nullable
                  as bool,
        condition: null == condition
            ? _value.condition
            : condition // ignore: cast_nullable_to_non_nullable
                  as ProductCondition,
        availableQuantity: null == availableQuantity
            ? _value.availableQuantity
            : availableQuantity // ignore: cast_nullable_to_non_nullable
                  as int,
        soldQuantity: null == soldQuantity
            ? _value.soldQuantity
            : soldQuantity // ignore: cast_nullable_to_non_nullable
                  as int,
        rating: null == rating
            ? _value.rating
            : rating // ignore: cast_nullable_to_non_nullable
                  as double,
        reviewCount: null == reviewCount
            ? _value.reviewCount
            : reviewCount // ignore: cast_nullable_to_non_nullable
                  as int,
        brand: freezed == brand
            ? _value.brand
            : brand // ignore: cast_nullable_to_non_nullable
                  as String?,
        model: freezed == model
            ? _value.model
            : model // ignore: cast_nullable_to_non_nullable
                  as String?,
        year: freezed == year
            ? _value.year
            : year // ignore: cast_nullable_to_non_nullable
                  as String?,
        location: freezed == location
            ? _value.location
            : location // ignore: cast_nullable_to_non_nullable
                  as String?,
        specifications: freezed == specifications
            ? _value._specifications
            : specifications // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
        tags: freezed == tags
            ? _value._tags
            : tags // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$MarketplaceItemImpl implements _MarketplaceItem {
  const _$MarketplaceItemImpl({
    required this.id,
    required this.name,
    required this.price,
    required this.category,
    required final List<String> images,
    required this.sellerId,
    required this.seller,
    this.description,
    this.isVerified = false,
    this.isExclusive = false,
    this.isFeatured = false,
    this.condition = ProductCondition.brandNew,
    this.availableQuantity = 1,
    this.soldQuantity = 0,
    this.rating = 0.0,
    this.reviewCount = 0,
    this.brand,
    this.model,
    this.year,
    this.location,
    final Map<String, dynamic>? specifications,
    final List<String>? tags,
    this.createdAt,
    this.updatedAt,
  }) : _images = images,
       _specifications = specifications,
       _tags = tags;

  factory _$MarketplaceItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$MarketplaceItemImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final double price;
  @override
  final ProductCategory category;
  final List<String> _images;
  @override
  List<String> get images {
    if (_images is EqualUnmodifiableListView) return _images;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_images);
  }

  @override
  final String sellerId;
  @override
  final Seller seller;
  @override
  final String? description;
  @override
  @JsonKey()
  final bool isVerified;
  @override
  @JsonKey()
  final bool isExclusive;
  @override
  @JsonKey()
  final bool isFeatured;
  @override
  @JsonKey()
  final ProductCondition condition;
  @override
  @JsonKey()
  final int availableQuantity;
  @override
  @JsonKey()
  final int soldQuantity;
  @override
  @JsonKey()
  final double rating;
  @override
  @JsonKey()
  final int reviewCount;
  @override
  final String? brand;
  @override
  final String? model;
  @override
  final String? year;
  @override
  final String? location;
  final Map<String, dynamic>? _specifications;
  @override
  Map<String, dynamic>? get specifications {
    final value = _specifications;
    if (value == null) return null;
    if (_specifications is EqualUnmodifiableMapView) return _specifications;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  final List<String>? _tags;
  @override
  List<String>? get tags {
    final value = _tags;
    if (value == null) return null;
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'MarketplaceItem(id: $id, name: $name, price: $price, category: $category, images: $images, sellerId: $sellerId, seller: $seller, description: $description, isVerified: $isVerified, isExclusive: $isExclusive, isFeatured: $isFeatured, condition: $condition, availableQuantity: $availableQuantity, soldQuantity: $soldQuantity, rating: $rating, reviewCount: $reviewCount, brand: $brand, model: $model, year: $year, location: $location, specifications: $specifications, tags: $tags, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MarketplaceItemImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.category, category) ||
                other.category == category) &&
            const DeepCollectionEquality().equals(other._images, _images) &&
            (identical(other.sellerId, sellerId) ||
                other.sellerId == sellerId) &&
            (identical(other.seller, seller) || other.seller == seller) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.isExclusive, isExclusive) ||
                other.isExclusive == isExclusive) &&
            (identical(other.isFeatured, isFeatured) ||
                other.isFeatured == isFeatured) &&
            (identical(other.condition, condition) ||
                other.condition == condition) &&
            (identical(other.availableQuantity, availableQuantity) ||
                other.availableQuantity == availableQuantity) &&
            (identical(other.soldQuantity, soldQuantity) ||
                other.soldQuantity == soldQuantity) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            (identical(other.reviewCount, reviewCount) ||
                other.reviewCount == reviewCount) &&
            (identical(other.brand, brand) || other.brand == brand) &&
            (identical(other.model, model) || other.model == model) &&
            (identical(other.year, year) || other.year == year) &&
            (identical(other.location, location) ||
                other.location == location) &&
            const DeepCollectionEquality().equals(
              other._specifications,
              _specifications,
            ) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    name,
    price,
    category,
    const DeepCollectionEquality().hash(_images),
    sellerId,
    seller,
    description,
    isVerified,
    isExclusive,
    isFeatured,
    condition,
    availableQuantity,
    soldQuantity,
    rating,
    reviewCount,
    brand,
    model,
    year,
    location,
    const DeepCollectionEquality().hash(_specifications),
    const DeepCollectionEquality().hash(_tags),
    createdAt,
    updatedAt,
  ]);

  /// Create a copy of MarketplaceItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MarketplaceItemImplCopyWith<_$MarketplaceItemImpl> get copyWith =>
      __$$MarketplaceItemImplCopyWithImpl<_$MarketplaceItemImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$MarketplaceItemImplToJson(this);
  }
}

abstract class _MarketplaceItem implements MarketplaceItem {
  const factory _MarketplaceItem({
    required final String id,
    required final String name,
    required final double price,
    required final ProductCategory category,
    required final List<String> images,
    required final String sellerId,
    required final Seller seller,
    final String? description,
    final bool isVerified,
    final bool isExclusive,
    final bool isFeatured,
    final ProductCondition condition,
    final int availableQuantity,
    final int soldQuantity,
    final double rating,
    final int reviewCount,
    final String? brand,
    final String? model,
    final String? year,
    final String? location,
    final Map<String, dynamic>? specifications,
    final List<String>? tags,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$MarketplaceItemImpl;

  factory _MarketplaceItem.fromJson(Map<String, dynamic> json) =
      _$MarketplaceItemImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  double get price;
  @override
  ProductCategory get category;
  @override
  List<String> get images;
  @override
  String get sellerId;
  @override
  Seller get seller;
  @override
  String? get description;
  @override
  bool get isVerified;
  @override
  bool get isExclusive;
  @override
  bool get isFeatured;
  @override
  ProductCondition get condition;
  @override
  int get availableQuantity;
  @override
  int get soldQuantity;
  @override
  double get rating;
  @override
  int get reviewCount;
  @override
  String? get brand;
  @override
  String? get model;
  @override
  String? get year;
  @override
  String? get location;
  @override
  Map<String, dynamic>? get specifications;
  @override
  List<String>? get tags;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of MarketplaceItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MarketplaceItemImplCopyWith<_$MarketplaceItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CartItem _$CartItemFromJson(Map<String, dynamic> json) {
  return _CartItem.fromJson(json);
}

/// @nodoc
mixin _$CartItem {
  String get id => throw _privateConstructorUsedError;
  String get itemId => throw _privateConstructorUsedError;
  MarketplaceItem get item => throw _privateConstructorUsedError;
  int get quantity => throw _privateConstructorUsedError;
  DateTime get addedAt => throw _privateConstructorUsedError;

  /// Serializes this CartItem to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CartItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CartItemCopyWith<CartItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CartItemCopyWith<$Res> {
  factory $CartItemCopyWith(CartItem value, $Res Function(CartItem) then) =
      _$CartItemCopyWithImpl<$Res, CartItem>;
  @useResult
  $Res call({
    String id,
    String itemId,
    MarketplaceItem item,
    int quantity,
    DateTime addedAt,
  });

  $MarketplaceItemCopyWith<$Res> get item;
}

/// @nodoc
class _$CartItemCopyWithImpl<$Res, $Val extends CartItem>
    implements $CartItemCopyWith<$Res> {
  _$CartItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CartItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? itemId = null,
    Object? item = null,
    Object? quantity = null,
    Object? addedAt = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            itemId: null == itemId
                ? _value.itemId
                : itemId // ignore: cast_nullable_to_non_nullable
                      as String,
            item: null == item
                ? _value.item
                : item // ignore: cast_nullable_to_non_nullable
                      as MarketplaceItem,
            quantity: null == quantity
                ? _value.quantity
                : quantity // ignore: cast_nullable_to_non_nullable
                      as int,
            addedAt: null == addedAt
                ? _value.addedAt
                : addedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
          )
          as $Val,
    );
  }

  /// Create a copy of CartItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MarketplaceItemCopyWith<$Res> get item {
    return $MarketplaceItemCopyWith<$Res>(_value.item, (value) {
      return _then(_value.copyWith(item: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CartItemImplCopyWith<$Res>
    implements $CartItemCopyWith<$Res> {
  factory _$$CartItemImplCopyWith(
    _$CartItemImpl value,
    $Res Function(_$CartItemImpl) then,
  ) = __$$CartItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String itemId,
    MarketplaceItem item,
    int quantity,
    DateTime addedAt,
  });

  @override
  $MarketplaceItemCopyWith<$Res> get item;
}

/// @nodoc
class __$$CartItemImplCopyWithImpl<$Res>
    extends _$CartItemCopyWithImpl<$Res, _$CartItemImpl>
    implements _$$CartItemImplCopyWith<$Res> {
  __$$CartItemImplCopyWithImpl(
    _$CartItemImpl _value,
    $Res Function(_$CartItemImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of CartItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? itemId = null,
    Object? item = null,
    Object? quantity = null,
    Object? addedAt = null,
  }) {
    return _then(
      _$CartItemImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        itemId: null == itemId
            ? _value.itemId
            : itemId // ignore: cast_nullable_to_non_nullable
                  as String,
        item: null == item
            ? _value.item
            : item // ignore: cast_nullable_to_non_nullable
                  as MarketplaceItem,
        quantity: null == quantity
            ? _value.quantity
            : quantity // ignore: cast_nullable_to_non_nullable
                  as int,
        addedAt: null == addedAt
            ? _value.addedAt
            : addedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CartItemImpl implements _CartItem {
  const _$CartItemImpl({
    required this.id,
    required this.itemId,
    required this.item,
    required this.quantity,
    required this.addedAt,
  });

  factory _$CartItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$CartItemImplFromJson(json);

  @override
  final String id;
  @override
  final String itemId;
  @override
  final MarketplaceItem item;
  @override
  final int quantity;
  @override
  final DateTime addedAt;

  @override
  String toString() {
    return 'CartItem(id: $id, itemId: $itemId, item: $item, quantity: $quantity, addedAt: $addedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CartItemImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.itemId, itemId) || other.itemId == itemId) &&
            (identical(other.item, item) || other.item == item) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.addedAt, addedAt) || other.addedAt == addedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, itemId, item, quantity, addedAt);

  /// Create a copy of CartItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CartItemImplCopyWith<_$CartItemImpl> get copyWith =>
      __$$CartItemImplCopyWithImpl<_$CartItemImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CartItemImplToJson(this);
  }
}

abstract class _CartItem implements CartItem {
  const factory _CartItem({
    required final String id,
    required final String itemId,
    required final MarketplaceItem item,
    required final int quantity,
    required final DateTime addedAt,
  }) = _$CartItemImpl;

  factory _CartItem.fromJson(Map<String, dynamic> json) =
      _$CartItemImpl.fromJson;

  @override
  String get id;
  @override
  String get itemId;
  @override
  MarketplaceItem get item;
  @override
  int get quantity;
  @override
  DateTime get addedAt;

  /// Create a copy of CartItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CartItemImplCopyWith<_$CartItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Cart _$CartFromJson(Map<String, dynamic> json) {
  return _Cart.fromJson(json);
}

/// @nodoc
mixin _$Cart {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  List<CartItem> get items => throw _privateConstructorUsedError;
  double get subtotal => throw _privateConstructorUsedError;
  double get tax => throw _privateConstructorUsedError;
  double get shipping => throw _privateConstructorUsedError;
  double get total => throw _privateConstructorUsedError;
  DateTime? get lastUpdated => throw _privateConstructorUsedError;

  /// Serializes this Cart to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Cart
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CartCopyWith<Cart> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CartCopyWith<$Res> {
  factory $CartCopyWith(Cart value, $Res Function(Cart) then) =
      _$CartCopyWithImpl<$Res, Cart>;
  @useResult
  $Res call({
    String id,
    String userId,
    List<CartItem> items,
    double subtotal,
    double tax,
    double shipping,
    double total,
    DateTime? lastUpdated,
  });
}

/// @nodoc
class _$CartCopyWithImpl<$Res, $Val extends Cart>
    implements $CartCopyWith<$Res> {
  _$CartCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Cart
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? items = null,
    Object? subtotal = null,
    Object? tax = null,
    Object? shipping = null,
    Object? total = null,
    Object? lastUpdated = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            items: null == items
                ? _value.items
                : items // ignore: cast_nullable_to_non_nullable
                      as List<CartItem>,
            subtotal: null == subtotal
                ? _value.subtotal
                : subtotal // ignore: cast_nullable_to_non_nullable
                      as double,
            tax: null == tax
                ? _value.tax
                : tax // ignore: cast_nullable_to_non_nullable
                      as double,
            shipping: null == shipping
                ? _value.shipping
                : shipping // ignore: cast_nullable_to_non_nullable
                      as double,
            total: null == total
                ? _value.total
                : total // ignore: cast_nullable_to_non_nullable
                      as double,
            lastUpdated: freezed == lastUpdated
                ? _value.lastUpdated
                : lastUpdated // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$CartImplCopyWith<$Res> implements $CartCopyWith<$Res> {
  factory _$$CartImplCopyWith(
    _$CartImpl value,
    $Res Function(_$CartImpl) then,
  ) = __$$CartImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    List<CartItem> items,
    double subtotal,
    double tax,
    double shipping,
    double total,
    DateTime? lastUpdated,
  });
}

/// @nodoc
class __$$CartImplCopyWithImpl<$Res>
    extends _$CartCopyWithImpl<$Res, _$CartImpl>
    implements _$$CartImplCopyWith<$Res> {
  __$$CartImplCopyWithImpl(_$CartImpl _value, $Res Function(_$CartImpl) _then)
    : super(_value, _then);

  /// Create a copy of Cart
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? items = null,
    Object? subtotal = null,
    Object? tax = null,
    Object? shipping = null,
    Object? total = null,
    Object? lastUpdated = freezed,
  }) {
    return _then(
      _$CartImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        items: null == items
            ? _value._items
            : items // ignore: cast_nullable_to_non_nullable
                  as List<CartItem>,
        subtotal: null == subtotal
            ? _value.subtotal
            : subtotal // ignore: cast_nullable_to_non_nullable
                  as double,
        tax: null == tax
            ? _value.tax
            : tax // ignore: cast_nullable_to_non_nullable
                  as double,
        shipping: null == shipping
            ? _value.shipping
            : shipping // ignore: cast_nullable_to_non_nullable
                  as double,
        total: null == total
            ? _value.total
            : total // ignore: cast_nullable_to_non_nullable
                  as double,
        lastUpdated: freezed == lastUpdated
            ? _value.lastUpdated
            : lastUpdated // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CartImpl implements _Cart {
  const _$CartImpl({
    required this.id,
    required this.userId,
    final List<CartItem> items = const [],
    this.subtotal = 0.0,
    this.tax = 0.0,
    this.shipping = 0.0,
    this.total = 0.0,
    this.lastUpdated,
  }) : _items = items;

  factory _$CartImpl.fromJson(Map<String, dynamic> json) =>
      _$$CartImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  final List<CartItem> _items;
  @override
  @JsonKey()
  List<CartItem> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  @override
  @JsonKey()
  final double subtotal;
  @override
  @JsonKey()
  final double tax;
  @override
  @JsonKey()
  final double shipping;
  @override
  @JsonKey()
  final double total;
  @override
  final DateTime? lastUpdated;

  @override
  String toString() {
    return 'Cart(id: $id, userId: $userId, items: $items, subtotal: $subtotal, tax: $tax, shipping: $shipping, total: $total, lastUpdated: $lastUpdated)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CartImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            (identical(other.subtotal, subtotal) ||
                other.subtotal == subtotal) &&
            (identical(other.tax, tax) || other.tax == tax) &&
            (identical(other.shipping, shipping) ||
                other.shipping == shipping) &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    userId,
    const DeepCollectionEquality().hash(_items),
    subtotal,
    tax,
    shipping,
    total,
    lastUpdated,
  );

  /// Create a copy of Cart
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CartImplCopyWith<_$CartImpl> get copyWith =>
      __$$CartImplCopyWithImpl<_$CartImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CartImplToJson(this);
  }
}

abstract class _Cart implements Cart {
  const factory _Cart({
    required final String id,
    required final String userId,
    final List<CartItem> items,
    final double subtotal,
    final double tax,
    final double shipping,
    final double total,
    final DateTime? lastUpdated,
  }) = _$CartImpl;

  factory _Cart.fromJson(Map<String, dynamic> json) = _$CartImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  List<CartItem> get items;
  @override
  double get subtotal;
  @override
  double get tax;
  @override
  double get shipping;
  @override
  double get total;
  @override
  DateTime? get lastUpdated;

  /// Create a copy of Cart
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CartImplCopyWith<_$CartImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Order _$OrderFromJson(Map<String, dynamic> json) {
  return _Order.fromJson(json);
}

/// @nodoc
mixin _$Order {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get sellerId => throw _privateConstructorUsedError;
  List<CartItem> get items => throw _privateConstructorUsedError;
  double get subtotal => throw _privateConstructorUsedError;
  double get tax => throw _privateConstructorUsedError;
  double get shipping => throw _privateConstructorUsedError;
  double get total => throw _privateConstructorUsedError;
  OrderStatus get status => throw _privateConstructorUsedError;
  PaymentStatus get paymentStatus => throw _privateConstructorUsedError;
  DateTime get orderDate => throw _privateConstructorUsedError;
  DateTime? get shippedDate => throw _privateConstructorUsedError;
  DateTime? get deliveredDate => throw _privateConstructorUsedError;
  String? get trackingNumber => throw _privateConstructorUsedError;
  String? get shippingAddress => throw _privateConstructorUsedError;
  String? get billingAddress => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;

  /// Serializes this Order to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Order
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrderCopyWith<Order> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderCopyWith<$Res> {
  factory $OrderCopyWith(Order value, $Res Function(Order) then) =
      _$OrderCopyWithImpl<$Res, Order>;
  @useResult
  $Res call({
    String id,
    String userId,
    String sellerId,
    List<CartItem> items,
    double subtotal,
    double tax,
    double shipping,
    double total,
    OrderStatus status,
    PaymentStatus paymentStatus,
    DateTime orderDate,
    DateTime? shippedDate,
    DateTime? deliveredDate,
    String? trackingNumber,
    String? shippingAddress,
    String? billingAddress,
    String? notes,
  });
}

/// @nodoc
class _$OrderCopyWithImpl<$Res, $Val extends Order>
    implements $OrderCopyWith<$Res> {
  _$OrderCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Order
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? sellerId = null,
    Object? items = null,
    Object? subtotal = null,
    Object? tax = null,
    Object? shipping = null,
    Object? total = null,
    Object? status = null,
    Object? paymentStatus = null,
    Object? orderDate = null,
    Object? shippedDate = freezed,
    Object? deliveredDate = freezed,
    Object? trackingNumber = freezed,
    Object? shippingAddress = freezed,
    Object? billingAddress = freezed,
    Object? notes = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            sellerId: null == sellerId
                ? _value.sellerId
                : sellerId // ignore: cast_nullable_to_non_nullable
                      as String,
            items: null == items
                ? _value.items
                : items // ignore: cast_nullable_to_non_nullable
                      as List<CartItem>,
            subtotal: null == subtotal
                ? _value.subtotal
                : subtotal // ignore: cast_nullable_to_non_nullable
                      as double,
            tax: null == tax
                ? _value.tax
                : tax // ignore: cast_nullable_to_non_nullable
                      as double,
            shipping: null == shipping
                ? _value.shipping
                : shipping // ignore: cast_nullable_to_non_nullable
                      as double,
            total: null == total
                ? _value.total
                : total // ignore: cast_nullable_to_non_nullable
                      as double,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as OrderStatus,
            paymentStatus: null == paymentStatus
                ? _value.paymentStatus
                : paymentStatus // ignore: cast_nullable_to_non_nullable
                      as PaymentStatus,
            orderDate: null == orderDate
                ? _value.orderDate
                : orderDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            shippedDate: freezed == shippedDate
                ? _value.shippedDate
                : shippedDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            deliveredDate: freezed == deliveredDate
                ? _value.deliveredDate
                : deliveredDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            trackingNumber: freezed == trackingNumber
                ? _value.trackingNumber
                : trackingNumber // ignore: cast_nullable_to_non_nullable
                      as String?,
            shippingAddress: freezed == shippingAddress
                ? _value.shippingAddress
                : shippingAddress // ignore: cast_nullable_to_non_nullable
                      as String?,
            billingAddress: freezed == billingAddress
                ? _value.billingAddress
                : billingAddress // ignore: cast_nullable_to_non_nullable
                      as String?,
            notes: freezed == notes
                ? _value.notes
                : notes // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$OrderImplCopyWith<$Res> implements $OrderCopyWith<$Res> {
  factory _$$OrderImplCopyWith(
    _$OrderImpl value,
    $Res Function(_$OrderImpl) then,
  ) = __$$OrderImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    String sellerId,
    List<CartItem> items,
    double subtotal,
    double tax,
    double shipping,
    double total,
    OrderStatus status,
    PaymentStatus paymentStatus,
    DateTime orderDate,
    DateTime? shippedDate,
    DateTime? deliveredDate,
    String? trackingNumber,
    String? shippingAddress,
    String? billingAddress,
    String? notes,
  });
}

/// @nodoc
class __$$OrderImplCopyWithImpl<$Res>
    extends _$OrderCopyWithImpl<$Res, _$OrderImpl>
    implements _$$OrderImplCopyWith<$Res> {
  __$$OrderImplCopyWithImpl(
    _$OrderImpl _value,
    $Res Function(_$OrderImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Order
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? sellerId = null,
    Object? items = null,
    Object? subtotal = null,
    Object? tax = null,
    Object? shipping = null,
    Object? total = null,
    Object? status = null,
    Object? paymentStatus = null,
    Object? orderDate = null,
    Object? shippedDate = freezed,
    Object? deliveredDate = freezed,
    Object? trackingNumber = freezed,
    Object? shippingAddress = freezed,
    Object? billingAddress = freezed,
    Object? notes = freezed,
  }) {
    return _then(
      _$OrderImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        sellerId: null == sellerId
            ? _value.sellerId
            : sellerId // ignore: cast_nullable_to_non_nullable
                  as String,
        items: null == items
            ? _value._items
            : items // ignore: cast_nullable_to_non_nullable
                  as List<CartItem>,
        subtotal: null == subtotal
            ? _value.subtotal
            : subtotal // ignore: cast_nullable_to_non_nullable
                  as double,
        tax: null == tax
            ? _value.tax
            : tax // ignore: cast_nullable_to_non_nullable
                  as double,
        shipping: null == shipping
            ? _value.shipping
            : shipping // ignore: cast_nullable_to_non_nullable
                  as double,
        total: null == total
            ? _value.total
            : total // ignore: cast_nullable_to_non_nullable
                  as double,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as OrderStatus,
        paymentStatus: null == paymentStatus
            ? _value.paymentStatus
            : paymentStatus // ignore: cast_nullable_to_non_nullable
                  as PaymentStatus,
        orderDate: null == orderDate
            ? _value.orderDate
            : orderDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        shippedDate: freezed == shippedDate
            ? _value.shippedDate
            : shippedDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        deliveredDate: freezed == deliveredDate
            ? _value.deliveredDate
            : deliveredDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        trackingNumber: freezed == trackingNumber
            ? _value.trackingNumber
            : trackingNumber // ignore: cast_nullable_to_non_nullable
                  as String?,
        shippingAddress: freezed == shippingAddress
            ? _value.shippingAddress
            : shippingAddress // ignore: cast_nullable_to_non_nullable
                  as String?,
        billingAddress: freezed == billingAddress
            ? _value.billingAddress
            : billingAddress // ignore: cast_nullable_to_non_nullable
                  as String?,
        notes: freezed == notes
            ? _value.notes
            : notes // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$OrderImpl implements _Order {
  const _$OrderImpl({
    required this.id,
    required this.userId,
    required this.sellerId,
    required final List<CartItem> items,
    required this.subtotal,
    required this.tax,
    required this.shipping,
    required this.total,
    required this.status,
    required this.paymentStatus,
    required this.orderDate,
    this.shippedDate,
    this.deliveredDate,
    this.trackingNumber,
    this.shippingAddress,
    this.billingAddress,
    this.notes,
  }) : _items = items;

  factory _$OrderImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String sellerId;
  final List<CartItem> _items;
  @override
  List<CartItem> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  @override
  final double subtotal;
  @override
  final double tax;
  @override
  final double shipping;
  @override
  final double total;
  @override
  final OrderStatus status;
  @override
  final PaymentStatus paymentStatus;
  @override
  final DateTime orderDate;
  @override
  final DateTime? shippedDate;
  @override
  final DateTime? deliveredDate;
  @override
  final String? trackingNumber;
  @override
  final String? shippingAddress;
  @override
  final String? billingAddress;
  @override
  final String? notes;

  @override
  String toString() {
    return 'Order(id: $id, userId: $userId, sellerId: $sellerId, items: $items, subtotal: $subtotal, tax: $tax, shipping: $shipping, total: $total, status: $status, paymentStatus: $paymentStatus, orderDate: $orderDate, shippedDate: $shippedDate, deliveredDate: $deliveredDate, trackingNumber: $trackingNumber, shippingAddress: $shippingAddress, billingAddress: $billingAddress, notes: $notes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.sellerId, sellerId) ||
                other.sellerId == sellerId) &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            (identical(other.subtotal, subtotal) ||
                other.subtotal == subtotal) &&
            (identical(other.tax, tax) || other.tax == tax) &&
            (identical(other.shipping, shipping) ||
                other.shipping == shipping) &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.paymentStatus, paymentStatus) ||
                other.paymentStatus == paymentStatus) &&
            (identical(other.orderDate, orderDate) ||
                other.orderDate == orderDate) &&
            (identical(other.shippedDate, shippedDate) ||
                other.shippedDate == shippedDate) &&
            (identical(other.deliveredDate, deliveredDate) ||
                other.deliveredDate == deliveredDate) &&
            (identical(other.trackingNumber, trackingNumber) ||
                other.trackingNumber == trackingNumber) &&
            (identical(other.shippingAddress, shippingAddress) ||
                other.shippingAddress == shippingAddress) &&
            (identical(other.billingAddress, billingAddress) ||
                other.billingAddress == billingAddress) &&
            (identical(other.notes, notes) || other.notes == notes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    userId,
    sellerId,
    const DeepCollectionEquality().hash(_items),
    subtotal,
    tax,
    shipping,
    total,
    status,
    paymentStatus,
    orderDate,
    shippedDate,
    deliveredDate,
    trackingNumber,
    shippingAddress,
    billingAddress,
    notes,
  );

  /// Create a copy of Order
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderImplCopyWith<_$OrderImpl> get copyWith =>
      __$$OrderImplCopyWithImpl<_$OrderImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderImplToJson(this);
  }
}

abstract class _Order implements Order {
  const factory _Order({
    required final String id,
    required final String userId,
    required final String sellerId,
    required final List<CartItem> items,
    required final double subtotal,
    required final double tax,
    required final double shipping,
    required final double total,
    required final OrderStatus status,
    required final PaymentStatus paymentStatus,
    required final DateTime orderDate,
    final DateTime? shippedDate,
    final DateTime? deliveredDate,
    final String? trackingNumber,
    final String? shippingAddress,
    final String? billingAddress,
    final String? notes,
  }) = _$OrderImpl;

  factory _Order.fromJson(Map<String, dynamic> json) = _$OrderImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get sellerId;
  @override
  List<CartItem> get items;
  @override
  double get subtotal;
  @override
  double get tax;
  @override
  double get shipping;
  @override
  double get total;
  @override
  OrderStatus get status;
  @override
  PaymentStatus get paymentStatus;
  @override
  DateTime get orderDate;
  @override
  DateTime? get shippedDate;
  @override
  DateTime? get deliveredDate;
  @override
  String? get trackingNumber;
  @override
  String? get shippingAddress;
  @override
  String? get billingAddress;
  @override
  String? get notes;

  /// Create a copy of Order
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderImplCopyWith<_$OrderImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CheckoutRequest _$CheckoutRequestFromJson(Map<String, dynamic> json) {
  return _CheckoutRequest.fromJson(json);
}

/// @nodoc
mixin _$CheckoutRequest {
  String get cartId => throw _privateConstructorUsedError;
  String get shippingAddress => throw _privateConstructorUsedError;
  String get billingAddress => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;
  String? get promoCode => throw _privateConstructorUsedError;

  /// Serializes this CheckoutRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CheckoutRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CheckoutRequestCopyWith<CheckoutRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CheckoutRequestCopyWith<$Res> {
  factory $CheckoutRequestCopyWith(
    CheckoutRequest value,
    $Res Function(CheckoutRequest) then,
  ) = _$CheckoutRequestCopyWithImpl<$Res, CheckoutRequest>;
  @useResult
  $Res call({
    String cartId,
    String shippingAddress,
    String billingAddress,
    String? notes,
    String? promoCode,
  });
}

/// @nodoc
class _$CheckoutRequestCopyWithImpl<$Res, $Val extends CheckoutRequest>
    implements $CheckoutRequestCopyWith<$Res> {
  _$CheckoutRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CheckoutRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cartId = null,
    Object? shippingAddress = null,
    Object? billingAddress = null,
    Object? notes = freezed,
    Object? promoCode = freezed,
  }) {
    return _then(
      _value.copyWith(
            cartId: null == cartId
                ? _value.cartId
                : cartId // ignore: cast_nullable_to_non_nullable
                      as String,
            shippingAddress: null == shippingAddress
                ? _value.shippingAddress
                : shippingAddress // ignore: cast_nullable_to_non_nullable
                      as String,
            billingAddress: null == billingAddress
                ? _value.billingAddress
                : billingAddress // ignore: cast_nullable_to_non_nullable
                      as String,
            notes: freezed == notes
                ? _value.notes
                : notes // ignore: cast_nullable_to_non_nullable
                      as String?,
            promoCode: freezed == promoCode
                ? _value.promoCode
                : promoCode // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$CheckoutRequestImplCopyWith<$Res>
    implements $CheckoutRequestCopyWith<$Res> {
  factory _$$CheckoutRequestImplCopyWith(
    _$CheckoutRequestImpl value,
    $Res Function(_$CheckoutRequestImpl) then,
  ) = __$$CheckoutRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String cartId,
    String shippingAddress,
    String billingAddress,
    String? notes,
    String? promoCode,
  });
}

/// @nodoc
class __$$CheckoutRequestImplCopyWithImpl<$Res>
    extends _$CheckoutRequestCopyWithImpl<$Res, _$CheckoutRequestImpl>
    implements _$$CheckoutRequestImplCopyWith<$Res> {
  __$$CheckoutRequestImplCopyWithImpl(
    _$CheckoutRequestImpl _value,
    $Res Function(_$CheckoutRequestImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of CheckoutRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cartId = null,
    Object? shippingAddress = null,
    Object? billingAddress = null,
    Object? notes = freezed,
    Object? promoCode = freezed,
  }) {
    return _then(
      _$CheckoutRequestImpl(
        cartId: null == cartId
            ? _value.cartId
            : cartId // ignore: cast_nullable_to_non_nullable
                  as String,
        shippingAddress: null == shippingAddress
            ? _value.shippingAddress
            : shippingAddress // ignore: cast_nullable_to_non_nullable
                  as String,
        billingAddress: null == billingAddress
            ? _value.billingAddress
            : billingAddress // ignore: cast_nullable_to_non_nullable
                  as String,
        notes: freezed == notes
            ? _value.notes
            : notes // ignore: cast_nullable_to_non_nullable
                  as String?,
        promoCode: freezed == promoCode
            ? _value.promoCode
            : promoCode // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CheckoutRequestImpl implements _CheckoutRequest {
  const _$CheckoutRequestImpl({
    required this.cartId,
    required this.shippingAddress,
    required this.billingAddress,
    this.notes,
    this.promoCode,
  });

  factory _$CheckoutRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$CheckoutRequestImplFromJson(json);

  @override
  final String cartId;
  @override
  final String shippingAddress;
  @override
  final String billingAddress;
  @override
  final String? notes;
  @override
  final String? promoCode;

  @override
  String toString() {
    return 'CheckoutRequest(cartId: $cartId, shippingAddress: $shippingAddress, billingAddress: $billingAddress, notes: $notes, promoCode: $promoCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CheckoutRequestImpl &&
            (identical(other.cartId, cartId) || other.cartId == cartId) &&
            (identical(other.shippingAddress, shippingAddress) ||
                other.shippingAddress == shippingAddress) &&
            (identical(other.billingAddress, billingAddress) ||
                other.billingAddress == billingAddress) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            (identical(other.promoCode, promoCode) ||
                other.promoCode == promoCode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    cartId,
    shippingAddress,
    billingAddress,
    notes,
    promoCode,
  );

  /// Create a copy of CheckoutRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CheckoutRequestImplCopyWith<_$CheckoutRequestImpl> get copyWith =>
      __$$CheckoutRequestImplCopyWithImpl<_$CheckoutRequestImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$CheckoutRequestImplToJson(this);
  }
}

abstract class _CheckoutRequest implements CheckoutRequest {
  const factory _CheckoutRequest({
    required final String cartId,
    required final String shippingAddress,
    required final String billingAddress,
    final String? notes,
    final String? promoCode,
  }) = _$CheckoutRequestImpl;

  factory _CheckoutRequest.fromJson(Map<String, dynamic> json) =
      _$CheckoutRequestImpl.fromJson;

  @override
  String get cartId;
  @override
  String get shippingAddress;
  @override
  String get billingAddress;
  @override
  String? get notes;
  @override
  String? get promoCode;

  /// Create a copy of CheckoutRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CheckoutRequestImplCopyWith<_$CheckoutRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

MarketplaceFilter _$MarketplaceFilterFromJson(Map<String, dynamic> json) {
  return _MarketplaceFilter.fromJson(json);
}

/// @nodoc
mixin _$MarketplaceFilter {
  Set<ProductCategory> get categories => throw _privateConstructorUsedError;
  double? get minPrice => throw _privateConstructorUsedError;
  double? get maxPrice => throw _privateConstructorUsedError;
  bool get verifiedOnly => throw _privateConstructorUsedError;
  bool get exclusiveOnly => throw _privateConstructorUsedError;
  bool get featuredOnly => throw _privateConstructorUsedError;
  String? get searchQuery => throw _privateConstructorUsedError;
  String? get brand => throw _privateConstructorUsedError;
  String? get sellerId => throw _privateConstructorUsedError;
  ProductCondition? get condition => throw _privateConstructorUsedError;
  double get minRating => throw _privateConstructorUsedError;
  String? get location => throw _privateConstructorUsedError;

  /// Serializes this MarketplaceFilter to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MarketplaceFilter
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MarketplaceFilterCopyWith<MarketplaceFilter> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MarketplaceFilterCopyWith<$Res> {
  factory $MarketplaceFilterCopyWith(
    MarketplaceFilter value,
    $Res Function(MarketplaceFilter) then,
  ) = _$MarketplaceFilterCopyWithImpl<$Res, MarketplaceFilter>;
  @useResult
  $Res call({
    Set<ProductCategory> categories,
    double? minPrice,
    double? maxPrice,
    bool verifiedOnly,
    bool exclusiveOnly,
    bool featuredOnly,
    String? searchQuery,
    String? brand,
    String? sellerId,
    ProductCondition? condition,
    double minRating,
    String? location,
  });
}

/// @nodoc
class _$MarketplaceFilterCopyWithImpl<$Res, $Val extends MarketplaceFilter>
    implements $MarketplaceFilterCopyWith<$Res> {
  _$MarketplaceFilterCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MarketplaceFilter
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? categories = null,
    Object? minPrice = freezed,
    Object? maxPrice = freezed,
    Object? verifiedOnly = null,
    Object? exclusiveOnly = null,
    Object? featuredOnly = null,
    Object? searchQuery = freezed,
    Object? brand = freezed,
    Object? sellerId = freezed,
    Object? condition = freezed,
    Object? minRating = null,
    Object? location = freezed,
  }) {
    return _then(
      _value.copyWith(
            categories: null == categories
                ? _value.categories
                : categories // ignore: cast_nullable_to_non_nullable
                      as Set<ProductCategory>,
            minPrice: freezed == minPrice
                ? _value.minPrice
                : minPrice // ignore: cast_nullable_to_non_nullable
                      as double?,
            maxPrice: freezed == maxPrice
                ? _value.maxPrice
                : maxPrice // ignore: cast_nullable_to_non_nullable
                      as double?,
            verifiedOnly: null == verifiedOnly
                ? _value.verifiedOnly
                : verifiedOnly // ignore: cast_nullable_to_non_nullable
                      as bool,
            exclusiveOnly: null == exclusiveOnly
                ? _value.exclusiveOnly
                : exclusiveOnly // ignore: cast_nullable_to_non_nullable
                      as bool,
            featuredOnly: null == featuredOnly
                ? _value.featuredOnly
                : featuredOnly // ignore: cast_nullable_to_non_nullable
                      as bool,
            searchQuery: freezed == searchQuery
                ? _value.searchQuery
                : searchQuery // ignore: cast_nullable_to_non_nullable
                      as String?,
            brand: freezed == brand
                ? _value.brand
                : brand // ignore: cast_nullable_to_non_nullable
                      as String?,
            sellerId: freezed == sellerId
                ? _value.sellerId
                : sellerId // ignore: cast_nullable_to_non_nullable
                      as String?,
            condition: freezed == condition
                ? _value.condition
                : condition // ignore: cast_nullable_to_non_nullable
                      as ProductCondition?,
            minRating: null == minRating
                ? _value.minRating
                : minRating // ignore: cast_nullable_to_non_nullable
                      as double,
            location: freezed == location
                ? _value.location
                : location // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$MarketplaceFilterImplCopyWith<$Res>
    implements $MarketplaceFilterCopyWith<$Res> {
  factory _$$MarketplaceFilterImplCopyWith(
    _$MarketplaceFilterImpl value,
    $Res Function(_$MarketplaceFilterImpl) then,
  ) = __$$MarketplaceFilterImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    Set<ProductCategory> categories,
    double? minPrice,
    double? maxPrice,
    bool verifiedOnly,
    bool exclusiveOnly,
    bool featuredOnly,
    String? searchQuery,
    String? brand,
    String? sellerId,
    ProductCondition? condition,
    double minRating,
    String? location,
  });
}

/// @nodoc
class __$$MarketplaceFilterImplCopyWithImpl<$Res>
    extends _$MarketplaceFilterCopyWithImpl<$Res, _$MarketplaceFilterImpl>
    implements _$$MarketplaceFilterImplCopyWith<$Res> {
  __$$MarketplaceFilterImplCopyWithImpl(
    _$MarketplaceFilterImpl _value,
    $Res Function(_$MarketplaceFilterImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MarketplaceFilter
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? categories = null,
    Object? minPrice = freezed,
    Object? maxPrice = freezed,
    Object? verifiedOnly = null,
    Object? exclusiveOnly = null,
    Object? featuredOnly = null,
    Object? searchQuery = freezed,
    Object? brand = freezed,
    Object? sellerId = freezed,
    Object? condition = freezed,
    Object? minRating = null,
    Object? location = freezed,
  }) {
    return _then(
      _$MarketplaceFilterImpl(
        categories: null == categories
            ? _value._categories
            : categories // ignore: cast_nullable_to_non_nullable
                  as Set<ProductCategory>,
        minPrice: freezed == minPrice
            ? _value.minPrice
            : minPrice // ignore: cast_nullable_to_non_nullable
                  as double?,
        maxPrice: freezed == maxPrice
            ? _value.maxPrice
            : maxPrice // ignore: cast_nullable_to_non_nullable
                  as double?,
        verifiedOnly: null == verifiedOnly
            ? _value.verifiedOnly
            : verifiedOnly // ignore: cast_nullable_to_non_nullable
                  as bool,
        exclusiveOnly: null == exclusiveOnly
            ? _value.exclusiveOnly
            : exclusiveOnly // ignore: cast_nullable_to_non_nullable
                  as bool,
        featuredOnly: null == featuredOnly
            ? _value.featuredOnly
            : featuredOnly // ignore: cast_nullable_to_non_nullable
                  as bool,
        searchQuery: freezed == searchQuery
            ? _value.searchQuery
            : searchQuery // ignore: cast_nullable_to_non_nullable
                  as String?,
        brand: freezed == brand
            ? _value.brand
            : brand // ignore: cast_nullable_to_non_nullable
                  as String?,
        sellerId: freezed == sellerId
            ? _value.sellerId
            : sellerId // ignore: cast_nullable_to_non_nullable
                  as String?,
        condition: freezed == condition
            ? _value.condition
            : condition // ignore: cast_nullable_to_non_nullable
                  as ProductCondition?,
        minRating: null == minRating
            ? _value.minRating
            : minRating // ignore: cast_nullable_to_non_nullable
                  as double,
        location: freezed == location
            ? _value.location
            : location // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$MarketplaceFilterImpl implements _MarketplaceFilter {
  const _$MarketplaceFilterImpl({
    final Set<ProductCategory> categories = const {},
    this.minPrice,
    this.maxPrice,
    this.verifiedOnly = false,
    this.exclusiveOnly = false,
    this.featuredOnly = false,
    this.searchQuery,
    this.brand,
    this.sellerId,
    this.condition,
    this.minRating = 0.0,
    this.location,
  }) : _categories = categories;

  factory _$MarketplaceFilterImpl.fromJson(Map<String, dynamic> json) =>
      _$$MarketplaceFilterImplFromJson(json);

  final Set<ProductCategory> _categories;
  @override
  @JsonKey()
  Set<ProductCategory> get categories {
    if (_categories is EqualUnmodifiableSetView) return _categories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_categories);
  }

  @override
  final double? minPrice;
  @override
  final double? maxPrice;
  @override
  @JsonKey()
  final bool verifiedOnly;
  @override
  @JsonKey()
  final bool exclusiveOnly;
  @override
  @JsonKey()
  final bool featuredOnly;
  @override
  final String? searchQuery;
  @override
  final String? brand;
  @override
  final String? sellerId;
  @override
  final ProductCondition? condition;
  @override
  @JsonKey()
  final double minRating;
  @override
  final String? location;

  @override
  String toString() {
    return 'MarketplaceFilter(categories: $categories, minPrice: $minPrice, maxPrice: $maxPrice, verifiedOnly: $verifiedOnly, exclusiveOnly: $exclusiveOnly, featuredOnly: $featuredOnly, searchQuery: $searchQuery, brand: $brand, sellerId: $sellerId, condition: $condition, minRating: $minRating, location: $location)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MarketplaceFilterImpl &&
            const DeepCollectionEquality().equals(
              other._categories,
              _categories,
            ) &&
            (identical(other.minPrice, minPrice) ||
                other.minPrice == minPrice) &&
            (identical(other.maxPrice, maxPrice) ||
                other.maxPrice == maxPrice) &&
            (identical(other.verifiedOnly, verifiedOnly) ||
                other.verifiedOnly == verifiedOnly) &&
            (identical(other.exclusiveOnly, exclusiveOnly) ||
                other.exclusiveOnly == exclusiveOnly) &&
            (identical(other.featuredOnly, featuredOnly) ||
                other.featuredOnly == featuredOnly) &&
            (identical(other.searchQuery, searchQuery) ||
                other.searchQuery == searchQuery) &&
            (identical(other.brand, brand) || other.brand == brand) &&
            (identical(other.sellerId, sellerId) ||
                other.sellerId == sellerId) &&
            (identical(other.condition, condition) ||
                other.condition == condition) &&
            (identical(other.minRating, minRating) ||
                other.minRating == minRating) &&
            (identical(other.location, location) ||
                other.location == location));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_categories),
    minPrice,
    maxPrice,
    verifiedOnly,
    exclusiveOnly,
    featuredOnly,
    searchQuery,
    brand,
    sellerId,
    condition,
    minRating,
    location,
  );

  /// Create a copy of MarketplaceFilter
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MarketplaceFilterImplCopyWith<_$MarketplaceFilterImpl> get copyWith =>
      __$$MarketplaceFilterImplCopyWithImpl<_$MarketplaceFilterImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$MarketplaceFilterImplToJson(this);
  }
}

abstract class _MarketplaceFilter implements MarketplaceFilter {
  const factory _MarketplaceFilter({
    final Set<ProductCategory> categories,
    final double? minPrice,
    final double? maxPrice,
    final bool verifiedOnly,
    final bool exclusiveOnly,
    final bool featuredOnly,
    final String? searchQuery,
    final String? brand,
    final String? sellerId,
    final ProductCondition? condition,
    final double minRating,
    final String? location,
  }) = _$MarketplaceFilterImpl;

  factory _MarketplaceFilter.fromJson(Map<String, dynamic> json) =
      _$MarketplaceFilterImpl.fromJson;

  @override
  Set<ProductCategory> get categories;
  @override
  double? get minPrice;
  @override
  double? get maxPrice;
  @override
  bool get verifiedOnly;
  @override
  bool get exclusiveOnly;
  @override
  bool get featuredOnly;
  @override
  String? get searchQuery;
  @override
  String? get brand;
  @override
  String? get sellerId;
  @override
  ProductCondition? get condition;
  @override
  double get minRating;
  @override
  String? get location;

  /// Create a copy of MarketplaceFilter
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MarketplaceFilterImplCopyWith<_$MarketplaceFilterImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProductReview _$ProductReviewFromJson(Map<String, dynamic> json) {
  return _ProductReview.fromJson(json);
}

/// @nodoc
mixin _$ProductReview {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get userName => throw _privateConstructorUsedError;
  String get userAvatarUrl => throw _privateConstructorUsedError;
  String get productId => throw _privateConstructorUsedError;
  double get rating => throw _privateConstructorUsedError;
  String get comment => throw _privateConstructorUsedError;
  DateTime get reviewDate => throw _privateConstructorUsedError;
  List<String>? get images => throw _privateConstructorUsedError;

  /// Serializes this ProductReview to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductReview
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductReviewCopyWith<ProductReview> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductReviewCopyWith<$Res> {
  factory $ProductReviewCopyWith(
    ProductReview value,
    $Res Function(ProductReview) then,
  ) = _$ProductReviewCopyWithImpl<$Res, ProductReview>;
  @useResult
  $Res call({
    String id,
    String userId,
    String userName,
    String userAvatarUrl,
    String productId,
    double rating,
    String comment,
    DateTime reviewDate,
    List<String>? images,
  });
}

/// @nodoc
class _$ProductReviewCopyWithImpl<$Res, $Val extends ProductReview>
    implements $ProductReviewCopyWith<$Res> {
  _$ProductReviewCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductReview
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? userName = null,
    Object? userAvatarUrl = null,
    Object? productId = null,
    Object? rating = null,
    Object? comment = null,
    Object? reviewDate = null,
    Object? images = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            userName: null == userName
                ? _value.userName
                : userName // ignore: cast_nullable_to_non_nullable
                      as String,
            userAvatarUrl: null == userAvatarUrl
                ? _value.userAvatarUrl
                : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            productId: null == productId
                ? _value.productId
                : productId // ignore: cast_nullable_to_non_nullable
                      as String,
            rating: null == rating
                ? _value.rating
                : rating // ignore: cast_nullable_to_non_nullable
                      as double,
            comment: null == comment
                ? _value.comment
                : comment // ignore: cast_nullable_to_non_nullable
                      as String,
            reviewDate: null == reviewDate
                ? _value.reviewDate
                : reviewDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            images: freezed == images
                ? _value.images
                : images // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ProductReviewImplCopyWith<$Res>
    implements $ProductReviewCopyWith<$Res> {
  factory _$$ProductReviewImplCopyWith(
    _$ProductReviewImpl value,
    $Res Function(_$ProductReviewImpl) then,
  ) = __$$ProductReviewImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    String userName,
    String userAvatarUrl,
    String productId,
    double rating,
    String comment,
    DateTime reviewDate,
    List<String>? images,
  });
}

/// @nodoc
class __$$ProductReviewImplCopyWithImpl<$Res>
    extends _$ProductReviewCopyWithImpl<$Res, _$ProductReviewImpl>
    implements _$$ProductReviewImplCopyWith<$Res> {
  __$$ProductReviewImplCopyWithImpl(
    _$ProductReviewImpl _value,
    $Res Function(_$ProductReviewImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ProductReview
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? userName = null,
    Object? userAvatarUrl = null,
    Object? productId = null,
    Object? rating = null,
    Object? comment = null,
    Object? reviewDate = null,
    Object? images = freezed,
  }) {
    return _then(
      _$ProductReviewImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        userName: null == userName
            ? _value.userName
            : userName // ignore: cast_nullable_to_non_nullable
                  as String,
        userAvatarUrl: null == userAvatarUrl
            ? _value.userAvatarUrl
            : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        productId: null == productId
            ? _value.productId
            : productId // ignore: cast_nullable_to_non_nullable
                  as String,
        rating: null == rating
            ? _value.rating
            : rating // ignore: cast_nullable_to_non_nullable
                  as double,
        comment: null == comment
            ? _value.comment
            : comment // ignore: cast_nullable_to_non_nullable
                  as String,
        reviewDate: null == reviewDate
            ? _value.reviewDate
            : reviewDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        images: freezed == images
            ? _value._images
            : images // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductReviewImpl implements _ProductReview {
  const _$ProductReviewImpl({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userAvatarUrl,
    required this.productId,
    required this.rating,
    required this.comment,
    required this.reviewDate,
    final List<String>? images,
  }) : _images = images;

  factory _$ProductReviewImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductReviewImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String userName;
  @override
  final String userAvatarUrl;
  @override
  final String productId;
  @override
  final double rating;
  @override
  final String comment;
  @override
  final DateTime reviewDate;
  final List<String>? _images;
  @override
  List<String>? get images {
    final value = _images;
    if (value == null) return null;
    if (_images is EqualUnmodifiableListView) return _images;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ProductReview(id: $id, userId: $userId, userName: $userName, userAvatarUrl: $userAvatarUrl, productId: $productId, rating: $rating, comment: $comment, reviewDate: $reviewDate, images: $images)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductReviewImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userName, userName) ||
                other.userName == userName) &&
            (identical(other.userAvatarUrl, userAvatarUrl) ||
                other.userAvatarUrl == userAvatarUrl) &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            (identical(other.comment, comment) || other.comment == comment) &&
            (identical(other.reviewDate, reviewDate) ||
                other.reviewDate == reviewDate) &&
            const DeepCollectionEquality().equals(other._images, _images));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    userId,
    userName,
    userAvatarUrl,
    productId,
    rating,
    comment,
    reviewDate,
    const DeepCollectionEquality().hash(_images),
  );

  /// Create a copy of ProductReview
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductReviewImplCopyWith<_$ProductReviewImpl> get copyWith =>
      __$$ProductReviewImplCopyWithImpl<_$ProductReviewImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductReviewImplToJson(this);
  }
}

abstract class _ProductReview implements ProductReview {
  const factory _ProductReview({
    required final String id,
    required final String userId,
    required final String userName,
    required final String userAvatarUrl,
    required final String productId,
    required final double rating,
    required final String comment,
    required final DateTime reviewDate,
    final List<String>? images,
  }) = _$ProductReviewImpl;

  factory _ProductReview.fromJson(Map<String, dynamic> json) =
      _$ProductReviewImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get userName;
  @override
  String get userAvatarUrl;
  @override
  String get productId;
  @override
  double get rating;
  @override
  String get comment;
  @override
  DateTime get reviewDate;
  @override
  List<String>? get images;

  /// Create a copy of ProductReview
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductReviewImplCopyWith<_$ProductReviewImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
