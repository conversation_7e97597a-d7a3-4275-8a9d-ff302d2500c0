// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'verification_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

VerificationRequest _$VerificationRequestFromJson(Map<String, dynamic> json) {
  return _VerificationRequest.fromJson(json);
}

/// @nodoc
mixin _$VerificationRequest {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get username => throw _privateConstructorUsedError;
  String get userEmail => throw _privateConstructorUsedError;
  VerificationTier get requestedTier => throw _privateConstructorUsedError;
  VerificationStatus get status => throw _privateConstructorUsedError;
  DateTime get submittedAt => throw _privateConstructorUsedError;
  DateTime? get reviewedAt => throw _privateConstructorUsedError;
  String? get reviewedBy =>
      throw _privateConstructorUsedError; // Admin ID who reviewed
  String? get rejectionReason =>
      throw _privateConstructorUsedError; // Supporting documents and information
  String get fullName => throw _privateConstructorUsedError;
  String get profession => throw _privateConstructorUsedError;
  String? get company => throw _privateConstructorUsedError;
  String? get website => throw _privateConstructorUsedError;
  String? get linkedinUrl => throw _privateConstructorUsedError;
  String? get twitterUrl => throw _privateConstructorUsedError;
  String? get instagramUrl => throw _privateConstructorUsedError;
  List<String>? get supportingDocuments =>
      throw _privateConstructorUsedError; // URLs to uploaded documents
  String? get additionalInfo =>
      throw _privateConstructorUsedError; // For billionaire verification
  String? get netWorthEvidence => throw _privateConstructorUsedError;
  String? get businessOwnership => throw _privateConstructorUsedError;
  String? get publicRecognition =>
      throw _privateConstructorUsedError; // For celebrity verification
  String? get publicProfile => throw _privateConstructorUsedError;
  String? get mediaPresence => throw _privateConstructorUsedError;
  String? get followerCount =>
      throw _privateConstructorUsedError; // Admin notes
  String? get adminNotes => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this VerificationRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VerificationRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VerificationRequestCopyWith<VerificationRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VerificationRequestCopyWith<$Res> {
  factory $VerificationRequestCopyWith(
    VerificationRequest value,
    $Res Function(VerificationRequest) then,
  ) = _$VerificationRequestCopyWithImpl<$Res, VerificationRequest>;
  @useResult
  $Res call({
    String id,
    String userId,
    String username,
    String userEmail,
    VerificationTier requestedTier,
    VerificationStatus status,
    DateTime submittedAt,
    DateTime? reviewedAt,
    String? reviewedBy,
    String? rejectionReason,
    String fullName,
    String profession,
    String? company,
    String? website,
    String? linkedinUrl,
    String? twitterUrl,
    String? instagramUrl,
    List<String>? supportingDocuments,
    String? additionalInfo,
    String? netWorthEvidence,
    String? businessOwnership,
    String? publicRecognition,
    String? publicProfile,
    String? mediaPresence,
    String? followerCount,
    String? adminNotes,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class _$VerificationRequestCopyWithImpl<$Res, $Val extends VerificationRequest>
    implements $VerificationRequestCopyWith<$Res> {
  _$VerificationRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VerificationRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? username = null,
    Object? userEmail = null,
    Object? requestedTier = null,
    Object? status = null,
    Object? submittedAt = null,
    Object? reviewedAt = freezed,
    Object? reviewedBy = freezed,
    Object? rejectionReason = freezed,
    Object? fullName = null,
    Object? profession = null,
    Object? company = freezed,
    Object? website = freezed,
    Object? linkedinUrl = freezed,
    Object? twitterUrl = freezed,
    Object? instagramUrl = freezed,
    Object? supportingDocuments = freezed,
    Object? additionalInfo = freezed,
    Object? netWorthEvidence = freezed,
    Object? businessOwnership = freezed,
    Object? publicRecognition = freezed,
    Object? publicProfile = freezed,
    Object? mediaPresence = freezed,
    Object? followerCount = freezed,
    Object? adminNotes = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            username: null == username
                ? _value.username
                : username // ignore: cast_nullable_to_non_nullable
                      as String,
            userEmail: null == userEmail
                ? _value.userEmail
                : userEmail // ignore: cast_nullable_to_non_nullable
                      as String,
            requestedTier: null == requestedTier
                ? _value.requestedTier
                : requestedTier // ignore: cast_nullable_to_non_nullable
                      as VerificationTier,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as VerificationStatus,
            submittedAt: null == submittedAt
                ? _value.submittedAt
                : submittedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            reviewedAt: freezed == reviewedAt
                ? _value.reviewedAt
                : reviewedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            reviewedBy: freezed == reviewedBy
                ? _value.reviewedBy
                : reviewedBy // ignore: cast_nullable_to_non_nullable
                      as String?,
            rejectionReason: freezed == rejectionReason
                ? _value.rejectionReason
                : rejectionReason // ignore: cast_nullable_to_non_nullable
                      as String?,
            fullName: null == fullName
                ? _value.fullName
                : fullName // ignore: cast_nullable_to_non_nullable
                      as String,
            profession: null == profession
                ? _value.profession
                : profession // ignore: cast_nullable_to_non_nullable
                      as String,
            company: freezed == company
                ? _value.company
                : company // ignore: cast_nullable_to_non_nullable
                      as String?,
            website: freezed == website
                ? _value.website
                : website // ignore: cast_nullable_to_non_nullable
                      as String?,
            linkedinUrl: freezed == linkedinUrl
                ? _value.linkedinUrl
                : linkedinUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            twitterUrl: freezed == twitterUrl
                ? _value.twitterUrl
                : twitterUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            instagramUrl: freezed == instagramUrl
                ? _value.instagramUrl
                : instagramUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            supportingDocuments: freezed == supportingDocuments
                ? _value.supportingDocuments
                : supportingDocuments // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            additionalInfo: freezed == additionalInfo
                ? _value.additionalInfo
                : additionalInfo // ignore: cast_nullable_to_non_nullable
                      as String?,
            netWorthEvidence: freezed == netWorthEvidence
                ? _value.netWorthEvidence
                : netWorthEvidence // ignore: cast_nullable_to_non_nullable
                      as String?,
            businessOwnership: freezed == businessOwnership
                ? _value.businessOwnership
                : businessOwnership // ignore: cast_nullable_to_non_nullable
                      as String?,
            publicRecognition: freezed == publicRecognition
                ? _value.publicRecognition
                : publicRecognition // ignore: cast_nullable_to_non_nullable
                      as String?,
            publicProfile: freezed == publicProfile
                ? _value.publicProfile
                : publicProfile // ignore: cast_nullable_to_non_nullable
                      as String?,
            mediaPresence: freezed == mediaPresence
                ? _value.mediaPresence
                : mediaPresence // ignore: cast_nullable_to_non_nullable
                      as String?,
            followerCount: freezed == followerCount
                ? _value.followerCount
                : followerCount // ignore: cast_nullable_to_non_nullable
                      as String?,
            adminNotes: freezed == adminNotes
                ? _value.adminNotes
                : adminNotes // ignore: cast_nullable_to_non_nullable
                      as String?,
            metadata: freezed == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$VerificationRequestImplCopyWith<$Res>
    implements $VerificationRequestCopyWith<$Res> {
  factory _$$VerificationRequestImplCopyWith(
    _$VerificationRequestImpl value,
    $Res Function(_$VerificationRequestImpl) then,
  ) = __$$VerificationRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    String username,
    String userEmail,
    VerificationTier requestedTier,
    VerificationStatus status,
    DateTime submittedAt,
    DateTime? reviewedAt,
    String? reviewedBy,
    String? rejectionReason,
    String fullName,
    String profession,
    String? company,
    String? website,
    String? linkedinUrl,
    String? twitterUrl,
    String? instagramUrl,
    List<String>? supportingDocuments,
    String? additionalInfo,
    String? netWorthEvidence,
    String? businessOwnership,
    String? publicRecognition,
    String? publicProfile,
    String? mediaPresence,
    String? followerCount,
    String? adminNotes,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class __$$VerificationRequestImplCopyWithImpl<$Res>
    extends _$VerificationRequestCopyWithImpl<$Res, _$VerificationRequestImpl>
    implements _$$VerificationRequestImplCopyWith<$Res> {
  __$$VerificationRequestImplCopyWithImpl(
    _$VerificationRequestImpl _value,
    $Res Function(_$VerificationRequestImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of VerificationRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? username = null,
    Object? userEmail = null,
    Object? requestedTier = null,
    Object? status = null,
    Object? submittedAt = null,
    Object? reviewedAt = freezed,
    Object? reviewedBy = freezed,
    Object? rejectionReason = freezed,
    Object? fullName = null,
    Object? profession = null,
    Object? company = freezed,
    Object? website = freezed,
    Object? linkedinUrl = freezed,
    Object? twitterUrl = freezed,
    Object? instagramUrl = freezed,
    Object? supportingDocuments = freezed,
    Object? additionalInfo = freezed,
    Object? netWorthEvidence = freezed,
    Object? businessOwnership = freezed,
    Object? publicRecognition = freezed,
    Object? publicProfile = freezed,
    Object? mediaPresence = freezed,
    Object? followerCount = freezed,
    Object? adminNotes = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _$VerificationRequestImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        username: null == username
            ? _value.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        userEmail: null == userEmail
            ? _value.userEmail
            : userEmail // ignore: cast_nullable_to_non_nullable
                  as String,
        requestedTier: null == requestedTier
            ? _value.requestedTier
            : requestedTier // ignore: cast_nullable_to_non_nullable
                  as VerificationTier,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as VerificationStatus,
        submittedAt: null == submittedAt
            ? _value.submittedAt
            : submittedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        reviewedAt: freezed == reviewedAt
            ? _value.reviewedAt
            : reviewedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        reviewedBy: freezed == reviewedBy
            ? _value.reviewedBy
            : reviewedBy // ignore: cast_nullable_to_non_nullable
                  as String?,
        rejectionReason: freezed == rejectionReason
            ? _value.rejectionReason
            : rejectionReason // ignore: cast_nullable_to_non_nullable
                  as String?,
        fullName: null == fullName
            ? _value.fullName
            : fullName // ignore: cast_nullable_to_non_nullable
                  as String,
        profession: null == profession
            ? _value.profession
            : profession // ignore: cast_nullable_to_non_nullable
                  as String,
        company: freezed == company
            ? _value.company
            : company // ignore: cast_nullable_to_non_nullable
                  as String?,
        website: freezed == website
            ? _value.website
            : website // ignore: cast_nullable_to_non_nullable
                  as String?,
        linkedinUrl: freezed == linkedinUrl
            ? _value.linkedinUrl
            : linkedinUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        twitterUrl: freezed == twitterUrl
            ? _value.twitterUrl
            : twitterUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        instagramUrl: freezed == instagramUrl
            ? _value.instagramUrl
            : instagramUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        supportingDocuments: freezed == supportingDocuments
            ? _value._supportingDocuments
            : supportingDocuments // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        additionalInfo: freezed == additionalInfo
            ? _value.additionalInfo
            : additionalInfo // ignore: cast_nullable_to_non_nullable
                  as String?,
        netWorthEvidence: freezed == netWorthEvidence
            ? _value.netWorthEvidence
            : netWorthEvidence // ignore: cast_nullable_to_non_nullable
                  as String?,
        businessOwnership: freezed == businessOwnership
            ? _value.businessOwnership
            : businessOwnership // ignore: cast_nullable_to_non_nullable
                  as String?,
        publicRecognition: freezed == publicRecognition
            ? _value.publicRecognition
            : publicRecognition // ignore: cast_nullable_to_non_nullable
                  as String?,
        publicProfile: freezed == publicProfile
            ? _value.publicProfile
            : publicProfile // ignore: cast_nullable_to_non_nullable
                  as String?,
        mediaPresence: freezed == mediaPresence
            ? _value.mediaPresence
            : mediaPresence // ignore: cast_nullable_to_non_nullable
                  as String?,
        followerCount: freezed == followerCount
            ? _value.followerCount
            : followerCount // ignore: cast_nullable_to_non_nullable
                  as String?,
        adminNotes: freezed == adminNotes
            ? _value.adminNotes
            : adminNotes // ignore: cast_nullable_to_non_nullable
                  as String?,
        metadata: freezed == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$VerificationRequestImpl implements _VerificationRequest {
  const _$VerificationRequestImpl({
    required this.id,
    required this.userId,
    required this.username,
    required this.userEmail,
    required this.requestedTier,
    required this.status,
    required this.submittedAt,
    this.reviewedAt,
    this.reviewedBy,
    this.rejectionReason,
    required this.fullName,
    required this.profession,
    this.company,
    this.website,
    this.linkedinUrl,
    this.twitterUrl,
    this.instagramUrl,
    final List<String>? supportingDocuments,
    this.additionalInfo,
    this.netWorthEvidence,
    this.businessOwnership,
    this.publicRecognition,
    this.publicProfile,
    this.mediaPresence,
    this.followerCount,
    this.adminNotes,
    final Map<String, dynamic>? metadata,
  }) : _supportingDocuments = supportingDocuments,
       _metadata = metadata;

  factory _$VerificationRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$VerificationRequestImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String username;
  @override
  final String userEmail;
  @override
  final VerificationTier requestedTier;
  @override
  final VerificationStatus status;
  @override
  final DateTime submittedAt;
  @override
  final DateTime? reviewedAt;
  @override
  final String? reviewedBy;
  // Admin ID who reviewed
  @override
  final String? rejectionReason;
  // Supporting documents and information
  @override
  final String fullName;
  @override
  final String profession;
  @override
  final String? company;
  @override
  final String? website;
  @override
  final String? linkedinUrl;
  @override
  final String? twitterUrl;
  @override
  final String? instagramUrl;
  final List<String>? _supportingDocuments;
  @override
  List<String>? get supportingDocuments {
    final value = _supportingDocuments;
    if (value == null) return null;
    if (_supportingDocuments is EqualUnmodifiableListView)
      return _supportingDocuments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  // URLs to uploaded documents
  @override
  final String? additionalInfo;
  // For billionaire verification
  @override
  final String? netWorthEvidence;
  @override
  final String? businessOwnership;
  @override
  final String? publicRecognition;
  // For celebrity verification
  @override
  final String? publicProfile;
  @override
  final String? mediaPresence;
  @override
  final String? followerCount;
  // Admin notes
  @override
  final String? adminNotes;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'VerificationRequest(id: $id, userId: $userId, username: $username, userEmail: $userEmail, requestedTier: $requestedTier, status: $status, submittedAt: $submittedAt, reviewedAt: $reviewedAt, reviewedBy: $reviewedBy, rejectionReason: $rejectionReason, fullName: $fullName, profession: $profession, company: $company, website: $website, linkedinUrl: $linkedinUrl, twitterUrl: $twitterUrl, instagramUrl: $instagramUrl, supportingDocuments: $supportingDocuments, additionalInfo: $additionalInfo, netWorthEvidence: $netWorthEvidence, businessOwnership: $businessOwnership, publicRecognition: $publicRecognition, publicProfile: $publicProfile, mediaPresence: $mediaPresence, followerCount: $followerCount, adminNotes: $adminNotes, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerificationRequestImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userEmail, userEmail) ||
                other.userEmail == userEmail) &&
            (identical(other.requestedTier, requestedTier) ||
                other.requestedTier == requestedTier) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.submittedAt, submittedAt) ||
                other.submittedAt == submittedAt) &&
            (identical(other.reviewedAt, reviewedAt) ||
                other.reviewedAt == reviewedAt) &&
            (identical(other.reviewedBy, reviewedBy) ||
                other.reviewedBy == reviewedBy) &&
            (identical(other.rejectionReason, rejectionReason) ||
                other.rejectionReason == rejectionReason) &&
            (identical(other.fullName, fullName) ||
                other.fullName == fullName) &&
            (identical(other.profession, profession) ||
                other.profession == profession) &&
            (identical(other.company, company) || other.company == company) &&
            (identical(other.website, website) || other.website == website) &&
            (identical(other.linkedinUrl, linkedinUrl) ||
                other.linkedinUrl == linkedinUrl) &&
            (identical(other.twitterUrl, twitterUrl) ||
                other.twitterUrl == twitterUrl) &&
            (identical(other.instagramUrl, instagramUrl) ||
                other.instagramUrl == instagramUrl) &&
            const DeepCollectionEquality().equals(
              other._supportingDocuments,
              _supportingDocuments,
            ) &&
            (identical(other.additionalInfo, additionalInfo) ||
                other.additionalInfo == additionalInfo) &&
            (identical(other.netWorthEvidence, netWorthEvidence) ||
                other.netWorthEvidence == netWorthEvidence) &&
            (identical(other.businessOwnership, businessOwnership) ||
                other.businessOwnership == businessOwnership) &&
            (identical(other.publicRecognition, publicRecognition) ||
                other.publicRecognition == publicRecognition) &&
            (identical(other.publicProfile, publicProfile) ||
                other.publicProfile == publicProfile) &&
            (identical(other.mediaPresence, mediaPresence) ||
                other.mediaPresence == mediaPresence) &&
            (identical(other.followerCount, followerCount) ||
                other.followerCount == followerCount) &&
            (identical(other.adminNotes, adminNotes) ||
                other.adminNotes == adminNotes) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    userId,
    username,
    userEmail,
    requestedTier,
    status,
    submittedAt,
    reviewedAt,
    reviewedBy,
    rejectionReason,
    fullName,
    profession,
    company,
    website,
    linkedinUrl,
    twitterUrl,
    instagramUrl,
    const DeepCollectionEquality().hash(_supportingDocuments),
    additionalInfo,
    netWorthEvidence,
    businessOwnership,
    publicRecognition,
    publicProfile,
    mediaPresence,
    followerCount,
    adminNotes,
    const DeepCollectionEquality().hash(_metadata),
  ]);

  /// Create a copy of VerificationRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VerificationRequestImplCopyWith<_$VerificationRequestImpl> get copyWith =>
      __$$VerificationRequestImplCopyWithImpl<_$VerificationRequestImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$VerificationRequestImplToJson(this);
  }
}

abstract class _VerificationRequest implements VerificationRequest {
  const factory _VerificationRequest({
    required final String id,
    required final String userId,
    required final String username,
    required final String userEmail,
    required final VerificationTier requestedTier,
    required final VerificationStatus status,
    required final DateTime submittedAt,
    final DateTime? reviewedAt,
    final String? reviewedBy,
    final String? rejectionReason,
    required final String fullName,
    required final String profession,
    final String? company,
    final String? website,
    final String? linkedinUrl,
    final String? twitterUrl,
    final String? instagramUrl,
    final List<String>? supportingDocuments,
    final String? additionalInfo,
    final String? netWorthEvidence,
    final String? businessOwnership,
    final String? publicRecognition,
    final String? publicProfile,
    final String? mediaPresence,
    final String? followerCount,
    final String? adminNotes,
    final Map<String, dynamic>? metadata,
  }) = _$VerificationRequestImpl;

  factory _VerificationRequest.fromJson(Map<String, dynamic> json) =
      _$VerificationRequestImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get username;
  @override
  String get userEmail;
  @override
  VerificationTier get requestedTier;
  @override
  VerificationStatus get status;
  @override
  DateTime get submittedAt;
  @override
  DateTime? get reviewedAt;
  @override
  String? get reviewedBy; // Admin ID who reviewed
  @override
  String? get rejectionReason; // Supporting documents and information
  @override
  String get fullName;
  @override
  String get profession;
  @override
  String? get company;
  @override
  String? get website;
  @override
  String? get linkedinUrl;
  @override
  String? get twitterUrl;
  @override
  String? get instagramUrl;
  @override
  List<String>? get supportingDocuments; // URLs to uploaded documents
  @override
  String? get additionalInfo; // For billionaire verification
  @override
  String? get netWorthEvidence;
  @override
  String? get businessOwnership;
  @override
  String? get publicRecognition; // For celebrity verification
  @override
  String? get publicProfile;
  @override
  String? get mediaPresence;
  @override
  String? get followerCount; // Admin notes
  @override
  String? get adminNotes;
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of VerificationRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VerificationRequestImplCopyWith<_$VerificationRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserVerification _$UserVerificationFromJson(Map<String, dynamic> json) {
  return _UserVerification.fromJson(json);
}

/// @nodoc
mixin _$UserVerification {
  String get userId => throw _privateConstructorUsedError;
  bool get isVerified => throw _privateConstructorUsedError;
  bool get isBillionaire => throw _privateConstructorUsedError;
  bool get isCelebrity => throw _privateConstructorUsedError;
  VerificationTier? get verificationTier => throw _privateConstructorUsedError;
  DateTime? get verifiedAt => throw _privateConstructorUsedError;
  String? get verifiedBy =>
      throw _privateConstructorUsedError; // Admin ID who verified
  String? get verificationRequestId =>
      throw _privateConstructorUsedError; // Badge customization
  String? get customBadgeColor => throw _privateConstructorUsedError;
  String? get customBadgeIcon =>
      throw _privateConstructorUsedError; // Verification metadata
  Map<String, dynamic>? get verificationMetadata =>
      throw _privateConstructorUsedError; // Privileges
  bool get hasBlueCheckmark => throw _privateConstructorUsedError;
  bool get hasGoldStar => throw _privateConstructorUsedError;
  bool get hasGoldB => throw _privateConstructorUsedError;
  bool get canCreateVerifiedContent => throw _privateConstructorUsedError;
  bool get priorityInSearch => throw _privateConstructorUsedError;
  bool get priorityInFeed => throw _privateConstructorUsedError;

  /// Serializes this UserVerification to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserVerification
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserVerificationCopyWith<UserVerification> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserVerificationCopyWith<$Res> {
  factory $UserVerificationCopyWith(
    UserVerification value,
    $Res Function(UserVerification) then,
  ) = _$UserVerificationCopyWithImpl<$Res, UserVerification>;
  @useResult
  $Res call({
    String userId,
    bool isVerified,
    bool isBillionaire,
    bool isCelebrity,
    VerificationTier? verificationTier,
    DateTime? verifiedAt,
    String? verifiedBy,
    String? verificationRequestId,
    String? customBadgeColor,
    String? customBadgeIcon,
    Map<String, dynamic>? verificationMetadata,
    bool hasBlueCheckmark,
    bool hasGoldStar,
    bool hasGoldB,
    bool canCreateVerifiedContent,
    bool priorityInSearch,
    bool priorityInFeed,
  });
}

/// @nodoc
class _$UserVerificationCopyWithImpl<$Res, $Val extends UserVerification>
    implements $UserVerificationCopyWith<$Res> {
  _$UserVerificationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserVerification
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? isVerified = null,
    Object? isBillionaire = null,
    Object? isCelebrity = null,
    Object? verificationTier = freezed,
    Object? verifiedAt = freezed,
    Object? verifiedBy = freezed,
    Object? verificationRequestId = freezed,
    Object? customBadgeColor = freezed,
    Object? customBadgeIcon = freezed,
    Object? verificationMetadata = freezed,
    Object? hasBlueCheckmark = null,
    Object? hasGoldStar = null,
    Object? hasGoldB = null,
    Object? canCreateVerifiedContent = null,
    Object? priorityInSearch = null,
    Object? priorityInFeed = null,
  }) {
    return _then(
      _value.copyWith(
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            isVerified: null == isVerified
                ? _value.isVerified
                : isVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            isBillionaire: null == isBillionaire
                ? _value.isBillionaire
                : isBillionaire // ignore: cast_nullable_to_non_nullable
                      as bool,
            isCelebrity: null == isCelebrity
                ? _value.isCelebrity
                : isCelebrity // ignore: cast_nullable_to_non_nullable
                      as bool,
            verificationTier: freezed == verificationTier
                ? _value.verificationTier
                : verificationTier // ignore: cast_nullable_to_non_nullable
                      as VerificationTier?,
            verifiedAt: freezed == verifiedAt
                ? _value.verifiedAt
                : verifiedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            verifiedBy: freezed == verifiedBy
                ? _value.verifiedBy
                : verifiedBy // ignore: cast_nullable_to_non_nullable
                      as String?,
            verificationRequestId: freezed == verificationRequestId
                ? _value.verificationRequestId
                : verificationRequestId // ignore: cast_nullable_to_non_nullable
                      as String?,
            customBadgeColor: freezed == customBadgeColor
                ? _value.customBadgeColor
                : customBadgeColor // ignore: cast_nullable_to_non_nullable
                      as String?,
            customBadgeIcon: freezed == customBadgeIcon
                ? _value.customBadgeIcon
                : customBadgeIcon // ignore: cast_nullable_to_non_nullable
                      as String?,
            verificationMetadata: freezed == verificationMetadata
                ? _value.verificationMetadata
                : verificationMetadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
            hasBlueCheckmark: null == hasBlueCheckmark
                ? _value.hasBlueCheckmark
                : hasBlueCheckmark // ignore: cast_nullable_to_non_nullable
                      as bool,
            hasGoldStar: null == hasGoldStar
                ? _value.hasGoldStar
                : hasGoldStar // ignore: cast_nullable_to_non_nullable
                      as bool,
            hasGoldB: null == hasGoldB
                ? _value.hasGoldB
                : hasGoldB // ignore: cast_nullable_to_non_nullable
                      as bool,
            canCreateVerifiedContent: null == canCreateVerifiedContent
                ? _value.canCreateVerifiedContent
                : canCreateVerifiedContent // ignore: cast_nullable_to_non_nullable
                      as bool,
            priorityInSearch: null == priorityInSearch
                ? _value.priorityInSearch
                : priorityInSearch // ignore: cast_nullable_to_non_nullable
                      as bool,
            priorityInFeed: null == priorityInFeed
                ? _value.priorityInFeed
                : priorityInFeed // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$UserVerificationImplCopyWith<$Res>
    implements $UserVerificationCopyWith<$Res> {
  factory _$$UserVerificationImplCopyWith(
    _$UserVerificationImpl value,
    $Res Function(_$UserVerificationImpl) then,
  ) = __$$UserVerificationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String userId,
    bool isVerified,
    bool isBillionaire,
    bool isCelebrity,
    VerificationTier? verificationTier,
    DateTime? verifiedAt,
    String? verifiedBy,
    String? verificationRequestId,
    String? customBadgeColor,
    String? customBadgeIcon,
    Map<String, dynamic>? verificationMetadata,
    bool hasBlueCheckmark,
    bool hasGoldStar,
    bool hasGoldB,
    bool canCreateVerifiedContent,
    bool priorityInSearch,
    bool priorityInFeed,
  });
}

/// @nodoc
class __$$UserVerificationImplCopyWithImpl<$Res>
    extends _$UserVerificationCopyWithImpl<$Res, _$UserVerificationImpl>
    implements _$$UserVerificationImplCopyWith<$Res> {
  __$$UserVerificationImplCopyWithImpl(
    _$UserVerificationImpl _value,
    $Res Function(_$UserVerificationImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of UserVerification
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? isVerified = null,
    Object? isBillionaire = null,
    Object? isCelebrity = null,
    Object? verificationTier = freezed,
    Object? verifiedAt = freezed,
    Object? verifiedBy = freezed,
    Object? verificationRequestId = freezed,
    Object? customBadgeColor = freezed,
    Object? customBadgeIcon = freezed,
    Object? verificationMetadata = freezed,
    Object? hasBlueCheckmark = null,
    Object? hasGoldStar = null,
    Object? hasGoldB = null,
    Object? canCreateVerifiedContent = null,
    Object? priorityInSearch = null,
    Object? priorityInFeed = null,
  }) {
    return _then(
      _$UserVerificationImpl(
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        isVerified: null == isVerified
            ? _value.isVerified
            : isVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        isBillionaire: null == isBillionaire
            ? _value.isBillionaire
            : isBillionaire // ignore: cast_nullable_to_non_nullable
                  as bool,
        isCelebrity: null == isCelebrity
            ? _value.isCelebrity
            : isCelebrity // ignore: cast_nullable_to_non_nullable
                  as bool,
        verificationTier: freezed == verificationTier
            ? _value.verificationTier
            : verificationTier // ignore: cast_nullable_to_non_nullable
                  as VerificationTier?,
        verifiedAt: freezed == verifiedAt
            ? _value.verifiedAt
            : verifiedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        verifiedBy: freezed == verifiedBy
            ? _value.verifiedBy
            : verifiedBy // ignore: cast_nullable_to_non_nullable
                  as String?,
        verificationRequestId: freezed == verificationRequestId
            ? _value.verificationRequestId
            : verificationRequestId // ignore: cast_nullable_to_non_nullable
                  as String?,
        customBadgeColor: freezed == customBadgeColor
            ? _value.customBadgeColor
            : customBadgeColor // ignore: cast_nullable_to_non_nullable
                  as String?,
        customBadgeIcon: freezed == customBadgeIcon
            ? _value.customBadgeIcon
            : customBadgeIcon // ignore: cast_nullable_to_non_nullable
                  as String?,
        verificationMetadata: freezed == verificationMetadata
            ? _value._verificationMetadata
            : verificationMetadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
        hasBlueCheckmark: null == hasBlueCheckmark
            ? _value.hasBlueCheckmark
            : hasBlueCheckmark // ignore: cast_nullable_to_non_nullable
                  as bool,
        hasGoldStar: null == hasGoldStar
            ? _value.hasGoldStar
            : hasGoldStar // ignore: cast_nullable_to_non_nullable
                  as bool,
        hasGoldB: null == hasGoldB
            ? _value.hasGoldB
            : hasGoldB // ignore: cast_nullable_to_non_nullable
                  as bool,
        canCreateVerifiedContent: null == canCreateVerifiedContent
            ? _value.canCreateVerifiedContent
            : canCreateVerifiedContent // ignore: cast_nullable_to_non_nullable
                  as bool,
        priorityInSearch: null == priorityInSearch
            ? _value.priorityInSearch
            : priorityInSearch // ignore: cast_nullable_to_non_nullable
                  as bool,
        priorityInFeed: null == priorityInFeed
            ? _value.priorityInFeed
            : priorityInFeed // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$UserVerificationImpl implements _UserVerification {
  const _$UserVerificationImpl({
    required this.userId,
    this.isVerified = false,
    this.isBillionaire = false,
    this.isCelebrity = false,
    this.verificationTier,
    this.verifiedAt,
    this.verifiedBy,
    this.verificationRequestId,
    this.customBadgeColor,
    this.customBadgeIcon,
    final Map<String, dynamic>? verificationMetadata,
    this.hasBlueCheckmark = false,
    this.hasGoldStar = false,
    this.hasGoldB = false,
    this.canCreateVerifiedContent = false,
    this.priorityInSearch = false,
    this.priorityInFeed = false,
  }) : _verificationMetadata = verificationMetadata;

  factory _$UserVerificationImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserVerificationImplFromJson(json);

  @override
  final String userId;
  @override
  @JsonKey()
  final bool isVerified;
  @override
  @JsonKey()
  final bool isBillionaire;
  @override
  @JsonKey()
  final bool isCelebrity;
  @override
  final VerificationTier? verificationTier;
  @override
  final DateTime? verifiedAt;
  @override
  final String? verifiedBy;
  // Admin ID who verified
  @override
  final String? verificationRequestId;
  // Badge customization
  @override
  final String? customBadgeColor;
  @override
  final String? customBadgeIcon;
  // Verification metadata
  final Map<String, dynamic>? _verificationMetadata;
  // Verification metadata
  @override
  Map<String, dynamic>? get verificationMetadata {
    final value = _verificationMetadata;
    if (value == null) return null;
    if (_verificationMetadata is EqualUnmodifiableMapView)
      return _verificationMetadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  // Privileges
  @override
  @JsonKey()
  final bool hasBlueCheckmark;
  @override
  @JsonKey()
  final bool hasGoldStar;
  @override
  @JsonKey()
  final bool hasGoldB;
  @override
  @JsonKey()
  final bool canCreateVerifiedContent;
  @override
  @JsonKey()
  final bool priorityInSearch;
  @override
  @JsonKey()
  final bool priorityInFeed;

  @override
  String toString() {
    return 'UserVerification(userId: $userId, isVerified: $isVerified, isBillionaire: $isBillionaire, isCelebrity: $isCelebrity, verificationTier: $verificationTier, verifiedAt: $verifiedAt, verifiedBy: $verifiedBy, verificationRequestId: $verificationRequestId, customBadgeColor: $customBadgeColor, customBadgeIcon: $customBadgeIcon, verificationMetadata: $verificationMetadata, hasBlueCheckmark: $hasBlueCheckmark, hasGoldStar: $hasGoldStar, hasGoldB: $hasGoldB, canCreateVerifiedContent: $canCreateVerifiedContent, priorityInSearch: $priorityInSearch, priorityInFeed: $priorityInFeed)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserVerificationImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.isBillionaire, isBillionaire) ||
                other.isBillionaire == isBillionaire) &&
            (identical(other.isCelebrity, isCelebrity) ||
                other.isCelebrity == isCelebrity) &&
            (identical(other.verificationTier, verificationTier) ||
                other.verificationTier == verificationTier) &&
            (identical(other.verifiedAt, verifiedAt) ||
                other.verifiedAt == verifiedAt) &&
            (identical(other.verifiedBy, verifiedBy) ||
                other.verifiedBy == verifiedBy) &&
            (identical(other.verificationRequestId, verificationRequestId) ||
                other.verificationRequestId == verificationRequestId) &&
            (identical(other.customBadgeColor, customBadgeColor) ||
                other.customBadgeColor == customBadgeColor) &&
            (identical(other.customBadgeIcon, customBadgeIcon) ||
                other.customBadgeIcon == customBadgeIcon) &&
            const DeepCollectionEquality().equals(
              other._verificationMetadata,
              _verificationMetadata,
            ) &&
            (identical(other.hasBlueCheckmark, hasBlueCheckmark) ||
                other.hasBlueCheckmark == hasBlueCheckmark) &&
            (identical(other.hasGoldStar, hasGoldStar) ||
                other.hasGoldStar == hasGoldStar) &&
            (identical(other.hasGoldB, hasGoldB) ||
                other.hasGoldB == hasGoldB) &&
            (identical(
                  other.canCreateVerifiedContent,
                  canCreateVerifiedContent,
                ) ||
                other.canCreateVerifiedContent == canCreateVerifiedContent) &&
            (identical(other.priorityInSearch, priorityInSearch) ||
                other.priorityInSearch == priorityInSearch) &&
            (identical(other.priorityInFeed, priorityInFeed) ||
                other.priorityInFeed == priorityInFeed));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    userId,
    isVerified,
    isBillionaire,
    isCelebrity,
    verificationTier,
    verifiedAt,
    verifiedBy,
    verificationRequestId,
    customBadgeColor,
    customBadgeIcon,
    const DeepCollectionEquality().hash(_verificationMetadata),
    hasBlueCheckmark,
    hasGoldStar,
    hasGoldB,
    canCreateVerifiedContent,
    priorityInSearch,
    priorityInFeed,
  );

  /// Create a copy of UserVerification
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserVerificationImplCopyWith<_$UserVerificationImpl> get copyWith =>
      __$$UserVerificationImplCopyWithImpl<_$UserVerificationImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$UserVerificationImplToJson(this);
  }
}

abstract class _UserVerification implements UserVerification {
  const factory _UserVerification({
    required final String userId,
    final bool isVerified,
    final bool isBillionaire,
    final bool isCelebrity,
    final VerificationTier? verificationTier,
    final DateTime? verifiedAt,
    final String? verifiedBy,
    final String? verificationRequestId,
    final String? customBadgeColor,
    final String? customBadgeIcon,
    final Map<String, dynamic>? verificationMetadata,
    final bool hasBlueCheckmark,
    final bool hasGoldStar,
    final bool hasGoldB,
    final bool canCreateVerifiedContent,
    final bool priorityInSearch,
    final bool priorityInFeed,
  }) = _$UserVerificationImpl;

  factory _UserVerification.fromJson(Map<String, dynamic> json) =
      _$UserVerificationImpl.fromJson;

  @override
  String get userId;
  @override
  bool get isVerified;
  @override
  bool get isBillionaire;
  @override
  bool get isCelebrity;
  @override
  VerificationTier? get verificationTier;
  @override
  DateTime? get verifiedAt;
  @override
  String? get verifiedBy; // Admin ID who verified
  @override
  String? get verificationRequestId; // Badge customization
  @override
  String? get customBadgeColor;
  @override
  String? get customBadgeIcon; // Verification metadata
  @override
  Map<String, dynamic>? get verificationMetadata; // Privileges
  @override
  bool get hasBlueCheckmark;
  @override
  bool get hasGoldStar;
  @override
  bool get hasGoldB;
  @override
  bool get canCreateVerifiedContent;
  @override
  bool get priorityInSearch;
  @override
  bool get priorityInFeed;

  /// Create a copy of UserVerification
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserVerificationImplCopyWith<_$UserVerificationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

VerificationCriteria _$VerificationCriteriaFromJson(Map<String, dynamic> json) {
  return _VerificationCriteria.fromJson(json);
}

/// @nodoc
mixin _$VerificationCriteria {
  VerificationTier get tier => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  List<String> get requirements => throw _privateConstructorUsedError;
  List<String> get documentationNeeded => throw _privateConstructorUsedError;
  String? get additionalNotes => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;

  /// Serializes this VerificationCriteria to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VerificationCriteria
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VerificationCriteriaCopyWith<VerificationCriteria> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VerificationCriteriaCopyWith<$Res> {
  factory $VerificationCriteriaCopyWith(
    VerificationCriteria value,
    $Res Function(VerificationCriteria) then,
  ) = _$VerificationCriteriaCopyWithImpl<$Res, VerificationCriteria>;
  @useResult
  $Res call({
    VerificationTier tier,
    String title,
    String description,
    List<String> requirements,
    List<String> documentationNeeded,
    String? additionalNotes,
    bool isActive,
  });
}

/// @nodoc
class _$VerificationCriteriaCopyWithImpl<
  $Res,
  $Val extends VerificationCriteria
>
    implements $VerificationCriteriaCopyWith<$Res> {
  _$VerificationCriteriaCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VerificationCriteria
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tier = null,
    Object? title = null,
    Object? description = null,
    Object? requirements = null,
    Object? documentationNeeded = null,
    Object? additionalNotes = freezed,
    Object? isActive = null,
  }) {
    return _then(
      _value.copyWith(
            tier: null == tier
                ? _value.tier
                : tier // ignore: cast_nullable_to_non_nullable
                      as VerificationTier,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            requirements: null == requirements
                ? _value.requirements
                : requirements // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            documentationNeeded: null == documentationNeeded
                ? _value.documentationNeeded
                : documentationNeeded // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            additionalNotes: freezed == additionalNotes
                ? _value.additionalNotes
                : additionalNotes // ignore: cast_nullable_to_non_nullable
                      as String?,
            isActive: null == isActive
                ? _value.isActive
                : isActive // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$VerificationCriteriaImplCopyWith<$Res>
    implements $VerificationCriteriaCopyWith<$Res> {
  factory _$$VerificationCriteriaImplCopyWith(
    _$VerificationCriteriaImpl value,
    $Res Function(_$VerificationCriteriaImpl) then,
  ) = __$$VerificationCriteriaImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    VerificationTier tier,
    String title,
    String description,
    List<String> requirements,
    List<String> documentationNeeded,
    String? additionalNotes,
    bool isActive,
  });
}

/// @nodoc
class __$$VerificationCriteriaImplCopyWithImpl<$Res>
    extends _$VerificationCriteriaCopyWithImpl<$Res, _$VerificationCriteriaImpl>
    implements _$$VerificationCriteriaImplCopyWith<$Res> {
  __$$VerificationCriteriaImplCopyWithImpl(
    _$VerificationCriteriaImpl _value,
    $Res Function(_$VerificationCriteriaImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of VerificationCriteria
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tier = null,
    Object? title = null,
    Object? description = null,
    Object? requirements = null,
    Object? documentationNeeded = null,
    Object? additionalNotes = freezed,
    Object? isActive = null,
  }) {
    return _then(
      _$VerificationCriteriaImpl(
        tier: null == tier
            ? _value.tier
            : tier // ignore: cast_nullable_to_non_nullable
                  as VerificationTier,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        requirements: null == requirements
            ? _value._requirements
            : requirements // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        documentationNeeded: null == documentationNeeded
            ? _value._documentationNeeded
            : documentationNeeded // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        additionalNotes: freezed == additionalNotes
            ? _value.additionalNotes
            : additionalNotes // ignore: cast_nullable_to_non_nullable
                  as String?,
        isActive: null == isActive
            ? _value.isActive
            : isActive // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$VerificationCriteriaImpl implements _VerificationCriteria {
  const _$VerificationCriteriaImpl({
    required this.tier,
    required this.title,
    required this.description,
    required final List<String> requirements,
    required final List<String> documentationNeeded,
    this.additionalNotes,
    this.isActive = false,
  }) : _requirements = requirements,
       _documentationNeeded = documentationNeeded;

  factory _$VerificationCriteriaImpl.fromJson(Map<String, dynamic> json) =>
      _$$VerificationCriteriaImplFromJson(json);

  @override
  final VerificationTier tier;
  @override
  final String title;
  @override
  final String description;
  final List<String> _requirements;
  @override
  List<String> get requirements {
    if (_requirements is EqualUnmodifiableListView) return _requirements;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_requirements);
  }

  final List<String> _documentationNeeded;
  @override
  List<String> get documentationNeeded {
    if (_documentationNeeded is EqualUnmodifiableListView)
      return _documentationNeeded;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_documentationNeeded);
  }

  @override
  final String? additionalNotes;
  @override
  @JsonKey()
  final bool isActive;

  @override
  String toString() {
    return 'VerificationCriteria(tier: $tier, title: $title, description: $description, requirements: $requirements, documentationNeeded: $documentationNeeded, additionalNotes: $additionalNotes, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerificationCriteriaImpl &&
            (identical(other.tier, tier) || other.tier == tier) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality().equals(
              other._requirements,
              _requirements,
            ) &&
            const DeepCollectionEquality().equals(
              other._documentationNeeded,
              _documentationNeeded,
            ) &&
            (identical(other.additionalNotes, additionalNotes) ||
                other.additionalNotes == additionalNotes) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    tier,
    title,
    description,
    const DeepCollectionEquality().hash(_requirements),
    const DeepCollectionEquality().hash(_documentationNeeded),
    additionalNotes,
    isActive,
  );

  /// Create a copy of VerificationCriteria
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VerificationCriteriaImplCopyWith<_$VerificationCriteriaImpl>
  get copyWith =>
      __$$VerificationCriteriaImplCopyWithImpl<_$VerificationCriteriaImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$VerificationCriteriaImplToJson(this);
  }
}

abstract class _VerificationCriteria implements VerificationCriteria {
  const factory _VerificationCriteria({
    required final VerificationTier tier,
    required final String title,
    required final String description,
    required final List<String> requirements,
    required final List<String> documentationNeeded,
    final String? additionalNotes,
    final bool isActive,
  }) = _$VerificationCriteriaImpl;

  factory _VerificationCriteria.fromJson(Map<String, dynamic> json) =
      _$VerificationCriteriaImpl.fromJson;

  @override
  VerificationTier get tier;
  @override
  String get title;
  @override
  String get description;
  @override
  List<String> get requirements;
  @override
  List<String> get documentationNeeded;
  @override
  String? get additionalNotes;
  @override
  bool get isActive;

  /// Create a copy of VerificationCriteria
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VerificationCriteriaImplCopyWith<_$VerificationCriteriaImpl>
  get copyWith => throw _privateConstructorUsedError;
}

VerificationBadgeConfig _$VerificationBadgeConfigFromJson(
  Map<String, dynamic> json,
) {
  return _VerificationBadgeConfig.fromJson(json);
}

/// @nodoc
mixin _$VerificationBadgeConfig {
  VerificationTier get tier => throw _privateConstructorUsedError;
  String get iconName => throw _privateConstructorUsedError;
  String get color => throw _privateConstructorUsedError;
  String get displayName => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  double get smallSize => throw _privateConstructorUsedError;
  double get mediumSize => throw _privateConstructorUsedError;
  double get largeSize => throw _privateConstructorUsedError;

  /// Serializes this VerificationBadgeConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VerificationBadgeConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VerificationBadgeConfigCopyWith<VerificationBadgeConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VerificationBadgeConfigCopyWith<$Res> {
  factory $VerificationBadgeConfigCopyWith(
    VerificationBadgeConfig value,
    $Res Function(VerificationBadgeConfig) then,
  ) = _$VerificationBadgeConfigCopyWithImpl<$Res, VerificationBadgeConfig>;
  @useResult
  $Res call({
    VerificationTier tier,
    String iconName,
    String color,
    String displayName,
    String description,
    double smallSize,
    double mediumSize,
    double largeSize,
  });
}

/// @nodoc
class _$VerificationBadgeConfigCopyWithImpl<
  $Res,
  $Val extends VerificationBadgeConfig
>
    implements $VerificationBadgeConfigCopyWith<$Res> {
  _$VerificationBadgeConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VerificationBadgeConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tier = null,
    Object? iconName = null,
    Object? color = null,
    Object? displayName = null,
    Object? description = null,
    Object? smallSize = null,
    Object? mediumSize = null,
    Object? largeSize = null,
  }) {
    return _then(
      _value.copyWith(
            tier: null == tier
                ? _value.tier
                : tier // ignore: cast_nullable_to_non_nullable
                      as VerificationTier,
            iconName: null == iconName
                ? _value.iconName
                : iconName // ignore: cast_nullable_to_non_nullable
                      as String,
            color: null == color
                ? _value.color
                : color // ignore: cast_nullable_to_non_nullable
                      as String,
            displayName: null == displayName
                ? _value.displayName
                : displayName // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            smallSize: null == smallSize
                ? _value.smallSize
                : smallSize // ignore: cast_nullable_to_non_nullable
                      as double,
            mediumSize: null == mediumSize
                ? _value.mediumSize
                : mediumSize // ignore: cast_nullable_to_non_nullable
                      as double,
            largeSize: null == largeSize
                ? _value.largeSize
                : largeSize // ignore: cast_nullable_to_non_nullable
                      as double,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$VerificationBadgeConfigImplCopyWith<$Res>
    implements $VerificationBadgeConfigCopyWith<$Res> {
  factory _$$VerificationBadgeConfigImplCopyWith(
    _$VerificationBadgeConfigImpl value,
    $Res Function(_$VerificationBadgeConfigImpl) then,
  ) = __$$VerificationBadgeConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    VerificationTier tier,
    String iconName,
    String color,
    String displayName,
    String description,
    double smallSize,
    double mediumSize,
    double largeSize,
  });
}

/// @nodoc
class __$$VerificationBadgeConfigImplCopyWithImpl<$Res>
    extends
        _$VerificationBadgeConfigCopyWithImpl<
          $Res,
          _$VerificationBadgeConfigImpl
        >
    implements _$$VerificationBadgeConfigImplCopyWith<$Res> {
  __$$VerificationBadgeConfigImplCopyWithImpl(
    _$VerificationBadgeConfigImpl _value,
    $Res Function(_$VerificationBadgeConfigImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of VerificationBadgeConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tier = null,
    Object? iconName = null,
    Object? color = null,
    Object? displayName = null,
    Object? description = null,
    Object? smallSize = null,
    Object? mediumSize = null,
    Object? largeSize = null,
  }) {
    return _then(
      _$VerificationBadgeConfigImpl(
        tier: null == tier
            ? _value.tier
            : tier // ignore: cast_nullable_to_non_nullable
                  as VerificationTier,
        iconName: null == iconName
            ? _value.iconName
            : iconName // ignore: cast_nullable_to_non_nullable
                  as String,
        color: null == color
            ? _value.color
            : color // ignore: cast_nullable_to_non_nullable
                  as String,
        displayName: null == displayName
            ? _value.displayName
            : displayName // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        smallSize: null == smallSize
            ? _value.smallSize
            : smallSize // ignore: cast_nullable_to_non_nullable
                  as double,
        mediumSize: null == mediumSize
            ? _value.mediumSize
            : mediumSize // ignore: cast_nullable_to_non_nullable
                  as double,
        largeSize: null == largeSize
            ? _value.largeSize
            : largeSize // ignore: cast_nullable_to_non_nullable
                  as double,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$VerificationBadgeConfigImpl implements _VerificationBadgeConfig {
  const _$VerificationBadgeConfigImpl({
    required this.tier,
    required this.iconName,
    required this.color,
    required this.displayName,
    required this.description,
    this.smallSize = 12.0,
    this.mediumSize = 16.0,
    this.largeSize = 20.0,
  });

  factory _$VerificationBadgeConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$VerificationBadgeConfigImplFromJson(json);

  @override
  final VerificationTier tier;
  @override
  final String iconName;
  @override
  final String color;
  @override
  final String displayName;
  @override
  final String description;
  @override
  @JsonKey()
  final double smallSize;
  @override
  @JsonKey()
  final double mediumSize;
  @override
  @JsonKey()
  final double largeSize;

  @override
  String toString() {
    return 'VerificationBadgeConfig(tier: $tier, iconName: $iconName, color: $color, displayName: $displayName, description: $description, smallSize: $smallSize, mediumSize: $mediumSize, largeSize: $largeSize)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerificationBadgeConfigImpl &&
            (identical(other.tier, tier) || other.tier == tier) &&
            (identical(other.iconName, iconName) ||
                other.iconName == iconName) &&
            (identical(other.color, color) || other.color == color) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.smallSize, smallSize) ||
                other.smallSize == smallSize) &&
            (identical(other.mediumSize, mediumSize) ||
                other.mediumSize == mediumSize) &&
            (identical(other.largeSize, largeSize) ||
                other.largeSize == largeSize));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    tier,
    iconName,
    color,
    displayName,
    description,
    smallSize,
    mediumSize,
    largeSize,
  );

  /// Create a copy of VerificationBadgeConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VerificationBadgeConfigImplCopyWith<_$VerificationBadgeConfigImpl>
  get copyWith =>
      __$$VerificationBadgeConfigImplCopyWithImpl<
        _$VerificationBadgeConfigImpl
      >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VerificationBadgeConfigImplToJson(this);
  }
}

abstract class _VerificationBadgeConfig implements VerificationBadgeConfig {
  const factory _VerificationBadgeConfig({
    required final VerificationTier tier,
    required final String iconName,
    required final String color,
    required final String displayName,
    required final String description,
    final double smallSize,
    final double mediumSize,
    final double largeSize,
  }) = _$VerificationBadgeConfigImpl;

  factory _VerificationBadgeConfig.fromJson(Map<String, dynamic> json) =
      _$VerificationBadgeConfigImpl.fromJson;

  @override
  VerificationTier get tier;
  @override
  String get iconName;
  @override
  String get color;
  @override
  String get displayName;
  @override
  String get description;
  @override
  double get smallSize;
  @override
  double get mediumSize;
  @override
  double get largeSize;

  /// Create a copy of VerificationBadgeConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VerificationBadgeConfigImplCopyWith<_$VerificationBadgeConfigImpl>
  get copyWith => throw _privateConstructorUsedError;
}

VerificationStats _$VerificationStatsFromJson(Map<String, dynamic> json) {
  return _VerificationStats.fromJson(json);
}

/// @nodoc
mixin _$VerificationStats {
  int get totalRequests => throw _privateConstructorUsedError;
  int get pendingRequests => throw _privateConstructorUsedError;
  int get approvedRequests => throw _privateConstructorUsedError;
  int get rejectedRequests => throw _privateConstructorUsedError;
  int get billionaireCount => throw _privateConstructorUsedError;
  int get celebrityCount => throw _privateConstructorUsedError;
  int get generalVerifiedCount => throw _privateConstructorUsedError;
  Map<String, int> get requestsByTier => throw _privateConstructorUsedError;
  Map<String, int> get requestsByStatus => throw _privateConstructorUsedError;
  DateTime get lastUpdated => throw _privateConstructorUsedError;

  /// Serializes this VerificationStats to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VerificationStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VerificationStatsCopyWith<VerificationStats> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VerificationStatsCopyWith<$Res> {
  factory $VerificationStatsCopyWith(
    VerificationStats value,
    $Res Function(VerificationStats) then,
  ) = _$VerificationStatsCopyWithImpl<$Res, VerificationStats>;
  @useResult
  $Res call({
    int totalRequests,
    int pendingRequests,
    int approvedRequests,
    int rejectedRequests,
    int billionaireCount,
    int celebrityCount,
    int generalVerifiedCount,
    Map<String, int> requestsByTier,
    Map<String, int> requestsByStatus,
    DateTime lastUpdated,
  });
}

/// @nodoc
class _$VerificationStatsCopyWithImpl<$Res, $Val extends VerificationStats>
    implements $VerificationStatsCopyWith<$Res> {
  _$VerificationStatsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VerificationStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalRequests = null,
    Object? pendingRequests = null,
    Object? approvedRequests = null,
    Object? rejectedRequests = null,
    Object? billionaireCount = null,
    Object? celebrityCount = null,
    Object? generalVerifiedCount = null,
    Object? requestsByTier = null,
    Object? requestsByStatus = null,
    Object? lastUpdated = null,
  }) {
    return _then(
      _value.copyWith(
            totalRequests: null == totalRequests
                ? _value.totalRequests
                : totalRequests // ignore: cast_nullable_to_non_nullable
                      as int,
            pendingRequests: null == pendingRequests
                ? _value.pendingRequests
                : pendingRequests // ignore: cast_nullable_to_non_nullable
                      as int,
            approvedRequests: null == approvedRequests
                ? _value.approvedRequests
                : approvedRequests // ignore: cast_nullable_to_non_nullable
                      as int,
            rejectedRequests: null == rejectedRequests
                ? _value.rejectedRequests
                : rejectedRequests // ignore: cast_nullable_to_non_nullable
                      as int,
            billionaireCount: null == billionaireCount
                ? _value.billionaireCount
                : billionaireCount // ignore: cast_nullable_to_non_nullable
                      as int,
            celebrityCount: null == celebrityCount
                ? _value.celebrityCount
                : celebrityCount // ignore: cast_nullable_to_non_nullable
                      as int,
            generalVerifiedCount: null == generalVerifiedCount
                ? _value.generalVerifiedCount
                : generalVerifiedCount // ignore: cast_nullable_to_non_nullable
                      as int,
            requestsByTier: null == requestsByTier
                ? _value.requestsByTier
                : requestsByTier // ignore: cast_nullable_to_non_nullable
                      as Map<String, int>,
            requestsByStatus: null == requestsByStatus
                ? _value.requestsByStatus
                : requestsByStatus // ignore: cast_nullable_to_non_nullable
                      as Map<String, int>,
            lastUpdated: null == lastUpdated
                ? _value.lastUpdated
                : lastUpdated // ignore: cast_nullable_to_non_nullable
                      as DateTime,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$VerificationStatsImplCopyWith<$Res>
    implements $VerificationStatsCopyWith<$Res> {
  factory _$$VerificationStatsImplCopyWith(
    _$VerificationStatsImpl value,
    $Res Function(_$VerificationStatsImpl) then,
  ) = __$$VerificationStatsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int totalRequests,
    int pendingRequests,
    int approvedRequests,
    int rejectedRequests,
    int billionaireCount,
    int celebrityCount,
    int generalVerifiedCount,
    Map<String, int> requestsByTier,
    Map<String, int> requestsByStatus,
    DateTime lastUpdated,
  });
}

/// @nodoc
class __$$VerificationStatsImplCopyWithImpl<$Res>
    extends _$VerificationStatsCopyWithImpl<$Res, _$VerificationStatsImpl>
    implements _$$VerificationStatsImplCopyWith<$Res> {
  __$$VerificationStatsImplCopyWithImpl(
    _$VerificationStatsImpl _value,
    $Res Function(_$VerificationStatsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of VerificationStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalRequests = null,
    Object? pendingRequests = null,
    Object? approvedRequests = null,
    Object? rejectedRequests = null,
    Object? billionaireCount = null,
    Object? celebrityCount = null,
    Object? generalVerifiedCount = null,
    Object? requestsByTier = null,
    Object? requestsByStatus = null,
    Object? lastUpdated = null,
  }) {
    return _then(
      _$VerificationStatsImpl(
        totalRequests: null == totalRequests
            ? _value.totalRequests
            : totalRequests // ignore: cast_nullable_to_non_nullable
                  as int,
        pendingRequests: null == pendingRequests
            ? _value.pendingRequests
            : pendingRequests // ignore: cast_nullable_to_non_nullable
                  as int,
        approvedRequests: null == approvedRequests
            ? _value.approvedRequests
            : approvedRequests // ignore: cast_nullable_to_non_nullable
                  as int,
        rejectedRequests: null == rejectedRequests
            ? _value.rejectedRequests
            : rejectedRequests // ignore: cast_nullable_to_non_nullable
                  as int,
        billionaireCount: null == billionaireCount
            ? _value.billionaireCount
            : billionaireCount // ignore: cast_nullable_to_non_nullable
                  as int,
        celebrityCount: null == celebrityCount
            ? _value.celebrityCount
            : celebrityCount // ignore: cast_nullable_to_non_nullable
                  as int,
        generalVerifiedCount: null == generalVerifiedCount
            ? _value.generalVerifiedCount
            : generalVerifiedCount // ignore: cast_nullable_to_non_nullable
                  as int,
        requestsByTier: null == requestsByTier
            ? _value._requestsByTier
            : requestsByTier // ignore: cast_nullable_to_non_nullable
                  as Map<String, int>,
        requestsByStatus: null == requestsByStatus
            ? _value._requestsByStatus
            : requestsByStatus // ignore: cast_nullable_to_non_nullable
                  as Map<String, int>,
        lastUpdated: null == lastUpdated
            ? _value.lastUpdated
            : lastUpdated // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$VerificationStatsImpl implements _VerificationStats {
  const _$VerificationStatsImpl({
    required this.totalRequests,
    required this.pendingRequests,
    required this.approvedRequests,
    required this.rejectedRequests,
    required this.billionaireCount,
    required this.celebrityCount,
    required this.generalVerifiedCount,
    required final Map<String, int> requestsByTier,
    required final Map<String, int> requestsByStatus,
    required this.lastUpdated,
  }) : _requestsByTier = requestsByTier,
       _requestsByStatus = requestsByStatus;

  factory _$VerificationStatsImpl.fromJson(Map<String, dynamic> json) =>
      _$$VerificationStatsImplFromJson(json);

  @override
  final int totalRequests;
  @override
  final int pendingRequests;
  @override
  final int approvedRequests;
  @override
  final int rejectedRequests;
  @override
  final int billionaireCount;
  @override
  final int celebrityCount;
  @override
  final int generalVerifiedCount;
  final Map<String, int> _requestsByTier;
  @override
  Map<String, int> get requestsByTier {
    if (_requestsByTier is EqualUnmodifiableMapView) return _requestsByTier;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_requestsByTier);
  }

  final Map<String, int> _requestsByStatus;
  @override
  Map<String, int> get requestsByStatus {
    if (_requestsByStatus is EqualUnmodifiableMapView) return _requestsByStatus;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_requestsByStatus);
  }

  @override
  final DateTime lastUpdated;

  @override
  String toString() {
    return 'VerificationStats(totalRequests: $totalRequests, pendingRequests: $pendingRequests, approvedRequests: $approvedRequests, rejectedRequests: $rejectedRequests, billionaireCount: $billionaireCount, celebrityCount: $celebrityCount, generalVerifiedCount: $generalVerifiedCount, requestsByTier: $requestsByTier, requestsByStatus: $requestsByStatus, lastUpdated: $lastUpdated)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerificationStatsImpl &&
            (identical(other.totalRequests, totalRequests) ||
                other.totalRequests == totalRequests) &&
            (identical(other.pendingRequests, pendingRequests) ||
                other.pendingRequests == pendingRequests) &&
            (identical(other.approvedRequests, approvedRequests) ||
                other.approvedRequests == approvedRequests) &&
            (identical(other.rejectedRequests, rejectedRequests) ||
                other.rejectedRequests == rejectedRequests) &&
            (identical(other.billionaireCount, billionaireCount) ||
                other.billionaireCount == billionaireCount) &&
            (identical(other.celebrityCount, celebrityCount) ||
                other.celebrityCount == celebrityCount) &&
            (identical(other.generalVerifiedCount, generalVerifiedCount) ||
                other.generalVerifiedCount == generalVerifiedCount) &&
            const DeepCollectionEquality().equals(
              other._requestsByTier,
              _requestsByTier,
            ) &&
            const DeepCollectionEquality().equals(
              other._requestsByStatus,
              _requestsByStatus,
            ) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    totalRequests,
    pendingRequests,
    approvedRequests,
    rejectedRequests,
    billionaireCount,
    celebrityCount,
    generalVerifiedCount,
    const DeepCollectionEquality().hash(_requestsByTier),
    const DeepCollectionEquality().hash(_requestsByStatus),
    lastUpdated,
  );

  /// Create a copy of VerificationStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VerificationStatsImplCopyWith<_$VerificationStatsImpl> get copyWith =>
      __$$VerificationStatsImplCopyWithImpl<_$VerificationStatsImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$VerificationStatsImplToJson(this);
  }
}

abstract class _VerificationStats implements VerificationStats {
  const factory _VerificationStats({
    required final int totalRequests,
    required final int pendingRequests,
    required final int approvedRequests,
    required final int rejectedRequests,
    required final int billionaireCount,
    required final int celebrityCount,
    required final int generalVerifiedCount,
    required final Map<String, int> requestsByTier,
    required final Map<String, int> requestsByStatus,
    required final DateTime lastUpdated,
  }) = _$VerificationStatsImpl;

  factory _VerificationStats.fromJson(Map<String, dynamic> json) =
      _$VerificationStatsImpl.fromJson;

  @override
  int get totalRequests;
  @override
  int get pendingRequests;
  @override
  int get approvedRequests;
  @override
  int get rejectedRequests;
  @override
  int get billionaireCount;
  @override
  int get celebrityCount;
  @override
  int get generalVerifiedCount;
  @override
  Map<String, int> get requestsByTier;
  @override
  Map<String, int> get requestsByStatus;
  @override
  DateTime get lastUpdated;

  /// Create a copy of VerificationStats
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VerificationStatsImplCopyWith<_$VerificationStatsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
