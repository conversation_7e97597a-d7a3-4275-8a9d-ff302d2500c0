// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'verification_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$VerificationRequestImpl _$$VerificationRequestImplFromJson(
  Map<String, dynamic> json,
) => _$VerificationRequestImpl(
  id: json['id'] as String,
  userId: json['userId'] as String,
  username: json['username'] as String,
  userEmail: json['userEmail'] as String,
  requestedTier: $enumDecode(_$VerificationTierEnumMap, json['requestedTier']),
  status: $enumDecode(_$VerificationStatusEnumMap, json['status']),
  submittedAt: DateTime.parse(json['submittedAt'] as String),
  reviewedAt: json['reviewedAt'] == null
      ? null
      : DateTime.parse(json['reviewedAt'] as String),
  reviewedBy: json['reviewedBy'] as String?,
  rejectionReason: json['rejectionReason'] as String?,
  fullName: json['fullName'] as String,
  profession: json['profession'] as String,
  company: json['company'] as String?,
  website: json['website'] as String?,
  linkedinUrl: json['linkedinUrl'] as String?,
  twitterUrl: json['twitterUrl'] as String?,
  instagramUrl: json['instagramUrl'] as String?,
  supportingDocuments: (json['supportingDocuments'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  additionalInfo: json['additionalInfo'] as String?,
  netWorthEvidence: json['netWorthEvidence'] as String?,
  businessOwnership: json['businessOwnership'] as String?,
  publicRecognition: json['publicRecognition'] as String?,
  publicProfile: json['publicProfile'] as String?,
  mediaPresence: json['mediaPresence'] as String?,
  followerCount: json['followerCount'] as String?,
  adminNotes: json['adminNotes'] as String?,
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$$VerificationRequestImplToJson(
  _$VerificationRequestImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'username': instance.username,
  'userEmail': instance.userEmail,
  'requestedTier': _$VerificationTierEnumMap[instance.requestedTier]!,
  'status': _$VerificationStatusEnumMap[instance.status]!,
  'submittedAt': instance.submittedAt.toIso8601String(),
  'reviewedAt': instance.reviewedAt?.toIso8601String(),
  'reviewedBy': instance.reviewedBy,
  'rejectionReason': instance.rejectionReason,
  'fullName': instance.fullName,
  'profession': instance.profession,
  'company': instance.company,
  'website': instance.website,
  'linkedinUrl': instance.linkedinUrl,
  'twitterUrl': instance.twitterUrl,
  'instagramUrl': instance.instagramUrl,
  'supportingDocuments': instance.supportingDocuments,
  'additionalInfo': instance.additionalInfo,
  'netWorthEvidence': instance.netWorthEvidence,
  'businessOwnership': instance.businessOwnership,
  'publicRecognition': instance.publicRecognition,
  'publicProfile': instance.publicProfile,
  'mediaPresence': instance.mediaPresence,
  'followerCount': instance.followerCount,
  'adminNotes': instance.adminNotes,
  'metadata': instance.metadata,
};

const _$VerificationTierEnumMap = {
  VerificationTier.general: 'general',
  VerificationTier.celebrity: 'celebrity',
  VerificationTier.billionaire: 'billionaire',
};

const _$VerificationStatusEnumMap = {
  VerificationStatus.pending: 'pending',
  VerificationStatus.approved: 'approved',
  VerificationStatus.rejected: 'rejected',
  VerificationStatus.revoked: 'revoked',
};

_$UserVerificationImpl _$$UserVerificationImplFromJson(
  Map<String, dynamic> json,
) => _$UserVerificationImpl(
  userId: json['userId'] as String,
  isVerified: json['isVerified'] as bool? ?? false,
  isBillionaire: json['isBillionaire'] as bool? ?? false,
  isCelebrity: json['isCelebrity'] as bool? ?? false,
  verificationTier: $enumDecodeNullable(
    _$VerificationTierEnumMap,
    json['verificationTier'],
  ),
  verifiedAt: json['verifiedAt'] == null
      ? null
      : DateTime.parse(json['verifiedAt'] as String),
  verifiedBy: json['verifiedBy'] as String?,
  verificationRequestId: json['verificationRequestId'] as String?,
  customBadgeColor: json['customBadgeColor'] as String?,
  customBadgeIcon: json['customBadgeIcon'] as String?,
  verificationMetadata: json['verificationMetadata'] as Map<String, dynamic>?,
  hasBlueCheckmark: json['hasBlueCheckmark'] as bool? ?? false,
  hasGoldStar: json['hasGoldStar'] as bool? ?? false,
  hasGoldB: json['hasGoldB'] as bool? ?? false,
  canCreateVerifiedContent: json['canCreateVerifiedContent'] as bool? ?? false,
  priorityInSearch: json['priorityInSearch'] as bool? ?? false,
  priorityInFeed: json['priorityInFeed'] as bool? ?? false,
);

Map<String, dynamic> _$$UserVerificationImplToJson(
  _$UserVerificationImpl instance,
) => <String, dynamic>{
  'userId': instance.userId,
  'isVerified': instance.isVerified,
  'isBillionaire': instance.isBillionaire,
  'isCelebrity': instance.isCelebrity,
  'verificationTier': _$VerificationTierEnumMap[instance.verificationTier],
  'verifiedAt': instance.verifiedAt?.toIso8601String(),
  'verifiedBy': instance.verifiedBy,
  'verificationRequestId': instance.verificationRequestId,
  'customBadgeColor': instance.customBadgeColor,
  'customBadgeIcon': instance.customBadgeIcon,
  'verificationMetadata': instance.verificationMetadata,
  'hasBlueCheckmark': instance.hasBlueCheckmark,
  'hasGoldStar': instance.hasGoldStar,
  'hasGoldB': instance.hasGoldB,
  'canCreateVerifiedContent': instance.canCreateVerifiedContent,
  'priorityInSearch': instance.priorityInSearch,
  'priorityInFeed': instance.priorityInFeed,
};

_$VerificationCriteriaImpl _$$VerificationCriteriaImplFromJson(
  Map<String, dynamic> json,
) => _$VerificationCriteriaImpl(
  tier: $enumDecode(_$VerificationTierEnumMap, json['tier']),
  title: json['title'] as String,
  description: json['description'] as String,
  requirements: (json['requirements'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  documentationNeeded: (json['documentationNeeded'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  additionalNotes: json['additionalNotes'] as String?,
  isActive: json['isActive'] as bool? ?? false,
);

Map<String, dynamic> _$$VerificationCriteriaImplToJson(
  _$VerificationCriteriaImpl instance,
) => <String, dynamic>{
  'tier': _$VerificationTierEnumMap[instance.tier]!,
  'title': instance.title,
  'description': instance.description,
  'requirements': instance.requirements,
  'documentationNeeded': instance.documentationNeeded,
  'additionalNotes': instance.additionalNotes,
  'isActive': instance.isActive,
};

_$VerificationBadgeConfigImpl _$$VerificationBadgeConfigImplFromJson(
  Map<String, dynamic> json,
) => _$VerificationBadgeConfigImpl(
  tier: $enumDecode(_$VerificationTierEnumMap, json['tier']),
  iconName: json['iconName'] as String,
  color: json['color'] as String,
  displayName: json['displayName'] as String,
  description: json['description'] as String,
  smallSize: (json['smallSize'] as num?)?.toDouble() ?? 12.0,
  mediumSize: (json['mediumSize'] as num?)?.toDouble() ?? 16.0,
  largeSize: (json['largeSize'] as num?)?.toDouble() ?? 20.0,
);

Map<String, dynamic> _$$VerificationBadgeConfigImplToJson(
  _$VerificationBadgeConfigImpl instance,
) => <String, dynamic>{
  'tier': _$VerificationTierEnumMap[instance.tier]!,
  'iconName': instance.iconName,
  'color': instance.color,
  'displayName': instance.displayName,
  'description': instance.description,
  'smallSize': instance.smallSize,
  'mediumSize': instance.mediumSize,
  'largeSize': instance.largeSize,
};

_$VerificationStatsImpl _$$VerificationStatsImplFromJson(
  Map<String, dynamic> json,
) => _$VerificationStatsImpl(
  totalRequests: (json['totalRequests'] as num).toInt(),
  pendingRequests: (json['pendingRequests'] as num).toInt(),
  approvedRequests: (json['approvedRequests'] as num).toInt(),
  rejectedRequests: (json['rejectedRequests'] as num).toInt(),
  billionaireCount: (json['billionaireCount'] as num).toInt(),
  celebrityCount: (json['celebrityCount'] as num).toInt(),
  generalVerifiedCount: (json['generalVerifiedCount'] as num).toInt(),
  requestsByTier: Map<String, int>.from(json['requestsByTier'] as Map),
  requestsByStatus: Map<String, int>.from(json['requestsByStatus'] as Map),
  lastUpdated: DateTime.parse(json['lastUpdated'] as String),
);

Map<String, dynamic> _$$VerificationStatsImplToJson(
  _$VerificationStatsImpl instance,
) => <String, dynamic>{
  'totalRequests': instance.totalRequests,
  'pendingRequests': instance.pendingRequests,
  'approvedRequests': instance.approvedRequests,
  'rejectedRequests': instance.rejectedRequests,
  'billionaireCount': instance.billionaireCount,
  'celebrityCount': instance.celebrityCount,
  'generalVerifiedCount': instance.generalVerifiedCount,
  'requestsByTier': instance.requestsByTier,
  'requestsByStatus': instance.requestsByStatus,
  'lastUpdated': instance.lastUpdated.toIso8601String(),
};
