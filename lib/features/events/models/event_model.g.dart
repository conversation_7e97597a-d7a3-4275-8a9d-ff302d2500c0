// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'event_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$EventModelImpl _$$EventModelImplFromJson(Map<String, dynamic> json) =>
    _$EventModelImpl(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      imageUrl: json['imageUrl'] as String,
      dateTime: DateTime.parse(json['dateTime'] as String),
      location: json['location'] as String,
      price: (json['price'] as num).toDouble(),
      isVerified: json['isVerified'] as bool? ?? false,
      isExclusive: json['isExclusive'] as bool? ?? false,
      hostId: json['hostId'] as String,
      host: EventHost.fromJson(json['host'] as Map<String, dynamic>),
      capacity: (json['capacity'] as num).toInt(),
      attendeeIds:
          (json['attendeeIds'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$$EventModelImplToJson(_$EventModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'imageUrl': instance.imageUrl,
      'dateTime': instance.dateTime.toIso8601String(),
      'location': instance.location,
      'price': instance.price,
      'isVerified': instance.isVerified,
      'isExclusive': instance.isExclusive,
      'hostId': instance.hostId,
      'host': instance.host,
      'capacity': instance.capacity,
      'attendeeIds': instance.attendeeIds,
      'createdAt': instance.createdAt?.toIso8601String(),
    };

_$EventHostImpl _$$EventHostImplFromJson(Map<String, dynamic> json) =>
    _$EventHostImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      avatarUrl: json['avatarUrl'] as String,
      isVerified: json['isVerified'] as bool? ?? false,
    );

Map<String, dynamic> _$$EventHostImplToJson(_$EventHostImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'avatarUrl': instance.avatarUrl,
      'isVerified': instance.isVerified,
    };
