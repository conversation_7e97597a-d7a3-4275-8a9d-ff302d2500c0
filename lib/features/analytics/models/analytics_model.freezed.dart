// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'analytics_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

AnalyticsData _$AnalyticsDataFromJson(Map<String, dynamic> json) {
  return _AnalyticsData.fromJson(json);
}

/// @nodoc
mixin _$AnalyticsData {
  int get profileVisits => throw _privateConstructorUsedError;
  int get postsCount => throw _privateConstructorUsedError;
  int get productsListed => throw _privateConstructorUsedError;
  int get eventsCreated => throw _privateConstructorUsedError;
  double get totalSales => throw _privateConstructorUsedError;
  List<FollowerDataPoint> get followerGains =>
      throw _privateConstructorUsedError;

  /// Serializes this AnalyticsData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AnalyticsData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AnalyticsDataCopyWith<AnalyticsData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AnalyticsDataCopyWith<$Res> {
  factory $AnalyticsDataCopyWith(
    AnalyticsData value,
    $Res Function(AnalyticsData) then,
  ) = _$AnalyticsDataCopyWithImpl<$Res, AnalyticsData>;
  @useResult
  $Res call({
    int profileVisits,
    int postsCount,
    int productsListed,
    int eventsCreated,
    double totalSales,
    List<FollowerDataPoint> followerGains,
  });
}

/// @nodoc
class _$AnalyticsDataCopyWithImpl<$Res, $Val extends AnalyticsData>
    implements $AnalyticsDataCopyWith<$Res> {
  _$AnalyticsDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AnalyticsData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? profileVisits = null,
    Object? postsCount = null,
    Object? productsListed = null,
    Object? eventsCreated = null,
    Object? totalSales = null,
    Object? followerGains = null,
  }) {
    return _then(
      _value.copyWith(
            profileVisits: null == profileVisits
                ? _value.profileVisits
                : profileVisits // ignore: cast_nullable_to_non_nullable
                      as int,
            postsCount: null == postsCount
                ? _value.postsCount
                : postsCount // ignore: cast_nullable_to_non_nullable
                      as int,
            productsListed: null == productsListed
                ? _value.productsListed
                : productsListed // ignore: cast_nullable_to_non_nullable
                      as int,
            eventsCreated: null == eventsCreated
                ? _value.eventsCreated
                : eventsCreated // ignore: cast_nullable_to_non_nullable
                      as int,
            totalSales: null == totalSales
                ? _value.totalSales
                : totalSales // ignore: cast_nullable_to_non_nullable
                      as double,
            followerGains: null == followerGains
                ? _value.followerGains
                : followerGains // ignore: cast_nullable_to_non_nullable
                      as List<FollowerDataPoint>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$AnalyticsDataImplCopyWith<$Res>
    implements $AnalyticsDataCopyWith<$Res> {
  factory _$$AnalyticsDataImplCopyWith(
    _$AnalyticsDataImpl value,
    $Res Function(_$AnalyticsDataImpl) then,
  ) = __$$AnalyticsDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int profileVisits,
    int postsCount,
    int productsListed,
    int eventsCreated,
    double totalSales,
    List<FollowerDataPoint> followerGains,
  });
}

/// @nodoc
class __$$AnalyticsDataImplCopyWithImpl<$Res>
    extends _$AnalyticsDataCopyWithImpl<$Res, _$AnalyticsDataImpl>
    implements _$$AnalyticsDataImplCopyWith<$Res> {
  __$$AnalyticsDataImplCopyWithImpl(
    _$AnalyticsDataImpl _value,
    $Res Function(_$AnalyticsDataImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AnalyticsData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? profileVisits = null,
    Object? postsCount = null,
    Object? productsListed = null,
    Object? eventsCreated = null,
    Object? totalSales = null,
    Object? followerGains = null,
  }) {
    return _then(
      _$AnalyticsDataImpl(
        profileVisits: null == profileVisits
            ? _value.profileVisits
            : profileVisits // ignore: cast_nullable_to_non_nullable
                  as int,
        postsCount: null == postsCount
            ? _value.postsCount
            : postsCount // ignore: cast_nullable_to_non_nullable
                  as int,
        productsListed: null == productsListed
            ? _value.productsListed
            : productsListed // ignore: cast_nullable_to_non_nullable
                  as int,
        eventsCreated: null == eventsCreated
            ? _value.eventsCreated
            : eventsCreated // ignore: cast_nullable_to_non_nullable
                  as int,
        totalSales: null == totalSales
            ? _value.totalSales
            : totalSales // ignore: cast_nullable_to_non_nullable
                  as double,
        followerGains: null == followerGains
            ? _value._followerGains
            : followerGains // ignore: cast_nullable_to_non_nullable
                  as List<FollowerDataPoint>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$AnalyticsDataImpl implements _AnalyticsData {
  const _$AnalyticsDataImpl({
    required this.profileVisits,
    required this.postsCount,
    required this.productsListed,
    required this.eventsCreated,
    required this.totalSales,
    required final List<FollowerDataPoint> followerGains,
  }) : _followerGains = followerGains;

  factory _$AnalyticsDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$AnalyticsDataImplFromJson(json);

  @override
  final int profileVisits;
  @override
  final int postsCount;
  @override
  final int productsListed;
  @override
  final int eventsCreated;
  @override
  final double totalSales;
  final List<FollowerDataPoint> _followerGains;
  @override
  List<FollowerDataPoint> get followerGains {
    if (_followerGains is EqualUnmodifiableListView) return _followerGains;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_followerGains);
  }

  @override
  String toString() {
    return 'AnalyticsData(profileVisits: $profileVisits, postsCount: $postsCount, productsListed: $productsListed, eventsCreated: $eventsCreated, totalSales: $totalSales, followerGains: $followerGains)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AnalyticsDataImpl &&
            (identical(other.profileVisits, profileVisits) ||
                other.profileVisits == profileVisits) &&
            (identical(other.postsCount, postsCount) ||
                other.postsCount == postsCount) &&
            (identical(other.productsListed, productsListed) ||
                other.productsListed == productsListed) &&
            (identical(other.eventsCreated, eventsCreated) ||
                other.eventsCreated == eventsCreated) &&
            (identical(other.totalSales, totalSales) ||
                other.totalSales == totalSales) &&
            const DeepCollectionEquality().equals(
              other._followerGains,
              _followerGains,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    profileVisits,
    postsCount,
    productsListed,
    eventsCreated,
    totalSales,
    const DeepCollectionEquality().hash(_followerGains),
  );

  /// Create a copy of AnalyticsData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AnalyticsDataImplCopyWith<_$AnalyticsDataImpl> get copyWith =>
      __$$AnalyticsDataImplCopyWithImpl<_$AnalyticsDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AnalyticsDataImplToJson(this);
  }
}

abstract class _AnalyticsData implements AnalyticsData {
  const factory _AnalyticsData({
    required final int profileVisits,
    required final int postsCount,
    required final int productsListed,
    required final int eventsCreated,
    required final double totalSales,
    required final List<FollowerDataPoint> followerGains,
  }) = _$AnalyticsDataImpl;

  factory _AnalyticsData.fromJson(Map<String, dynamic> json) =
      _$AnalyticsDataImpl.fromJson;

  @override
  int get profileVisits;
  @override
  int get postsCount;
  @override
  int get productsListed;
  @override
  int get eventsCreated;
  @override
  double get totalSales;
  @override
  List<FollowerDataPoint> get followerGains;

  /// Create a copy of AnalyticsData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AnalyticsDataImplCopyWith<_$AnalyticsDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

FollowerDataPoint _$FollowerDataPointFromJson(Map<String, dynamic> json) {
  return _FollowerDataPoint.fromJson(json);
}

/// @nodoc
mixin _$FollowerDataPoint {
  DateTime get date => throw _privateConstructorUsedError;
  int get count => throw _privateConstructorUsedError;

  /// Serializes this FollowerDataPoint to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FollowerDataPoint
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FollowerDataPointCopyWith<FollowerDataPoint> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FollowerDataPointCopyWith<$Res> {
  factory $FollowerDataPointCopyWith(
    FollowerDataPoint value,
    $Res Function(FollowerDataPoint) then,
  ) = _$FollowerDataPointCopyWithImpl<$Res, FollowerDataPoint>;
  @useResult
  $Res call({DateTime date, int count});
}

/// @nodoc
class _$FollowerDataPointCopyWithImpl<$Res, $Val extends FollowerDataPoint>
    implements $FollowerDataPointCopyWith<$Res> {
  _$FollowerDataPointCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FollowerDataPoint
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? date = null, Object? count = null}) {
    return _then(
      _value.copyWith(
            date: null == date
                ? _value.date
                : date // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            count: null == count
                ? _value.count
                : count // ignore: cast_nullable_to_non_nullable
                      as int,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$FollowerDataPointImplCopyWith<$Res>
    implements $FollowerDataPointCopyWith<$Res> {
  factory _$$FollowerDataPointImplCopyWith(
    _$FollowerDataPointImpl value,
    $Res Function(_$FollowerDataPointImpl) then,
  ) = __$$FollowerDataPointImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({DateTime date, int count});
}

/// @nodoc
class __$$FollowerDataPointImplCopyWithImpl<$Res>
    extends _$FollowerDataPointCopyWithImpl<$Res, _$FollowerDataPointImpl>
    implements _$$FollowerDataPointImplCopyWith<$Res> {
  __$$FollowerDataPointImplCopyWithImpl(
    _$FollowerDataPointImpl _value,
    $Res Function(_$FollowerDataPointImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of FollowerDataPoint
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? date = null, Object? count = null}) {
    return _then(
      _$FollowerDataPointImpl(
        date: null == date
            ? _value.date
            : date // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        count: null == count
            ? _value.count
            : count // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$FollowerDataPointImpl implements _FollowerDataPoint {
  const _$FollowerDataPointImpl({required this.date, required this.count});

  factory _$FollowerDataPointImpl.fromJson(Map<String, dynamic> json) =>
      _$$FollowerDataPointImplFromJson(json);

  @override
  final DateTime date;
  @override
  final int count;

  @override
  String toString() {
    return 'FollowerDataPoint(date: $date, count: $count)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FollowerDataPointImpl &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.count, count) || other.count == count));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, date, count);

  /// Create a copy of FollowerDataPoint
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FollowerDataPointImplCopyWith<_$FollowerDataPointImpl> get copyWith =>
      __$$FollowerDataPointImplCopyWithImpl<_$FollowerDataPointImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$FollowerDataPointImplToJson(this);
  }
}

abstract class _FollowerDataPoint implements FollowerDataPoint {
  const factory _FollowerDataPoint({
    required final DateTime date,
    required final int count,
  }) = _$FollowerDataPointImpl;

  factory _FollowerDataPoint.fromJson(Map<String, dynamic> json) =
      _$FollowerDataPointImpl.fromJson;

  @override
  DateTime get date;
  @override
  int get count;

  /// Create a copy of FollowerDataPoint
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FollowerDataPointImplCopyWith<_$FollowerDataPointImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
