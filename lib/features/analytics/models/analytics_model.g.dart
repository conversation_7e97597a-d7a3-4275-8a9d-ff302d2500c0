// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'analytics_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AnalyticsDataImpl _$$AnalyticsDataImplFromJson(Map<String, dynamic> json) =>
    _$AnalyticsDataImpl(
      profileVisits: (json['profileVisits'] as num).toInt(),
      postsCount: (json['postsCount'] as num).toInt(),
      productsListed: (json['productsListed'] as num).toInt(),
      eventsCreated: (json['eventsCreated'] as num).toInt(),
      totalSales: (json['totalSales'] as num).toDouble(),
      followerGains: (json['followerGains'] as List<dynamic>)
          .map((e) => FollowerDataPoint.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$AnalyticsDataImplToJson(_$AnalyticsDataImpl instance) =>
    <String, dynamic>{
      'profileVisits': instance.profileVisits,
      'postsCount': instance.postsCount,
      'productsListed': instance.productsListed,
      'eventsCreated': instance.eventsCreated,
      'totalSales': instance.totalSales,
      'followerGains': instance.followerGains,
    };

_$FollowerDataPointImpl _$$FollowerDataPointImplFromJson(
  Map<String, dynamic> json,
) => _$FollowerDataPointImpl(
  date: DateTime.parse(json['date'] as String),
  count: (json['count'] as num).toInt(),
);

Map<String, dynamic> _$$FollowerDataPointImplToJson(
  _$FollowerDataPointImpl instance,
) => <String, dynamic>{
  'date': instance.date.toIso8601String(),
  'count': instance.count,
};
