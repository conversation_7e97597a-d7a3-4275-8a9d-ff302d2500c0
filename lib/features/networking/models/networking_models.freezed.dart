// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'networking_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

NetworkConnection _$NetworkConnectionFromJson(Map<String, dynamic> json) {
  return _NetworkConnection.fromJson(json);
}

/// @nodoc
mixin _$NetworkConnection {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get connectedUserId => throw _privateConstructorUsedError;
  ConnectionStatus get status => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime? get acceptedAt => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;
  NetworkConnectionType get type => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this NetworkConnection to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of NetworkConnection
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $NetworkConnectionCopyWith<NetworkConnection> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NetworkConnectionCopyWith<$Res> {
  factory $NetworkConnectionCopyWith(
    NetworkConnection value,
    $Res Function(NetworkConnection) then,
  ) = _$NetworkConnectionCopyWithImpl<$Res, NetworkConnection>;
  @useResult
  $Res call({
    String id,
    String userId,
    String connectedUserId,
    ConnectionStatus status,
    DateTime createdAt,
    DateTime? acceptedAt,
    String? message,
    NetworkConnectionType type,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class _$NetworkConnectionCopyWithImpl<$Res, $Val extends NetworkConnection>
    implements $NetworkConnectionCopyWith<$Res> {
  _$NetworkConnectionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NetworkConnection
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? connectedUserId = null,
    Object? status = null,
    Object? createdAt = null,
    Object? acceptedAt = freezed,
    Object? message = freezed,
    Object? type = null,
    Object? metadata = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            connectedUserId: null == connectedUserId
                ? _value.connectedUserId
                : connectedUserId // ignore: cast_nullable_to_non_nullable
                      as String,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as ConnectionStatus,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            acceptedAt: freezed == acceptedAt
                ? _value.acceptedAt
                : acceptedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            message: freezed == message
                ? _value.message
                : message // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as NetworkConnectionType,
            metadata: freezed == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$NetworkConnectionImplCopyWith<$Res>
    implements $NetworkConnectionCopyWith<$Res> {
  factory _$$NetworkConnectionImplCopyWith(
    _$NetworkConnectionImpl value,
    $Res Function(_$NetworkConnectionImpl) then,
  ) = __$$NetworkConnectionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    String connectedUserId,
    ConnectionStatus status,
    DateTime createdAt,
    DateTime? acceptedAt,
    String? message,
    NetworkConnectionType type,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class __$$NetworkConnectionImplCopyWithImpl<$Res>
    extends _$NetworkConnectionCopyWithImpl<$Res, _$NetworkConnectionImpl>
    implements _$$NetworkConnectionImplCopyWith<$Res> {
  __$$NetworkConnectionImplCopyWithImpl(
    _$NetworkConnectionImpl _value,
    $Res Function(_$NetworkConnectionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NetworkConnection
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? connectedUserId = null,
    Object? status = null,
    Object? createdAt = null,
    Object? acceptedAt = freezed,
    Object? message = freezed,
    Object? type = null,
    Object? metadata = freezed,
  }) {
    return _then(
      _$NetworkConnectionImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        connectedUserId: null == connectedUserId
            ? _value.connectedUserId
            : connectedUserId // ignore: cast_nullable_to_non_nullable
                  as String,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as ConnectionStatus,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        acceptedAt: freezed == acceptedAt
            ? _value.acceptedAt
            : acceptedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        message: freezed == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as NetworkConnectionType,
        metadata: freezed == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$NetworkConnectionImpl implements _NetworkConnection {
  const _$NetworkConnectionImpl({
    required this.id,
    required this.userId,
    required this.connectedUserId,
    required this.status,
    required this.createdAt,
    this.acceptedAt,
    this.message,
    required this.type,
    final Map<String, dynamic>? metadata,
  }) : _metadata = metadata;

  factory _$NetworkConnectionImpl.fromJson(Map<String, dynamic> json) =>
      _$$NetworkConnectionImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String connectedUserId;
  @override
  final ConnectionStatus status;
  @override
  final DateTime createdAt;
  @override
  final DateTime? acceptedAt;
  @override
  final String? message;
  @override
  final NetworkConnectionType type;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'NetworkConnection(id: $id, userId: $userId, connectedUserId: $connectedUserId, status: $status, createdAt: $createdAt, acceptedAt: $acceptedAt, message: $message, type: $type, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NetworkConnectionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.connectedUserId, connectedUserId) ||
                other.connectedUserId == connectedUserId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.acceptedAt, acceptedAt) ||
                other.acceptedAt == acceptedAt) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    userId,
    connectedUserId,
    status,
    createdAt,
    acceptedAt,
    message,
    type,
    const DeepCollectionEquality().hash(_metadata),
  );

  /// Create a copy of NetworkConnection
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NetworkConnectionImplCopyWith<_$NetworkConnectionImpl> get copyWith =>
      __$$NetworkConnectionImplCopyWithImpl<_$NetworkConnectionImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$NetworkConnectionImplToJson(this);
  }
}

abstract class _NetworkConnection implements NetworkConnection {
  const factory _NetworkConnection({
    required final String id,
    required final String userId,
    required final String connectedUserId,
    required final ConnectionStatus status,
    required final DateTime createdAt,
    final DateTime? acceptedAt,
    final String? message,
    required final NetworkConnectionType type,
    final Map<String, dynamic>? metadata,
  }) = _$NetworkConnectionImpl;

  factory _NetworkConnection.fromJson(Map<String, dynamic> json) =
      _$NetworkConnectionImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get connectedUserId;
  @override
  ConnectionStatus get status;
  @override
  DateTime get createdAt;
  @override
  DateTime? get acceptedAt;
  @override
  String? get message;
  @override
  NetworkConnectionType get type;
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of NetworkConnection
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NetworkConnectionImplCopyWith<_$NetworkConnectionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

NetworkProfile _$NetworkProfileFromJson(Map<String, dynamic> json) {
  return _NetworkProfile.fromJson(json);
}

/// @nodoc
mixin _$NetworkProfile {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get company => throw _privateConstructorUsedError;
  String? get bio => throw _privateConstructorUsedError;
  String? get profilePictureUrl => throw _privateConstructorUsedError;
  String? get coverPhotoUrl => throw _privateConstructorUsedError;
  List<String> get industries => throw _privateConstructorUsedError;
  List<String> get skills => throw _privateConstructorUsedError;
  List<String> get interests => throw _privateConstructorUsedError;
  String? get location => throw _privateConstructorUsedError;
  String? get website => throw _privateConstructorUsedError;
  String? get linkedinUrl => throw _privateConstructorUsedError;
  String? get twitterUrl => throw _privateConstructorUsedError;
  String? get instagramUrl => throw _privateConstructorUsedError;
  NetworkProfileVisibility get visibility => throw _privateConstructorUsedError;
  bool get isVerified => throw _privateConstructorUsedError;
  bool get isPremium => throw _privateConstructorUsedError;
  int get connectionCount => throw _privateConstructorUsedError;
  int get mutualConnectionCount => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime? get lastActiveAt => throw _privateConstructorUsedError;
  Map<String, dynamic>? get achievements => throw _privateConstructorUsedError;
  List<String>? get certifications => throw _privateConstructorUsedError;
  List<String>? get languages => throw _privateConstructorUsedError;
  String? get education => throw _privateConstructorUsedError;
  String? get experience => throw _privateConstructorUsedError;
  Map<String, dynamic>? get preferences => throw _privateConstructorUsedError;

  /// Serializes this NetworkProfile to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of NetworkProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $NetworkProfileCopyWith<NetworkProfile> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NetworkProfileCopyWith<$Res> {
  factory $NetworkProfileCopyWith(
    NetworkProfile value,
    $Res Function(NetworkProfile) then,
  ) = _$NetworkProfileCopyWithImpl<$Res, NetworkProfile>;
  @useResult
  $Res call({
    String id,
    String userId,
    String name,
    String title,
    String company,
    String? bio,
    String? profilePictureUrl,
    String? coverPhotoUrl,
    List<String> industries,
    List<String> skills,
    List<String> interests,
    String? location,
    String? website,
    String? linkedinUrl,
    String? twitterUrl,
    String? instagramUrl,
    NetworkProfileVisibility visibility,
    bool isVerified,
    bool isPremium,
    int connectionCount,
    int mutualConnectionCount,
    DateTime createdAt,
    DateTime? lastActiveAt,
    Map<String, dynamic>? achievements,
    List<String>? certifications,
    List<String>? languages,
    String? education,
    String? experience,
    Map<String, dynamic>? preferences,
  });
}

/// @nodoc
class _$NetworkProfileCopyWithImpl<$Res, $Val extends NetworkProfile>
    implements $NetworkProfileCopyWith<$Res> {
  _$NetworkProfileCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NetworkProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? name = null,
    Object? title = null,
    Object? company = null,
    Object? bio = freezed,
    Object? profilePictureUrl = freezed,
    Object? coverPhotoUrl = freezed,
    Object? industries = null,
    Object? skills = null,
    Object? interests = null,
    Object? location = freezed,
    Object? website = freezed,
    Object? linkedinUrl = freezed,
    Object? twitterUrl = freezed,
    Object? instagramUrl = freezed,
    Object? visibility = null,
    Object? isVerified = null,
    Object? isPremium = null,
    Object? connectionCount = null,
    Object? mutualConnectionCount = null,
    Object? createdAt = null,
    Object? lastActiveAt = freezed,
    Object? achievements = freezed,
    Object? certifications = freezed,
    Object? languages = freezed,
    Object? education = freezed,
    Object? experience = freezed,
    Object? preferences = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            company: null == company
                ? _value.company
                : company // ignore: cast_nullable_to_non_nullable
                      as String,
            bio: freezed == bio
                ? _value.bio
                : bio // ignore: cast_nullable_to_non_nullable
                      as String?,
            profilePictureUrl: freezed == profilePictureUrl
                ? _value.profilePictureUrl
                : profilePictureUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            coverPhotoUrl: freezed == coverPhotoUrl
                ? _value.coverPhotoUrl
                : coverPhotoUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            industries: null == industries
                ? _value.industries
                : industries // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            skills: null == skills
                ? _value.skills
                : skills // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            interests: null == interests
                ? _value.interests
                : interests // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            location: freezed == location
                ? _value.location
                : location // ignore: cast_nullable_to_non_nullable
                      as String?,
            website: freezed == website
                ? _value.website
                : website // ignore: cast_nullable_to_non_nullable
                      as String?,
            linkedinUrl: freezed == linkedinUrl
                ? _value.linkedinUrl
                : linkedinUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            twitterUrl: freezed == twitterUrl
                ? _value.twitterUrl
                : twitterUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            instagramUrl: freezed == instagramUrl
                ? _value.instagramUrl
                : instagramUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            visibility: null == visibility
                ? _value.visibility
                : visibility // ignore: cast_nullable_to_non_nullable
                      as NetworkProfileVisibility,
            isVerified: null == isVerified
                ? _value.isVerified
                : isVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            isPremium: null == isPremium
                ? _value.isPremium
                : isPremium // ignore: cast_nullable_to_non_nullable
                      as bool,
            connectionCount: null == connectionCount
                ? _value.connectionCount
                : connectionCount // ignore: cast_nullable_to_non_nullable
                      as int,
            mutualConnectionCount: null == mutualConnectionCount
                ? _value.mutualConnectionCount
                : mutualConnectionCount // ignore: cast_nullable_to_non_nullable
                      as int,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            lastActiveAt: freezed == lastActiveAt
                ? _value.lastActiveAt
                : lastActiveAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            achievements: freezed == achievements
                ? _value.achievements
                : achievements // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
            certifications: freezed == certifications
                ? _value.certifications
                : certifications // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            languages: freezed == languages
                ? _value.languages
                : languages // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            education: freezed == education
                ? _value.education
                : education // ignore: cast_nullable_to_non_nullable
                      as String?,
            experience: freezed == experience
                ? _value.experience
                : experience // ignore: cast_nullable_to_non_nullable
                      as String?,
            preferences: freezed == preferences
                ? _value.preferences
                : preferences // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$NetworkProfileImplCopyWith<$Res>
    implements $NetworkProfileCopyWith<$Res> {
  factory _$$NetworkProfileImplCopyWith(
    _$NetworkProfileImpl value,
    $Res Function(_$NetworkProfileImpl) then,
  ) = __$$NetworkProfileImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    String name,
    String title,
    String company,
    String? bio,
    String? profilePictureUrl,
    String? coverPhotoUrl,
    List<String> industries,
    List<String> skills,
    List<String> interests,
    String? location,
    String? website,
    String? linkedinUrl,
    String? twitterUrl,
    String? instagramUrl,
    NetworkProfileVisibility visibility,
    bool isVerified,
    bool isPremium,
    int connectionCount,
    int mutualConnectionCount,
    DateTime createdAt,
    DateTime? lastActiveAt,
    Map<String, dynamic>? achievements,
    List<String>? certifications,
    List<String>? languages,
    String? education,
    String? experience,
    Map<String, dynamic>? preferences,
  });
}

/// @nodoc
class __$$NetworkProfileImplCopyWithImpl<$Res>
    extends _$NetworkProfileCopyWithImpl<$Res, _$NetworkProfileImpl>
    implements _$$NetworkProfileImplCopyWith<$Res> {
  __$$NetworkProfileImplCopyWithImpl(
    _$NetworkProfileImpl _value,
    $Res Function(_$NetworkProfileImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NetworkProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? name = null,
    Object? title = null,
    Object? company = null,
    Object? bio = freezed,
    Object? profilePictureUrl = freezed,
    Object? coverPhotoUrl = freezed,
    Object? industries = null,
    Object? skills = null,
    Object? interests = null,
    Object? location = freezed,
    Object? website = freezed,
    Object? linkedinUrl = freezed,
    Object? twitterUrl = freezed,
    Object? instagramUrl = freezed,
    Object? visibility = null,
    Object? isVerified = null,
    Object? isPremium = null,
    Object? connectionCount = null,
    Object? mutualConnectionCount = null,
    Object? createdAt = null,
    Object? lastActiveAt = freezed,
    Object? achievements = freezed,
    Object? certifications = freezed,
    Object? languages = freezed,
    Object? education = freezed,
    Object? experience = freezed,
    Object? preferences = freezed,
  }) {
    return _then(
      _$NetworkProfileImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        company: null == company
            ? _value.company
            : company // ignore: cast_nullable_to_non_nullable
                  as String,
        bio: freezed == bio
            ? _value.bio
            : bio // ignore: cast_nullable_to_non_nullable
                  as String?,
        profilePictureUrl: freezed == profilePictureUrl
            ? _value.profilePictureUrl
            : profilePictureUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        coverPhotoUrl: freezed == coverPhotoUrl
            ? _value.coverPhotoUrl
            : coverPhotoUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        industries: null == industries
            ? _value._industries
            : industries // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        skills: null == skills
            ? _value._skills
            : skills // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        interests: null == interests
            ? _value._interests
            : interests // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        location: freezed == location
            ? _value.location
            : location // ignore: cast_nullable_to_non_nullable
                  as String?,
        website: freezed == website
            ? _value.website
            : website // ignore: cast_nullable_to_non_nullable
                  as String?,
        linkedinUrl: freezed == linkedinUrl
            ? _value.linkedinUrl
            : linkedinUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        twitterUrl: freezed == twitterUrl
            ? _value.twitterUrl
            : twitterUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        instagramUrl: freezed == instagramUrl
            ? _value.instagramUrl
            : instagramUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        visibility: null == visibility
            ? _value.visibility
            : visibility // ignore: cast_nullable_to_non_nullable
                  as NetworkProfileVisibility,
        isVerified: null == isVerified
            ? _value.isVerified
            : isVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        isPremium: null == isPremium
            ? _value.isPremium
            : isPremium // ignore: cast_nullable_to_non_nullable
                  as bool,
        connectionCount: null == connectionCount
            ? _value.connectionCount
            : connectionCount // ignore: cast_nullable_to_non_nullable
                  as int,
        mutualConnectionCount: null == mutualConnectionCount
            ? _value.mutualConnectionCount
            : mutualConnectionCount // ignore: cast_nullable_to_non_nullable
                  as int,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        lastActiveAt: freezed == lastActiveAt
            ? _value.lastActiveAt
            : lastActiveAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        achievements: freezed == achievements
            ? _value._achievements
            : achievements // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
        certifications: freezed == certifications
            ? _value._certifications
            : certifications // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        languages: freezed == languages
            ? _value._languages
            : languages // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        education: freezed == education
            ? _value.education
            : education // ignore: cast_nullable_to_non_nullable
                  as String?,
        experience: freezed == experience
            ? _value.experience
            : experience // ignore: cast_nullable_to_non_nullable
                  as String?,
        preferences: freezed == preferences
            ? _value._preferences
            : preferences // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$NetworkProfileImpl implements _NetworkProfile {
  const _$NetworkProfileImpl({
    required this.id,
    required this.userId,
    required this.name,
    required this.title,
    required this.company,
    this.bio,
    this.profilePictureUrl,
    this.coverPhotoUrl,
    required final List<String> industries,
    required final List<String> skills,
    required final List<String> interests,
    this.location,
    this.website,
    this.linkedinUrl,
    this.twitterUrl,
    this.instagramUrl,
    required this.visibility,
    required this.isVerified,
    required this.isPremium,
    required this.connectionCount,
    required this.mutualConnectionCount,
    required this.createdAt,
    this.lastActiveAt,
    final Map<String, dynamic>? achievements,
    final List<String>? certifications,
    final List<String>? languages,
    this.education,
    this.experience,
    final Map<String, dynamic>? preferences,
  }) : _industries = industries,
       _skills = skills,
       _interests = interests,
       _achievements = achievements,
       _certifications = certifications,
       _languages = languages,
       _preferences = preferences;

  factory _$NetworkProfileImpl.fromJson(Map<String, dynamic> json) =>
      _$$NetworkProfileImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String name;
  @override
  final String title;
  @override
  final String company;
  @override
  final String? bio;
  @override
  final String? profilePictureUrl;
  @override
  final String? coverPhotoUrl;
  final List<String> _industries;
  @override
  List<String> get industries {
    if (_industries is EqualUnmodifiableListView) return _industries;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_industries);
  }

  final List<String> _skills;
  @override
  List<String> get skills {
    if (_skills is EqualUnmodifiableListView) return _skills;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_skills);
  }

  final List<String> _interests;
  @override
  List<String> get interests {
    if (_interests is EqualUnmodifiableListView) return _interests;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_interests);
  }

  @override
  final String? location;
  @override
  final String? website;
  @override
  final String? linkedinUrl;
  @override
  final String? twitterUrl;
  @override
  final String? instagramUrl;
  @override
  final NetworkProfileVisibility visibility;
  @override
  final bool isVerified;
  @override
  final bool isPremium;
  @override
  final int connectionCount;
  @override
  final int mutualConnectionCount;
  @override
  final DateTime createdAt;
  @override
  final DateTime? lastActiveAt;
  final Map<String, dynamic>? _achievements;
  @override
  Map<String, dynamic>? get achievements {
    final value = _achievements;
    if (value == null) return null;
    if (_achievements is EqualUnmodifiableMapView) return _achievements;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  final List<String>? _certifications;
  @override
  List<String>? get certifications {
    final value = _certifications;
    if (value == null) return null;
    if (_certifications is EqualUnmodifiableListView) return _certifications;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _languages;
  @override
  List<String>? get languages {
    final value = _languages;
    if (value == null) return null;
    if (_languages is EqualUnmodifiableListView) return _languages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? education;
  @override
  final String? experience;
  final Map<String, dynamic>? _preferences;
  @override
  Map<String, dynamic>? get preferences {
    final value = _preferences;
    if (value == null) return null;
    if (_preferences is EqualUnmodifiableMapView) return _preferences;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'NetworkProfile(id: $id, userId: $userId, name: $name, title: $title, company: $company, bio: $bio, profilePictureUrl: $profilePictureUrl, coverPhotoUrl: $coverPhotoUrl, industries: $industries, skills: $skills, interests: $interests, location: $location, website: $website, linkedinUrl: $linkedinUrl, twitterUrl: $twitterUrl, instagramUrl: $instagramUrl, visibility: $visibility, isVerified: $isVerified, isPremium: $isPremium, connectionCount: $connectionCount, mutualConnectionCount: $mutualConnectionCount, createdAt: $createdAt, lastActiveAt: $lastActiveAt, achievements: $achievements, certifications: $certifications, languages: $languages, education: $education, experience: $experience, preferences: $preferences)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NetworkProfileImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.company, company) || other.company == company) &&
            (identical(other.bio, bio) || other.bio == bio) &&
            (identical(other.profilePictureUrl, profilePictureUrl) ||
                other.profilePictureUrl == profilePictureUrl) &&
            (identical(other.coverPhotoUrl, coverPhotoUrl) ||
                other.coverPhotoUrl == coverPhotoUrl) &&
            const DeepCollectionEquality().equals(
              other._industries,
              _industries,
            ) &&
            const DeepCollectionEquality().equals(other._skills, _skills) &&
            const DeepCollectionEquality().equals(
              other._interests,
              _interests,
            ) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.website, website) || other.website == website) &&
            (identical(other.linkedinUrl, linkedinUrl) ||
                other.linkedinUrl == linkedinUrl) &&
            (identical(other.twitterUrl, twitterUrl) ||
                other.twitterUrl == twitterUrl) &&
            (identical(other.instagramUrl, instagramUrl) ||
                other.instagramUrl == instagramUrl) &&
            (identical(other.visibility, visibility) ||
                other.visibility == visibility) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.isPremium, isPremium) ||
                other.isPremium == isPremium) &&
            (identical(other.connectionCount, connectionCount) ||
                other.connectionCount == connectionCount) &&
            (identical(other.mutualConnectionCount, mutualConnectionCount) ||
                other.mutualConnectionCount == mutualConnectionCount) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.lastActiveAt, lastActiveAt) ||
                other.lastActiveAt == lastActiveAt) &&
            const DeepCollectionEquality().equals(
              other._achievements,
              _achievements,
            ) &&
            const DeepCollectionEquality().equals(
              other._certifications,
              _certifications,
            ) &&
            const DeepCollectionEquality().equals(
              other._languages,
              _languages,
            ) &&
            (identical(other.education, education) ||
                other.education == education) &&
            (identical(other.experience, experience) ||
                other.experience == experience) &&
            const DeepCollectionEquality().equals(
              other._preferences,
              _preferences,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    userId,
    name,
    title,
    company,
    bio,
    profilePictureUrl,
    coverPhotoUrl,
    const DeepCollectionEquality().hash(_industries),
    const DeepCollectionEquality().hash(_skills),
    const DeepCollectionEquality().hash(_interests),
    location,
    website,
    linkedinUrl,
    twitterUrl,
    instagramUrl,
    visibility,
    isVerified,
    isPremium,
    connectionCount,
    mutualConnectionCount,
    createdAt,
    lastActiveAt,
    const DeepCollectionEquality().hash(_achievements),
    const DeepCollectionEquality().hash(_certifications),
    const DeepCollectionEquality().hash(_languages),
    education,
    experience,
    const DeepCollectionEquality().hash(_preferences),
  ]);

  /// Create a copy of NetworkProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NetworkProfileImplCopyWith<_$NetworkProfileImpl> get copyWith =>
      __$$NetworkProfileImplCopyWithImpl<_$NetworkProfileImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$NetworkProfileImplToJson(this);
  }
}

abstract class _NetworkProfile implements NetworkProfile {
  const factory _NetworkProfile({
    required final String id,
    required final String userId,
    required final String name,
    required final String title,
    required final String company,
    final String? bio,
    final String? profilePictureUrl,
    final String? coverPhotoUrl,
    required final List<String> industries,
    required final List<String> skills,
    required final List<String> interests,
    final String? location,
    final String? website,
    final String? linkedinUrl,
    final String? twitterUrl,
    final String? instagramUrl,
    required final NetworkProfileVisibility visibility,
    required final bool isVerified,
    required final bool isPremium,
    required final int connectionCount,
    required final int mutualConnectionCount,
    required final DateTime createdAt,
    final DateTime? lastActiveAt,
    final Map<String, dynamic>? achievements,
    final List<String>? certifications,
    final List<String>? languages,
    final String? education,
    final String? experience,
    final Map<String, dynamic>? preferences,
  }) = _$NetworkProfileImpl;

  factory _NetworkProfile.fromJson(Map<String, dynamic> json) =
      _$NetworkProfileImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get name;
  @override
  String get title;
  @override
  String get company;
  @override
  String? get bio;
  @override
  String? get profilePictureUrl;
  @override
  String? get coverPhotoUrl;
  @override
  List<String> get industries;
  @override
  List<String> get skills;
  @override
  List<String> get interests;
  @override
  String? get location;
  @override
  String? get website;
  @override
  String? get linkedinUrl;
  @override
  String? get twitterUrl;
  @override
  String? get instagramUrl;
  @override
  NetworkProfileVisibility get visibility;
  @override
  bool get isVerified;
  @override
  bool get isPremium;
  @override
  int get connectionCount;
  @override
  int get mutualConnectionCount;
  @override
  DateTime get createdAt;
  @override
  DateTime? get lastActiveAt;
  @override
  Map<String, dynamic>? get achievements;
  @override
  List<String>? get certifications;
  @override
  List<String>? get languages;
  @override
  String? get education;
  @override
  String? get experience;
  @override
  Map<String, dynamic>? get preferences;

  /// Create a copy of NetworkProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NetworkProfileImplCopyWith<_$NetworkProfileImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

NetworkingEvent _$NetworkingEventFromJson(Map<String, dynamic> json) {
  return _NetworkingEvent.fromJson(json);
}

/// @nodoc
mixin _$NetworkingEvent {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get organizerId => throw _privateConstructorUsedError;
  String get organizerName => throw _privateConstructorUsedError;
  DateTime get startDate => throw _privateConstructorUsedError;
  DateTime get endDate => throw _privateConstructorUsedError;
  String get location => throw _privateConstructorUsedError;
  String? get virtualMeetingUrl => throw _privateConstructorUsedError;
  EventType get type => throw _privateConstructorUsedError;
  EventVisibility get visibility => throw _privateConstructorUsedError;
  int get maxAttendees => throw _privateConstructorUsedError;
  int get currentAttendees => throw _privateConstructorUsedError;
  double get price => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  List<String>? get tags => throw _privateConstructorUsedError;
  List<String>? get industries => throw _privateConstructorUsedError;
  String? get coverImageUrl => throw _privateConstructorUsedError;
  bool get isExclusive => throw _privateConstructorUsedError;
  bool get isVerified => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  Map<String, dynamic>? get agenda => throw _privateConstructorUsedError;
  List<String>? get speakers => throw _privateConstructorUsedError;
  String? get venueDetails => throw _privateConstructorUsedError;
  Map<String, dynamic>? get requirements => throw _privateConstructorUsedError;

  /// Serializes this NetworkingEvent to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of NetworkingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $NetworkingEventCopyWith<NetworkingEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NetworkingEventCopyWith<$Res> {
  factory $NetworkingEventCopyWith(
    NetworkingEvent value,
    $Res Function(NetworkingEvent) then,
  ) = _$NetworkingEventCopyWithImpl<$Res, NetworkingEvent>;
  @useResult
  $Res call({
    String id,
    String title,
    String description,
    String organizerId,
    String organizerName,
    DateTime startDate,
    DateTime endDate,
    String location,
    String? virtualMeetingUrl,
    EventType type,
    EventVisibility visibility,
    int maxAttendees,
    int currentAttendees,
    double price,
    String currency,
    List<String>? tags,
    List<String>? industries,
    String? coverImageUrl,
    bool isExclusive,
    bool isVerified,
    DateTime createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? agenda,
    List<String>? speakers,
    String? venueDetails,
    Map<String, dynamic>? requirements,
  });
}

/// @nodoc
class _$NetworkingEventCopyWithImpl<$Res, $Val extends NetworkingEvent>
    implements $NetworkingEventCopyWith<$Res> {
  _$NetworkingEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NetworkingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? organizerId = null,
    Object? organizerName = null,
    Object? startDate = null,
    Object? endDate = null,
    Object? location = null,
    Object? virtualMeetingUrl = freezed,
    Object? type = null,
    Object? visibility = null,
    Object? maxAttendees = null,
    Object? currentAttendees = null,
    Object? price = null,
    Object? currency = null,
    Object? tags = freezed,
    Object? industries = freezed,
    Object? coverImageUrl = freezed,
    Object? isExclusive = null,
    Object? isVerified = null,
    Object? createdAt = null,
    Object? updatedAt = freezed,
    Object? agenda = freezed,
    Object? speakers = freezed,
    Object? venueDetails = freezed,
    Object? requirements = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            organizerId: null == organizerId
                ? _value.organizerId
                : organizerId // ignore: cast_nullable_to_non_nullable
                      as String,
            organizerName: null == organizerName
                ? _value.organizerName
                : organizerName // ignore: cast_nullable_to_non_nullable
                      as String,
            startDate: null == startDate
                ? _value.startDate
                : startDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            endDate: null == endDate
                ? _value.endDate
                : endDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            location: null == location
                ? _value.location
                : location // ignore: cast_nullable_to_non_nullable
                      as String,
            virtualMeetingUrl: freezed == virtualMeetingUrl
                ? _value.virtualMeetingUrl
                : virtualMeetingUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as EventType,
            visibility: null == visibility
                ? _value.visibility
                : visibility // ignore: cast_nullable_to_non_nullable
                      as EventVisibility,
            maxAttendees: null == maxAttendees
                ? _value.maxAttendees
                : maxAttendees // ignore: cast_nullable_to_non_nullable
                      as int,
            currentAttendees: null == currentAttendees
                ? _value.currentAttendees
                : currentAttendees // ignore: cast_nullable_to_non_nullable
                      as int,
            price: null == price
                ? _value.price
                : price // ignore: cast_nullable_to_non_nullable
                      as double,
            currency: null == currency
                ? _value.currency
                : currency // ignore: cast_nullable_to_non_nullable
                      as String,
            tags: freezed == tags
                ? _value.tags
                : tags // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            industries: freezed == industries
                ? _value.industries
                : industries // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            coverImageUrl: freezed == coverImageUrl
                ? _value.coverImageUrl
                : coverImageUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            isExclusive: null == isExclusive
                ? _value.isExclusive
                : isExclusive // ignore: cast_nullable_to_non_nullable
                      as bool,
            isVerified: null == isVerified
                ? _value.isVerified
                : isVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            agenda: freezed == agenda
                ? _value.agenda
                : agenda // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
            speakers: freezed == speakers
                ? _value.speakers
                : speakers // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            venueDetails: freezed == venueDetails
                ? _value.venueDetails
                : venueDetails // ignore: cast_nullable_to_non_nullable
                      as String?,
            requirements: freezed == requirements
                ? _value.requirements
                : requirements // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$NetworkingEventImplCopyWith<$Res>
    implements $NetworkingEventCopyWith<$Res> {
  factory _$$NetworkingEventImplCopyWith(
    _$NetworkingEventImpl value,
    $Res Function(_$NetworkingEventImpl) then,
  ) = __$$NetworkingEventImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String title,
    String description,
    String organizerId,
    String organizerName,
    DateTime startDate,
    DateTime endDate,
    String location,
    String? virtualMeetingUrl,
    EventType type,
    EventVisibility visibility,
    int maxAttendees,
    int currentAttendees,
    double price,
    String currency,
    List<String>? tags,
    List<String>? industries,
    String? coverImageUrl,
    bool isExclusive,
    bool isVerified,
    DateTime createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? agenda,
    List<String>? speakers,
    String? venueDetails,
    Map<String, dynamic>? requirements,
  });
}

/// @nodoc
class __$$NetworkingEventImplCopyWithImpl<$Res>
    extends _$NetworkingEventCopyWithImpl<$Res, _$NetworkingEventImpl>
    implements _$$NetworkingEventImplCopyWith<$Res> {
  __$$NetworkingEventImplCopyWithImpl(
    _$NetworkingEventImpl _value,
    $Res Function(_$NetworkingEventImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NetworkingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? organizerId = null,
    Object? organizerName = null,
    Object? startDate = null,
    Object? endDate = null,
    Object? location = null,
    Object? virtualMeetingUrl = freezed,
    Object? type = null,
    Object? visibility = null,
    Object? maxAttendees = null,
    Object? currentAttendees = null,
    Object? price = null,
    Object? currency = null,
    Object? tags = freezed,
    Object? industries = freezed,
    Object? coverImageUrl = freezed,
    Object? isExclusive = null,
    Object? isVerified = null,
    Object? createdAt = null,
    Object? updatedAt = freezed,
    Object? agenda = freezed,
    Object? speakers = freezed,
    Object? venueDetails = freezed,
    Object? requirements = freezed,
  }) {
    return _then(
      _$NetworkingEventImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        organizerId: null == organizerId
            ? _value.organizerId
            : organizerId // ignore: cast_nullable_to_non_nullable
                  as String,
        organizerName: null == organizerName
            ? _value.organizerName
            : organizerName // ignore: cast_nullable_to_non_nullable
                  as String,
        startDate: null == startDate
            ? _value.startDate
            : startDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        endDate: null == endDate
            ? _value.endDate
            : endDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        location: null == location
            ? _value.location
            : location // ignore: cast_nullable_to_non_nullable
                  as String,
        virtualMeetingUrl: freezed == virtualMeetingUrl
            ? _value.virtualMeetingUrl
            : virtualMeetingUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as EventType,
        visibility: null == visibility
            ? _value.visibility
            : visibility // ignore: cast_nullable_to_non_nullable
                  as EventVisibility,
        maxAttendees: null == maxAttendees
            ? _value.maxAttendees
            : maxAttendees // ignore: cast_nullable_to_non_nullable
                  as int,
        currentAttendees: null == currentAttendees
            ? _value.currentAttendees
            : currentAttendees // ignore: cast_nullable_to_non_nullable
                  as int,
        price: null == price
            ? _value.price
            : price // ignore: cast_nullable_to_non_nullable
                  as double,
        currency: null == currency
            ? _value.currency
            : currency // ignore: cast_nullable_to_non_nullable
                  as String,
        tags: freezed == tags
            ? _value._tags
            : tags // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        industries: freezed == industries
            ? _value._industries
            : industries // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        coverImageUrl: freezed == coverImageUrl
            ? _value.coverImageUrl
            : coverImageUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        isExclusive: null == isExclusive
            ? _value.isExclusive
            : isExclusive // ignore: cast_nullable_to_non_nullable
                  as bool,
        isVerified: null == isVerified
            ? _value.isVerified
            : isVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        agenda: freezed == agenda
            ? _value._agenda
            : agenda // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
        speakers: freezed == speakers
            ? _value._speakers
            : speakers // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        venueDetails: freezed == venueDetails
            ? _value.venueDetails
            : venueDetails // ignore: cast_nullable_to_non_nullable
                  as String?,
        requirements: freezed == requirements
            ? _value._requirements
            : requirements // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$NetworkingEventImpl implements _NetworkingEvent {
  const _$NetworkingEventImpl({
    required this.id,
    required this.title,
    required this.description,
    required this.organizerId,
    required this.organizerName,
    required this.startDate,
    required this.endDate,
    required this.location,
    this.virtualMeetingUrl,
    required this.type,
    required this.visibility,
    required this.maxAttendees,
    required this.currentAttendees,
    required this.price,
    required this.currency,
    final List<String>? tags,
    final List<String>? industries,
    this.coverImageUrl,
    required this.isExclusive,
    required this.isVerified,
    required this.createdAt,
    this.updatedAt,
    final Map<String, dynamic>? agenda,
    final List<String>? speakers,
    this.venueDetails,
    final Map<String, dynamic>? requirements,
  }) : _tags = tags,
       _industries = industries,
       _agenda = agenda,
       _speakers = speakers,
       _requirements = requirements;

  factory _$NetworkingEventImpl.fromJson(Map<String, dynamic> json) =>
      _$$NetworkingEventImplFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String description;
  @override
  final String organizerId;
  @override
  final String organizerName;
  @override
  final DateTime startDate;
  @override
  final DateTime endDate;
  @override
  final String location;
  @override
  final String? virtualMeetingUrl;
  @override
  final EventType type;
  @override
  final EventVisibility visibility;
  @override
  final int maxAttendees;
  @override
  final int currentAttendees;
  @override
  final double price;
  @override
  final String currency;
  final List<String>? _tags;
  @override
  List<String>? get tags {
    final value = _tags;
    if (value == null) return null;
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _industries;
  @override
  List<String>? get industries {
    final value = _industries;
    if (value == null) return null;
    if (_industries is EqualUnmodifiableListView) return _industries;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? coverImageUrl;
  @override
  final bool isExclusive;
  @override
  final bool isVerified;
  @override
  final DateTime createdAt;
  @override
  final DateTime? updatedAt;
  final Map<String, dynamic>? _agenda;
  @override
  Map<String, dynamic>? get agenda {
    final value = _agenda;
    if (value == null) return null;
    if (_agenda is EqualUnmodifiableMapView) return _agenda;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  final List<String>? _speakers;
  @override
  List<String>? get speakers {
    final value = _speakers;
    if (value == null) return null;
    if (_speakers is EqualUnmodifiableListView) return _speakers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? venueDetails;
  final Map<String, dynamic>? _requirements;
  @override
  Map<String, dynamic>? get requirements {
    final value = _requirements;
    if (value == null) return null;
    if (_requirements is EqualUnmodifiableMapView) return _requirements;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'NetworkingEvent(id: $id, title: $title, description: $description, organizerId: $organizerId, organizerName: $organizerName, startDate: $startDate, endDate: $endDate, location: $location, virtualMeetingUrl: $virtualMeetingUrl, type: $type, visibility: $visibility, maxAttendees: $maxAttendees, currentAttendees: $currentAttendees, price: $price, currency: $currency, tags: $tags, industries: $industries, coverImageUrl: $coverImageUrl, isExclusive: $isExclusive, isVerified: $isVerified, createdAt: $createdAt, updatedAt: $updatedAt, agenda: $agenda, speakers: $speakers, venueDetails: $venueDetails, requirements: $requirements)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NetworkingEventImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.organizerId, organizerId) ||
                other.organizerId == organizerId) &&
            (identical(other.organizerName, organizerName) ||
                other.organizerName == organizerName) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.virtualMeetingUrl, virtualMeetingUrl) ||
                other.virtualMeetingUrl == virtualMeetingUrl) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.visibility, visibility) ||
                other.visibility == visibility) &&
            (identical(other.maxAttendees, maxAttendees) ||
                other.maxAttendees == maxAttendees) &&
            (identical(other.currentAttendees, currentAttendees) ||
                other.currentAttendees == currentAttendees) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            const DeepCollectionEquality().equals(
              other._industries,
              _industries,
            ) &&
            (identical(other.coverImageUrl, coverImageUrl) ||
                other.coverImageUrl == coverImageUrl) &&
            (identical(other.isExclusive, isExclusive) ||
                other.isExclusive == isExclusive) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            const DeepCollectionEquality().equals(other._agenda, _agenda) &&
            const DeepCollectionEquality().equals(other._speakers, _speakers) &&
            (identical(other.venueDetails, venueDetails) ||
                other.venueDetails == venueDetails) &&
            const DeepCollectionEquality().equals(
              other._requirements,
              _requirements,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    title,
    description,
    organizerId,
    organizerName,
    startDate,
    endDate,
    location,
    virtualMeetingUrl,
    type,
    visibility,
    maxAttendees,
    currentAttendees,
    price,
    currency,
    const DeepCollectionEquality().hash(_tags),
    const DeepCollectionEquality().hash(_industries),
    coverImageUrl,
    isExclusive,
    isVerified,
    createdAt,
    updatedAt,
    const DeepCollectionEquality().hash(_agenda),
    const DeepCollectionEquality().hash(_speakers),
    venueDetails,
    const DeepCollectionEquality().hash(_requirements),
  ]);

  /// Create a copy of NetworkingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NetworkingEventImplCopyWith<_$NetworkingEventImpl> get copyWith =>
      __$$NetworkingEventImplCopyWithImpl<_$NetworkingEventImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$NetworkingEventImplToJson(this);
  }
}

abstract class _NetworkingEvent implements NetworkingEvent {
  const factory _NetworkingEvent({
    required final String id,
    required final String title,
    required final String description,
    required final String organizerId,
    required final String organizerName,
    required final DateTime startDate,
    required final DateTime endDate,
    required final String location,
    final String? virtualMeetingUrl,
    required final EventType type,
    required final EventVisibility visibility,
    required final int maxAttendees,
    required final int currentAttendees,
    required final double price,
    required final String currency,
    final List<String>? tags,
    final List<String>? industries,
    final String? coverImageUrl,
    required final bool isExclusive,
    required final bool isVerified,
    required final DateTime createdAt,
    final DateTime? updatedAt,
    final Map<String, dynamic>? agenda,
    final List<String>? speakers,
    final String? venueDetails,
    final Map<String, dynamic>? requirements,
  }) = _$NetworkingEventImpl;

  factory _NetworkingEvent.fromJson(Map<String, dynamic> json) =
      _$NetworkingEventImpl.fromJson;

  @override
  String get id;
  @override
  String get title;
  @override
  String get description;
  @override
  String get organizerId;
  @override
  String get organizerName;
  @override
  DateTime get startDate;
  @override
  DateTime get endDate;
  @override
  String get location;
  @override
  String? get virtualMeetingUrl;
  @override
  EventType get type;
  @override
  EventVisibility get visibility;
  @override
  int get maxAttendees;
  @override
  int get currentAttendees;
  @override
  double get price;
  @override
  String get currency;
  @override
  List<String>? get tags;
  @override
  List<String>? get industries;
  @override
  String? get coverImageUrl;
  @override
  bool get isExclusive;
  @override
  bool get isVerified;
  @override
  DateTime get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  Map<String, dynamic>? get agenda;
  @override
  List<String>? get speakers;
  @override
  String? get venueDetails;
  @override
  Map<String, dynamic>? get requirements;

  /// Create a copy of NetworkingEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NetworkingEventImplCopyWith<_$NetworkingEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

BusinessCard _$BusinessCardFromJson(Map<String, dynamic> json) {
  return _BusinessCard.fromJson(json);
}

/// @nodoc
mixin _$BusinessCard {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get company => throw _privateConstructorUsedError;
  String? get email => throw _privateConstructorUsedError;
  String? get phone => throw _privateConstructorUsedError;
  String? get website => throw _privateConstructorUsedError;
  String? get linkedinUrl => throw _privateConstructorUsedError;
  String? get profilePictureUrl => throw _privateConstructorUsedError;
  String? get qrCodeUrl => throw _privateConstructorUsedError;
  BusinessCardDesign get design => throw _privateConstructorUsedError;
  bool get isDigital => throw _privateConstructorUsedError;
  bool get isShared => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime? get lastSharedAt => throw _privateConstructorUsedError;
  Map<String, dynamic>? get customFields => throw _privateConstructorUsedError;
  List<String>? get tags => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;

  /// Serializes this BusinessCard to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BusinessCard
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BusinessCardCopyWith<BusinessCard> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BusinessCardCopyWith<$Res> {
  factory $BusinessCardCopyWith(
    BusinessCard value,
    $Res Function(BusinessCard) then,
  ) = _$BusinessCardCopyWithImpl<$Res, BusinessCard>;
  @useResult
  $Res call({
    String id,
    String userId,
    String name,
    String title,
    String company,
    String? email,
    String? phone,
    String? website,
    String? linkedinUrl,
    String? profilePictureUrl,
    String? qrCodeUrl,
    BusinessCardDesign design,
    bool isDigital,
    bool isShared,
    DateTime createdAt,
    DateTime? lastSharedAt,
    Map<String, dynamic>? customFields,
    List<String>? tags,
    String? notes,
  });
}

/// @nodoc
class _$BusinessCardCopyWithImpl<$Res, $Val extends BusinessCard>
    implements $BusinessCardCopyWith<$Res> {
  _$BusinessCardCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BusinessCard
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? name = null,
    Object? title = null,
    Object? company = null,
    Object? email = freezed,
    Object? phone = freezed,
    Object? website = freezed,
    Object? linkedinUrl = freezed,
    Object? profilePictureUrl = freezed,
    Object? qrCodeUrl = freezed,
    Object? design = null,
    Object? isDigital = null,
    Object? isShared = null,
    Object? createdAt = null,
    Object? lastSharedAt = freezed,
    Object? customFields = freezed,
    Object? tags = freezed,
    Object? notes = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            company: null == company
                ? _value.company
                : company // ignore: cast_nullable_to_non_nullable
                      as String,
            email: freezed == email
                ? _value.email
                : email // ignore: cast_nullable_to_non_nullable
                      as String?,
            phone: freezed == phone
                ? _value.phone
                : phone // ignore: cast_nullable_to_non_nullable
                      as String?,
            website: freezed == website
                ? _value.website
                : website // ignore: cast_nullable_to_non_nullable
                      as String?,
            linkedinUrl: freezed == linkedinUrl
                ? _value.linkedinUrl
                : linkedinUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            profilePictureUrl: freezed == profilePictureUrl
                ? _value.profilePictureUrl
                : profilePictureUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            qrCodeUrl: freezed == qrCodeUrl
                ? _value.qrCodeUrl
                : qrCodeUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            design: null == design
                ? _value.design
                : design // ignore: cast_nullable_to_non_nullable
                      as BusinessCardDesign,
            isDigital: null == isDigital
                ? _value.isDigital
                : isDigital // ignore: cast_nullable_to_non_nullable
                      as bool,
            isShared: null == isShared
                ? _value.isShared
                : isShared // ignore: cast_nullable_to_non_nullable
                      as bool,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            lastSharedAt: freezed == lastSharedAt
                ? _value.lastSharedAt
                : lastSharedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            customFields: freezed == customFields
                ? _value.customFields
                : customFields // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
            tags: freezed == tags
                ? _value.tags
                : tags // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            notes: freezed == notes
                ? _value.notes
                : notes // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$BusinessCardImplCopyWith<$Res>
    implements $BusinessCardCopyWith<$Res> {
  factory _$$BusinessCardImplCopyWith(
    _$BusinessCardImpl value,
    $Res Function(_$BusinessCardImpl) then,
  ) = __$$BusinessCardImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    String name,
    String title,
    String company,
    String? email,
    String? phone,
    String? website,
    String? linkedinUrl,
    String? profilePictureUrl,
    String? qrCodeUrl,
    BusinessCardDesign design,
    bool isDigital,
    bool isShared,
    DateTime createdAt,
    DateTime? lastSharedAt,
    Map<String, dynamic>? customFields,
    List<String>? tags,
    String? notes,
  });
}

/// @nodoc
class __$$BusinessCardImplCopyWithImpl<$Res>
    extends _$BusinessCardCopyWithImpl<$Res, _$BusinessCardImpl>
    implements _$$BusinessCardImplCopyWith<$Res> {
  __$$BusinessCardImplCopyWithImpl(
    _$BusinessCardImpl _value,
    $Res Function(_$BusinessCardImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of BusinessCard
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? name = null,
    Object? title = null,
    Object? company = null,
    Object? email = freezed,
    Object? phone = freezed,
    Object? website = freezed,
    Object? linkedinUrl = freezed,
    Object? profilePictureUrl = freezed,
    Object? qrCodeUrl = freezed,
    Object? design = null,
    Object? isDigital = null,
    Object? isShared = null,
    Object? createdAt = null,
    Object? lastSharedAt = freezed,
    Object? customFields = freezed,
    Object? tags = freezed,
    Object? notes = freezed,
  }) {
    return _then(
      _$BusinessCardImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        company: null == company
            ? _value.company
            : company // ignore: cast_nullable_to_non_nullable
                  as String,
        email: freezed == email
            ? _value.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String?,
        phone: freezed == phone
            ? _value.phone
            : phone // ignore: cast_nullable_to_non_nullable
                  as String?,
        website: freezed == website
            ? _value.website
            : website // ignore: cast_nullable_to_non_nullable
                  as String?,
        linkedinUrl: freezed == linkedinUrl
            ? _value.linkedinUrl
            : linkedinUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        profilePictureUrl: freezed == profilePictureUrl
            ? _value.profilePictureUrl
            : profilePictureUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        qrCodeUrl: freezed == qrCodeUrl
            ? _value.qrCodeUrl
            : qrCodeUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        design: null == design
            ? _value.design
            : design // ignore: cast_nullable_to_non_nullable
                  as BusinessCardDesign,
        isDigital: null == isDigital
            ? _value.isDigital
            : isDigital // ignore: cast_nullable_to_non_nullable
                  as bool,
        isShared: null == isShared
            ? _value.isShared
            : isShared // ignore: cast_nullable_to_non_nullable
                  as bool,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        lastSharedAt: freezed == lastSharedAt
            ? _value.lastSharedAt
            : lastSharedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        customFields: freezed == customFields
            ? _value._customFields
            : customFields // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
        tags: freezed == tags
            ? _value._tags
            : tags // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        notes: freezed == notes
            ? _value.notes
            : notes // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$BusinessCardImpl implements _BusinessCard {
  const _$BusinessCardImpl({
    required this.id,
    required this.userId,
    required this.name,
    required this.title,
    required this.company,
    this.email,
    this.phone,
    this.website,
    this.linkedinUrl,
    this.profilePictureUrl,
    this.qrCodeUrl,
    required this.design,
    required this.isDigital,
    required this.isShared,
    required this.createdAt,
    this.lastSharedAt,
    final Map<String, dynamic>? customFields,
    final List<String>? tags,
    this.notes,
  }) : _customFields = customFields,
       _tags = tags;

  factory _$BusinessCardImpl.fromJson(Map<String, dynamic> json) =>
      _$$BusinessCardImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String name;
  @override
  final String title;
  @override
  final String company;
  @override
  final String? email;
  @override
  final String? phone;
  @override
  final String? website;
  @override
  final String? linkedinUrl;
  @override
  final String? profilePictureUrl;
  @override
  final String? qrCodeUrl;
  @override
  final BusinessCardDesign design;
  @override
  final bool isDigital;
  @override
  final bool isShared;
  @override
  final DateTime createdAt;
  @override
  final DateTime? lastSharedAt;
  final Map<String, dynamic>? _customFields;
  @override
  Map<String, dynamic>? get customFields {
    final value = _customFields;
    if (value == null) return null;
    if (_customFields is EqualUnmodifiableMapView) return _customFields;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  final List<String>? _tags;
  @override
  List<String>? get tags {
    final value = _tags;
    if (value == null) return null;
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? notes;

  @override
  String toString() {
    return 'BusinessCard(id: $id, userId: $userId, name: $name, title: $title, company: $company, email: $email, phone: $phone, website: $website, linkedinUrl: $linkedinUrl, profilePictureUrl: $profilePictureUrl, qrCodeUrl: $qrCodeUrl, design: $design, isDigital: $isDigital, isShared: $isShared, createdAt: $createdAt, lastSharedAt: $lastSharedAt, customFields: $customFields, tags: $tags, notes: $notes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BusinessCardImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.company, company) || other.company == company) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.website, website) || other.website == website) &&
            (identical(other.linkedinUrl, linkedinUrl) ||
                other.linkedinUrl == linkedinUrl) &&
            (identical(other.profilePictureUrl, profilePictureUrl) ||
                other.profilePictureUrl == profilePictureUrl) &&
            (identical(other.qrCodeUrl, qrCodeUrl) ||
                other.qrCodeUrl == qrCodeUrl) &&
            (identical(other.design, design) || other.design == design) &&
            (identical(other.isDigital, isDigital) ||
                other.isDigital == isDigital) &&
            (identical(other.isShared, isShared) ||
                other.isShared == isShared) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.lastSharedAt, lastSharedAt) ||
                other.lastSharedAt == lastSharedAt) &&
            const DeepCollectionEquality().equals(
              other._customFields,
              _customFields,
            ) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.notes, notes) || other.notes == notes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    userId,
    name,
    title,
    company,
    email,
    phone,
    website,
    linkedinUrl,
    profilePictureUrl,
    qrCodeUrl,
    design,
    isDigital,
    isShared,
    createdAt,
    lastSharedAt,
    const DeepCollectionEquality().hash(_customFields),
    const DeepCollectionEquality().hash(_tags),
    notes,
  ]);

  /// Create a copy of BusinessCard
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BusinessCardImplCopyWith<_$BusinessCardImpl> get copyWith =>
      __$$BusinessCardImplCopyWithImpl<_$BusinessCardImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BusinessCardImplToJson(this);
  }
}

abstract class _BusinessCard implements BusinessCard {
  const factory _BusinessCard({
    required final String id,
    required final String userId,
    required final String name,
    required final String title,
    required final String company,
    final String? email,
    final String? phone,
    final String? website,
    final String? linkedinUrl,
    final String? profilePictureUrl,
    final String? qrCodeUrl,
    required final BusinessCardDesign design,
    required final bool isDigital,
    required final bool isShared,
    required final DateTime createdAt,
    final DateTime? lastSharedAt,
    final Map<String, dynamic>? customFields,
    final List<String>? tags,
    final String? notes,
  }) = _$BusinessCardImpl;

  factory _BusinessCard.fromJson(Map<String, dynamic> json) =
      _$BusinessCardImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get name;
  @override
  String get title;
  @override
  String get company;
  @override
  String? get email;
  @override
  String? get phone;
  @override
  String? get website;
  @override
  String? get linkedinUrl;
  @override
  String? get profilePictureUrl;
  @override
  String? get qrCodeUrl;
  @override
  BusinessCardDesign get design;
  @override
  bool get isDigital;
  @override
  bool get isShared;
  @override
  DateTime get createdAt;
  @override
  DateTime? get lastSharedAt;
  @override
  Map<String, dynamic>? get customFields;
  @override
  List<String>? get tags;
  @override
  String? get notes;

  /// Create a copy of BusinessCard
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BusinessCardImplCopyWith<_$BusinessCardImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

NetworkingRecommendation _$NetworkingRecommendationFromJson(
  Map<String, dynamic> json,
) {
  return _NetworkingRecommendation.fromJson(json);
}

/// @nodoc
mixin _$NetworkingRecommendation {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get recommendedUserId => throw _privateConstructorUsedError;
  String get reason => throw _privateConstructorUsedError;
  double get score => throw _privateConstructorUsedError;
  RecommendationType get type => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this NetworkingRecommendation to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of NetworkingRecommendation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $NetworkingRecommendationCopyWith<NetworkingRecommendation> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NetworkingRecommendationCopyWith<$Res> {
  factory $NetworkingRecommendationCopyWith(
    NetworkingRecommendation value,
    $Res Function(NetworkingRecommendation) then,
  ) = _$NetworkingRecommendationCopyWithImpl<$Res, NetworkingRecommendation>;
  @useResult
  $Res call({
    String id,
    String userId,
    String recommendedUserId,
    String reason,
    double score,
    RecommendationType type,
    DateTime createdAt,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class _$NetworkingRecommendationCopyWithImpl<
  $Res,
  $Val extends NetworkingRecommendation
>
    implements $NetworkingRecommendationCopyWith<$Res> {
  _$NetworkingRecommendationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NetworkingRecommendation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? recommendedUserId = null,
    Object? reason = null,
    Object? score = null,
    Object? type = null,
    Object? createdAt = null,
    Object? metadata = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            recommendedUserId: null == recommendedUserId
                ? _value.recommendedUserId
                : recommendedUserId // ignore: cast_nullable_to_non_nullable
                      as String,
            reason: null == reason
                ? _value.reason
                : reason // ignore: cast_nullable_to_non_nullable
                      as String,
            score: null == score
                ? _value.score
                : score // ignore: cast_nullable_to_non_nullable
                      as double,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as RecommendationType,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            metadata: freezed == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$NetworkingRecommendationImplCopyWith<$Res>
    implements $NetworkingRecommendationCopyWith<$Res> {
  factory _$$NetworkingRecommendationImplCopyWith(
    _$NetworkingRecommendationImpl value,
    $Res Function(_$NetworkingRecommendationImpl) then,
  ) = __$$NetworkingRecommendationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    String recommendedUserId,
    String reason,
    double score,
    RecommendationType type,
    DateTime createdAt,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class __$$NetworkingRecommendationImplCopyWithImpl<$Res>
    extends
        _$NetworkingRecommendationCopyWithImpl<
          $Res,
          _$NetworkingRecommendationImpl
        >
    implements _$$NetworkingRecommendationImplCopyWith<$Res> {
  __$$NetworkingRecommendationImplCopyWithImpl(
    _$NetworkingRecommendationImpl _value,
    $Res Function(_$NetworkingRecommendationImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NetworkingRecommendation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? recommendedUserId = null,
    Object? reason = null,
    Object? score = null,
    Object? type = null,
    Object? createdAt = null,
    Object? metadata = freezed,
  }) {
    return _then(
      _$NetworkingRecommendationImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        recommendedUserId: null == recommendedUserId
            ? _value.recommendedUserId
            : recommendedUserId // ignore: cast_nullable_to_non_nullable
                  as String,
        reason: null == reason
            ? _value.reason
            : reason // ignore: cast_nullable_to_non_nullable
                  as String,
        score: null == score
            ? _value.score
            : score // ignore: cast_nullable_to_non_nullable
                  as double,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as RecommendationType,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        metadata: freezed == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$NetworkingRecommendationImpl implements _NetworkingRecommendation {
  const _$NetworkingRecommendationImpl({
    required this.id,
    required this.userId,
    required this.recommendedUserId,
    required this.reason,
    required this.score,
    required this.type,
    required this.createdAt,
    final Map<String, dynamic>? metadata,
  }) : _metadata = metadata;

  factory _$NetworkingRecommendationImpl.fromJson(Map<String, dynamic> json) =>
      _$$NetworkingRecommendationImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String recommendedUserId;
  @override
  final String reason;
  @override
  final double score;
  @override
  final RecommendationType type;
  @override
  final DateTime createdAt;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'NetworkingRecommendation(id: $id, userId: $userId, recommendedUserId: $recommendedUserId, reason: $reason, score: $score, type: $type, createdAt: $createdAt, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NetworkingRecommendationImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.recommendedUserId, recommendedUserId) ||
                other.recommendedUserId == recommendedUserId) &&
            (identical(other.reason, reason) || other.reason == reason) &&
            (identical(other.score, score) || other.score == score) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    userId,
    recommendedUserId,
    reason,
    score,
    type,
    createdAt,
    const DeepCollectionEquality().hash(_metadata),
  );

  /// Create a copy of NetworkingRecommendation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NetworkingRecommendationImplCopyWith<_$NetworkingRecommendationImpl>
  get copyWith =>
      __$$NetworkingRecommendationImplCopyWithImpl<
        _$NetworkingRecommendationImpl
      >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NetworkingRecommendationImplToJson(this);
  }
}

abstract class _NetworkingRecommendation implements NetworkingRecommendation {
  const factory _NetworkingRecommendation({
    required final String id,
    required final String userId,
    required final String recommendedUserId,
    required final String reason,
    required final double score,
    required final RecommendationType type,
    required final DateTime createdAt,
    final Map<String, dynamic>? metadata,
  }) = _$NetworkingRecommendationImpl;

  factory _NetworkingRecommendation.fromJson(Map<String, dynamic> json) =
      _$NetworkingRecommendationImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get recommendedUserId;
  @override
  String get reason;
  @override
  double get score;
  @override
  RecommendationType get type;
  @override
  DateTime get createdAt;
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of NetworkingRecommendation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NetworkingRecommendationImplCopyWith<_$NetworkingRecommendationImpl>
  get copyWith => throw _privateConstructorUsedError;
}

NetworkingMessage _$NetworkingMessageFromJson(Map<String, dynamic> json) {
  return _NetworkingMessage.fromJson(json);
}

/// @nodoc
mixin _$NetworkingMessage {
  String get id => throw _privateConstructorUsedError;
  String get senderId => throw _privateConstructorUsedError;
  String get receiverId => throw _privateConstructorUsedError;
  String get content => throw _privateConstructorUsedError;
  MessageType get type => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;
  bool get isRead => throw _privateConstructorUsedError;
  String? get attachmentUrl => throw _privateConstructorUsedError;
  String? get attachmentType => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this NetworkingMessage to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of NetworkingMessage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $NetworkingMessageCopyWith<NetworkingMessage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NetworkingMessageCopyWith<$Res> {
  factory $NetworkingMessageCopyWith(
    NetworkingMessage value,
    $Res Function(NetworkingMessage) then,
  ) = _$NetworkingMessageCopyWithImpl<$Res, NetworkingMessage>;
  @useResult
  $Res call({
    String id,
    String senderId,
    String receiverId,
    String content,
    MessageType type,
    DateTime timestamp,
    bool isRead,
    String? attachmentUrl,
    String? attachmentType,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class _$NetworkingMessageCopyWithImpl<$Res, $Val extends NetworkingMessage>
    implements $NetworkingMessageCopyWith<$Res> {
  _$NetworkingMessageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NetworkingMessage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? senderId = null,
    Object? receiverId = null,
    Object? content = null,
    Object? type = null,
    Object? timestamp = null,
    Object? isRead = null,
    Object? attachmentUrl = freezed,
    Object? attachmentType = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            senderId: null == senderId
                ? _value.senderId
                : senderId // ignore: cast_nullable_to_non_nullable
                      as String,
            receiverId: null == receiverId
                ? _value.receiverId
                : receiverId // ignore: cast_nullable_to_non_nullable
                      as String,
            content: null == content
                ? _value.content
                : content // ignore: cast_nullable_to_non_nullable
                      as String,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as MessageType,
            timestamp: null == timestamp
                ? _value.timestamp
                : timestamp // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isRead: null == isRead
                ? _value.isRead
                : isRead // ignore: cast_nullable_to_non_nullable
                      as bool,
            attachmentUrl: freezed == attachmentUrl
                ? _value.attachmentUrl
                : attachmentUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            attachmentType: freezed == attachmentType
                ? _value.attachmentType
                : attachmentType // ignore: cast_nullable_to_non_nullable
                      as String?,
            metadata: freezed == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$NetworkingMessageImplCopyWith<$Res>
    implements $NetworkingMessageCopyWith<$Res> {
  factory _$$NetworkingMessageImplCopyWith(
    _$NetworkingMessageImpl value,
    $Res Function(_$NetworkingMessageImpl) then,
  ) = __$$NetworkingMessageImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String senderId,
    String receiverId,
    String content,
    MessageType type,
    DateTime timestamp,
    bool isRead,
    String? attachmentUrl,
    String? attachmentType,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class __$$NetworkingMessageImplCopyWithImpl<$Res>
    extends _$NetworkingMessageCopyWithImpl<$Res, _$NetworkingMessageImpl>
    implements _$$NetworkingMessageImplCopyWith<$Res> {
  __$$NetworkingMessageImplCopyWithImpl(
    _$NetworkingMessageImpl _value,
    $Res Function(_$NetworkingMessageImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NetworkingMessage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? senderId = null,
    Object? receiverId = null,
    Object? content = null,
    Object? type = null,
    Object? timestamp = null,
    Object? isRead = null,
    Object? attachmentUrl = freezed,
    Object? attachmentType = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _$NetworkingMessageImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        senderId: null == senderId
            ? _value.senderId
            : senderId // ignore: cast_nullable_to_non_nullable
                  as String,
        receiverId: null == receiverId
            ? _value.receiverId
            : receiverId // ignore: cast_nullable_to_non_nullable
                  as String,
        content: null == content
            ? _value.content
            : content // ignore: cast_nullable_to_non_nullable
                  as String,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as MessageType,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isRead: null == isRead
            ? _value.isRead
            : isRead // ignore: cast_nullable_to_non_nullable
                  as bool,
        attachmentUrl: freezed == attachmentUrl
            ? _value.attachmentUrl
            : attachmentUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        attachmentType: freezed == attachmentType
            ? _value.attachmentType
            : attachmentType // ignore: cast_nullable_to_non_nullable
                  as String?,
        metadata: freezed == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$NetworkingMessageImpl implements _NetworkingMessage {
  const _$NetworkingMessageImpl({
    required this.id,
    required this.senderId,
    required this.receiverId,
    required this.content,
    required this.type,
    required this.timestamp,
    required this.isRead,
    this.attachmentUrl,
    this.attachmentType,
    final Map<String, dynamic>? metadata,
  }) : _metadata = metadata;

  factory _$NetworkingMessageImpl.fromJson(Map<String, dynamic> json) =>
      _$$NetworkingMessageImplFromJson(json);

  @override
  final String id;
  @override
  final String senderId;
  @override
  final String receiverId;
  @override
  final String content;
  @override
  final MessageType type;
  @override
  final DateTime timestamp;
  @override
  final bool isRead;
  @override
  final String? attachmentUrl;
  @override
  final String? attachmentType;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'NetworkingMessage(id: $id, senderId: $senderId, receiverId: $receiverId, content: $content, type: $type, timestamp: $timestamp, isRead: $isRead, attachmentUrl: $attachmentUrl, attachmentType: $attachmentType, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NetworkingMessageImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.senderId, senderId) ||
                other.senderId == senderId) &&
            (identical(other.receiverId, receiverId) ||
                other.receiverId == receiverId) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.isRead, isRead) || other.isRead == isRead) &&
            (identical(other.attachmentUrl, attachmentUrl) ||
                other.attachmentUrl == attachmentUrl) &&
            (identical(other.attachmentType, attachmentType) ||
                other.attachmentType == attachmentType) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    senderId,
    receiverId,
    content,
    type,
    timestamp,
    isRead,
    attachmentUrl,
    attachmentType,
    const DeepCollectionEquality().hash(_metadata),
  );

  /// Create a copy of NetworkingMessage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NetworkingMessageImplCopyWith<_$NetworkingMessageImpl> get copyWith =>
      __$$NetworkingMessageImplCopyWithImpl<_$NetworkingMessageImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$NetworkingMessageImplToJson(this);
  }
}

abstract class _NetworkingMessage implements NetworkingMessage {
  const factory _NetworkingMessage({
    required final String id,
    required final String senderId,
    required final String receiverId,
    required final String content,
    required final MessageType type,
    required final DateTime timestamp,
    required final bool isRead,
    final String? attachmentUrl,
    final String? attachmentType,
    final Map<String, dynamic>? metadata,
  }) = _$NetworkingMessageImpl;

  factory _NetworkingMessage.fromJson(Map<String, dynamic> json) =
      _$NetworkingMessageImpl.fromJson;

  @override
  String get id;
  @override
  String get senderId;
  @override
  String get receiverId;
  @override
  String get content;
  @override
  MessageType get type;
  @override
  DateTime get timestamp;
  @override
  bool get isRead;
  @override
  String? get attachmentUrl;
  @override
  String? get attachmentType;
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of NetworkingMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NetworkingMessageImplCopyWith<_$NetworkingMessageImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
