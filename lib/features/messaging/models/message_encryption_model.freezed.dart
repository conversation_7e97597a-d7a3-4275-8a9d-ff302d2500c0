// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'message_encryption_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

MessageEncryptionModel _$MessageEncryptionModelFromJson(
  Map<String, dynamic> json,
) {
  return _MessageEncryptionModel.fromJson(json);
}

/// @nodoc
mixin _$MessageEncryptionModel {
  String get id => throw _privateConstructorUsedError;
  String get messageId => throw _privateConstructorUsedError;
  EncryptionStatus get status => throw _privateConstructorUsedError;
  EncryptionType get type => throw _privateConstructorUsedError;
  String get encryptedContent => throw _privateConstructorUsedError;
  String? get decryptedContent => throw _privateConstructorUsedError;
  String get senderPublicKey => throw _privateConstructorUsedError;
  String? get recipientPublicKey => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime? get decryptedAt => throw _privateConstructorUsedError;
  MessageEncryptionMetadata get metadata => throw _privateConstructorUsedError;

  /// Serializes this MessageEncryptionModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MessageEncryptionModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MessageEncryptionModelCopyWith<MessageEncryptionModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessageEncryptionModelCopyWith<$Res> {
  factory $MessageEncryptionModelCopyWith(
    MessageEncryptionModel value,
    $Res Function(MessageEncryptionModel) then,
  ) = _$MessageEncryptionModelCopyWithImpl<$Res, MessageEncryptionModel>;
  @useResult
  $Res call({
    String id,
    String messageId,
    EncryptionStatus status,
    EncryptionType type,
    String encryptedContent,
    String? decryptedContent,
    String senderPublicKey,
    String? recipientPublicKey,
    DateTime createdAt,
    DateTime? decryptedAt,
    MessageEncryptionMetadata metadata,
  });

  $MessageEncryptionMetadataCopyWith<$Res> get metadata;
}

/// @nodoc
class _$MessageEncryptionModelCopyWithImpl<
  $Res,
  $Val extends MessageEncryptionModel
>
    implements $MessageEncryptionModelCopyWith<$Res> {
  _$MessageEncryptionModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MessageEncryptionModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? messageId = null,
    Object? status = null,
    Object? type = null,
    Object? encryptedContent = null,
    Object? decryptedContent = freezed,
    Object? senderPublicKey = null,
    Object? recipientPublicKey = freezed,
    Object? createdAt = null,
    Object? decryptedAt = freezed,
    Object? metadata = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            messageId: null == messageId
                ? _value.messageId
                : messageId // ignore: cast_nullable_to_non_nullable
                      as String,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as EncryptionStatus,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as EncryptionType,
            encryptedContent: null == encryptedContent
                ? _value.encryptedContent
                : encryptedContent // ignore: cast_nullable_to_non_nullable
                      as String,
            decryptedContent: freezed == decryptedContent
                ? _value.decryptedContent
                : decryptedContent // ignore: cast_nullable_to_non_nullable
                      as String?,
            senderPublicKey: null == senderPublicKey
                ? _value.senderPublicKey
                : senderPublicKey // ignore: cast_nullable_to_non_nullable
                      as String,
            recipientPublicKey: freezed == recipientPublicKey
                ? _value.recipientPublicKey
                : recipientPublicKey // ignore: cast_nullable_to_non_nullable
                      as String?,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            decryptedAt: freezed == decryptedAt
                ? _value.decryptedAt
                : decryptedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as MessageEncryptionMetadata,
          )
          as $Val,
    );
  }

  /// Create a copy of MessageEncryptionModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MessageEncryptionMetadataCopyWith<$Res> get metadata {
    return $MessageEncryptionMetadataCopyWith<$Res>(_value.metadata, (value) {
      return _then(_value.copyWith(metadata: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$MessageEncryptionModelImplCopyWith<$Res>
    implements $MessageEncryptionModelCopyWith<$Res> {
  factory _$$MessageEncryptionModelImplCopyWith(
    _$MessageEncryptionModelImpl value,
    $Res Function(_$MessageEncryptionModelImpl) then,
  ) = __$$MessageEncryptionModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String messageId,
    EncryptionStatus status,
    EncryptionType type,
    String encryptedContent,
    String? decryptedContent,
    String senderPublicKey,
    String? recipientPublicKey,
    DateTime createdAt,
    DateTime? decryptedAt,
    MessageEncryptionMetadata metadata,
  });

  @override
  $MessageEncryptionMetadataCopyWith<$Res> get metadata;
}

/// @nodoc
class __$$MessageEncryptionModelImplCopyWithImpl<$Res>
    extends
        _$MessageEncryptionModelCopyWithImpl<$Res, _$MessageEncryptionModelImpl>
    implements _$$MessageEncryptionModelImplCopyWith<$Res> {
  __$$MessageEncryptionModelImplCopyWithImpl(
    _$MessageEncryptionModelImpl _value,
    $Res Function(_$MessageEncryptionModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MessageEncryptionModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? messageId = null,
    Object? status = null,
    Object? type = null,
    Object? encryptedContent = null,
    Object? decryptedContent = freezed,
    Object? senderPublicKey = null,
    Object? recipientPublicKey = freezed,
    Object? createdAt = null,
    Object? decryptedAt = freezed,
    Object? metadata = null,
  }) {
    return _then(
      _$MessageEncryptionModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        messageId: null == messageId
            ? _value.messageId
            : messageId // ignore: cast_nullable_to_non_nullable
                  as String,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as EncryptionStatus,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as EncryptionType,
        encryptedContent: null == encryptedContent
            ? _value.encryptedContent
            : encryptedContent // ignore: cast_nullable_to_non_nullable
                  as String,
        decryptedContent: freezed == decryptedContent
            ? _value.decryptedContent
            : decryptedContent // ignore: cast_nullable_to_non_nullable
                  as String?,
        senderPublicKey: null == senderPublicKey
            ? _value.senderPublicKey
            : senderPublicKey // ignore: cast_nullable_to_non_nullable
                  as String,
        recipientPublicKey: freezed == recipientPublicKey
            ? _value.recipientPublicKey
            : recipientPublicKey // ignore: cast_nullable_to_non_nullable
                  as String?,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        decryptedAt: freezed == decryptedAt
            ? _value.decryptedAt
            : decryptedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        metadata: null == metadata
            ? _value.metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as MessageEncryptionMetadata,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$MessageEncryptionModelImpl implements _MessageEncryptionModel {
  const _$MessageEncryptionModelImpl({
    required this.id,
    required this.messageId,
    required this.status,
    required this.type,
    required this.encryptedContent,
    required this.decryptedContent,
    required this.senderPublicKey,
    required this.recipientPublicKey,
    required this.createdAt,
    required this.decryptedAt,
    required this.metadata,
  });

  factory _$MessageEncryptionModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$MessageEncryptionModelImplFromJson(json);

  @override
  final String id;
  @override
  final String messageId;
  @override
  final EncryptionStatus status;
  @override
  final EncryptionType type;
  @override
  final String encryptedContent;
  @override
  final String? decryptedContent;
  @override
  final String senderPublicKey;
  @override
  final String? recipientPublicKey;
  @override
  final DateTime createdAt;
  @override
  final DateTime? decryptedAt;
  @override
  final MessageEncryptionMetadata metadata;

  @override
  String toString() {
    return 'MessageEncryptionModel(id: $id, messageId: $messageId, status: $status, type: $type, encryptedContent: $encryptedContent, decryptedContent: $decryptedContent, senderPublicKey: $senderPublicKey, recipientPublicKey: $recipientPublicKey, createdAt: $createdAt, decryptedAt: $decryptedAt, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MessageEncryptionModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.encryptedContent, encryptedContent) ||
                other.encryptedContent == encryptedContent) &&
            (identical(other.decryptedContent, decryptedContent) ||
                other.decryptedContent == decryptedContent) &&
            (identical(other.senderPublicKey, senderPublicKey) ||
                other.senderPublicKey == senderPublicKey) &&
            (identical(other.recipientPublicKey, recipientPublicKey) ||
                other.recipientPublicKey == recipientPublicKey) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.decryptedAt, decryptedAt) ||
                other.decryptedAt == decryptedAt) &&
            (identical(other.metadata, metadata) ||
                other.metadata == metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    messageId,
    status,
    type,
    encryptedContent,
    decryptedContent,
    senderPublicKey,
    recipientPublicKey,
    createdAt,
    decryptedAt,
    metadata,
  );

  /// Create a copy of MessageEncryptionModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MessageEncryptionModelImplCopyWith<_$MessageEncryptionModelImpl>
  get copyWith =>
      __$$MessageEncryptionModelImplCopyWithImpl<_$MessageEncryptionModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$MessageEncryptionModelImplToJson(this);
  }
}

abstract class _MessageEncryptionModel implements MessageEncryptionModel {
  const factory _MessageEncryptionModel({
    required final String id,
    required final String messageId,
    required final EncryptionStatus status,
    required final EncryptionType type,
    required final String encryptedContent,
    required final String? decryptedContent,
    required final String senderPublicKey,
    required final String? recipientPublicKey,
    required final DateTime createdAt,
    required final DateTime? decryptedAt,
    required final MessageEncryptionMetadata metadata,
  }) = _$MessageEncryptionModelImpl;

  factory _MessageEncryptionModel.fromJson(Map<String, dynamic> json) =
      _$MessageEncryptionModelImpl.fromJson;

  @override
  String get id;
  @override
  String get messageId;
  @override
  EncryptionStatus get status;
  @override
  EncryptionType get type;
  @override
  String get encryptedContent;
  @override
  String? get decryptedContent;
  @override
  String get senderPublicKey;
  @override
  String? get recipientPublicKey;
  @override
  DateTime get createdAt;
  @override
  DateTime? get decryptedAt;
  @override
  MessageEncryptionMetadata get metadata;

  /// Create a copy of MessageEncryptionModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MessageEncryptionModelImplCopyWith<_$MessageEncryptionModelImpl>
  get copyWith => throw _privateConstructorUsedError;
}

MessageEncryptionMetadata _$MessageEncryptionMetadataFromJson(
  Map<String, dynamic> json,
) {
  return _MessageEncryptionMetadata.fromJson(json);
}

/// @nodoc
mixin _$MessageEncryptionMetadata {
  String get algorithm => throw _privateConstructorUsedError;
  String get keySize => throw _privateConstructorUsedError;
  String? get sessionKey => throw _privateConstructorUsedError;
  bool get isForwardSecrecy => throw _privateConstructorUsedError;
  DateTime? get keyExpiry => throw _privateConstructorUsedError;
  Map<String, dynamic>? get additionalData =>
      throw _privateConstructorUsedError;

  /// Serializes this MessageEncryptionMetadata to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MessageEncryptionMetadata
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MessageEncryptionMetadataCopyWith<MessageEncryptionMetadata> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessageEncryptionMetadataCopyWith<$Res> {
  factory $MessageEncryptionMetadataCopyWith(
    MessageEncryptionMetadata value,
    $Res Function(MessageEncryptionMetadata) then,
  ) = _$MessageEncryptionMetadataCopyWithImpl<$Res, MessageEncryptionMetadata>;
  @useResult
  $Res call({
    String algorithm,
    String keySize,
    String? sessionKey,
    bool isForwardSecrecy,
    DateTime? keyExpiry,
    Map<String, dynamic>? additionalData,
  });
}

/// @nodoc
class _$MessageEncryptionMetadataCopyWithImpl<
  $Res,
  $Val extends MessageEncryptionMetadata
>
    implements $MessageEncryptionMetadataCopyWith<$Res> {
  _$MessageEncryptionMetadataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MessageEncryptionMetadata
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? algorithm = null,
    Object? keySize = null,
    Object? sessionKey = freezed,
    Object? isForwardSecrecy = null,
    Object? keyExpiry = freezed,
    Object? additionalData = freezed,
  }) {
    return _then(
      _value.copyWith(
            algorithm: null == algorithm
                ? _value.algorithm
                : algorithm // ignore: cast_nullable_to_non_nullable
                      as String,
            keySize: null == keySize
                ? _value.keySize
                : keySize // ignore: cast_nullable_to_non_nullable
                      as String,
            sessionKey: freezed == sessionKey
                ? _value.sessionKey
                : sessionKey // ignore: cast_nullable_to_non_nullable
                      as String?,
            isForwardSecrecy: null == isForwardSecrecy
                ? _value.isForwardSecrecy
                : isForwardSecrecy // ignore: cast_nullable_to_non_nullable
                      as bool,
            keyExpiry: freezed == keyExpiry
                ? _value.keyExpiry
                : keyExpiry // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            additionalData: freezed == additionalData
                ? _value.additionalData
                : additionalData // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$MessageEncryptionMetadataImplCopyWith<$Res>
    implements $MessageEncryptionMetadataCopyWith<$Res> {
  factory _$$MessageEncryptionMetadataImplCopyWith(
    _$MessageEncryptionMetadataImpl value,
    $Res Function(_$MessageEncryptionMetadataImpl) then,
  ) = __$$MessageEncryptionMetadataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String algorithm,
    String keySize,
    String? sessionKey,
    bool isForwardSecrecy,
    DateTime? keyExpiry,
    Map<String, dynamic>? additionalData,
  });
}

/// @nodoc
class __$$MessageEncryptionMetadataImplCopyWithImpl<$Res>
    extends
        _$MessageEncryptionMetadataCopyWithImpl<
          $Res,
          _$MessageEncryptionMetadataImpl
        >
    implements _$$MessageEncryptionMetadataImplCopyWith<$Res> {
  __$$MessageEncryptionMetadataImplCopyWithImpl(
    _$MessageEncryptionMetadataImpl _value,
    $Res Function(_$MessageEncryptionMetadataImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MessageEncryptionMetadata
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? algorithm = null,
    Object? keySize = null,
    Object? sessionKey = freezed,
    Object? isForwardSecrecy = null,
    Object? keyExpiry = freezed,
    Object? additionalData = freezed,
  }) {
    return _then(
      _$MessageEncryptionMetadataImpl(
        algorithm: null == algorithm
            ? _value.algorithm
            : algorithm // ignore: cast_nullable_to_non_nullable
                  as String,
        keySize: null == keySize
            ? _value.keySize
            : keySize // ignore: cast_nullable_to_non_nullable
                  as String,
        sessionKey: freezed == sessionKey
            ? _value.sessionKey
            : sessionKey // ignore: cast_nullable_to_non_nullable
                  as String?,
        isForwardSecrecy: null == isForwardSecrecy
            ? _value.isForwardSecrecy
            : isForwardSecrecy // ignore: cast_nullable_to_non_nullable
                  as bool,
        keyExpiry: freezed == keyExpiry
            ? _value.keyExpiry
            : keyExpiry // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        additionalData: freezed == additionalData
            ? _value._additionalData
            : additionalData // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$MessageEncryptionMetadataImpl implements _MessageEncryptionMetadata {
  const _$MessageEncryptionMetadataImpl({
    required this.algorithm,
    required this.keySize,
    required this.sessionKey,
    required this.isForwardSecrecy,
    required this.keyExpiry,
    required final Map<String, dynamic>? additionalData,
  }) : _additionalData = additionalData;

  factory _$MessageEncryptionMetadataImpl.fromJson(Map<String, dynamic> json) =>
      _$$MessageEncryptionMetadataImplFromJson(json);

  @override
  final String algorithm;
  @override
  final String keySize;
  @override
  final String? sessionKey;
  @override
  final bool isForwardSecrecy;
  @override
  final DateTime? keyExpiry;
  final Map<String, dynamic>? _additionalData;
  @override
  Map<String, dynamic>? get additionalData {
    final value = _additionalData;
    if (value == null) return null;
    if (_additionalData is EqualUnmodifiableMapView) return _additionalData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'MessageEncryptionMetadata(algorithm: $algorithm, keySize: $keySize, sessionKey: $sessionKey, isForwardSecrecy: $isForwardSecrecy, keyExpiry: $keyExpiry, additionalData: $additionalData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MessageEncryptionMetadataImpl &&
            (identical(other.algorithm, algorithm) ||
                other.algorithm == algorithm) &&
            (identical(other.keySize, keySize) || other.keySize == keySize) &&
            (identical(other.sessionKey, sessionKey) ||
                other.sessionKey == sessionKey) &&
            (identical(other.isForwardSecrecy, isForwardSecrecy) ||
                other.isForwardSecrecy == isForwardSecrecy) &&
            (identical(other.keyExpiry, keyExpiry) ||
                other.keyExpiry == keyExpiry) &&
            const DeepCollectionEquality().equals(
              other._additionalData,
              _additionalData,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    algorithm,
    keySize,
    sessionKey,
    isForwardSecrecy,
    keyExpiry,
    const DeepCollectionEquality().hash(_additionalData),
  );

  /// Create a copy of MessageEncryptionMetadata
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MessageEncryptionMetadataImplCopyWith<_$MessageEncryptionMetadataImpl>
  get copyWith =>
      __$$MessageEncryptionMetadataImplCopyWithImpl<
        _$MessageEncryptionMetadataImpl
      >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MessageEncryptionMetadataImplToJson(this);
  }
}

abstract class _MessageEncryptionMetadata implements MessageEncryptionMetadata {
  const factory _MessageEncryptionMetadata({
    required final String algorithm,
    required final String keySize,
    required final String? sessionKey,
    required final bool isForwardSecrecy,
    required final DateTime? keyExpiry,
    required final Map<String, dynamic>? additionalData,
  }) = _$MessageEncryptionMetadataImpl;

  factory _MessageEncryptionMetadata.fromJson(Map<String, dynamic> json) =
      _$MessageEncryptionMetadataImpl.fromJson;

  @override
  String get algorithm;
  @override
  String get keySize;
  @override
  String? get sessionKey;
  @override
  bool get isForwardSecrecy;
  @override
  DateTime? get keyExpiry;
  @override
  Map<String, dynamic>? get additionalData;

  /// Create a copy of MessageEncryptionMetadata
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MessageEncryptionMetadataImplCopyWith<_$MessageEncryptionMetadataImpl>
  get copyWith => throw _privateConstructorUsedError;
}

EncryptionKeyModel _$EncryptionKeyModelFromJson(Map<String, dynamic> json) {
  return _EncryptionKeyModel.fromJson(json);
}

/// @nodoc
mixin _$EncryptionKeyModel {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get publicKey => throw _privateConstructorUsedError;
  String? get privateKey =>
      throw _privateConstructorUsedError; // Only stored locally
  KeyType get type => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime? get expiresAt => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  List<String> get authorizedUsers => throw _privateConstructorUsedError;

  /// Serializes this EncryptionKeyModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of EncryptionKeyModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EncryptionKeyModelCopyWith<EncryptionKeyModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EncryptionKeyModelCopyWith<$Res> {
  factory $EncryptionKeyModelCopyWith(
    EncryptionKeyModel value,
    $Res Function(EncryptionKeyModel) then,
  ) = _$EncryptionKeyModelCopyWithImpl<$Res, EncryptionKeyModel>;
  @useResult
  $Res call({
    String id,
    String userId,
    String publicKey,
    String? privateKey,
    KeyType type,
    DateTime createdAt,
    DateTime? expiresAt,
    bool isActive,
    List<String> authorizedUsers,
  });
}

/// @nodoc
class _$EncryptionKeyModelCopyWithImpl<$Res, $Val extends EncryptionKeyModel>
    implements $EncryptionKeyModelCopyWith<$Res> {
  _$EncryptionKeyModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EncryptionKeyModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? publicKey = null,
    Object? privateKey = freezed,
    Object? type = null,
    Object? createdAt = null,
    Object? expiresAt = freezed,
    Object? isActive = null,
    Object? authorizedUsers = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            publicKey: null == publicKey
                ? _value.publicKey
                : publicKey // ignore: cast_nullable_to_non_nullable
                      as String,
            privateKey: freezed == privateKey
                ? _value.privateKey
                : privateKey // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as KeyType,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            expiresAt: freezed == expiresAt
                ? _value.expiresAt
                : expiresAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            isActive: null == isActive
                ? _value.isActive
                : isActive // ignore: cast_nullable_to_non_nullable
                      as bool,
            authorizedUsers: null == authorizedUsers
                ? _value.authorizedUsers
                : authorizedUsers // ignore: cast_nullable_to_non_nullable
                      as List<String>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$EncryptionKeyModelImplCopyWith<$Res>
    implements $EncryptionKeyModelCopyWith<$Res> {
  factory _$$EncryptionKeyModelImplCopyWith(
    _$EncryptionKeyModelImpl value,
    $Res Function(_$EncryptionKeyModelImpl) then,
  ) = __$$EncryptionKeyModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    String publicKey,
    String? privateKey,
    KeyType type,
    DateTime createdAt,
    DateTime? expiresAt,
    bool isActive,
    List<String> authorizedUsers,
  });
}

/// @nodoc
class __$$EncryptionKeyModelImplCopyWithImpl<$Res>
    extends _$EncryptionKeyModelCopyWithImpl<$Res, _$EncryptionKeyModelImpl>
    implements _$$EncryptionKeyModelImplCopyWith<$Res> {
  __$$EncryptionKeyModelImplCopyWithImpl(
    _$EncryptionKeyModelImpl _value,
    $Res Function(_$EncryptionKeyModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EncryptionKeyModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? publicKey = null,
    Object? privateKey = freezed,
    Object? type = null,
    Object? createdAt = null,
    Object? expiresAt = freezed,
    Object? isActive = null,
    Object? authorizedUsers = null,
  }) {
    return _then(
      _$EncryptionKeyModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        publicKey: null == publicKey
            ? _value.publicKey
            : publicKey // ignore: cast_nullable_to_non_nullable
                  as String,
        privateKey: freezed == privateKey
            ? _value.privateKey
            : privateKey // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as KeyType,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        expiresAt: freezed == expiresAt
            ? _value.expiresAt
            : expiresAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        isActive: null == isActive
            ? _value.isActive
            : isActive // ignore: cast_nullable_to_non_nullable
                  as bool,
        authorizedUsers: null == authorizedUsers
            ? _value._authorizedUsers
            : authorizedUsers // ignore: cast_nullable_to_non_nullable
                  as List<String>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$EncryptionKeyModelImpl implements _EncryptionKeyModel {
  const _$EncryptionKeyModelImpl({
    required this.id,
    required this.userId,
    required this.publicKey,
    required this.privateKey,
    required this.type,
    required this.createdAt,
    required this.expiresAt,
    required this.isActive,
    required final List<String> authorizedUsers,
  }) : _authorizedUsers = authorizedUsers;

  factory _$EncryptionKeyModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$EncryptionKeyModelImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String publicKey;
  @override
  final String? privateKey;
  // Only stored locally
  @override
  final KeyType type;
  @override
  final DateTime createdAt;
  @override
  final DateTime? expiresAt;
  @override
  final bool isActive;
  final List<String> _authorizedUsers;
  @override
  List<String> get authorizedUsers {
    if (_authorizedUsers is EqualUnmodifiableListView) return _authorizedUsers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_authorizedUsers);
  }

  @override
  String toString() {
    return 'EncryptionKeyModel(id: $id, userId: $userId, publicKey: $publicKey, privateKey: $privateKey, type: $type, createdAt: $createdAt, expiresAt: $expiresAt, isActive: $isActive, authorizedUsers: $authorizedUsers)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EncryptionKeyModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.publicKey, publicKey) ||
                other.publicKey == publicKey) &&
            (identical(other.privateKey, privateKey) ||
                other.privateKey == privateKey) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            const DeepCollectionEquality().equals(
              other._authorizedUsers,
              _authorizedUsers,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    userId,
    publicKey,
    privateKey,
    type,
    createdAt,
    expiresAt,
    isActive,
    const DeepCollectionEquality().hash(_authorizedUsers),
  );

  /// Create a copy of EncryptionKeyModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EncryptionKeyModelImplCopyWith<_$EncryptionKeyModelImpl> get copyWith =>
      __$$EncryptionKeyModelImplCopyWithImpl<_$EncryptionKeyModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$EncryptionKeyModelImplToJson(this);
  }
}

abstract class _EncryptionKeyModel implements EncryptionKeyModel {
  const factory _EncryptionKeyModel({
    required final String id,
    required final String userId,
    required final String publicKey,
    required final String? privateKey,
    required final KeyType type,
    required final DateTime createdAt,
    required final DateTime? expiresAt,
    required final bool isActive,
    required final List<String> authorizedUsers,
  }) = _$EncryptionKeyModelImpl;

  factory _EncryptionKeyModel.fromJson(Map<String, dynamic> json) =
      _$EncryptionKeyModelImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get publicKey;
  @override
  String? get privateKey; // Only stored locally
  @override
  KeyType get type;
  @override
  DateTime get createdAt;
  @override
  DateTime? get expiresAt;
  @override
  bool get isActive;
  @override
  List<String> get authorizedUsers;

  /// Create a copy of EncryptionKeyModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EncryptionKeyModelImplCopyWith<_$EncryptionKeyModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

EncryptionSettings _$EncryptionSettingsFromJson(Map<String, dynamic> json) {
  return _EncryptionSettings.fromJson(json);
}

/// @nodoc
mixin _$EncryptionSettings {
  bool get enableEndToEndEncryption => throw _privateConstructorUsedError;
  bool get requireEncryption => throw _privateConstructorUsedError;
  EncryptionAlgorithm get algorithm => throw _privateConstructorUsedError;
  KeyExchangeProtocol get keyExchange => throw _privateConstructorUsedError;
  bool get enableForwardSecrecy => throw _privateConstructorUsedError;
  int get keyRotationDays => throw _privateConstructorUsedError;
  bool get showEncryptionStatus => throw _privateConstructorUsedError;
  bool get verifyKeyFingerprints => throw _privateConstructorUsedError;
  bool get allowUnencryptedMessages => throw _privateConstructorUsedError;
  bool get autoEncryptMedia => throw _privateConstructorUsedError;

  /// Serializes this EncryptionSettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of EncryptionSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EncryptionSettingsCopyWith<EncryptionSettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EncryptionSettingsCopyWith<$Res> {
  factory $EncryptionSettingsCopyWith(
    EncryptionSettings value,
    $Res Function(EncryptionSettings) then,
  ) = _$EncryptionSettingsCopyWithImpl<$Res, EncryptionSettings>;
  @useResult
  $Res call({
    bool enableEndToEndEncryption,
    bool requireEncryption,
    EncryptionAlgorithm algorithm,
    KeyExchangeProtocol keyExchange,
    bool enableForwardSecrecy,
    int keyRotationDays,
    bool showEncryptionStatus,
    bool verifyKeyFingerprints,
    bool allowUnencryptedMessages,
    bool autoEncryptMedia,
  });
}

/// @nodoc
class _$EncryptionSettingsCopyWithImpl<$Res, $Val extends EncryptionSettings>
    implements $EncryptionSettingsCopyWith<$Res> {
  _$EncryptionSettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EncryptionSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enableEndToEndEncryption = null,
    Object? requireEncryption = null,
    Object? algorithm = null,
    Object? keyExchange = null,
    Object? enableForwardSecrecy = null,
    Object? keyRotationDays = null,
    Object? showEncryptionStatus = null,
    Object? verifyKeyFingerprints = null,
    Object? allowUnencryptedMessages = null,
    Object? autoEncryptMedia = null,
  }) {
    return _then(
      _value.copyWith(
            enableEndToEndEncryption: null == enableEndToEndEncryption
                ? _value.enableEndToEndEncryption
                : enableEndToEndEncryption // ignore: cast_nullable_to_non_nullable
                      as bool,
            requireEncryption: null == requireEncryption
                ? _value.requireEncryption
                : requireEncryption // ignore: cast_nullable_to_non_nullable
                      as bool,
            algorithm: null == algorithm
                ? _value.algorithm
                : algorithm // ignore: cast_nullable_to_non_nullable
                      as EncryptionAlgorithm,
            keyExchange: null == keyExchange
                ? _value.keyExchange
                : keyExchange // ignore: cast_nullable_to_non_nullable
                      as KeyExchangeProtocol,
            enableForwardSecrecy: null == enableForwardSecrecy
                ? _value.enableForwardSecrecy
                : enableForwardSecrecy // ignore: cast_nullable_to_non_nullable
                      as bool,
            keyRotationDays: null == keyRotationDays
                ? _value.keyRotationDays
                : keyRotationDays // ignore: cast_nullable_to_non_nullable
                      as int,
            showEncryptionStatus: null == showEncryptionStatus
                ? _value.showEncryptionStatus
                : showEncryptionStatus // ignore: cast_nullable_to_non_nullable
                      as bool,
            verifyKeyFingerprints: null == verifyKeyFingerprints
                ? _value.verifyKeyFingerprints
                : verifyKeyFingerprints // ignore: cast_nullable_to_non_nullable
                      as bool,
            allowUnencryptedMessages: null == allowUnencryptedMessages
                ? _value.allowUnencryptedMessages
                : allowUnencryptedMessages // ignore: cast_nullable_to_non_nullable
                      as bool,
            autoEncryptMedia: null == autoEncryptMedia
                ? _value.autoEncryptMedia
                : autoEncryptMedia // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$EncryptionSettingsImplCopyWith<$Res>
    implements $EncryptionSettingsCopyWith<$Res> {
  factory _$$EncryptionSettingsImplCopyWith(
    _$EncryptionSettingsImpl value,
    $Res Function(_$EncryptionSettingsImpl) then,
  ) = __$$EncryptionSettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    bool enableEndToEndEncryption,
    bool requireEncryption,
    EncryptionAlgorithm algorithm,
    KeyExchangeProtocol keyExchange,
    bool enableForwardSecrecy,
    int keyRotationDays,
    bool showEncryptionStatus,
    bool verifyKeyFingerprints,
    bool allowUnencryptedMessages,
    bool autoEncryptMedia,
  });
}

/// @nodoc
class __$$EncryptionSettingsImplCopyWithImpl<$Res>
    extends _$EncryptionSettingsCopyWithImpl<$Res, _$EncryptionSettingsImpl>
    implements _$$EncryptionSettingsImplCopyWith<$Res> {
  __$$EncryptionSettingsImplCopyWithImpl(
    _$EncryptionSettingsImpl _value,
    $Res Function(_$EncryptionSettingsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EncryptionSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enableEndToEndEncryption = null,
    Object? requireEncryption = null,
    Object? algorithm = null,
    Object? keyExchange = null,
    Object? enableForwardSecrecy = null,
    Object? keyRotationDays = null,
    Object? showEncryptionStatus = null,
    Object? verifyKeyFingerprints = null,
    Object? allowUnencryptedMessages = null,
    Object? autoEncryptMedia = null,
  }) {
    return _then(
      _$EncryptionSettingsImpl(
        enableEndToEndEncryption: null == enableEndToEndEncryption
            ? _value.enableEndToEndEncryption
            : enableEndToEndEncryption // ignore: cast_nullable_to_non_nullable
                  as bool,
        requireEncryption: null == requireEncryption
            ? _value.requireEncryption
            : requireEncryption // ignore: cast_nullable_to_non_nullable
                  as bool,
        algorithm: null == algorithm
            ? _value.algorithm
            : algorithm // ignore: cast_nullable_to_non_nullable
                  as EncryptionAlgorithm,
        keyExchange: null == keyExchange
            ? _value.keyExchange
            : keyExchange // ignore: cast_nullable_to_non_nullable
                  as KeyExchangeProtocol,
        enableForwardSecrecy: null == enableForwardSecrecy
            ? _value.enableForwardSecrecy
            : enableForwardSecrecy // ignore: cast_nullable_to_non_nullable
                  as bool,
        keyRotationDays: null == keyRotationDays
            ? _value.keyRotationDays
            : keyRotationDays // ignore: cast_nullable_to_non_nullable
                  as int,
        showEncryptionStatus: null == showEncryptionStatus
            ? _value.showEncryptionStatus
            : showEncryptionStatus // ignore: cast_nullable_to_non_nullable
                  as bool,
        verifyKeyFingerprints: null == verifyKeyFingerprints
            ? _value.verifyKeyFingerprints
            : verifyKeyFingerprints // ignore: cast_nullable_to_non_nullable
                  as bool,
        allowUnencryptedMessages: null == allowUnencryptedMessages
            ? _value.allowUnencryptedMessages
            : allowUnencryptedMessages // ignore: cast_nullable_to_non_nullable
                  as bool,
        autoEncryptMedia: null == autoEncryptMedia
            ? _value.autoEncryptMedia
            : autoEncryptMedia // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$EncryptionSettingsImpl implements _EncryptionSettings {
  const _$EncryptionSettingsImpl({
    this.enableEndToEndEncryption = true,
    this.requireEncryption = true,
    this.algorithm = EncryptionAlgorithm.aes256,
    this.keyExchange = KeyExchangeProtocol.diffieHellman,
    this.enableForwardSecrecy = true,
    this.keyRotationDays = 30,
    this.showEncryptionStatus = true,
    this.verifyKeyFingerprints = true,
    this.allowUnencryptedMessages = false,
    this.autoEncryptMedia = true,
  });

  factory _$EncryptionSettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$EncryptionSettingsImplFromJson(json);

  @override
  @JsonKey()
  final bool enableEndToEndEncryption;
  @override
  @JsonKey()
  final bool requireEncryption;
  @override
  @JsonKey()
  final EncryptionAlgorithm algorithm;
  @override
  @JsonKey()
  final KeyExchangeProtocol keyExchange;
  @override
  @JsonKey()
  final bool enableForwardSecrecy;
  @override
  @JsonKey()
  final int keyRotationDays;
  @override
  @JsonKey()
  final bool showEncryptionStatus;
  @override
  @JsonKey()
  final bool verifyKeyFingerprints;
  @override
  @JsonKey()
  final bool allowUnencryptedMessages;
  @override
  @JsonKey()
  final bool autoEncryptMedia;

  @override
  String toString() {
    return 'EncryptionSettings(enableEndToEndEncryption: $enableEndToEndEncryption, requireEncryption: $requireEncryption, algorithm: $algorithm, keyExchange: $keyExchange, enableForwardSecrecy: $enableForwardSecrecy, keyRotationDays: $keyRotationDays, showEncryptionStatus: $showEncryptionStatus, verifyKeyFingerprints: $verifyKeyFingerprints, allowUnencryptedMessages: $allowUnencryptedMessages, autoEncryptMedia: $autoEncryptMedia)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EncryptionSettingsImpl &&
            (identical(
                  other.enableEndToEndEncryption,
                  enableEndToEndEncryption,
                ) ||
                other.enableEndToEndEncryption == enableEndToEndEncryption) &&
            (identical(other.requireEncryption, requireEncryption) ||
                other.requireEncryption == requireEncryption) &&
            (identical(other.algorithm, algorithm) ||
                other.algorithm == algorithm) &&
            (identical(other.keyExchange, keyExchange) ||
                other.keyExchange == keyExchange) &&
            (identical(other.enableForwardSecrecy, enableForwardSecrecy) ||
                other.enableForwardSecrecy == enableForwardSecrecy) &&
            (identical(other.keyRotationDays, keyRotationDays) ||
                other.keyRotationDays == keyRotationDays) &&
            (identical(other.showEncryptionStatus, showEncryptionStatus) ||
                other.showEncryptionStatus == showEncryptionStatus) &&
            (identical(other.verifyKeyFingerprints, verifyKeyFingerprints) ||
                other.verifyKeyFingerprints == verifyKeyFingerprints) &&
            (identical(
                  other.allowUnencryptedMessages,
                  allowUnencryptedMessages,
                ) ||
                other.allowUnencryptedMessages == allowUnencryptedMessages) &&
            (identical(other.autoEncryptMedia, autoEncryptMedia) ||
                other.autoEncryptMedia == autoEncryptMedia));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    enableEndToEndEncryption,
    requireEncryption,
    algorithm,
    keyExchange,
    enableForwardSecrecy,
    keyRotationDays,
    showEncryptionStatus,
    verifyKeyFingerprints,
    allowUnencryptedMessages,
    autoEncryptMedia,
  );

  /// Create a copy of EncryptionSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EncryptionSettingsImplCopyWith<_$EncryptionSettingsImpl> get copyWith =>
      __$$EncryptionSettingsImplCopyWithImpl<_$EncryptionSettingsImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$EncryptionSettingsImplToJson(this);
  }
}

abstract class _EncryptionSettings implements EncryptionSettings {
  const factory _EncryptionSettings({
    final bool enableEndToEndEncryption,
    final bool requireEncryption,
    final EncryptionAlgorithm algorithm,
    final KeyExchangeProtocol keyExchange,
    final bool enableForwardSecrecy,
    final int keyRotationDays,
    final bool showEncryptionStatus,
    final bool verifyKeyFingerprints,
    final bool allowUnencryptedMessages,
    final bool autoEncryptMedia,
  }) = _$EncryptionSettingsImpl;

  factory _EncryptionSettings.fromJson(Map<String, dynamic> json) =
      _$EncryptionSettingsImpl.fromJson;

  @override
  bool get enableEndToEndEncryption;
  @override
  bool get requireEncryption;
  @override
  EncryptionAlgorithm get algorithm;
  @override
  KeyExchangeProtocol get keyExchange;
  @override
  bool get enableForwardSecrecy;
  @override
  int get keyRotationDays;
  @override
  bool get showEncryptionStatus;
  @override
  bool get verifyKeyFingerprints;
  @override
  bool get allowUnencryptedMessages;
  @override
  bool get autoEncryptMedia;

  /// Create a copy of EncryptionSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EncryptionSettingsImplCopyWith<_$EncryptionSettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

KeyExchangeSession _$KeyExchangeSessionFromJson(Map<String, dynamic> json) {
  return _KeyExchangeSession.fromJson(json);
}

/// @nodoc
mixin _$KeyExchangeSession {
  String get id => throw _privateConstructorUsedError;
  String get initiatorId => throw _privateConstructorUsedError;
  String get recipientId => throw _privateConstructorUsedError;
  KeyExchangeStatus get status => throw _privateConstructorUsedError;
  String get initiatorPublicKey => throw _privateConstructorUsedError;
  String? get recipientPublicKey => throw _privateConstructorUsedError;
  String? get sharedSecret => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime? get completedAt => throw _privateConstructorUsedError;
  DateTime? get expiresAt => throw _privateConstructorUsedError;

  /// Serializes this KeyExchangeSession to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of KeyExchangeSession
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $KeyExchangeSessionCopyWith<KeyExchangeSession> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $KeyExchangeSessionCopyWith<$Res> {
  factory $KeyExchangeSessionCopyWith(
    KeyExchangeSession value,
    $Res Function(KeyExchangeSession) then,
  ) = _$KeyExchangeSessionCopyWithImpl<$Res, KeyExchangeSession>;
  @useResult
  $Res call({
    String id,
    String initiatorId,
    String recipientId,
    KeyExchangeStatus status,
    String initiatorPublicKey,
    String? recipientPublicKey,
    String? sharedSecret,
    DateTime createdAt,
    DateTime? completedAt,
    DateTime? expiresAt,
  });
}

/// @nodoc
class _$KeyExchangeSessionCopyWithImpl<$Res, $Val extends KeyExchangeSession>
    implements $KeyExchangeSessionCopyWith<$Res> {
  _$KeyExchangeSessionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of KeyExchangeSession
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? initiatorId = null,
    Object? recipientId = null,
    Object? status = null,
    Object? initiatorPublicKey = null,
    Object? recipientPublicKey = freezed,
    Object? sharedSecret = freezed,
    Object? createdAt = null,
    Object? completedAt = freezed,
    Object? expiresAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            initiatorId: null == initiatorId
                ? _value.initiatorId
                : initiatorId // ignore: cast_nullable_to_non_nullable
                      as String,
            recipientId: null == recipientId
                ? _value.recipientId
                : recipientId // ignore: cast_nullable_to_non_nullable
                      as String,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as KeyExchangeStatus,
            initiatorPublicKey: null == initiatorPublicKey
                ? _value.initiatorPublicKey
                : initiatorPublicKey // ignore: cast_nullable_to_non_nullable
                      as String,
            recipientPublicKey: freezed == recipientPublicKey
                ? _value.recipientPublicKey
                : recipientPublicKey // ignore: cast_nullable_to_non_nullable
                      as String?,
            sharedSecret: freezed == sharedSecret
                ? _value.sharedSecret
                : sharedSecret // ignore: cast_nullable_to_non_nullable
                      as String?,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            completedAt: freezed == completedAt
                ? _value.completedAt
                : completedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            expiresAt: freezed == expiresAt
                ? _value.expiresAt
                : expiresAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$KeyExchangeSessionImplCopyWith<$Res>
    implements $KeyExchangeSessionCopyWith<$Res> {
  factory _$$KeyExchangeSessionImplCopyWith(
    _$KeyExchangeSessionImpl value,
    $Res Function(_$KeyExchangeSessionImpl) then,
  ) = __$$KeyExchangeSessionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String initiatorId,
    String recipientId,
    KeyExchangeStatus status,
    String initiatorPublicKey,
    String? recipientPublicKey,
    String? sharedSecret,
    DateTime createdAt,
    DateTime? completedAt,
    DateTime? expiresAt,
  });
}

/// @nodoc
class __$$KeyExchangeSessionImplCopyWithImpl<$Res>
    extends _$KeyExchangeSessionCopyWithImpl<$Res, _$KeyExchangeSessionImpl>
    implements _$$KeyExchangeSessionImplCopyWith<$Res> {
  __$$KeyExchangeSessionImplCopyWithImpl(
    _$KeyExchangeSessionImpl _value,
    $Res Function(_$KeyExchangeSessionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of KeyExchangeSession
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? initiatorId = null,
    Object? recipientId = null,
    Object? status = null,
    Object? initiatorPublicKey = null,
    Object? recipientPublicKey = freezed,
    Object? sharedSecret = freezed,
    Object? createdAt = null,
    Object? completedAt = freezed,
    Object? expiresAt = freezed,
  }) {
    return _then(
      _$KeyExchangeSessionImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        initiatorId: null == initiatorId
            ? _value.initiatorId
            : initiatorId // ignore: cast_nullable_to_non_nullable
                  as String,
        recipientId: null == recipientId
            ? _value.recipientId
            : recipientId // ignore: cast_nullable_to_non_nullable
                  as String,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as KeyExchangeStatus,
        initiatorPublicKey: null == initiatorPublicKey
            ? _value.initiatorPublicKey
            : initiatorPublicKey // ignore: cast_nullable_to_non_nullable
                  as String,
        recipientPublicKey: freezed == recipientPublicKey
            ? _value.recipientPublicKey
            : recipientPublicKey // ignore: cast_nullable_to_non_nullable
                  as String?,
        sharedSecret: freezed == sharedSecret
            ? _value.sharedSecret
            : sharedSecret // ignore: cast_nullable_to_non_nullable
                  as String?,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        completedAt: freezed == completedAt
            ? _value.completedAt
            : completedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        expiresAt: freezed == expiresAt
            ? _value.expiresAt
            : expiresAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$KeyExchangeSessionImpl implements _KeyExchangeSession {
  const _$KeyExchangeSessionImpl({
    required this.id,
    required this.initiatorId,
    required this.recipientId,
    required this.status,
    required this.initiatorPublicKey,
    required this.recipientPublicKey,
    required this.sharedSecret,
    required this.createdAt,
    required this.completedAt,
    required this.expiresAt,
  });

  factory _$KeyExchangeSessionImpl.fromJson(Map<String, dynamic> json) =>
      _$$KeyExchangeSessionImplFromJson(json);

  @override
  final String id;
  @override
  final String initiatorId;
  @override
  final String recipientId;
  @override
  final KeyExchangeStatus status;
  @override
  final String initiatorPublicKey;
  @override
  final String? recipientPublicKey;
  @override
  final String? sharedSecret;
  @override
  final DateTime createdAt;
  @override
  final DateTime? completedAt;
  @override
  final DateTime? expiresAt;

  @override
  String toString() {
    return 'KeyExchangeSession(id: $id, initiatorId: $initiatorId, recipientId: $recipientId, status: $status, initiatorPublicKey: $initiatorPublicKey, recipientPublicKey: $recipientPublicKey, sharedSecret: $sharedSecret, createdAt: $createdAt, completedAt: $completedAt, expiresAt: $expiresAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$KeyExchangeSessionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.initiatorId, initiatorId) ||
                other.initiatorId == initiatorId) &&
            (identical(other.recipientId, recipientId) ||
                other.recipientId == recipientId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.initiatorPublicKey, initiatorPublicKey) ||
                other.initiatorPublicKey == initiatorPublicKey) &&
            (identical(other.recipientPublicKey, recipientPublicKey) ||
                other.recipientPublicKey == recipientPublicKey) &&
            (identical(other.sharedSecret, sharedSecret) ||
                other.sharedSecret == sharedSecret) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.completedAt, completedAt) ||
                other.completedAt == completedAt) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    initiatorId,
    recipientId,
    status,
    initiatorPublicKey,
    recipientPublicKey,
    sharedSecret,
    createdAt,
    completedAt,
    expiresAt,
  );

  /// Create a copy of KeyExchangeSession
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$KeyExchangeSessionImplCopyWith<_$KeyExchangeSessionImpl> get copyWith =>
      __$$KeyExchangeSessionImplCopyWithImpl<_$KeyExchangeSessionImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$KeyExchangeSessionImplToJson(this);
  }
}

abstract class _KeyExchangeSession implements KeyExchangeSession {
  const factory _KeyExchangeSession({
    required final String id,
    required final String initiatorId,
    required final String recipientId,
    required final KeyExchangeStatus status,
    required final String initiatorPublicKey,
    required final String? recipientPublicKey,
    required final String? sharedSecret,
    required final DateTime createdAt,
    required final DateTime? completedAt,
    required final DateTime? expiresAt,
  }) = _$KeyExchangeSessionImpl;

  factory _KeyExchangeSession.fromJson(Map<String, dynamic> json) =
      _$KeyExchangeSessionImpl.fromJson;

  @override
  String get id;
  @override
  String get initiatorId;
  @override
  String get recipientId;
  @override
  KeyExchangeStatus get status;
  @override
  String get initiatorPublicKey;
  @override
  String? get recipientPublicKey;
  @override
  String? get sharedSecret;
  @override
  DateTime get createdAt;
  @override
  DateTime? get completedAt;
  @override
  DateTime? get expiresAt;

  /// Create a copy of KeyExchangeSession
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$KeyExchangeSessionImplCopyWith<_$KeyExchangeSessionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
