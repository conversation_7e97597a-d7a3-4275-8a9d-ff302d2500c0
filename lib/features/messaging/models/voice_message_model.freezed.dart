// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'voice_message_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

VoiceMessageModel _$VoiceMessageModelFromJson(Map<String, dynamic> json) {
  return _VoiceMessageModel.fromJson(json);
}

/// @nodoc
mixin _$VoiceMessageModel {
  String get id => throw _privateConstructorUsedError;
  String get messageId => throw _privateConstructorUsedError;
  String get audioUrl => throw _privateConstructorUsedError;
  int get durationInSeconds => throw _privateConstructorUsedError;
  String? get transcription => throw _privateConstructorUsedError;
  VoiceMessageStatus get status => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime? get playedAt => throw _privateConstructorUsedError;
  List<VoiceMessageReaction> get reactions =>
      throw _privateConstructorUsedError;
  VoiceMessageMetadata get metadata => throw _privateConstructorUsedError;

  /// Serializes this VoiceMessageModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VoiceMessageModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VoiceMessageModelCopyWith<VoiceMessageModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VoiceMessageModelCopyWith<$Res> {
  factory $VoiceMessageModelCopyWith(
    VoiceMessageModel value,
    $Res Function(VoiceMessageModel) then,
  ) = _$VoiceMessageModelCopyWithImpl<$Res, VoiceMessageModel>;
  @useResult
  $Res call({
    String id,
    String messageId,
    String audioUrl,
    int durationInSeconds,
    String? transcription,
    VoiceMessageStatus status,
    DateTime createdAt,
    DateTime? playedAt,
    List<VoiceMessageReaction> reactions,
    VoiceMessageMetadata metadata,
  });

  $VoiceMessageMetadataCopyWith<$Res> get metadata;
}

/// @nodoc
class _$VoiceMessageModelCopyWithImpl<$Res, $Val extends VoiceMessageModel>
    implements $VoiceMessageModelCopyWith<$Res> {
  _$VoiceMessageModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VoiceMessageModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? messageId = null,
    Object? audioUrl = null,
    Object? durationInSeconds = null,
    Object? transcription = freezed,
    Object? status = null,
    Object? createdAt = null,
    Object? playedAt = freezed,
    Object? reactions = null,
    Object? metadata = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            messageId: null == messageId
                ? _value.messageId
                : messageId // ignore: cast_nullable_to_non_nullable
                      as String,
            audioUrl: null == audioUrl
                ? _value.audioUrl
                : audioUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            durationInSeconds: null == durationInSeconds
                ? _value.durationInSeconds
                : durationInSeconds // ignore: cast_nullable_to_non_nullable
                      as int,
            transcription: freezed == transcription
                ? _value.transcription
                : transcription // ignore: cast_nullable_to_non_nullable
                      as String?,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as VoiceMessageStatus,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            playedAt: freezed == playedAt
                ? _value.playedAt
                : playedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            reactions: null == reactions
                ? _value.reactions
                : reactions // ignore: cast_nullable_to_non_nullable
                      as List<VoiceMessageReaction>,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as VoiceMessageMetadata,
          )
          as $Val,
    );
  }

  /// Create a copy of VoiceMessageModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $VoiceMessageMetadataCopyWith<$Res> get metadata {
    return $VoiceMessageMetadataCopyWith<$Res>(_value.metadata, (value) {
      return _then(_value.copyWith(metadata: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$VoiceMessageModelImplCopyWith<$Res>
    implements $VoiceMessageModelCopyWith<$Res> {
  factory _$$VoiceMessageModelImplCopyWith(
    _$VoiceMessageModelImpl value,
    $Res Function(_$VoiceMessageModelImpl) then,
  ) = __$$VoiceMessageModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String messageId,
    String audioUrl,
    int durationInSeconds,
    String? transcription,
    VoiceMessageStatus status,
    DateTime createdAt,
    DateTime? playedAt,
    List<VoiceMessageReaction> reactions,
    VoiceMessageMetadata metadata,
  });

  @override
  $VoiceMessageMetadataCopyWith<$Res> get metadata;
}

/// @nodoc
class __$$VoiceMessageModelImplCopyWithImpl<$Res>
    extends _$VoiceMessageModelCopyWithImpl<$Res, _$VoiceMessageModelImpl>
    implements _$$VoiceMessageModelImplCopyWith<$Res> {
  __$$VoiceMessageModelImplCopyWithImpl(
    _$VoiceMessageModelImpl _value,
    $Res Function(_$VoiceMessageModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of VoiceMessageModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? messageId = null,
    Object? audioUrl = null,
    Object? durationInSeconds = null,
    Object? transcription = freezed,
    Object? status = null,
    Object? createdAt = null,
    Object? playedAt = freezed,
    Object? reactions = null,
    Object? metadata = null,
  }) {
    return _then(
      _$VoiceMessageModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        messageId: null == messageId
            ? _value.messageId
            : messageId // ignore: cast_nullable_to_non_nullable
                  as String,
        audioUrl: null == audioUrl
            ? _value.audioUrl
            : audioUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        durationInSeconds: null == durationInSeconds
            ? _value.durationInSeconds
            : durationInSeconds // ignore: cast_nullable_to_non_nullable
                  as int,
        transcription: freezed == transcription
            ? _value.transcription
            : transcription // ignore: cast_nullable_to_non_nullable
                  as String?,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as VoiceMessageStatus,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        playedAt: freezed == playedAt
            ? _value.playedAt
            : playedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        reactions: null == reactions
            ? _value._reactions
            : reactions // ignore: cast_nullable_to_non_nullable
                  as List<VoiceMessageReaction>,
        metadata: null == metadata
            ? _value.metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as VoiceMessageMetadata,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$VoiceMessageModelImpl implements _VoiceMessageModel {
  const _$VoiceMessageModelImpl({
    required this.id,
    required this.messageId,
    required this.audioUrl,
    required this.durationInSeconds,
    required this.transcription,
    required this.status,
    required this.createdAt,
    required this.playedAt,
    required final List<VoiceMessageReaction> reactions,
    required this.metadata,
  }) : _reactions = reactions;

  factory _$VoiceMessageModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$VoiceMessageModelImplFromJson(json);

  @override
  final String id;
  @override
  final String messageId;
  @override
  final String audioUrl;
  @override
  final int durationInSeconds;
  @override
  final String? transcription;
  @override
  final VoiceMessageStatus status;
  @override
  final DateTime createdAt;
  @override
  final DateTime? playedAt;
  final List<VoiceMessageReaction> _reactions;
  @override
  List<VoiceMessageReaction> get reactions {
    if (_reactions is EqualUnmodifiableListView) return _reactions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_reactions);
  }

  @override
  final VoiceMessageMetadata metadata;

  @override
  String toString() {
    return 'VoiceMessageModel(id: $id, messageId: $messageId, audioUrl: $audioUrl, durationInSeconds: $durationInSeconds, transcription: $transcription, status: $status, createdAt: $createdAt, playedAt: $playedAt, reactions: $reactions, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VoiceMessageModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.audioUrl, audioUrl) ||
                other.audioUrl == audioUrl) &&
            (identical(other.durationInSeconds, durationInSeconds) ||
                other.durationInSeconds == durationInSeconds) &&
            (identical(other.transcription, transcription) ||
                other.transcription == transcription) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.playedAt, playedAt) ||
                other.playedAt == playedAt) &&
            const DeepCollectionEquality().equals(
              other._reactions,
              _reactions,
            ) &&
            (identical(other.metadata, metadata) ||
                other.metadata == metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    messageId,
    audioUrl,
    durationInSeconds,
    transcription,
    status,
    createdAt,
    playedAt,
    const DeepCollectionEquality().hash(_reactions),
    metadata,
  );

  /// Create a copy of VoiceMessageModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VoiceMessageModelImplCopyWith<_$VoiceMessageModelImpl> get copyWith =>
      __$$VoiceMessageModelImplCopyWithImpl<_$VoiceMessageModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$VoiceMessageModelImplToJson(this);
  }
}

abstract class _VoiceMessageModel implements VoiceMessageModel {
  const factory _VoiceMessageModel({
    required final String id,
    required final String messageId,
    required final String audioUrl,
    required final int durationInSeconds,
    required final String? transcription,
    required final VoiceMessageStatus status,
    required final DateTime createdAt,
    required final DateTime? playedAt,
    required final List<VoiceMessageReaction> reactions,
    required final VoiceMessageMetadata metadata,
  }) = _$VoiceMessageModelImpl;

  factory _VoiceMessageModel.fromJson(Map<String, dynamic> json) =
      _$VoiceMessageModelImpl.fromJson;

  @override
  String get id;
  @override
  String get messageId;
  @override
  String get audioUrl;
  @override
  int get durationInSeconds;
  @override
  String? get transcription;
  @override
  VoiceMessageStatus get status;
  @override
  DateTime get createdAt;
  @override
  DateTime? get playedAt;
  @override
  List<VoiceMessageReaction> get reactions;
  @override
  VoiceMessageMetadata get metadata;

  /// Create a copy of VoiceMessageModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VoiceMessageModelImplCopyWith<_$VoiceMessageModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

VoiceMessageReaction _$VoiceMessageReactionFromJson(Map<String, dynamic> json) {
  return _VoiceMessageReaction.fromJson(json);
}

/// @nodoc
mixin _$VoiceMessageReaction {
  String get userId => throw _privateConstructorUsedError;
  VoiceReactionType get type => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;

  /// Serializes this VoiceMessageReaction to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VoiceMessageReaction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VoiceMessageReactionCopyWith<VoiceMessageReaction> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VoiceMessageReactionCopyWith<$Res> {
  factory $VoiceMessageReactionCopyWith(
    VoiceMessageReaction value,
    $Res Function(VoiceMessageReaction) then,
  ) = _$VoiceMessageReactionCopyWithImpl<$Res, VoiceMessageReaction>;
  @useResult
  $Res call({String userId, VoiceReactionType type, DateTime timestamp});
}

/// @nodoc
class _$VoiceMessageReactionCopyWithImpl<
  $Res,
  $Val extends VoiceMessageReaction
>
    implements $VoiceMessageReactionCopyWith<$Res> {
  _$VoiceMessageReactionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VoiceMessageReaction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? type = null,
    Object? timestamp = null,
  }) {
    return _then(
      _value.copyWith(
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as VoiceReactionType,
            timestamp: null == timestamp
                ? _value.timestamp
                : timestamp // ignore: cast_nullable_to_non_nullable
                      as DateTime,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$VoiceMessageReactionImplCopyWith<$Res>
    implements $VoiceMessageReactionCopyWith<$Res> {
  factory _$$VoiceMessageReactionImplCopyWith(
    _$VoiceMessageReactionImpl value,
    $Res Function(_$VoiceMessageReactionImpl) then,
  ) = __$$VoiceMessageReactionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String userId, VoiceReactionType type, DateTime timestamp});
}

/// @nodoc
class __$$VoiceMessageReactionImplCopyWithImpl<$Res>
    extends _$VoiceMessageReactionCopyWithImpl<$Res, _$VoiceMessageReactionImpl>
    implements _$$VoiceMessageReactionImplCopyWith<$Res> {
  __$$VoiceMessageReactionImplCopyWithImpl(
    _$VoiceMessageReactionImpl _value,
    $Res Function(_$VoiceMessageReactionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of VoiceMessageReaction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? type = null,
    Object? timestamp = null,
  }) {
    return _then(
      _$VoiceMessageReactionImpl(
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as VoiceReactionType,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$VoiceMessageReactionImpl implements _VoiceMessageReaction {
  const _$VoiceMessageReactionImpl({
    required this.userId,
    required this.type,
    required this.timestamp,
  });

  factory _$VoiceMessageReactionImpl.fromJson(Map<String, dynamic> json) =>
      _$$VoiceMessageReactionImplFromJson(json);

  @override
  final String userId;
  @override
  final VoiceReactionType type;
  @override
  final DateTime timestamp;

  @override
  String toString() {
    return 'VoiceMessageReaction(userId: $userId, type: $type, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VoiceMessageReactionImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, userId, type, timestamp);

  /// Create a copy of VoiceMessageReaction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VoiceMessageReactionImplCopyWith<_$VoiceMessageReactionImpl>
  get copyWith =>
      __$$VoiceMessageReactionImplCopyWithImpl<_$VoiceMessageReactionImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$VoiceMessageReactionImplToJson(this);
  }
}

abstract class _VoiceMessageReaction implements VoiceMessageReaction {
  const factory _VoiceMessageReaction({
    required final String userId,
    required final VoiceReactionType type,
    required final DateTime timestamp,
  }) = _$VoiceMessageReactionImpl;

  factory _VoiceMessageReaction.fromJson(Map<String, dynamic> json) =
      _$VoiceMessageReactionImpl.fromJson;

  @override
  String get userId;
  @override
  VoiceReactionType get type;
  @override
  DateTime get timestamp;

  /// Create a copy of VoiceMessageReaction
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VoiceMessageReactionImplCopyWith<_$VoiceMessageReactionImpl>
  get copyWith => throw _privateConstructorUsedError;
}

VoiceMessageMetadata _$VoiceMessageMetadataFromJson(Map<String, dynamic> json) {
  return _VoiceMessageMetadata.fromJson(json);
}

/// @nodoc
mixin _$VoiceMessageMetadata {
  String get fileSize => throw _privateConstructorUsedError;
  String get audioFormat => throw _privateConstructorUsedError;
  int get sampleRate => throw _privateConstructorUsedError;
  int get bitRate => throw _privateConstructorUsedError;
  bool get isCompressed => throw _privateConstructorUsedError;
  String? get recordingDevice => throw _privateConstructorUsedError;
  Map<String, dynamic>? get additionalData =>
      throw _privateConstructorUsedError;

  /// Serializes this VoiceMessageMetadata to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VoiceMessageMetadata
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VoiceMessageMetadataCopyWith<VoiceMessageMetadata> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VoiceMessageMetadataCopyWith<$Res> {
  factory $VoiceMessageMetadataCopyWith(
    VoiceMessageMetadata value,
    $Res Function(VoiceMessageMetadata) then,
  ) = _$VoiceMessageMetadataCopyWithImpl<$Res, VoiceMessageMetadata>;
  @useResult
  $Res call({
    String fileSize,
    String audioFormat,
    int sampleRate,
    int bitRate,
    bool isCompressed,
    String? recordingDevice,
    Map<String, dynamic>? additionalData,
  });
}

/// @nodoc
class _$VoiceMessageMetadataCopyWithImpl<
  $Res,
  $Val extends VoiceMessageMetadata
>
    implements $VoiceMessageMetadataCopyWith<$Res> {
  _$VoiceMessageMetadataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VoiceMessageMetadata
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fileSize = null,
    Object? audioFormat = null,
    Object? sampleRate = null,
    Object? bitRate = null,
    Object? isCompressed = null,
    Object? recordingDevice = freezed,
    Object? additionalData = freezed,
  }) {
    return _then(
      _value.copyWith(
            fileSize: null == fileSize
                ? _value.fileSize
                : fileSize // ignore: cast_nullable_to_non_nullable
                      as String,
            audioFormat: null == audioFormat
                ? _value.audioFormat
                : audioFormat // ignore: cast_nullable_to_non_nullable
                      as String,
            sampleRate: null == sampleRate
                ? _value.sampleRate
                : sampleRate // ignore: cast_nullable_to_non_nullable
                      as int,
            bitRate: null == bitRate
                ? _value.bitRate
                : bitRate // ignore: cast_nullable_to_non_nullable
                      as int,
            isCompressed: null == isCompressed
                ? _value.isCompressed
                : isCompressed // ignore: cast_nullable_to_non_nullable
                      as bool,
            recordingDevice: freezed == recordingDevice
                ? _value.recordingDevice
                : recordingDevice // ignore: cast_nullable_to_non_nullable
                      as String?,
            additionalData: freezed == additionalData
                ? _value.additionalData
                : additionalData // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$VoiceMessageMetadataImplCopyWith<$Res>
    implements $VoiceMessageMetadataCopyWith<$Res> {
  factory _$$VoiceMessageMetadataImplCopyWith(
    _$VoiceMessageMetadataImpl value,
    $Res Function(_$VoiceMessageMetadataImpl) then,
  ) = __$$VoiceMessageMetadataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String fileSize,
    String audioFormat,
    int sampleRate,
    int bitRate,
    bool isCompressed,
    String? recordingDevice,
    Map<String, dynamic>? additionalData,
  });
}

/// @nodoc
class __$$VoiceMessageMetadataImplCopyWithImpl<$Res>
    extends _$VoiceMessageMetadataCopyWithImpl<$Res, _$VoiceMessageMetadataImpl>
    implements _$$VoiceMessageMetadataImplCopyWith<$Res> {
  __$$VoiceMessageMetadataImplCopyWithImpl(
    _$VoiceMessageMetadataImpl _value,
    $Res Function(_$VoiceMessageMetadataImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of VoiceMessageMetadata
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fileSize = null,
    Object? audioFormat = null,
    Object? sampleRate = null,
    Object? bitRate = null,
    Object? isCompressed = null,
    Object? recordingDevice = freezed,
    Object? additionalData = freezed,
  }) {
    return _then(
      _$VoiceMessageMetadataImpl(
        fileSize: null == fileSize
            ? _value.fileSize
            : fileSize // ignore: cast_nullable_to_non_nullable
                  as String,
        audioFormat: null == audioFormat
            ? _value.audioFormat
            : audioFormat // ignore: cast_nullable_to_non_nullable
                  as String,
        sampleRate: null == sampleRate
            ? _value.sampleRate
            : sampleRate // ignore: cast_nullable_to_non_nullable
                  as int,
        bitRate: null == bitRate
            ? _value.bitRate
            : bitRate // ignore: cast_nullable_to_non_nullable
                  as int,
        isCompressed: null == isCompressed
            ? _value.isCompressed
            : isCompressed // ignore: cast_nullable_to_non_nullable
                  as bool,
        recordingDevice: freezed == recordingDevice
            ? _value.recordingDevice
            : recordingDevice // ignore: cast_nullable_to_non_nullable
                  as String?,
        additionalData: freezed == additionalData
            ? _value._additionalData
            : additionalData // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$VoiceMessageMetadataImpl implements _VoiceMessageMetadata {
  const _$VoiceMessageMetadataImpl({
    required this.fileSize,
    required this.audioFormat,
    required this.sampleRate,
    required this.bitRate,
    required this.isCompressed,
    required this.recordingDevice,
    required final Map<String, dynamic>? additionalData,
  }) : _additionalData = additionalData;

  factory _$VoiceMessageMetadataImpl.fromJson(Map<String, dynamic> json) =>
      _$$VoiceMessageMetadataImplFromJson(json);

  @override
  final String fileSize;
  @override
  final String audioFormat;
  @override
  final int sampleRate;
  @override
  final int bitRate;
  @override
  final bool isCompressed;
  @override
  final String? recordingDevice;
  final Map<String, dynamic>? _additionalData;
  @override
  Map<String, dynamic>? get additionalData {
    final value = _additionalData;
    if (value == null) return null;
    if (_additionalData is EqualUnmodifiableMapView) return _additionalData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'VoiceMessageMetadata(fileSize: $fileSize, audioFormat: $audioFormat, sampleRate: $sampleRate, bitRate: $bitRate, isCompressed: $isCompressed, recordingDevice: $recordingDevice, additionalData: $additionalData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VoiceMessageMetadataImpl &&
            (identical(other.fileSize, fileSize) ||
                other.fileSize == fileSize) &&
            (identical(other.audioFormat, audioFormat) ||
                other.audioFormat == audioFormat) &&
            (identical(other.sampleRate, sampleRate) ||
                other.sampleRate == sampleRate) &&
            (identical(other.bitRate, bitRate) || other.bitRate == bitRate) &&
            (identical(other.isCompressed, isCompressed) ||
                other.isCompressed == isCompressed) &&
            (identical(other.recordingDevice, recordingDevice) ||
                other.recordingDevice == recordingDevice) &&
            const DeepCollectionEquality().equals(
              other._additionalData,
              _additionalData,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    fileSize,
    audioFormat,
    sampleRate,
    bitRate,
    isCompressed,
    recordingDevice,
    const DeepCollectionEquality().hash(_additionalData),
  );

  /// Create a copy of VoiceMessageMetadata
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VoiceMessageMetadataImplCopyWith<_$VoiceMessageMetadataImpl>
  get copyWith =>
      __$$VoiceMessageMetadataImplCopyWithImpl<_$VoiceMessageMetadataImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$VoiceMessageMetadataImplToJson(this);
  }
}

abstract class _VoiceMessageMetadata implements VoiceMessageMetadata {
  const factory _VoiceMessageMetadata({
    required final String fileSize,
    required final String audioFormat,
    required final int sampleRate,
    required final int bitRate,
    required final bool isCompressed,
    required final String? recordingDevice,
    required final Map<String, dynamic>? additionalData,
  }) = _$VoiceMessageMetadataImpl;

  factory _VoiceMessageMetadata.fromJson(Map<String, dynamic> json) =
      _$VoiceMessageMetadataImpl.fromJson;

  @override
  String get fileSize;
  @override
  String get audioFormat;
  @override
  int get sampleRate;
  @override
  int get bitRate;
  @override
  bool get isCompressed;
  @override
  String? get recordingDevice;
  @override
  Map<String, dynamic>? get additionalData;

  /// Create a copy of VoiceMessageMetadata
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VoiceMessageMetadataImplCopyWith<_$VoiceMessageMetadataImpl>
  get copyWith => throw _privateConstructorUsedError;
}

VoiceMessageSettings _$VoiceMessageSettingsFromJson(Map<String, dynamic> json) {
  return _VoiceMessageSettings.fromJson(json);
}

/// @nodoc
mixin _$VoiceMessageSettings {
  bool get autoTranscribe => throw _privateConstructorUsedError;
  bool get allowReactions => throw _privateConstructorUsedError;
  int get maxDurationSeconds => throw _privateConstructorUsedError; // 5 minutes
  int get minDurationSeconds =>
      throw _privateConstructorUsedError; // 10 seconds
  bool get showWaveform => throw _privateConstructorUsedError;
  bool get allowPlaybackSpeed => throw _privateConstructorUsedError;
  List<String> get playbackSpeeds => throw _privateConstructorUsedError;
  bool get saveToGallery => throw _privateConstructorUsedError;
  bool get requireConfirmation => throw _privateConstructorUsedError;

  /// Serializes this VoiceMessageSettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VoiceMessageSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VoiceMessageSettingsCopyWith<VoiceMessageSettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VoiceMessageSettingsCopyWith<$Res> {
  factory $VoiceMessageSettingsCopyWith(
    VoiceMessageSettings value,
    $Res Function(VoiceMessageSettings) then,
  ) = _$VoiceMessageSettingsCopyWithImpl<$Res, VoiceMessageSettings>;
  @useResult
  $Res call({
    bool autoTranscribe,
    bool allowReactions,
    int maxDurationSeconds,
    int minDurationSeconds,
    bool showWaveform,
    bool allowPlaybackSpeed,
    List<String> playbackSpeeds,
    bool saveToGallery,
    bool requireConfirmation,
  });
}

/// @nodoc
class _$VoiceMessageSettingsCopyWithImpl<
  $Res,
  $Val extends VoiceMessageSettings
>
    implements $VoiceMessageSettingsCopyWith<$Res> {
  _$VoiceMessageSettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VoiceMessageSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? autoTranscribe = null,
    Object? allowReactions = null,
    Object? maxDurationSeconds = null,
    Object? minDurationSeconds = null,
    Object? showWaveform = null,
    Object? allowPlaybackSpeed = null,
    Object? playbackSpeeds = null,
    Object? saveToGallery = null,
    Object? requireConfirmation = null,
  }) {
    return _then(
      _value.copyWith(
            autoTranscribe: null == autoTranscribe
                ? _value.autoTranscribe
                : autoTranscribe // ignore: cast_nullable_to_non_nullable
                      as bool,
            allowReactions: null == allowReactions
                ? _value.allowReactions
                : allowReactions // ignore: cast_nullable_to_non_nullable
                      as bool,
            maxDurationSeconds: null == maxDurationSeconds
                ? _value.maxDurationSeconds
                : maxDurationSeconds // ignore: cast_nullable_to_non_nullable
                      as int,
            minDurationSeconds: null == minDurationSeconds
                ? _value.minDurationSeconds
                : minDurationSeconds // ignore: cast_nullable_to_non_nullable
                      as int,
            showWaveform: null == showWaveform
                ? _value.showWaveform
                : showWaveform // ignore: cast_nullable_to_non_nullable
                      as bool,
            allowPlaybackSpeed: null == allowPlaybackSpeed
                ? _value.allowPlaybackSpeed
                : allowPlaybackSpeed // ignore: cast_nullable_to_non_nullable
                      as bool,
            playbackSpeeds: null == playbackSpeeds
                ? _value.playbackSpeeds
                : playbackSpeeds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            saveToGallery: null == saveToGallery
                ? _value.saveToGallery
                : saveToGallery // ignore: cast_nullable_to_non_nullable
                      as bool,
            requireConfirmation: null == requireConfirmation
                ? _value.requireConfirmation
                : requireConfirmation // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$VoiceMessageSettingsImplCopyWith<$Res>
    implements $VoiceMessageSettingsCopyWith<$Res> {
  factory _$$VoiceMessageSettingsImplCopyWith(
    _$VoiceMessageSettingsImpl value,
    $Res Function(_$VoiceMessageSettingsImpl) then,
  ) = __$$VoiceMessageSettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    bool autoTranscribe,
    bool allowReactions,
    int maxDurationSeconds,
    int minDurationSeconds,
    bool showWaveform,
    bool allowPlaybackSpeed,
    List<String> playbackSpeeds,
    bool saveToGallery,
    bool requireConfirmation,
  });
}

/// @nodoc
class __$$VoiceMessageSettingsImplCopyWithImpl<$Res>
    extends _$VoiceMessageSettingsCopyWithImpl<$Res, _$VoiceMessageSettingsImpl>
    implements _$$VoiceMessageSettingsImplCopyWith<$Res> {
  __$$VoiceMessageSettingsImplCopyWithImpl(
    _$VoiceMessageSettingsImpl _value,
    $Res Function(_$VoiceMessageSettingsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of VoiceMessageSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? autoTranscribe = null,
    Object? allowReactions = null,
    Object? maxDurationSeconds = null,
    Object? minDurationSeconds = null,
    Object? showWaveform = null,
    Object? allowPlaybackSpeed = null,
    Object? playbackSpeeds = null,
    Object? saveToGallery = null,
    Object? requireConfirmation = null,
  }) {
    return _then(
      _$VoiceMessageSettingsImpl(
        autoTranscribe: null == autoTranscribe
            ? _value.autoTranscribe
            : autoTranscribe // ignore: cast_nullable_to_non_nullable
                  as bool,
        allowReactions: null == allowReactions
            ? _value.allowReactions
            : allowReactions // ignore: cast_nullable_to_non_nullable
                  as bool,
        maxDurationSeconds: null == maxDurationSeconds
            ? _value.maxDurationSeconds
            : maxDurationSeconds // ignore: cast_nullable_to_non_nullable
                  as int,
        minDurationSeconds: null == minDurationSeconds
            ? _value.minDurationSeconds
            : minDurationSeconds // ignore: cast_nullable_to_non_nullable
                  as int,
        showWaveform: null == showWaveform
            ? _value.showWaveform
            : showWaveform // ignore: cast_nullable_to_non_nullable
                  as bool,
        allowPlaybackSpeed: null == allowPlaybackSpeed
            ? _value.allowPlaybackSpeed
            : allowPlaybackSpeed // ignore: cast_nullable_to_non_nullable
                  as bool,
        playbackSpeeds: null == playbackSpeeds
            ? _value._playbackSpeeds
            : playbackSpeeds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        saveToGallery: null == saveToGallery
            ? _value.saveToGallery
            : saveToGallery // ignore: cast_nullable_to_non_nullable
                  as bool,
        requireConfirmation: null == requireConfirmation
            ? _value.requireConfirmation
            : requireConfirmation // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$VoiceMessageSettingsImpl implements _VoiceMessageSettings {
  const _$VoiceMessageSettingsImpl({
    this.autoTranscribe = true,
    this.allowReactions = true,
    this.maxDurationSeconds = 300,
    this.minDurationSeconds = 10,
    this.showWaveform = true,
    this.allowPlaybackSpeed = true,
    final List<String> playbackSpeeds = const ['0.5x', '1x', '1.5x', '2x'],
    this.saveToGallery = true,
    this.requireConfirmation = false,
  }) : _playbackSpeeds = playbackSpeeds;

  factory _$VoiceMessageSettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$VoiceMessageSettingsImplFromJson(json);

  @override
  @JsonKey()
  final bool autoTranscribe;
  @override
  @JsonKey()
  final bool allowReactions;
  @override
  @JsonKey()
  final int maxDurationSeconds;
  // 5 minutes
  @override
  @JsonKey()
  final int minDurationSeconds;
  // 10 seconds
  @override
  @JsonKey()
  final bool showWaveform;
  @override
  @JsonKey()
  final bool allowPlaybackSpeed;
  final List<String> _playbackSpeeds;
  @override
  @JsonKey()
  List<String> get playbackSpeeds {
    if (_playbackSpeeds is EqualUnmodifiableListView) return _playbackSpeeds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_playbackSpeeds);
  }

  @override
  @JsonKey()
  final bool saveToGallery;
  @override
  @JsonKey()
  final bool requireConfirmation;

  @override
  String toString() {
    return 'VoiceMessageSettings(autoTranscribe: $autoTranscribe, allowReactions: $allowReactions, maxDurationSeconds: $maxDurationSeconds, minDurationSeconds: $minDurationSeconds, showWaveform: $showWaveform, allowPlaybackSpeed: $allowPlaybackSpeed, playbackSpeeds: $playbackSpeeds, saveToGallery: $saveToGallery, requireConfirmation: $requireConfirmation)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VoiceMessageSettingsImpl &&
            (identical(other.autoTranscribe, autoTranscribe) ||
                other.autoTranscribe == autoTranscribe) &&
            (identical(other.allowReactions, allowReactions) ||
                other.allowReactions == allowReactions) &&
            (identical(other.maxDurationSeconds, maxDurationSeconds) ||
                other.maxDurationSeconds == maxDurationSeconds) &&
            (identical(other.minDurationSeconds, minDurationSeconds) ||
                other.minDurationSeconds == minDurationSeconds) &&
            (identical(other.showWaveform, showWaveform) ||
                other.showWaveform == showWaveform) &&
            (identical(other.allowPlaybackSpeed, allowPlaybackSpeed) ||
                other.allowPlaybackSpeed == allowPlaybackSpeed) &&
            const DeepCollectionEquality().equals(
              other._playbackSpeeds,
              _playbackSpeeds,
            ) &&
            (identical(other.saveToGallery, saveToGallery) ||
                other.saveToGallery == saveToGallery) &&
            (identical(other.requireConfirmation, requireConfirmation) ||
                other.requireConfirmation == requireConfirmation));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    autoTranscribe,
    allowReactions,
    maxDurationSeconds,
    minDurationSeconds,
    showWaveform,
    allowPlaybackSpeed,
    const DeepCollectionEquality().hash(_playbackSpeeds),
    saveToGallery,
    requireConfirmation,
  );

  /// Create a copy of VoiceMessageSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VoiceMessageSettingsImplCopyWith<_$VoiceMessageSettingsImpl>
  get copyWith =>
      __$$VoiceMessageSettingsImplCopyWithImpl<_$VoiceMessageSettingsImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$VoiceMessageSettingsImplToJson(this);
  }
}

abstract class _VoiceMessageSettings implements VoiceMessageSettings {
  const factory _VoiceMessageSettings({
    final bool autoTranscribe,
    final bool allowReactions,
    final int maxDurationSeconds,
    final int minDurationSeconds,
    final bool showWaveform,
    final bool allowPlaybackSpeed,
    final List<String> playbackSpeeds,
    final bool saveToGallery,
    final bool requireConfirmation,
  }) = _$VoiceMessageSettingsImpl;

  factory _VoiceMessageSettings.fromJson(Map<String, dynamic> json) =
      _$VoiceMessageSettingsImpl.fromJson;

  @override
  bool get autoTranscribe;
  @override
  bool get allowReactions;
  @override
  int get maxDurationSeconds; // 5 minutes
  @override
  int get minDurationSeconds; // 10 seconds
  @override
  bool get showWaveform;
  @override
  bool get allowPlaybackSpeed;
  @override
  List<String> get playbackSpeeds;
  @override
  bool get saveToGallery;
  @override
  bool get requireConfirmation;

  /// Create a copy of VoiceMessageSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VoiceMessageSettingsImplCopyWith<_$VoiceMessageSettingsImpl>
  get copyWith => throw _privateConstructorUsedError;
}
