// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'chat_settings_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

ChatSettings _$ChatSettingsFromJson(Map<String, dynamic> json) {
  return _ChatSettings.fromJson(json);
}

/// @nodoc
mixin _$ChatSettings {
  // Screenshot Control
  bool get allowScreenshots => throw _privateConstructorUsedError;
  bool get notifyOnScreenshot =>
      throw _privateConstructorUsedError; // Message Editing
  bool get allowMessageEditing => throw _privateConstructorUsedError;
  EditWindowDuration get editWindowDuration =>
      throw _privateConstructorUsedError;
  int get customEditWindowMinutes =>
      throw _privateConstructorUsedError; // Mute Settings
  bool get muteAllChats =>
      throw _privateConstructorUsedError; // Message Requests
  bool get allowMessagesFromNonFollowers => throw _privateConstructorUsedError;
  bool get autoBlockSpamAccounts =>
      throw _privateConstructorUsedError; // Self-Destruct Messages
  bool get enableSelfDestruct => throw _privateConstructorUsedError;
  SelfDestructTimer get selfDestructTimer => throw _privateConstructorUsedError;
  int get customSelfDestructSeconds =>
      throw _privateConstructorUsedError; // Seen & Typing Status
  bool get showSeenStatus => throw _privateConstructorUsedError;
  bool get showTypingIndicators =>
      throw _privateConstructorUsedError; // Sync Settings
  bool get syncAcrossDevices => throw _privateConstructorUsedError; // Privacy
  bool get requirePasswordForHiddenChats => throw _privateConstructorUsedError;
  bool get useFaceIDForHiddenChats => throw _privateConstructorUsedError;

  /// Serializes this ChatSettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ChatSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ChatSettingsCopyWith<ChatSettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChatSettingsCopyWith<$Res> {
  factory $ChatSettingsCopyWith(
    ChatSettings value,
    $Res Function(ChatSettings) then,
  ) = _$ChatSettingsCopyWithImpl<$Res, ChatSettings>;
  @useResult
  $Res call({
    bool allowScreenshots,
    bool notifyOnScreenshot,
    bool allowMessageEditing,
    EditWindowDuration editWindowDuration,
    int customEditWindowMinutes,
    bool muteAllChats,
    bool allowMessagesFromNonFollowers,
    bool autoBlockSpamAccounts,
    bool enableSelfDestruct,
    SelfDestructTimer selfDestructTimer,
    int customSelfDestructSeconds,
    bool showSeenStatus,
    bool showTypingIndicators,
    bool syncAcrossDevices,
    bool requirePasswordForHiddenChats,
    bool useFaceIDForHiddenChats,
  });
}

/// @nodoc
class _$ChatSettingsCopyWithImpl<$Res, $Val extends ChatSettings>
    implements $ChatSettingsCopyWith<$Res> {
  _$ChatSettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ChatSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? allowScreenshots = null,
    Object? notifyOnScreenshot = null,
    Object? allowMessageEditing = null,
    Object? editWindowDuration = null,
    Object? customEditWindowMinutes = null,
    Object? muteAllChats = null,
    Object? allowMessagesFromNonFollowers = null,
    Object? autoBlockSpamAccounts = null,
    Object? enableSelfDestruct = null,
    Object? selfDestructTimer = null,
    Object? customSelfDestructSeconds = null,
    Object? showSeenStatus = null,
    Object? showTypingIndicators = null,
    Object? syncAcrossDevices = null,
    Object? requirePasswordForHiddenChats = null,
    Object? useFaceIDForHiddenChats = null,
  }) {
    return _then(
      _value.copyWith(
            allowScreenshots: null == allowScreenshots
                ? _value.allowScreenshots
                : allowScreenshots // ignore: cast_nullable_to_non_nullable
                      as bool,
            notifyOnScreenshot: null == notifyOnScreenshot
                ? _value.notifyOnScreenshot
                : notifyOnScreenshot // ignore: cast_nullable_to_non_nullable
                      as bool,
            allowMessageEditing: null == allowMessageEditing
                ? _value.allowMessageEditing
                : allowMessageEditing // ignore: cast_nullable_to_non_nullable
                      as bool,
            editWindowDuration: null == editWindowDuration
                ? _value.editWindowDuration
                : editWindowDuration // ignore: cast_nullable_to_non_nullable
                      as EditWindowDuration,
            customEditWindowMinutes: null == customEditWindowMinutes
                ? _value.customEditWindowMinutes
                : customEditWindowMinutes // ignore: cast_nullable_to_non_nullable
                      as int,
            muteAllChats: null == muteAllChats
                ? _value.muteAllChats
                : muteAllChats // ignore: cast_nullable_to_non_nullable
                      as bool,
            allowMessagesFromNonFollowers: null == allowMessagesFromNonFollowers
                ? _value.allowMessagesFromNonFollowers
                : allowMessagesFromNonFollowers // ignore: cast_nullable_to_non_nullable
                      as bool,
            autoBlockSpamAccounts: null == autoBlockSpamAccounts
                ? _value.autoBlockSpamAccounts
                : autoBlockSpamAccounts // ignore: cast_nullable_to_non_nullable
                      as bool,
            enableSelfDestruct: null == enableSelfDestruct
                ? _value.enableSelfDestruct
                : enableSelfDestruct // ignore: cast_nullable_to_non_nullable
                      as bool,
            selfDestructTimer: null == selfDestructTimer
                ? _value.selfDestructTimer
                : selfDestructTimer // ignore: cast_nullable_to_non_nullable
                      as SelfDestructTimer,
            customSelfDestructSeconds: null == customSelfDestructSeconds
                ? _value.customSelfDestructSeconds
                : customSelfDestructSeconds // ignore: cast_nullable_to_non_nullable
                      as int,
            showSeenStatus: null == showSeenStatus
                ? _value.showSeenStatus
                : showSeenStatus // ignore: cast_nullable_to_non_nullable
                      as bool,
            showTypingIndicators: null == showTypingIndicators
                ? _value.showTypingIndicators
                : showTypingIndicators // ignore: cast_nullable_to_non_nullable
                      as bool,
            syncAcrossDevices: null == syncAcrossDevices
                ? _value.syncAcrossDevices
                : syncAcrossDevices // ignore: cast_nullable_to_non_nullable
                      as bool,
            requirePasswordForHiddenChats: null == requirePasswordForHiddenChats
                ? _value.requirePasswordForHiddenChats
                : requirePasswordForHiddenChats // ignore: cast_nullable_to_non_nullable
                      as bool,
            useFaceIDForHiddenChats: null == useFaceIDForHiddenChats
                ? _value.useFaceIDForHiddenChats
                : useFaceIDForHiddenChats // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ChatSettingsImplCopyWith<$Res>
    implements $ChatSettingsCopyWith<$Res> {
  factory _$$ChatSettingsImplCopyWith(
    _$ChatSettingsImpl value,
    $Res Function(_$ChatSettingsImpl) then,
  ) = __$$ChatSettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    bool allowScreenshots,
    bool notifyOnScreenshot,
    bool allowMessageEditing,
    EditWindowDuration editWindowDuration,
    int customEditWindowMinutes,
    bool muteAllChats,
    bool allowMessagesFromNonFollowers,
    bool autoBlockSpamAccounts,
    bool enableSelfDestruct,
    SelfDestructTimer selfDestructTimer,
    int customSelfDestructSeconds,
    bool showSeenStatus,
    bool showTypingIndicators,
    bool syncAcrossDevices,
    bool requirePasswordForHiddenChats,
    bool useFaceIDForHiddenChats,
  });
}

/// @nodoc
class __$$ChatSettingsImplCopyWithImpl<$Res>
    extends _$ChatSettingsCopyWithImpl<$Res, _$ChatSettingsImpl>
    implements _$$ChatSettingsImplCopyWith<$Res> {
  __$$ChatSettingsImplCopyWithImpl(
    _$ChatSettingsImpl _value,
    $Res Function(_$ChatSettingsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ChatSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? allowScreenshots = null,
    Object? notifyOnScreenshot = null,
    Object? allowMessageEditing = null,
    Object? editWindowDuration = null,
    Object? customEditWindowMinutes = null,
    Object? muteAllChats = null,
    Object? allowMessagesFromNonFollowers = null,
    Object? autoBlockSpamAccounts = null,
    Object? enableSelfDestruct = null,
    Object? selfDestructTimer = null,
    Object? customSelfDestructSeconds = null,
    Object? showSeenStatus = null,
    Object? showTypingIndicators = null,
    Object? syncAcrossDevices = null,
    Object? requirePasswordForHiddenChats = null,
    Object? useFaceIDForHiddenChats = null,
  }) {
    return _then(
      _$ChatSettingsImpl(
        allowScreenshots: null == allowScreenshots
            ? _value.allowScreenshots
            : allowScreenshots // ignore: cast_nullable_to_non_nullable
                  as bool,
        notifyOnScreenshot: null == notifyOnScreenshot
            ? _value.notifyOnScreenshot
            : notifyOnScreenshot // ignore: cast_nullable_to_non_nullable
                  as bool,
        allowMessageEditing: null == allowMessageEditing
            ? _value.allowMessageEditing
            : allowMessageEditing // ignore: cast_nullable_to_non_nullable
                  as bool,
        editWindowDuration: null == editWindowDuration
            ? _value.editWindowDuration
            : editWindowDuration // ignore: cast_nullable_to_non_nullable
                  as EditWindowDuration,
        customEditWindowMinutes: null == customEditWindowMinutes
            ? _value.customEditWindowMinutes
            : customEditWindowMinutes // ignore: cast_nullable_to_non_nullable
                  as int,
        muteAllChats: null == muteAllChats
            ? _value.muteAllChats
            : muteAllChats // ignore: cast_nullable_to_non_nullable
                  as bool,
        allowMessagesFromNonFollowers: null == allowMessagesFromNonFollowers
            ? _value.allowMessagesFromNonFollowers
            : allowMessagesFromNonFollowers // ignore: cast_nullable_to_non_nullable
                  as bool,
        autoBlockSpamAccounts: null == autoBlockSpamAccounts
            ? _value.autoBlockSpamAccounts
            : autoBlockSpamAccounts // ignore: cast_nullable_to_non_nullable
                  as bool,
        enableSelfDestruct: null == enableSelfDestruct
            ? _value.enableSelfDestruct
            : enableSelfDestruct // ignore: cast_nullable_to_non_nullable
                  as bool,
        selfDestructTimer: null == selfDestructTimer
            ? _value.selfDestructTimer
            : selfDestructTimer // ignore: cast_nullable_to_non_nullable
                  as SelfDestructTimer,
        customSelfDestructSeconds: null == customSelfDestructSeconds
            ? _value.customSelfDestructSeconds
            : customSelfDestructSeconds // ignore: cast_nullable_to_non_nullable
                  as int,
        showSeenStatus: null == showSeenStatus
            ? _value.showSeenStatus
            : showSeenStatus // ignore: cast_nullable_to_non_nullable
                  as bool,
        showTypingIndicators: null == showTypingIndicators
            ? _value.showTypingIndicators
            : showTypingIndicators // ignore: cast_nullable_to_non_nullable
                  as bool,
        syncAcrossDevices: null == syncAcrossDevices
            ? _value.syncAcrossDevices
            : syncAcrossDevices // ignore: cast_nullable_to_non_nullable
                  as bool,
        requirePasswordForHiddenChats: null == requirePasswordForHiddenChats
            ? _value.requirePasswordForHiddenChats
            : requirePasswordForHiddenChats // ignore: cast_nullable_to_non_nullable
                  as bool,
        useFaceIDForHiddenChats: null == useFaceIDForHiddenChats
            ? _value.useFaceIDForHiddenChats
            : useFaceIDForHiddenChats // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ChatSettingsImpl implements _ChatSettings {
  const _$ChatSettingsImpl({
    this.allowScreenshots = false,
    this.notifyOnScreenshot = true,
    this.allowMessageEditing = true,
    this.editWindowDuration = EditWindowDuration.tenMinutes,
    this.customEditWindowMinutes = 10,
    this.muteAllChats = false,
    this.allowMessagesFromNonFollowers = true,
    this.autoBlockSpamAccounts = true,
    this.enableSelfDestruct = false,
    this.selfDestructTimer = SelfDestructTimer.afterReading,
    this.customSelfDestructSeconds = 10,
    this.showSeenStatus = true,
    this.showTypingIndicators = true,
    this.syncAcrossDevices = true,
    this.requirePasswordForHiddenChats = false,
    this.useFaceIDForHiddenChats = false,
  });

  factory _$ChatSettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChatSettingsImplFromJson(json);

  // Screenshot Control
  @override
  @JsonKey()
  final bool allowScreenshots;
  @override
  @JsonKey()
  final bool notifyOnScreenshot;
  // Message Editing
  @override
  @JsonKey()
  final bool allowMessageEditing;
  @override
  @JsonKey()
  final EditWindowDuration editWindowDuration;
  @override
  @JsonKey()
  final int customEditWindowMinutes;
  // Mute Settings
  @override
  @JsonKey()
  final bool muteAllChats;
  // Message Requests
  @override
  @JsonKey()
  final bool allowMessagesFromNonFollowers;
  @override
  @JsonKey()
  final bool autoBlockSpamAccounts;
  // Self-Destruct Messages
  @override
  @JsonKey()
  final bool enableSelfDestruct;
  @override
  @JsonKey()
  final SelfDestructTimer selfDestructTimer;
  @override
  @JsonKey()
  final int customSelfDestructSeconds;
  // Seen & Typing Status
  @override
  @JsonKey()
  final bool showSeenStatus;
  @override
  @JsonKey()
  final bool showTypingIndicators;
  // Sync Settings
  @override
  @JsonKey()
  final bool syncAcrossDevices;
  // Privacy
  @override
  @JsonKey()
  final bool requirePasswordForHiddenChats;
  @override
  @JsonKey()
  final bool useFaceIDForHiddenChats;

  @override
  String toString() {
    return 'ChatSettings(allowScreenshots: $allowScreenshots, notifyOnScreenshot: $notifyOnScreenshot, allowMessageEditing: $allowMessageEditing, editWindowDuration: $editWindowDuration, customEditWindowMinutes: $customEditWindowMinutes, muteAllChats: $muteAllChats, allowMessagesFromNonFollowers: $allowMessagesFromNonFollowers, autoBlockSpamAccounts: $autoBlockSpamAccounts, enableSelfDestruct: $enableSelfDestruct, selfDestructTimer: $selfDestructTimer, customSelfDestructSeconds: $customSelfDestructSeconds, showSeenStatus: $showSeenStatus, showTypingIndicators: $showTypingIndicators, syncAcrossDevices: $syncAcrossDevices, requirePasswordForHiddenChats: $requirePasswordForHiddenChats, useFaceIDForHiddenChats: $useFaceIDForHiddenChats)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChatSettingsImpl &&
            (identical(other.allowScreenshots, allowScreenshots) ||
                other.allowScreenshots == allowScreenshots) &&
            (identical(other.notifyOnScreenshot, notifyOnScreenshot) ||
                other.notifyOnScreenshot == notifyOnScreenshot) &&
            (identical(other.allowMessageEditing, allowMessageEditing) ||
                other.allowMessageEditing == allowMessageEditing) &&
            (identical(other.editWindowDuration, editWindowDuration) ||
                other.editWindowDuration == editWindowDuration) &&
            (identical(
                  other.customEditWindowMinutes,
                  customEditWindowMinutes,
                ) ||
                other.customEditWindowMinutes == customEditWindowMinutes) &&
            (identical(other.muteAllChats, muteAllChats) ||
                other.muteAllChats == muteAllChats) &&
            (identical(
                  other.allowMessagesFromNonFollowers,
                  allowMessagesFromNonFollowers,
                ) ||
                other.allowMessagesFromNonFollowers ==
                    allowMessagesFromNonFollowers) &&
            (identical(other.autoBlockSpamAccounts, autoBlockSpamAccounts) ||
                other.autoBlockSpamAccounts == autoBlockSpamAccounts) &&
            (identical(other.enableSelfDestruct, enableSelfDestruct) ||
                other.enableSelfDestruct == enableSelfDestruct) &&
            (identical(other.selfDestructTimer, selfDestructTimer) ||
                other.selfDestructTimer == selfDestructTimer) &&
            (identical(
                  other.customSelfDestructSeconds,
                  customSelfDestructSeconds,
                ) ||
                other.customSelfDestructSeconds == customSelfDestructSeconds) &&
            (identical(other.showSeenStatus, showSeenStatus) ||
                other.showSeenStatus == showSeenStatus) &&
            (identical(other.showTypingIndicators, showTypingIndicators) ||
                other.showTypingIndicators == showTypingIndicators) &&
            (identical(other.syncAcrossDevices, syncAcrossDevices) ||
                other.syncAcrossDevices == syncAcrossDevices) &&
            (identical(
                  other.requirePasswordForHiddenChats,
                  requirePasswordForHiddenChats,
                ) ||
                other.requirePasswordForHiddenChats ==
                    requirePasswordForHiddenChats) &&
            (identical(
                  other.useFaceIDForHiddenChats,
                  useFaceIDForHiddenChats,
                ) ||
                other.useFaceIDForHiddenChats == useFaceIDForHiddenChats));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    allowScreenshots,
    notifyOnScreenshot,
    allowMessageEditing,
    editWindowDuration,
    customEditWindowMinutes,
    muteAllChats,
    allowMessagesFromNonFollowers,
    autoBlockSpamAccounts,
    enableSelfDestruct,
    selfDestructTimer,
    customSelfDestructSeconds,
    showSeenStatus,
    showTypingIndicators,
    syncAcrossDevices,
    requirePasswordForHiddenChats,
    useFaceIDForHiddenChats,
  );

  /// Create a copy of ChatSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChatSettingsImplCopyWith<_$ChatSettingsImpl> get copyWith =>
      __$$ChatSettingsImplCopyWithImpl<_$ChatSettingsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ChatSettingsImplToJson(this);
  }
}

abstract class _ChatSettings implements ChatSettings {
  const factory _ChatSettings({
    final bool allowScreenshots,
    final bool notifyOnScreenshot,
    final bool allowMessageEditing,
    final EditWindowDuration editWindowDuration,
    final int customEditWindowMinutes,
    final bool muteAllChats,
    final bool allowMessagesFromNonFollowers,
    final bool autoBlockSpamAccounts,
    final bool enableSelfDestruct,
    final SelfDestructTimer selfDestructTimer,
    final int customSelfDestructSeconds,
    final bool showSeenStatus,
    final bool showTypingIndicators,
    final bool syncAcrossDevices,
    final bool requirePasswordForHiddenChats,
    final bool useFaceIDForHiddenChats,
  }) = _$ChatSettingsImpl;

  factory _ChatSettings.fromJson(Map<String, dynamic> json) =
      _$ChatSettingsImpl.fromJson;

  // Screenshot Control
  @override
  bool get allowScreenshots;
  @override
  bool get notifyOnScreenshot; // Message Editing
  @override
  bool get allowMessageEditing;
  @override
  EditWindowDuration get editWindowDuration;
  @override
  int get customEditWindowMinutes; // Mute Settings
  @override
  bool get muteAllChats; // Message Requests
  @override
  bool get allowMessagesFromNonFollowers;
  @override
  bool get autoBlockSpamAccounts; // Self-Destruct Messages
  @override
  bool get enableSelfDestruct;
  @override
  SelfDestructTimer get selfDestructTimer;
  @override
  int get customSelfDestructSeconds; // Seen & Typing Status
  @override
  bool get showSeenStatus;
  @override
  bool get showTypingIndicators; // Sync Settings
  @override
  bool get syncAcrossDevices; // Privacy
  @override
  bool get requirePasswordForHiddenChats;
  @override
  bool get useFaceIDForHiddenChats;

  /// Create a copy of ChatSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChatSettingsImplCopyWith<_$ChatSettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

IndividualChatSettings _$IndividualChatSettingsFromJson(
  Map<String, dynamic> json,
) {
  return _IndividualChatSettings.fromJson(json);
}

/// @nodoc
mixin _$IndividualChatSettings {
  String get chatId =>
      throw _privateConstructorUsedError; // Override global settings
  bool? get allowScreenshots => throw _privateConstructorUsedError;
  bool? get notifyOnScreenshot => throw _privateConstructorUsedError;
  bool? get allowMessageEditing => throw _privateConstructorUsedError;
  bool? get muteChat => throw _privateConstructorUsedError;
  bool? get showSeenStatus => throw _privateConstructorUsedError;
  bool? get showTypingIndicators => throw _privateConstructorUsedError;
  bool? get enableSelfDestruct => throw _privateConstructorUsedError;
  SelfDestructTimer? get selfDestructTimer =>
      throw _privateConstructorUsedError;
  int? get customSelfDestructSeconds =>
      throw _privateConstructorUsedError; // Chat-specific settings
  bool get isHidden => throw _privateConstructorUsedError;
  bool get isPinned => throw _privateConstructorUsedError;
  bool get isArchived => throw _privateConstructorUsedError;
  bool get isStarred => throw _privateConstructorUsedError;

  /// Serializes this IndividualChatSettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of IndividualChatSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $IndividualChatSettingsCopyWith<IndividualChatSettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IndividualChatSettingsCopyWith<$Res> {
  factory $IndividualChatSettingsCopyWith(
    IndividualChatSettings value,
    $Res Function(IndividualChatSettings) then,
  ) = _$IndividualChatSettingsCopyWithImpl<$Res, IndividualChatSettings>;
  @useResult
  $Res call({
    String chatId,
    bool? allowScreenshots,
    bool? notifyOnScreenshot,
    bool? allowMessageEditing,
    bool? muteChat,
    bool? showSeenStatus,
    bool? showTypingIndicators,
    bool? enableSelfDestruct,
    SelfDestructTimer? selfDestructTimer,
    int? customSelfDestructSeconds,
    bool isHidden,
    bool isPinned,
    bool isArchived,
    bool isStarred,
  });
}

/// @nodoc
class _$IndividualChatSettingsCopyWithImpl<
  $Res,
  $Val extends IndividualChatSettings
>
    implements $IndividualChatSettingsCopyWith<$Res> {
  _$IndividualChatSettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of IndividualChatSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? chatId = null,
    Object? allowScreenshots = freezed,
    Object? notifyOnScreenshot = freezed,
    Object? allowMessageEditing = freezed,
    Object? muteChat = freezed,
    Object? showSeenStatus = freezed,
    Object? showTypingIndicators = freezed,
    Object? enableSelfDestruct = freezed,
    Object? selfDestructTimer = freezed,
    Object? customSelfDestructSeconds = freezed,
    Object? isHidden = null,
    Object? isPinned = null,
    Object? isArchived = null,
    Object? isStarred = null,
  }) {
    return _then(
      _value.copyWith(
            chatId: null == chatId
                ? _value.chatId
                : chatId // ignore: cast_nullable_to_non_nullable
                      as String,
            allowScreenshots: freezed == allowScreenshots
                ? _value.allowScreenshots
                : allowScreenshots // ignore: cast_nullable_to_non_nullable
                      as bool?,
            notifyOnScreenshot: freezed == notifyOnScreenshot
                ? _value.notifyOnScreenshot
                : notifyOnScreenshot // ignore: cast_nullable_to_non_nullable
                      as bool?,
            allowMessageEditing: freezed == allowMessageEditing
                ? _value.allowMessageEditing
                : allowMessageEditing // ignore: cast_nullable_to_non_nullable
                      as bool?,
            muteChat: freezed == muteChat
                ? _value.muteChat
                : muteChat // ignore: cast_nullable_to_non_nullable
                      as bool?,
            showSeenStatus: freezed == showSeenStatus
                ? _value.showSeenStatus
                : showSeenStatus // ignore: cast_nullable_to_non_nullable
                      as bool?,
            showTypingIndicators: freezed == showTypingIndicators
                ? _value.showTypingIndicators
                : showTypingIndicators // ignore: cast_nullable_to_non_nullable
                      as bool?,
            enableSelfDestruct: freezed == enableSelfDestruct
                ? _value.enableSelfDestruct
                : enableSelfDestruct // ignore: cast_nullable_to_non_nullable
                      as bool?,
            selfDestructTimer: freezed == selfDestructTimer
                ? _value.selfDestructTimer
                : selfDestructTimer // ignore: cast_nullable_to_non_nullable
                      as SelfDestructTimer?,
            customSelfDestructSeconds: freezed == customSelfDestructSeconds
                ? _value.customSelfDestructSeconds
                : customSelfDestructSeconds // ignore: cast_nullable_to_non_nullable
                      as int?,
            isHidden: null == isHidden
                ? _value.isHidden
                : isHidden // ignore: cast_nullable_to_non_nullable
                      as bool,
            isPinned: null == isPinned
                ? _value.isPinned
                : isPinned // ignore: cast_nullable_to_non_nullable
                      as bool,
            isArchived: null == isArchived
                ? _value.isArchived
                : isArchived // ignore: cast_nullable_to_non_nullable
                      as bool,
            isStarred: null == isStarred
                ? _value.isStarred
                : isStarred // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$IndividualChatSettingsImplCopyWith<$Res>
    implements $IndividualChatSettingsCopyWith<$Res> {
  factory _$$IndividualChatSettingsImplCopyWith(
    _$IndividualChatSettingsImpl value,
    $Res Function(_$IndividualChatSettingsImpl) then,
  ) = __$$IndividualChatSettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String chatId,
    bool? allowScreenshots,
    bool? notifyOnScreenshot,
    bool? allowMessageEditing,
    bool? muteChat,
    bool? showSeenStatus,
    bool? showTypingIndicators,
    bool? enableSelfDestruct,
    SelfDestructTimer? selfDestructTimer,
    int? customSelfDestructSeconds,
    bool isHidden,
    bool isPinned,
    bool isArchived,
    bool isStarred,
  });
}

/// @nodoc
class __$$IndividualChatSettingsImplCopyWithImpl<$Res>
    extends
        _$IndividualChatSettingsCopyWithImpl<$Res, _$IndividualChatSettingsImpl>
    implements _$$IndividualChatSettingsImplCopyWith<$Res> {
  __$$IndividualChatSettingsImplCopyWithImpl(
    _$IndividualChatSettingsImpl _value,
    $Res Function(_$IndividualChatSettingsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of IndividualChatSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? chatId = null,
    Object? allowScreenshots = freezed,
    Object? notifyOnScreenshot = freezed,
    Object? allowMessageEditing = freezed,
    Object? muteChat = freezed,
    Object? showSeenStatus = freezed,
    Object? showTypingIndicators = freezed,
    Object? enableSelfDestruct = freezed,
    Object? selfDestructTimer = freezed,
    Object? customSelfDestructSeconds = freezed,
    Object? isHidden = null,
    Object? isPinned = null,
    Object? isArchived = null,
    Object? isStarred = null,
  }) {
    return _then(
      _$IndividualChatSettingsImpl(
        chatId: null == chatId
            ? _value.chatId
            : chatId // ignore: cast_nullable_to_non_nullable
                  as String,
        allowScreenshots: freezed == allowScreenshots
            ? _value.allowScreenshots
            : allowScreenshots // ignore: cast_nullable_to_non_nullable
                  as bool?,
        notifyOnScreenshot: freezed == notifyOnScreenshot
            ? _value.notifyOnScreenshot
            : notifyOnScreenshot // ignore: cast_nullable_to_non_nullable
                  as bool?,
        allowMessageEditing: freezed == allowMessageEditing
            ? _value.allowMessageEditing
            : allowMessageEditing // ignore: cast_nullable_to_non_nullable
                  as bool?,
        muteChat: freezed == muteChat
            ? _value.muteChat
            : muteChat // ignore: cast_nullable_to_non_nullable
                  as bool?,
        showSeenStatus: freezed == showSeenStatus
            ? _value.showSeenStatus
            : showSeenStatus // ignore: cast_nullable_to_non_nullable
                  as bool?,
        showTypingIndicators: freezed == showTypingIndicators
            ? _value.showTypingIndicators
            : showTypingIndicators // ignore: cast_nullable_to_non_nullable
                  as bool?,
        enableSelfDestruct: freezed == enableSelfDestruct
            ? _value.enableSelfDestruct
            : enableSelfDestruct // ignore: cast_nullable_to_non_nullable
                  as bool?,
        selfDestructTimer: freezed == selfDestructTimer
            ? _value.selfDestructTimer
            : selfDestructTimer // ignore: cast_nullable_to_non_nullable
                  as SelfDestructTimer?,
        customSelfDestructSeconds: freezed == customSelfDestructSeconds
            ? _value.customSelfDestructSeconds
            : customSelfDestructSeconds // ignore: cast_nullable_to_non_nullable
                  as int?,
        isHidden: null == isHidden
            ? _value.isHidden
            : isHidden // ignore: cast_nullable_to_non_nullable
                  as bool,
        isPinned: null == isPinned
            ? _value.isPinned
            : isPinned // ignore: cast_nullable_to_non_nullable
                  as bool,
        isArchived: null == isArchived
            ? _value.isArchived
            : isArchived // ignore: cast_nullable_to_non_nullable
                  as bool,
        isStarred: null == isStarred
            ? _value.isStarred
            : isStarred // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$IndividualChatSettingsImpl implements _IndividualChatSettings {
  const _$IndividualChatSettingsImpl({
    required this.chatId,
    this.allowScreenshots,
    this.notifyOnScreenshot,
    this.allowMessageEditing,
    this.muteChat,
    this.showSeenStatus,
    this.showTypingIndicators,
    this.enableSelfDestruct,
    this.selfDestructTimer,
    this.customSelfDestructSeconds,
    this.isHidden = false,
    this.isPinned = false,
    this.isArchived = false,
    this.isStarred = false,
  });

  factory _$IndividualChatSettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$IndividualChatSettingsImplFromJson(json);

  @override
  final String chatId;
  // Override global settings
  @override
  final bool? allowScreenshots;
  @override
  final bool? notifyOnScreenshot;
  @override
  final bool? allowMessageEditing;
  @override
  final bool? muteChat;
  @override
  final bool? showSeenStatus;
  @override
  final bool? showTypingIndicators;
  @override
  final bool? enableSelfDestruct;
  @override
  final SelfDestructTimer? selfDestructTimer;
  @override
  final int? customSelfDestructSeconds;
  // Chat-specific settings
  @override
  @JsonKey()
  final bool isHidden;
  @override
  @JsonKey()
  final bool isPinned;
  @override
  @JsonKey()
  final bool isArchived;
  @override
  @JsonKey()
  final bool isStarred;

  @override
  String toString() {
    return 'IndividualChatSettings(chatId: $chatId, allowScreenshots: $allowScreenshots, notifyOnScreenshot: $notifyOnScreenshot, allowMessageEditing: $allowMessageEditing, muteChat: $muteChat, showSeenStatus: $showSeenStatus, showTypingIndicators: $showTypingIndicators, enableSelfDestruct: $enableSelfDestruct, selfDestructTimer: $selfDestructTimer, customSelfDestructSeconds: $customSelfDestructSeconds, isHidden: $isHidden, isPinned: $isPinned, isArchived: $isArchived, isStarred: $isStarred)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IndividualChatSettingsImpl &&
            (identical(other.chatId, chatId) || other.chatId == chatId) &&
            (identical(other.allowScreenshots, allowScreenshots) ||
                other.allowScreenshots == allowScreenshots) &&
            (identical(other.notifyOnScreenshot, notifyOnScreenshot) ||
                other.notifyOnScreenshot == notifyOnScreenshot) &&
            (identical(other.allowMessageEditing, allowMessageEditing) ||
                other.allowMessageEditing == allowMessageEditing) &&
            (identical(other.muteChat, muteChat) ||
                other.muteChat == muteChat) &&
            (identical(other.showSeenStatus, showSeenStatus) ||
                other.showSeenStatus == showSeenStatus) &&
            (identical(other.showTypingIndicators, showTypingIndicators) ||
                other.showTypingIndicators == showTypingIndicators) &&
            (identical(other.enableSelfDestruct, enableSelfDestruct) ||
                other.enableSelfDestruct == enableSelfDestruct) &&
            (identical(other.selfDestructTimer, selfDestructTimer) ||
                other.selfDestructTimer == selfDestructTimer) &&
            (identical(
                  other.customSelfDestructSeconds,
                  customSelfDestructSeconds,
                ) ||
                other.customSelfDestructSeconds == customSelfDestructSeconds) &&
            (identical(other.isHidden, isHidden) ||
                other.isHidden == isHidden) &&
            (identical(other.isPinned, isPinned) ||
                other.isPinned == isPinned) &&
            (identical(other.isArchived, isArchived) ||
                other.isArchived == isArchived) &&
            (identical(other.isStarred, isStarred) ||
                other.isStarred == isStarred));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    chatId,
    allowScreenshots,
    notifyOnScreenshot,
    allowMessageEditing,
    muteChat,
    showSeenStatus,
    showTypingIndicators,
    enableSelfDestruct,
    selfDestructTimer,
    customSelfDestructSeconds,
    isHidden,
    isPinned,
    isArchived,
    isStarred,
  );

  /// Create a copy of IndividualChatSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$IndividualChatSettingsImplCopyWith<_$IndividualChatSettingsImpl>
  get copyWith =>
      __$$IndividualChatSettingsImplCopyWithImpl<_$IndividualChatSettingsImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$IndividualChatSettingsImplToJson(this);
  }
}

abstract class _IndividualChatSettings implements IndividualChatSettings {
  const factory _IndividualChatSettings({
    required final String chatId,
    final bool? allowScreenshots,
    final bool? notifyOnScreenshot,
    final bool? allowMessageEditing,
    final bool? muteChat,
    final bool? showSeenStatus,
    final bool? showTypingIndicators,
    final bool? enableSelfDestruct,
    final SelfDestructTimer? selfDestructTimer,
    final int? customSelfDestructSeconds,
    final bool isHidden,
    final bool isPinned,
    final bool isArchived,
    final bool isStarred,
  }) = _$IndividualChatSettingsImpl;

  factory _IndividualChatSettings.fromJson(Map<String, dynamic> json) =
      _$IndividualChatSettingsImpl.fromJson;

  @override
  String get chatId; // Override global settings
  @override
  bool? get allowScreenshots;
  @override
  bool? get notifyOnScreenshot;
  @override
  bool? get allowMessageEditing;
  @override
  bool? get muteChat;
  @override
  bool? get showSeenStatus;
  @override
  bool? get showTypingIndicators;
  @override
  bool? get enableSelfDestruct;
  @override
  SelfDestructTimer? get selfDestructTimer;
  @override
  int? get customSelfDestructSeconds; // Chat-specific settings
  @override
  bool get isHidden;
  @override
  bool get isPinned;
  @override
  bool get isArchived;
  @override
  bool get isStarred;

  /// Create a copy of IndividualChatSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$IndividualChatSettingsImplCopyWith<_$IndividualChatSettingsImpl>
  get copyWith => throw _privateConstructorUsedError;
}
