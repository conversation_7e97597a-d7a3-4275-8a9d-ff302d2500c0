// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_settings_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ChatSettingsImpl _$$ChatSettingsImplFromJson(Map<String, dynamic> json) =>
    _$ChatSettingsImpl(
      allowScreenshots: json['allowScreenshots'] as bool? ?? false,
      notifyOnScreenshot: json['notifyOnScreenshot'] as bool? ?? true,
      allowMessageEditing: json['allowMessageEditing'] as bool? ?? true,
      editWindowDuration:
          $enumDecodeNullable(
            _$EditWindowDurationEnumMap,
            json['editWindowDuration'],
          ) ??
          EditWindowDuration.tenMinutes,
      customEditWindowMinutes:
          (json['customEditWindowMinutes'] as num?)?.toInt() ?? 10,
      muteAllChats: json['muteAllChats'] as bool? ?? false,
      allowMessagesFromNonFollowers:
          json['allowMessagesFromNonFollowers'] as bool? ?? true,
      autoBlockSpamAccounts: json['autoBlockSpamAccounts'] as bool? ?? true,
      enableSelfDestruct: json['enableSelfDestruct'] as bool? ?? false,
      selfDestructTimer:
          $enumDecodeNullable(
            _$SelfDestructTimerEnumMap,
            json['selfDestructTimer'],
          ) ??
          SelfDestructTimer.afterReading,
      customSelfDestructSeconds:
          (json['customSelfDestructSeconds'] as num?)?.toInt() ?? 10,
      showSeenStatus: json['showSeenStatus'] as bool? ?? true,
      showTypingIndicators: json['showTypingIndicators'] as bool? ?? true,
      syncAcrossDevices: json['syncAcrossDevices'] as bool? ?? true,
      requirePasswordForHiddenChats:
          json['requirePasswordForHiddenChats'] as bool? ?? false,
      useFaceIDForHiddenChats:
          json['useFaceIDForHiddenChats'] as bool? ?? false,
    );

Map<String, dynamic> _$$ChatSettingsImplToJson(
  _$ChatSettingsImpl instance,
) => <String, dynamic>{
  'allowScreenshots': instance.allowScreenshots,
  'notifyOnScreenshot': instance.notifyOnScreenshot,
  'allowMessageEditing': instance.allowMessageEditing,
  'editWindowDuration':
      _$EditWindowDurationEnumMap[instance.editWindowDuration]!,
  'customEditWindowMinutes': instance.customEditWindowMinutes,
  'muteAllChats': instance.muteAllChats,
  'allowMessagesFromNonFollowers': instance.allowMessagesFromNonFollowers,
  'autoBlockSpamAccounts': instance.autoBlockSpamAccounts,
  'enableSelfDestruct': instance.enableSelfDestruct,
  'selfDestructTimer': _$SelfDestructTimerEnumMap[instance.selfDestructTimer]!,
  'customSelfDestructSeconds': instance.customSelfDestructSeconds,
  'showSeenStatus': instance.showSeenStatus,
  'showTypingIndicators': instance.showTypingIndicators,
  'syncAcrossDevices': instance.syncAcrossDevices,
  'requirePasswordForHiddenChats': instance.requirePasswordForHiddenChats,
  'useFaceIDForHiddenChats': instance.useFaceIDForHiddenChats,
};

const _$EditWindowDurationEnumMap = {
  EditWindowDuration.fiveMinutes: 'fiveMinutes',
  EditWindowDuration.tenMinutes: 'tenMinutes',
  EditWindowDuration.thirtyMinutes: 'thirtyMinutes',
  EditWindowDuration.oneHour: 'oneHour',
  EditWindowDuration.custom: 'custom',
};

const _$SelfDestructTimerEnumMap = {
  SelfDestructTimer.afterReading: 'afterReading',
  SelfDestructTimer.after24h: 'after24h',
  SelfDestructTimer.after7days: 'after7days',
  SelfDestructTimer.custom: 'custom',
};

_$IndividualChatSettingsImpl _$$IndividualChatSettingsImplFromJson(
  Map<String, dynamic> json,
) => _$IndividualChatSettingsImpl(
  chatId: json['chatId'] as String,
  allowScreenshots: json['allowScreenshots'] as bool?,
  notifyOnScreenshot: json['notifyOnScreenshot'] as bool?,
  allowMessageEditing: json['allowMessageEditing'] as bool?,
  muteChat: json['muteChat'] as bool?,
  showSeenStatus: json['showSeenStatus'] as bool?,
  showTypingIndicators: json['showTypingIndicators'] as bool?,
  enableSelfDestruct: json['enableSelfDestruct'] as bool?,
  selfDestructTimer: $enumDecodeNullable(
    _$SelfDestructTimerEnumMap,
    json['selfDestructTimer'],
  ),
  customSelfDestructSeconds: (json['customSelfDestructSeconds'] as num?)
      ?.toInt(),
  isHidden: json['isHidden'] as bool? ?? false,
  isPinned: json['isPinned'] as bool? ?? false,
  isArchived: json['isArchived'] as bool? ?? false,
  isStarred: json['isStarred'] as bool? ?? false,
);

Map<String, dynamic> _$$IndividualChatSettingsImplToJson(
  _$IndividualChatSettingsImpl instance,
) => <String, dynamic>{
  'chatId': instance.chatId,
  'allowScreenshots': instance.allowScreenshots,
  'notifyOnScreenshot': instance.notifyOnScreenshot,
  'allowMessageEditing': instance.allowMessageEditing,
  'muteChat': instance.muteChat,
  'showSeenStatus': instance.showSeenStatus,
  'showTypingIndicators': instance.showTypingIndicators,
  'enableSelfDestruct': instance.enableSelfDestruct,
  'selfDestructTimer': _$SelfDestructTimerEnumMap[instance.selfDestructTimer],
  'customSelfDestructSeconds': instance.customSelfDestructSeconds,
  'isHidden': instance.isHidden,
  'isPinned': instance.isPinned,
  'isArchived': instance.isArchived,
  'isStarred': instance.isStarred,
};
