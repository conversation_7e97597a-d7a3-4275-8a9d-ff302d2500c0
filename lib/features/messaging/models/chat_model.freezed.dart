// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'chat_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

ChatModel _$ChatModelFromJson(Map<String, dynamic> json) {
  return _ChatModel.fromJson(json);
}

/// @nodoc
mixin _$ChatModel {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get avatarUrl => throw _privateConstructorUsedError;
  String get lastMessage => throw _privateConstructorUsedError;
  DateTime get lastMessageTime => throw _privateConstructorUsedError;
  bool get isGroup => throw _privateConstructorUsedError;
  List<String> get participants => throw _privateConstructorUsedError;
  bool get isHidden => throw _privateConstructorUsedError;
  bool get isBlocked => throw _privateConstructorUsedError;
  ChatPrivacySettings get privacySettings => throw _privateConstructorUsedError;
  int get unreadCount => throw _privateConstructorUsedError;
  MessageStatus get lastMessageStatus => throw _privateConstructorUsedError;

  /// Serializes this ChatModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ChatModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ChatModelCopyWith<ChatModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChatModelCopyWith<$Res> {
  factory $ChatModelCopyWith(ChatModel value, $Res Function(ChatModel) then) =
      _$ChatModelCopyWithImpl<$Res, ChatModel>;
  @useResult
  $Res call({
    String id,
    String name,
    String avatarUrl,
    String lastMessage,
    DateTime lastMessageTime,
    bool isGroup,
    List<String> participants,
    bool isHidden,
    bool isBlocked,
    ChatPrivacySettings privacySettings,
    int unreadCount,
    MessageStatus lastMessageStatus,
  });

  $ChatPrivacySettingsCopyWith<$Res> get privacySettings;
}

/// @nodoc
class _$ChatModelCopyWithImpl<$Res, $Val extends ChatModel>
    implements $ChatModelCopyWith<$Res> {
  _$ChatModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ChatModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? avatarUrl = null,
    Object? lastMessage = null,
    Object? lastMessageTime = null,
    Object? isGroup = null,
    Object? participants = null,
    Object? isHidden = null,
    Object? isBlocked = null,
    Object? privacySettings = null,
    Object? unreadCount = null,
    Object? lastMessageStatus = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            avatarUrl: null == avatarUrl
                ? _value.avatarUrl
                : avatarUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            lastMessage: null == lastMessage
                ? _value.lastMessage
                : lastMessage // ignore: cast_nullable_to_non_nullable
                      as String,
            lastMessageTime: null == lastMessageTime
                ? _value.lastMessageTime
                : lastMessageTime // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isGroup: null == isGroup
                ? _value.isGroup
                : isGroup // ignore: cast_nullable_to_non_nullable
                      as bool,
            participants: null == participants
                ? _value.participants
                : participants // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            isHidden: null == isHidden
                ? _value.isHidden
                : isHidden // ignore: cast_nullable_to_non_nullable
                      as bool,
            isBlocked: null == isBlocked
                ? _value.isBlocked
                : isBlocked // ignore: cast_nullable_to_non_nullable
                      as bool,
            privacySettings: null == privacySettings
                ? _value.privacySettings
                : privacySettings // ignore: cast_nullable_to_non_nullable
                      as ChatPrivacySettings,
            unreadCount: null == unreadCount
                ? _value.unreadCount
                : unreadCount // ignore: cast_nullable_to_non_nullable
                      as int,
            lastMessageStatus: null == lastMessageStatus
                ? _value.lastMessageStatus
                : lastMessageStatus // ignore: cast_nullable_to_non_nullable
                      as MessageStatus,
          )
          as $Val,
    );
  }

  /// Create a copy of ChatModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ChatPrivacySettingsCopyWith<$Res> get privacySettings {
    return $ChatPrivacySettingsCopyWith<$Res>(_value.privacySettings, (value) {
      return _then(_value.copyWith(privacySettings: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ChatModelImplCopyWith<$Res>
    implements $ChatModelCopyWith<$Res> {
  factory _$$ChatModelImplCopyWith(
    _$ChatModelImpl value,
    $Res Function(_$ChatModelImpl) then,
  ) = __$$ChatModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String avatarUrl,
    String lastMessage,
    DateTime lastMessageTime,
    bool isGroup,
    List<String> participants,
    bool isHidden,
    bool isBlocked,
    ChatPrivacySettings privacySettings,
    int unreadCount,
    MessageStatus lastMessageStatus,
  });

  @override
  $ChatPrivacySettingsCopyWith<$Res> get privacySettings;
}

/// @nodoc
class __$$ChatModelImplCopyWithImpl<$Res>
    extends _$ChatModelCopyWithImpl<$Res, _$ChatModelImpl>
    implements _$$ChatModelImplCopyWith<$Res> {
  __$$ChatModelImplCopyWithImpl(
    _$ChatModelImpl _value,
    $Res Function(_$ChatModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ChatModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? avatarUrl = null,
    Object? lastMessage = null,
    Object? lastMessageTime = null,
    Object? isGroup = null,
    Object? participants = null,
    Object? isHidden = null,
    Object? isBlocked = null,
    Object? privacySettings = null,
    Object? unreadCount = null,
    Object? lastMessageStatus = null,
  }) {
    return _then(
      _$ChatModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        avatarUrl: null == avatarUrl
            ? _value.avatarUrl
            : avatarUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        lastMessage: null == lastMessage
            ? _value.lastMessage
            : lastMessage // ignore: cast_nullable_to_non_nullable
                  as String,
        lastMessageTime: null == lastMessageTime
            ? _value.lastMessageTime
            : lastMessageTime // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isGroup: null == isGroup
            ? _value.isGroup
            : isGroup // ignore: cast_nullable_to_non_nullable
                  as bool,
        participants: null == participants
            ? _value._participants
            : participants // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        isHidden: null == isHidden
            ? _value.isHidden
            : isHidden // ignore: cast_nullable_to_non_nullable
                  as bool,
        isBlocked: null == isBlocked
            ? _value.isBlocked
            : isBlocked // ignore: cast_nullable_to_non_nullable
                  as bool,
        privacySettings: null == privacySettings
            ? _value.privacySettings
            : privacySettings // ignore: cast_nullable_to_non_nullable
                  as ChatPrivacySettings,
        unreadCount: null == unreadCount
            ? _value.unreadCount
            : unreadCount // ignore: cast_nullable_to_non_nullable
                  as int,
        lastMessageStatus: null == lastMessageStatus
            ? _value.lastMessageStatus
            : lastMessageStatus // ignore: cast_nullable_to_non_nullable
                  as MessageStatus,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ChatModelImpl implements _ChatModel {
  const _$ChatModelImpl({
    required this.id,
    required this.name,
    required this.avatarUrl,
    required this.lastMessage,
    required this.lastMessageTime,
    required this.isGroup,
    required final List<String> participants,
    required this.isHidden,
    required this.isBlocked,
    required this.privacySettings,
    required this.unreadCount,
    required this.lastMessageStatus,
  }) : _participants = participants;

  factory _$ChatModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChatModelImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String avatarUrl;
  @override
  final String lastMessage;
  @override
  final DateTime lastMessageTime;
  @override
  final bool isGroup;
  final List<String> _participants;
  @override
  List<String> get participants {
    if (_participants is EqualUnmodifiableListView) return _participants;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_participants);
  }

  @override
  final bool isHidden;
  @override
  final bool isBlocked;
  @override
  final ChatPrivacySettings privacySettings;
  @override
  final int unreadCount;
  @override
  final MessageStatus lastMessageStatus;

  @override
  String toString() {
    return 'ChatModel(id: $id, name: $name, avatarUrl: $avatarUrl, lastMessage: $lastMessage, lastMessageTime: $lastMessageTime, isGroup: $isGroup, participants: $participants, isHidden: $isHidden, isBlocked: $isBlocked, privacySettings: $privacySettings, unreadCount: $unreadCount, lastMessageStatus: $lastMessageStatus)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChatModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.avatarUrl, avatarUrl) ||
                other.avatarUrl == avatarUrl) &&
            (identical(other.lastMessage, lastMessage) ||
                other.lastMessage == lastMessage) &&
            (identical(other.lastMessageTime, lastMessageTime) ||
                other.lastMessageTime == lastMessageTime) &&
            (identical(other.isGroup, isGroup) || other.isGroup == isGroup) &&
            const DeepCollectionEquality().equals(
              other._participants,
              _participants,
            ) &&
            (identical(other.isHidden, isHidden) ||
                other.isHidden == isHidden) &&
            (identical(other.isBlocked, isBlocked) ||
                other.isBlocked == isBlocked) &&
            (identical(other.privacySettings, privacySettings) ||
                other.privacySettings == privacySettings) &&
            (identical(other.unreadCount, unreadCount) ||
                other.unreadCount == unreadCount) &&
            (identical(other.lastMessageStatus, lastMessageStatus) ||
                other.lastMessageStatus == lastMessageStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    avatarUrl,
    lastMessage,
    lastMessageTime,
    isGroup,
    const DeepCollectionEquality().hash(_participants),
    isHidden,
    isBlocked,
    privacySettings,
    unreadCount,
    lastMessageStatus,
  );

  /// Create a copy of ChatModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChatModelImplCopyWith<_$ChatModelImpl> get copyWith =>
      __$$ChatModelImplCopyWithImpl<_$ChatModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ChatModelImplToJson(this);
  }
}

abstract class _ChatModel implements ChatModel {
  const factory _ChatModel({
    required final String id,
    required final String name,
    required final String avatarUrl,
    required final String lastMessage,
    required final DateTime lastMessageTime,
    required final bool isGroup,
    required final List<String> participants,
    required final bool isHidden,
    required final bool isBlocked,
    required final ChatPrivacySettings privacySettings,
    required final int unreadCount,
    required final MessageStatus lastMessageStatus,
  }) = _$ChatModelImpl;

  factory _ChatModel.fromJson(Map<String, dynamic> json) =
      _$ChatModelImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get avatarUrl;
  @override
  String get lastMessage;
  @override
  DateTime get lastMessageTime;
  @override
  bool get isGroup;
  @override
  List<String> get participants;
  @override
  bool get isHidden;
  @override
  bool get isBlocked;
  @override
  ChatPrivacySettings get privacySettings;
  @override
  int get unreadCount;
  @override
  MessageStatus get lastMessageStatus;

  /// Create a copy of ChatModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChatModelImplCopyWith<_$ChatModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

MessageModel _$MessageModelFromJson(Map<String, dynamic> json) {
  return _MessageModel.fromJson(json);
}

/// @nodoc
mixin _$MessageModel {
  String get id => throw _privateConstructorUsedError;
  String get chatId => throw _privateConstructorUsedError;
  String get senderId => throw _privateConstructorUsedError;
  String get content => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;
  MessageType get type => throw _privateConstructorUsedError;
  MessageStatus get status => throw _privateConstructorUsedError;
  bool get isEdited => throw _privateConstructorUsedError;
  DateTime? get editedAt => throw _privateConstructorUsedError;
  List<MessageReaction> get reactions => throw _privateConstructorUsedError;
  SelfDestructSettings? get selfDestructSettings =>
      throw _privateConstructorUsedError;
  List<String> get seenBy => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;
  DateTime? get deletedAt =>
      throw _privateConstructorUsedError; // Additional fields for enhanced messaging features
  String get senderName => throw _privateConstructorUsedError;
  String get senderAvatarUrl => throw _privateConstructorUsedError;
  String? get mediaUrl =>
      throw _privateConstructorUsedError; // For images, videos, files
  String? get fileName =>
      throw _privateConstructorUsedError; // For file attachments
  int? get fileSize => throw _privateConstructorUsedError; // File size in bytes
  String? get thumbnailUrl =>
      throw _privateConstructorUsedError; // For video thumbnails
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this MessageModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MessageModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MessageModelCopyWith<MessageModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessageModelCopyWith<$Res> {
  factory $MessageModelCopyWith(
    MessageModel value,
    $Res Function(MessageModel) then,
  ) = _$MessageModelCopyWithImpl<$Res, MessageModel>;
  @useResult
  $Res call({
    String id,
    String chatId,
    String senderId,
    String content,
    DateTime timestamp,
    MessageType type,
    MessageStatus status,
    bool isEdited,
    DateTime? editedAt,
    List<MessageReaction> reactions,
    SelfDestructSettings? selfDestructSettings,
    List<String> seenBy,
    bool isDeleted,
    DateTime? deletedAt,
    String senderName,
    String senderAvatarUrl,
    String? mediaUrl,
    String? fileName,
    int? fileSize,
    String? thumbnailUrl,
    Map<String, dynamic>? metadata,
  });

  $SelfDestructSettingsCopyWith<$Res>? get selfDestructSettings;
}

/// @nodoc
class _$MessageModelCopyWithImpl<$Res, $Val extends MessageModel>
    implements $MessageModelCopyWith<$Res> {
  _$MessageModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MessageModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? chatId = null,
    Object? senderId = null,
    Object? content = null,
    Object? timestamp = null,
    Object? type = null,
    Object? status = null,
    Object? isEdited = null,
    Object? editedAt = freezed,
    Object? reactions = null,
    Object? selfDestructSettings = freezed,
    Object? seenBy = null,
    Object? isDeleted = null,
    Object? deletedAt = freezed,
    Object? senderName = null,
    Object? senderAvatarUrl = null,
    Object? mediaUrl = freezed,
    Object? fileName = freezed,
    Object? fileSize = freezed,
    Object? thumbnailUrl = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            chatId: null == chatId
                ? _value.chatId
                : chatId // ignore: cast_nullable_to_non_nullable
                      as String,
            senderId: null == senderId
                ? _value.senderId
                : senderId // ignore: cast_nullable_to_non_nullable
                      as String,
            content: null == content
                ? _value.content
                : content // ignore: cast_nullable_to_non_nullable
                      as String,
            timestamp: null == timestamp
                ? _value.timestamp
                : timestamp // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as MessageType,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as MessageStatus,
            isEdited: null == isEdited
                ? _value.isEdited
                : isEdited // ignore: cast_nullable_to_non_nullable
                      as bool,
            editedAt: freezed == editedAt
                ? _value.editedAt
                : editedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            reactions: null == reactions
                ? _value.reactions
                : reactions // ignore: cast_nullable_to_non_nullable
                      as List<MessageReaction>,
            selfDestructSettings: freezed == selfDestructSettings
                ? _value.selfDestructSettings
                : selfDestructSettings // ignore: cast_nullable_to_non_nullable
                      as SelfDestructSettings?,
            seenBy: null == seenBy
                ? _value.seenBy
                : seenBy // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            isDeleted: null == isDeleted
                ? _value.isDeleted
                : isDeleted // ignore: cast_nullable_to_non_nullable
                      as bool,
            deletedAt: freezed == deletedAt
                ? _value.deletedAt
                : deletedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            senderName: null == senderName
                ? _value.senderName
                : senderName // ignore: cast_nullable_to_non_nullable
                      as String,
            senderAvatarUrl: null == senderAvatarUrl
                ? _value.senderAvatarUrl
                : senderAvatarUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            mediaUrl: freezed == mediaUrl
                ? _value.mediaUrl
                : mediaUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            fileName: freezed == fileName
                ? _value.fileName
                : fileName // ignore: cast_nullable_to_non_nullable
                      as String?,
            fileSize: freezed == fileSize
                ? _value.fileSize
                : fileSize // ignore: cast_nullable_to_non_nullable
                      as int?,
            thumbnailUrl: freezed == thumbnailUrl
                ? _value.thumbnailUrl
                : thumbnailUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            metadata: freezed == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
          )
          as $Val,
    );
  }

  /// Create a copy of MessageModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SelfDestructSettingsCopyWith<$Res>? get selfDestructSettings {
    if (_value.selfDestructSettings == null) {
      return null;
    }

    return $SelfDestructSettingsCopyWith<$Res>(_value.selfDestructSettings!, (
      value,
    ) {
      return _then(_value.copyWith(selfDestructSettings: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$MessageModelImplCopyWith<$Res>
    implements $MessageModelCopyWith<$Res> {
  factory _$$MessageModelImplCopyWith(
    _$MessageModelImpl value,
    $Res Function(_$MessageModelImpl) then,
  ) = __$$MessageModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String chatId,
    String senderId,
    String content,
    DateTime timestamp,
    MessageType type,
    MessageStatus status,
    bool isEdited,
    DateTime? editedAt,
    List<MessageReaction> reactions,
    SelfDestructSettings? selfDestructSettings,
    List<String> seenBy,
    bool isDeleted,
    DateTime? deletedAt,
    String senderName,
    String senderAvatarUrl,
    String? mediaUrl,
    String? fileName,
    int? fileSize,
    String? thumbnailUrl,
    Map<String, dynamic>? metadata,
  });

  @override
  $SelfDestructSettingsCopyWith<$Res>? get selfDestructSettings;
}

/// @nodoc
class __$$MessageModelImplCopyWithImpl<$Res>
    extends _$MessageModelCopyWithImpl<$Res, _$MessageModelImpl>
    implements _$$MessageModelImplCopyWith<$Res> {
  __$$MessageModelImplCopyWithImpl(
    _$MessageModelImpl _value,
    $Res Function(_$MessageModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MessageModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? chatId = null,
    Object? senderId = null,
    Object? content = null,
    Object? timestamp = null,
    Object? type = null,
    Object? status = null,
    Object? isEdited = null,
    Object? editedAt = freezed,
    Object? reactions = null,
    Object? selfDestructSettings = freezed,
    Object? seenBy = null,
    Object? isDeleted = null,
    Object? deletedAt = freezed,
    Object? senderName = null,
    Object? senderAvatarUrl = null,
    Object? mediaUrl = freezed,
    Object? fileName = freezed,
    Object? fileSize = freezed,
    Object? thumbnailUrl = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _$MessageModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        chatId: null == chatId
            ? _value.chatId
            : chatId // ignore: cast_nullable_to_non_nullable
                  as String,
        senderId: null == senderId
            ? _value.senderId
            : senderId // ignore: cast_nullable_to_non_nullable
                  as String,
        content: null == content
            ? _value.content
            : content // ignore: cast_nullable_to_non_nullable
                  as String,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as MessageType,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as MessageStatus,
        isEdited: null == isEdited
            ? _value.isEdited
            : isEdited // ignore: cast_nullable_to_non_nullable
                  as bool,
        editedAt: freezed == editedAt
            ? _value.editedAt
            : editedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        reactions: null == reactions
            ? _value._reactions
            : reactions // ignore: cast_nullable_to_non_nullable
                  as List<MessageReaction>,
        selfDestructSettings: freezed == selfDestructSettings
            ? _value.selfDestructSettings
            : selfDestructSettings // ignore: cast_nullable_to_non_nullable
                  as SelfDestructSettings?,
        seenBy: null == seenBy
            ? _value._seenBy
            : seenBy // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        isDeleted: null == isDeleted
            ? _value.isDeleted
            : isDeleted // ignore: cast_nullable_to_non_nullable
                  as bool,
        deletedAt: freezed == deletedAt
            ? _value.deletedAt
            : deletedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        senderName: null == senderName
            ? _value.senderName
            : senderName // ignore: cast_nullable_to_non_nullable
                  as String,
        senderAvatarUrl: null == senderAvatarUrl
            ? _value.senderAvatarUrl
            : senderAvatarUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        mediaUrl: freezed == mediaUrl
            ? _value.mediaUrl
            : mediaUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        fileName: freezed == fileName
            ? _value.fileName
            : fileName // ignore: cast_nullable_to_non_nullable
                  as String?,
        fileSize: freezed == fileSize
            ? _value.fileSize
            : fileSize // ignore: cast_nullable_to_non_nullable
                  as int?,
        thumbnailUrl: freezed == thumbnailUrl
            ? _value.thumbnailUrl
            : thumbnailUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        metadata: freezed == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$MessageModelImpl implements _MessageModel {
  const _$MessageModelImpl({
    required this.id,
    required this.chatId,
    required this.senderId,
    required this.content,
    required this.timestamp,
    required this.type,
    required this.status,
    required this.isEdited,
    required this.editedAt,
    required final List<MessageReaction> reactions,
    required this.selfDestructSettings,
    required final List<String> seenBy,
    required this.isDeleted,
    required this.deletedAt,
    this.senderName = '',
    this.senderAvatarUrl = '',
    this.mediaUrl,
    this.fileName,
    this.fileSize,
    this.thumbnailUrl,
    final Map<String, dynamic>? metadata,
  }) : _reactions = reactions,
       _seenBy = seenBy,
       _metadata = metadata;

  factory _$MessageModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$MessageModelImplFromJson(json);

  @override
  final String id;
  @override
  final String chatId;
  @override
  final String senderId;
  @override
  final String content;
  @override
  final DateTime timestamp;
  @override
  final MessageType type;
  @override
  final MessageStatus status;
  @override
  final bool isEdited;
  @override
  final DateTime? editedAt;
  final List<MessageReaction> _reactions;
  @override
  List<MessageReaction> get reactions {
    if (_reactions is EqualUnmodifiableListView) return _reactions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_reactions);
  }

  @override
  final SelfDestructSettings? selfDestructSettings;
  final List<String> _seenBy;
  @override
  List<String> get seenBy {
    if (_seenBy is EqualUnmodifiableListView) return _seenBy;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_seenBy);
  }

  @override
  final bool isDeleted;
  @override
  final DateTime? deletedAt;
  // Additional fields for enhanced messaging features
  @override
  @JsonKey()
  final String senderName;
  @override
  @JsonKey()
  final String senderAvatarUrl;
  @override
  final String? mediaUrl;
  // For images, videos, files
  @override
  final String? fileName;
  // For file attachments
  @override
  final int? fileSize;
  // File size in bytes
  @override
  final String? thumbnailUrl;
  // For video thumbnails
  final Map<String, dynamic>? _metadata;
  // For video thumbnails
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'MessageModel(id: $id, chatId: $chatId, senderId: $senderId, content: $content, timestamp: $timestamp, type: $type, status: $status, isEdited: $isEdited, editedAt: $editedAt, reactions: $reactions, selfDestructSettings: $selfDestructSettings, seenBy: $seenBy, isDeleted: $isDeleted, deletedAt: $deletedAt, senderName: $senderName, senderAvatarUrl: $senderAvatarUrl, mediaUrl: $mediaUrl, fileName: $fileName, fileSize: $fileSize, thumbnailUrl: $thumbnailUrl, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MessageModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.chatId, chatId) || other.chatId == chatId) &&
            (identical(other.senderId, senderId) ||
                other.senderId == senderId) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.isEdited, isEdited) ||
                other.isEdited == isEdited) &&
            (identical(other.editedAt, editedAt) ||
                other.editedAt == editedAt) &&
            const DeepCollectionEquality().equals(
              other._reactions,
              _reactions,
            ) &&
            (identical(other.selfDestructSettings, selfDestructSettings) ||
                other.selfDestructSettings == selfDestructSettings) &&
            const DeepCollectionEquality().equals(other._seenBy, _seenBy) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted) &&
            (identical(other.deletedAt, deletedAt) ||
                other.deletedAt == deletedAt) &&
            (identical(other.senderName, senderName) ||
                other.senderName == senderName) &&
            (identical(other.senderAvatarUrl, senderAvatarUrl) ||
                other.senderAvatarUrl == senderAvatarUrl) &&
            (identical(other.mediaUrl, mediaUrl) ||
                other.mediaUrl == mediaUrl) &&
            (identical(other.fileName, fileName) ||
                other.fileName == fileName) &&
            (identical(other.fileSize, fileSize) ||
                other.fileSize == fileSize) &&
            (identical(other.thumbnailUrl, thumbnailUrl) ||
                other.thumbnailUrl == thumbnailUrl) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    chatId,
    senderId,
    content,
    timestamp,
    type,
    status,
    isEdited,
    editedAt,
    const DeepCollectionEquality().hash(_reactions),
    selfDestructSettings,
    const DeepCollectionEquality().hash(_seenBy),
    isDeleted,
    deletedAt,
    senderName,
    senderAvatarUrl,
    mediaUrl,
    fileName,
    fileSize,
    thumbnailUrl,
    const DeepCollectionEquality().hash(_metadata),
  ]);

  /// Create a copy of MessageModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MessageModelImplCopyWith<_$MessageModelImpl> get copyWith =>
      __$$MessageModelImplCopyWithImpl<_$MessageModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MessageModelImplToJson(this);
  }
}

abstract class _MessageModel implements MessageModel {
  const factory _MessageModel({
    required final String id,
    required final String chatId,
    required final String senderId,
    required final String content,
    required final DateTime timestamp,
    required final MessageType type,
    required final MessageStatus status,
    required final bool isEdited,
    required final DateTime? editedAt,
    required final List<MessageReaction> reactions,
    required final SelfDestructSettings? selfDestructSettings,
    required final List<String> seenBy,
    required final bool isDeleted,
    required final DateTime? deletedAt,
    final String senderName,
    final String senderAvatarUrl,
    final String? mediaUrl,
    final String? fileName,
    final int? fileSize,
    final String? thumbnailUrl,
    final Map<String, dynamic>? metadata,
  }) = _$MessageModelImpl;

  factory _MessageModel.fromJson(Map<String, dynamic> json) =
      _$MessageModelImpl.fromJson;

  @override
  String get id;
  @override
  String get chatId;
  @override
  String get senderId;
  @override
  String get content;
  @override
  DateTime get timestamp;
  @override
  MessageType get type;
  @override
  MessageStatus get status;
  @override
  bool get isEdited;
  @override
  DateTime? get editedAt;
  @override
  List<MessageReaction> get reactions;
  @override
  SelfDestructSettings? get selfDestructSettings;
  @override
  List<String> get seenBy;
  @override
  bool get isDeleted;
  @override
  DateTime? get deletedAt; // Additional fields for enhanced messaging features
  @override
  String get senderName;
  @override
  String get senderAvatarUrl;
  @override
  String? get mediaUrl; // For images, videos, files
  @override
  String? get fileName; // For file attachments
  @override
  int? get fileSize; // File size in bytes
  @override
  String? get thumbnailUrl; // For video thumbnails
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of MessageModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MessageModelImplCopyWith<_$MessageModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

MessageReaction _$MessageReactionFromJson(Map<String, dynamic> json) {
  return _MessageReaction.fromJson(json);
}

/// @nodoc
mixin _$MessageReaction {
  String get userId => throw _privateConstructorUsedError;
  String get emoji => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;

  /// Serializes this MessageReaction to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MessageReaction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MessageReactionCopyWith<MessageReaction> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessageReactionCopyWith<$Res> {
  factory $MessageReactionCopyWith(
    MessageReaction value,
    $Res Function(MessageReaction) then,
  ) = _$MessageReactionCopyWithImpl<$Res, MessageReaction>;
  @useResult
  $Res call({String userId, String emoji, DateTime timestamp});
}

/// @nodoc
class _$MessageReactionCopyWithImpl<$Res, $Val extends MessageReaction>
    implements $MessageReactionCopyWith<$Res> {
  _$MessageReactionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MessageReaction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? emoji = null,
    Object? timestamp = null,
  }) {
    return _then(
      _value.copyWith(
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            emoji: null == emoji
                ? _value.emoji
                : emoji // ignore: cast_nullable_to_non_nullable
                      as String,
            timestamp: null == timestamp
                ? _value.timestamp
                : timestamp // ignore: cast_nullable_to_non_nullable
                      as DateTime,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$MessageReactionImplCopyWith<$Res>
    implements $MessageReactionCopyWith<$Res> {
  factory _$$MessageReactionImplCopyWith(
    _$MessageReactionImpl value,
    $Res Function(_$MessageReactionImpl) then,
  ) = __$$MessageReactionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String userId, String emoji, DateTime timestamp});
}

/// @nodoc
class __$$MessageReactionImplCopyWithImpl<$Res>
    extends _$MessageReactionCopyWithImpl<$Res, _$MessageReactionImpl>
    implements _$$MessageReactionImplCopyWith<$Res> {
  __$$MessageReactionImplCopyWithImpl(
    _$MessageReactionImpl _value,
    $Res Function(_$MessageReactionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MessageReaction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? emoji = null,
    Object? timestamp = null,
  }) {
    return _then(
      _$MessageReactionImpl(
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        emoji: null == emoji
            ? _value.emoji
            : emoji // ignore: cast_nullable_to_non_nullable
                  as String,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$MessageReactionImpl implements _MessageReaction {
  const _$MessageReactionImpl({
    required this.userId,
    required this.emoji,
    required this.timestamp,
  });

  factory _$MessageReactionImpl.fromJson(Map<String, dynamic> json) =>
      _$$MessageReactionImplFromJson(json);

  @override
  final String userId;
  @override
  final String emoji;
  @override
  final DateTime timestamp;

  @override
  String toString() {
    return 'MessageReaction(userId: $userId, emoji: $emoji, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MessageReactionImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.emoji, emoji) || other.emoji == emoji) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, userId, emoji, timestamp);

  /// Create a copy of MessageReaction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MessageReactionImplCopyWith<_$MessageReactionImpl> get copyWith =>
      __$$MessageReactionImplCopyWithImpl<_$MessageReactionImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$MessageReactionImplToJson(this);
  }
}

abstract class _MessageReaction implements MessageReaction {
  const factory _MessageReaction({
    required final String userId,
    required final String emoji,
    required final DateTime timestamp,
  }) = _$MessageReactionImpl;

  factory _MessageReaction.fromJson(Map<String, dynamic> json) =
      _$MessageReactionImpl.fromJson;

  @override
  String get userId;
  @override
  String get emoji;
  @override
  DateTime get timestamp;

  /// Create a copy of MessageReaction
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MessageReactionImplCopyWith<_$MessageReactionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SelfDestructSettings _$SelfDestructSettingsFromJson(Map<String, dynamic> json) {
  return _SelfDestructSettings.fromJson(json);
}

/// @nodoc
mixin _$SelfDestructSettings {
  SelfDestructType get type => throw _privateConstructorUsedError;
  int get durationInSeconds => throw _privateConstructorUsedError;
  DateTime? get expiresAt => throw _privateConstructorUsedError;

  /// Serializes this SelfDestructSettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SelfDestructSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SelfDestructSettingsCopyWith<SelfDestructSettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SelfDestructSettingsCopyWith<$Res> {
  factory $SelfDestructSettingsCopyWith(
    SelfDestructSettings value,
    $Res Function(SelfDestructSettings) then,
  ) = _$SelfDestructSettingsCopyWithImpl<$Res, SelfDestructSettings>;
  @useResult
  $Res call({
    SelfDestructType type,
    int durationInSeconds,
    DateTime? expiresAt,
  });
}

/// @nodoc
class _$SelfDestructSettingsCopyWithImpl<
  $Res,
  $Val extends SelfDestructSettings
>
    implements $SelfDestructSettingsCopyWith<$Res> {
  _$SelfDestructSettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SelfDestructSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? durationInSeconds = null,
    Object? expiresAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as SelfDestructType,
            durationInSeconds: null == durationInSeconds
                ? _value.durationInSeconds
                : durationInSeconds // ignore: cast_nullable_to_non_nullable
                      as int,
            expiresAt: freezed == expiresAt
                ? _value.expiresAt
                : expiresAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$SelfDestructSettingsImplCopyWith<$Res>
    implements $SelfDestructSettingsCopyWith<$Res> {
  factory _$$SelfDestructSettingsImplCopyWith(
    _$SelfDestructSettingsImpl value,
    $Res Function(_$SelfDestructSettingsImpl) then,
  ) = __$$SelfDestructSettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    SelfDestructType type,
    int durationInSeconds,
    DateTime? expiresAt,
  });
}

/// @nodoc
class __$$SelfDestructSettingsImplCopyWithImpl<$Res>
    extends _$SelfDestructSettingsCopyWithImpl<$Res, _$SelfDestructSettingsImpl>
    implements _$$SelfDestructSettingsImplCopyWith<$Res> {
  __$$SelfDestructSettingsImplCopyWithImpl(
    _$SelfDestructSettingsImpl _value,
    $Res Function(_$SelfDestructSettingsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of SelfDestructSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? durationInSeconds = null,
    Object? expiresAt = freezed,
  }) {
    return _then(
      _$SelfDestructSettingsImpl(
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as SelfDestructType,
        durationInSeconds: null == durationInSeconds
            ? _value.durationInSeconds
            : durationInSeconds // ignore: cast_nullable_to_non_nullable
                  as int,
        expiresAt: freezed == expiresAt
            ? _value.expiresAt
            : expiresAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$SelfDestructSettingsImpl implements _SelfDestructSettings {
  const _$SelfDestructSettingsImpl({
    required this.type,
    required this.durationInSeconds,
    required this.expiresAt,
  });

  factory _$SelfDestructSettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$SelfDestructSettingsImplFromJson(json);

  @override
  final SelfDestructType type;
  @override
  final int durationInSeconds;
  @override
  final DateTime? expiresAt;

  @override
  String toString() {
    return 'SelfDestructSettings(type: $type, durationInSeconds: $durationInSeconds, expiresAt: $expiresAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SelfDestructSettingsImpl &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.durationInSeconds, durationInSeconds) ||
                other.durationInSeconds == durationInSeconds) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, type, durationInSeconds, expiresAt);

  /// Create a copy of SelfDestructSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SelfDestructSettingsImplCopyWith<_$SelfDestructSettingsImpl>
  get copyWith =>
      __$$SelfDestructSettingsImplCopyWithImpl<_$SelfDestructSettingsImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$SelfDestructSettingsImplToJson(this);
  }
}

abstract class _SelfDestructSettings implements SelfDestructSettings {
  const factory _SelfDestructSettings({
    required final SelfDestructType type,
    required final int durationInSeconds,
    required final DateTime? expiresAt,
  }) = _$SelfDestructSettingsImpl;

  factory _SelfDestructSettings.fromJson(Map<String, dynamic> json) =
      _$SelfDestructSettingsImpl.fromJson;

  @override
  SelfDestructType get type;
  @override
  int get durationInSeconds;
  @override
  DateTime? get expiresAt;

  /// Create a copy of SelfDestructSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SelfDestructSettingsImplCopyWith<_$SelfDestructSettingsImpl>
  get copyWith => throw _privateConstructorUsedError;
}

ChatPrivacySettings _$ChatPrivacySettingsFromJson(Map<String, dynamic> json) {
  return _ChatPrivacySettings.fromJson(json);
}

/// @nodoc
mixin _$ChatPrivacySettings {
  bool get blockScreenshots => throw _privateConstructorUsedError;
  bool get screenshotAlerts => throw _privateConstructorUsedError;
  bool get readReceipts => throw _privateConstructorUsedError;
  bool get typingIndicators => throw _privateConstructorUsedError;
  bool get messageReactions => throw _privateConstructorUsedError;
  bool get selfDestructEnabled => throw _privateConstructorUsedError;
  int get defaultSelfDestructSeconds => throw _privateConstructorUsedError;

  /// Serializes this ChatPrivacySettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ChatPrivacySettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ChatPrivacySettingsCopyWith<ChatPrivacySettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChatPrivacySettingsCopyWith<$Res> {
  factory $ChatPrivacySettingsCopyWith(
    ChatPrivacySettings value,
    $Res Function(ChatPrivacySettings) then,
  ) = _$ChatPrivacySettingsCopyWithImpl<$Res, ChatPrivacySettings>;
  @useResult
  $Res call({
    bool blockScreenshots,
    bool screenshotAlerts,
    bool readReceipts,
    bool typingIndicators,
    bool messageReactions,
    bool selfDestructEnabled,
    int defaultSelfDestructSeconds,
  });
}

/// @nodoc
class _$ChatPrivacySettingsCopyWithImpl<$Res, $Val extends ChatPrivacySettings>
    implements $ChatPrivacySettingsCopyWith<$Res> {
  _$ChatPrivacySettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ChatPrivacySettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? blockScreenshots = null,
    Object? screenshotAlerts = null,
    Object? readReceipts = null,
    Object? typingIndicators = null,
    Object? messageReactions = null,
    Object? selfDestructEnabled = null,
    Object? defaultSelfDestructSeconds = null,
  }) {
    return _then(
      _value.copyWith(
            blockScreenshots: null == blockScreenshots
                ? _value.blockScreenshots
                : blockScreenshots // ignore: cast_nullable_to_non_nullable
                      as bool,
            screenshotAlerts: null == screenshotAlerts
                ? _value.screenshotAlerts
                : screenshotAlerts // ignore: cast_nullable_to_non_nullable
                      as bool,
            readReceipts: null == readReceipts
                ? _value.readReceipts
                : readReceipts // ignore: cast_nullable_to_non_nullable
                      as bool,
            typingIndicators: null == typingIndicators
                ? _value.typingIndicators
                : typingIndicators // ignore: cast_nullable_to_non_nullable
                      as bool,
            messageReactions: null == messageReactions
                ? _value.messageReactions
                : messageReactions // ignore: cast_nullable_to_non_nullable
                      as bool,
            selfDestructEnabled: null == selfDestructEnabled
                ? _value.selfDestructEnabled
                : selfDestructEnabled // ignore: cast_nullable_to_non_nullable
                      as bool,
            defaultSelfDestructSeconds: null == defaultSelfDestructSeconds
                ? _value.defaultSelfDestructSeconds
                : defaultSelfDestructSeconds // ignore: cast_nullable_to_non_nullable
                      as int,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ChatPrivacySettingsImplCopyWith<$Res>
    implements $ChatPrivacySettingsCopyWith<$Res> {
  factory _$$ChatPrivacySettingsImplCopyWith(
    _$ChatPrivacySettingsImpl value,
    $Res Function(_$ChatPrivacySettingsImpl) then,
  ) = __$$ChatPrivacySettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    bool blockScreenshots,
    bool screenshotAlerts,
    bool readReceipts,
    bool typingIndicators,
    bool messageReactions,
    bool selfDestructEnabled,
    int defaultSelfDestructSeconds,
  });
}

/// @nodoc
class __$$ChatPrivacySettingsImplCopyWithImpl<$Res>
    extends _$ChatPrivacySettingsCopyWithImpl<$Res, _$ChatPrivacySettingsImpl>
    implements _$$ChatPrivacySettingsImplCopyWith<$Res> {
  __$$ChatPrivacySettingsImplCopyWithImpl(
    _$ChatPrivacySettingsImpl _value,
    $Res Function(_$ChatPrivacySettingsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ChatPrivacySettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? blockScreenshots = null,
    Object? screenshotAlerts = null,
    Object? readReceipts = null,
    Object? typingIndicators = null,
    Object? messageReactions = null,
    Object? selfDestructEnabled = null,
    Object? defaultSelfDestructSeconds = null,
  }) {
    return _then(
      _$ChatPrivacySettingsImpl(
        blockScreenshots: null == blockScreenshots
            ? _value.blockScreenshots
            : blockScreenshots // ignore: cast_nullable_to_non_nullable
                  as bool,
        screenshotAlerts: null == screenshotAlerts
            ? _value.screenshotAlerts
            : screenshotAlerts // ignore: cast_nullable_to_non_nullable
                  as bool,
        readReceipts: null == readReceipts
            ? _value.readReceipts
            : readReceipts // ignore: cast_nullable_to_non_nullable
                  as bool,
        typingIndicators: null == typingIndicators
            ? _value.typingIndicators
            : typingIndicators // ignore: cast_nullable_to_non_nullable
                  as bool,
        messageReactions: null == messageReactions
            ? _value.messageReactions
            : messageReactions // ignore: cast_nullable_to_non_nullable
                  as bool,
        selfDestructEnabled: null == selfDestructEnabled
            ? _value.selfDestructEnabled
            : selfDestructEnabled // ignore: cast_nullable_to_non_nullable
                  as bool,
        defaultSelfDestructSeconds: null == defaultSelfDestructSeconds
            ? _value.defaultSelfDestructSeconds
            : defaultSelfDestructSeconds // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ChatPrivacySettingsImpl implements _ChatPrivacySettings {
  const _$ChatPrivacySettingsImpl({
    required this.blockScreenshots,
    required this.screenshotAlerts,
    required this.readReceipts,
    required this.typingIndicators,
    required this.messageReactions,
    required this.selfDestructEnabled,
    required this.defaultSelfDestructSeconds,
  });

  factory _$ChatPrivacySettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChatPrivacySettingsImplFromJson(json);

  @override
  final bool blockScreenshots;
  @override
  final bool screenshotAlerts;
  @override
  final bool readReceipts;
  @override
  final bool typingIndicators;
  @override
  final bool messageReactions;
  @override
  final bool selfDestructEnabled;
  @override
  final int defaultSelfDestructSeconds;

  @override
  String toString() {
    return 'ChatPrivacySettings(blockScreenshots: $blockScreenshots, screenshotAlerts: $screenshotAlerts, readReceipts: $readReceipts, typingIndicators: $typingIndicators, messageReactions: $messageReactions, selfDestructEnabled: $selfDestructEnabled, defaultSelfDestructSeconds: $defaultSelfDestructSeconds)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChatPrivacySettingsImpl &&
            (identical(other.blockScreenshots, blockScreenshots) ||
                other.blockScreenshots == blockScreenshots) &&
            (identical(other.screenshotAlerts, screenshotAlerts) ||
                other.screenshotAlerts == screenshotAlerts) &&
            (identical(other.readReceipts, readReceipts) ||
                other.readReceipts == readReceipts) &&
            (identical(other.typingIndicators, typingIndicators) ||
                other.typingIndicators == typingIndicators) &&
            (identical(other.messageReactions, messageReactions) ||
                other.messageReactions == messageReactions) &&
            (identical(other.selfDestructEnabled, selfDestructEnabled) ||
                other.selfDestructEnabled == selfDestructEnabled) &&
            (identical(
                  other.defaultSelfDestructSeconds,
                  defaultSelfDestructSeconds,
                ) ||
                other.defaultSelfDestructSeconds ==
                    defaultSelfDestructSeconds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    blockScreenshots,
    screenshotAlerts,
    readReceipts,
    typingIndicators,
    messageReactions,
    selfDestructEnabled,
    defaultSelfDestructSeconds,
  );

  /// Create a copy of ChatPrivacySettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChatPrivacySettingsImplCopyWith<_$ChatPrivacySettingsImpl> get copyWith =>
      __$$ChatPrivacySettingsImplCopyWithImpl<_$ChatPrivacySettingsImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ChatPrivacySettingsImplToJson(this);
  }
}

abstract class _ChatPrivacySettings implements ChatPrivacySettings {
  const factory _ChatPrivacySettings({
    required final bool blockScreenshots,
    required final bool screenshotAlerts,
    required final bool readReceipts,
    required final bool typingIndicators,
    required final bool messageReactions,
    required final bool selfDestructEnabled,
    required final int defaultSelfDestructSeconds,
  }) = _$ChatPrivacySettingsImpl;

  factory _ChatPrivacySettings.fromJson(Map<String, dynamic> json) =
      _$ChatPrivacySettingsImpl.fromJson;

  @override
  bool get blockScreenshots;
  @override
  bool get screenshotAlerts;
  @override
  bool get readReceipts;
  @override
  bool get typingIndicators;
  @override
  bool get messageReactions;
  @override
  bool get selfDestructEnabled;
  @override
  int get defaultSelfDestructSeconds;

  /// Create a copy of ChatPrivacySettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChatPrivacySettingsImplCopyWith<_$ChatPrivacySettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
