// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'voice_message_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$VoiceMessageModelImpl _$$VoiceMessageModelImplFromJson(
  Map<String, dynamic> json,
) => _$VoiceMessageModelImpl(
  id: json['id'] as String,
  messageId: json['messageId'] as String,
  audioUrl: json['audioUrl'] as String,
  durationInSeconds: (json['durationInSeconds'] as num).toInt(),
  transcription: json['transcription'] as String?,
  status: $enumDecode(_$VoiceMessageStatusEnumMap, json['status']),
  createdAt: DateTime.parse(json['createdAt'] as String),
  playedAt: json['playedAt'] == null
      ? null
      : DateTime.parse(json['playedAt'] as String),
  reactions: (json['reactions'] as List<dynamic>)
      .map((e) => VoiceMessageReaction.fromJson(e as Map<String, dynamic>))
      .toList(),
  metadata: VoiceMessageMetadata.fromJson(
    json['metadata'] as Map<String, dynamic>,
  ),
);

Map<String, dynamic> _$$VoiceMessageModelImplToJson(
  _$VoiceMessageModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'messageId': instance.messageId,
  'audioUrl': instance.audioUrl,
  'durationInSeconds': instance.durationInSeconds,
  'transcription': instance.transcription,
  'status': _$VoiceMessageStatusEnumMap[instance.status]!,
  'createdAt': instance.createdAt.toIso8601String(),
  'playedAt': instance.playedAt?.toIso8601String(),
  'reactions': instance.reactions,
  'metadata': instance.metadata,
};

const _$VoiceMessageStatusEnumMap = {
  VoiceMessageStatus.recording: 'recording',
  VoiceMessageStatus.processing: 'processing',
  VoiceMessageStatus.ready: 'ready',
  VoiceMessageStatus.playing: 'playing',
  VoiceMessageStatus.paused: 'paused',
  VoiceMessageStatus.completed: 'completed',
  VoiceMessageStatus.failed: 'failed',
  VoiceMessageStatus.deleted: 'deleted',
};

_$VoiceMessageReactionImpl _$$VoiceMessageReactionImplFromJson(
  Map<String, dynamic> json,
) => _$VoiceMessageReactionImpl(
  userId: json['userId'] as String,
  type: $enumDecode(_$VoiceReactionTypeEnumMap, json['type']),
  timestamp: DateTime.parse(json['timestamp'] as String),
);

Map<String, dynamic> _$$VoiceMessageReactionImplToJson(
  _$VoiceMessageReactionImpl instance,
) => <String, dynamic>{
  'userId': instance.userId,
  'type': _$VoiceReactionTypeEnumMap[instance.type]!,
  'timestamp': instance.timestamp.toIso8601String(),
};

const _$VoiceReactionTypeEnumMap = {
  VoiceReactionType.like: 'like',
  VoiceReactionType.love: 'love',
  VoiceReactionType.laugh: 'laugh',
  VoiceReactionType.wow: 'wow',
  VoiceReactionType.sad: 'sad',
  VoiceReactionType.angry: 'angry',
  VoiceReactionType.custom: 'custom',
};

_$VoiceMessageMetadataImpl _$$VoiceMessageMetadataImplFromJson(
  Map<String, dynamic> json,
) => _$VoiceMessageMetadataImpl(
  fileSize: json['fileSize'] as String,
  audioFormat: json['audioFormat'] as String,
  sampleRate: (json['sampleRate'] as num).toInt(),
  bitRate: (json['bitRate'] as num).toInt(),
  isCompressed: json['isCompressed'] as bool,
  recordingDevice: json['recordingDevice'] as String?,
  additionalData: json['additionalData'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$$VoiceMessageMetadataImplToJson(
  _$VoiceMessageMetadataImpl instance,
) => <String, dynamic>{
  'fileSize': instance.fileSize,
  'audioFormat': instance.audioFormat,
  'sampleRate': instance.sampleRate,
  'bitRate': instance.bitRate,
  'isCompressed': instance.isCompressed,
  'recordingDevice': instance.recordingDevice,
  'additionalData': instance.additionalData,
};

_$VoiceMessageSettingsImpl _$$VoiceMessageSettingsImplFromJson(
  Map<String, dynamic> json,
) => _$VoiceMessageSettingsImpl(
  autoTranscribe: json['autoTranscribe'] as bool? ?? true,
  allowReactions: json['allowReactions'] as bool? ?? true,
  maxDurationSeconds: (json['maxDurationSeconds'] as num?)?.toInt() ?? 300,
  minDurationSeconds: (json['minDurationSeconds'] as num?)?.toInt() ?? 10,
  showWaveform: json['showWaveform'] as bool? ?? true,
  allowPlaybackSpeed: json['allowPlaybackSpeed'] as bool? ?? true,
  playbackSpeeds:
      (json['playbackSpeeds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const ['0.5x', '1x', '1.5x', '2x'],
  saveToGallery: json['saveToGallery'] as bool? ?? true,
  requireConfirmation: json['requireConfirmation'] as bool? ?? false,
);

Map<String, dynamic> _$$VoiceMessageSettingsImplToJson(
  _$VoiceMessageSettingsImpl instance,
) => <String, dynamic>{
  'autoTranscribe': instance.autoTranscribe,
  'allowReactions': instance.allowReactions,
  'maxDurationSeconds': instance.maxDurationSeconds,
  'minDurationSeconds': instance.minDurationSeconds,
  'showWaveform': instance.showWaveform,
  'allowPlaybackSpeed': instance.allowPlaybackSpeed,
  'playbackSpeeds': instance.playbackSpeeds,
  'saveToGallery': instance.saveToGallery,
  'requireConfirmation': instance.requireConfirmation,
};
