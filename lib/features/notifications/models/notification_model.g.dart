// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$NotificationModelImpl _$$NotificationModelImplFromJson(
  Map<String, dynamic> json,
) => _$NotificationModelImpl(
  id: json['id'] as String,
  userId: json['userId'] as String,
  title: json['title'] as String,
  body: json['body'] as String,
  type: $enumDecode(_$NotificationTypeEnumMap, json['type']),
  timestamp: DateTime.parse(json['timestamp'] as String),
  status: $enumDecode(_$NotificationStatusEnumMap, json['status']),
  senderId: json['senderId'] as String,
  senderName: json['senderName'] as String,
  senderAvatarUrl: json['senderAvatarUrl'] as String,
  postId: json['postId'] as String?,
  commentId: json['commentId'] as String?,
  messageId: json['messageId'] as String?,
  storyId: json['storyId'] as String?,
  eventId: json['eventId'] as String?,
  data: json['data'] as Map<String, dynamic>?,
  actionUrl: json['actionUrl'] as String?,
  actionType: json['actionType'] as String?,
);

Map<String, dynamic> _$$NotificationModelImplToJson(
  _$NotificationModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'title': instance.title,
  'body': instance.body,
  'type': _$NotificationTypeEnumMap[instance.type]!,
  'timestamp': instance.timestamp.toIso8601String(),
  'status': _$NotificationStatusEnumMap[instance.status]!,
  'senderId': instance.senderId,
  'senderName': instance.senderName,
  'senderAvatarUrl': instance.senderAvatarUrl,
  'postId': instance.postId,
  'commentId': instance.commentId,
  'messageId': instance.messageId,
  'storyId': instance.storyId,
  'eventId': instance.eventId,
  'data': instance.data,
  'actionUrl': instance.actionUrl,
  'actionType': instance.actionType,
};

const _$NotificationTypeEnumMap = {
  NotificationType.follow: 'follow',
  NotificationType.like: 'like',
  NotificationType.comment: 'comment',
  NotificationType.mention: 'mention',
  NotificationType.message: 'message',
  NotificationType.share: 'share',
  NotificationType.admin: 'admin',
  NotificationType.story: 'story',
  NotificationType.event: 'event',
  NotificationType.system: 'system',
};

const _$NotificationStatusEnumMap = {
  NotificationStatus.unread: 'unread',
  NotificationStatus.read: 'read',
  NotificationStatus.dismissed: 'dismissed',
};

_$NotificationPreferencesImpl _$$NotificationPreferencesImplFromJson(
  Map<String, dynamic> json,
) => _$NotificationPreferencesImpl(
  followNotifications: json['followNotifications'] as bool? ?? true,
  likeNotifications: json['likeNotifications'] as bool? ?? true,
  commentNotifications: json['commentNotifications'] as bool? ?? true,
  mentionNotifications: json['mentionNotifications'] as bool? ?? true,
  messageNotifications: json['messageNotifications'] as bool? ?? true,
  shareNotifications: json['shareNotifications'] as bool? ?? true,
  adminNotifications: json['adminNotifications'] as bool? ?? true,
  storyNotifications: json['storyNotifications'] as bool? ?? true,
  eventNotifications: json['eventNotifications'] as bool? ?? true,
  systemNotifications: json['systemNotifications'] as bool? ?? true,
  pushNotifications: json['pushNotifications'] as bool? ?? true,
  inAppNotifications: json['inAppNotifications'] as bool? ?? true,
  emailNotifications: json['emailNotifications'] as bool? ?? true,
);

Map<String, dynamic> _$$NotificationPreferencesImplToJson(
  _$NotificationPreferencesImpl instance,
) => <String, dynamic>{
  'followNotifications': instance.followNotifications,
  'likeNotifications': instance.likeNotifications,
  'commentNotifications': instance.commentNotifications,
  'mentionNotifications': instance.mentionNotifications,
  'messageNotifications': instance.messageNotifications,
  'shareNotifications': instance.shareNotifications,
  'adminNotifications': instance.adminNotifications,
  'storyNotifications': instance.storyNotifications,
  'eventNotifications': instance.eventNotifications,
  'systemNotifications': instance.systemNotifications,
  'pushNotifications': instance.pushNotifications,
  'inAppNotifications': instance.inAppNotifications,
  'emailNotifications': instance.emailNotifications,
};
