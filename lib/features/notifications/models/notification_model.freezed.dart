// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'notification_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

NotificationModel _$NotificationModelFromJson(Map<String, dynamic> json) {
  return _NotificationModel.fromJson(json);
}

/// @nodoc
mixin _$NotificationModel {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get body => throw _privateConstructorUsedError;
  NotificationType get type => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;
  NotificationStatus get status =>
      throw _privateConstructorUsedError; // User who triggered the notification
  String get senderId => throw _privateConstructorUsedError;
  String get senderName => throw _privateConstructorUsedError;
  String get senderAvatarUrl =>
      throw _privateConstructorUsedError; // Related content (optional)
  String? get postId => throw _privateConstructorUsedError;
  String? get commentId => throw _privateConstructorUsedError;
  String? get messageId => throw _privateConstructorUsedError;
  String? get storyId => throw _privateConstructorUsedError;
  String? get eventId => throw _privateConstructorUsedError; // Additional data
  Map<String, dynamic>? get data =>
      throw _privateConstructorUsedError; // Action data
  String? get actionUrl => throw _privateConstructorUsedError;
  String? get actionType => throw _privateConstructorUsedError;

  /// Serializes this NotificationModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of NotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $NotificationModelCopyWith<NotificationModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationModelCopyWith<$Res> {
  factory $NotificationModelCopyWith(
    NotificationModel value,
    $Res Function(NotificationModel) then,
  ) = _$NotificationModelCopyWithImpl<$Res, NotificationModel>;
  @useResult
  $Res call({
    String id,
    String userId,
    String title,
    String body,
    NotificationType type,
    DateTime timestamp,
    NotificationStatus status,
    String senderId,
    String senderName,
    String senderAvatarUrl,
    String? postId,
    String? commentId,
    String? messageId,
    String? storyId,
    String? eventId,
    Map<String, dynamic>? data,
    String? actionUrl,
    String? actionType,
  });
}

/// @nodoc
class _$NotificationModelCopyWithImpl<$Res, $Val extends NotificationModel>
    implements $NotificationModelCopyWith<$Res> {
  _$NotificationModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? title = null,
    Object? body = null,
    Object? type = null,
    Object? timestamp = null,
    Object? status = null,
    Object? senderId = null,
    Object? senderName = null,
    Object? senderAvatarUrl = null,
    Object? postId = freezed,
    Object? commentId = freezed,
    Object? messageId = freezed,
    Object? storyId = freezed,
    Object? eventId = freezed,
    Object? data = freezed,
    Object? actionUrl = freezed,
    Object? actionType = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            body: null == body
                ? _value.body
                : body // ignore: cast_nullable_to_non_nullable
                      as String,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as NotificationType,
            timestamp: null == timestamp
                ? _value.timestamp
                : timestamp // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as NotificationStatus,
            senderId: null == senderId
                ? _value.senderId
                : senderId // ignore: cast_nullable_to_non_nullable
                      as String,
            senderName: null == senderName
                ? _value.senderName
                : senderName // ignore: cast_nullable_to_non_nullable
                      as String,
            senderAvatarUrl: null == senderAvatarUrl
                ? _value.senderAvatarUrl
                : senderAvatarUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            postId: freezed == postId
                ? _value.postId
                : postId // ignore: cast_nullable_to_non_nullable
                      as String?,
            commentId: freezed == commentId
                ? _value.commentId
                : commentId // ignore: cast_nullable_to_non_nullable
                      as String?,
            messageId: freezed == messageId
                ? _value.messageId
                : messageId // ignore: cast_nullable_to_non_nullable
                      as String?,
            storyId: freezed == storyId
                ? _value.storyId
                : storyId // ignore: cast_nullable_to_non_nullable
                      as String?,
            eventId: freezed == eventId
                ? _value.eventId
                : eventId // ignore: cast_nullable_to_non_nullable
                      as String?,
            data: freezed == data
                ? _value.data
                : data // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
            actionUrl: freezed == actionUrl
                ? _value.actionUrl
                : actionUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            actionType: freezed == actionType
                ? _value.actionType
                : actionType // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$NotificationModelImplCopyWith<$Res>
    implements $NotificationModelCopyWith<$Res> {
  factory _$$NotificationModelImplCopyWith(
    _$NotificationModelImpl value,
    $Res Function(_$NotificationModelImpl) then,
  ) = __$$NotificationModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    String title,
    String body,
    NotificationType type,
    DateTime timestamp,
    NotificationStatus status,
    String senderId,
    String senderName,
    String senderAvatarUrl,
    String? postId,
    String? commentId,
    String? messageId,
    String? storyId,
    String? eventId,
    Map<String, dynamic>? data,
    String? actionUrl,
    String? actionType,
  });
}

/// @nodoc
class __$$NotificationModelImplCopyWithImpl<$Res>
    extends _$NotificationModelCopyWithImpl<$Res, _$NotificationModelImpl>
    implements _$$NotificationModelImplCopyWith<$Res> {
  __$$NotificationModelImplCopyWithImpl(
    _$NotificationModelImpl _value,
    $Res Function(_$NotificationModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? title = null,
    Object? body = null,
    Object? type = null,
    Object? timestamp = null,
    Object? status = null,
    Object? senderId = null,
    Object? senderName = null,
    Object? senderAvatarUrl = null,
    Object? postId = freezed,
    Object? commentId = freezed,
    Object? messageId = freezed,
    Object? storyId = freezed,
    Object? eventId = freezed,
    Object? data = freezed,
    Object? actionUrl = freezed,
    Object? actionType = freezed,
  }) {
    return _then(
      _$NotificationModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        body: null == body
            ? _value.body
            : body // ignore: cast_nullable_to_non_nullable
                  as String,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as NotificationType,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as NotificationStatus,
        senderId: null == senderId
            ? _value.senderId
            : senderId // ignore: cast_nullable_to_non_nullable
                  as String,
        senderName: null == senderName
            ? _value.senderName
            : senderName // ignore: cast_nullable_to_non_nullable
                  as String,
        senderAvatarUrl: null == senderAvatarUrl
            ? _value.senderAvatarUrl
            : senderAvatarUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        postId: freezed == postId
            ? _value.postId
            : postId // ignore: cast_nullable_to_non_nullable
                  as String?,
        commentId: freezed == commentId
            ? _value.commentId
            : commentId // ignore: cast_nullable_to_non_nullable
                  as String?,
        messageId: freezed == messageId
            ? _value.messageId
            : messageId // ignore: cast_nullable_to_non_nullable
                  as String?,
        storyId: freezed == storyId
            ? _value.storyId
            : storyId // ignore: cast_nullable_to_non_nullable
                  as String?,
        eventId: freezed == eventId
            ? _value.eventId
            : eventId // ignore: cast_nullable_to_non_nullable
                  as String?,
        data: freezed == data
            ? _value._data
            : data // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
        actionUrl: freezed == actionUrl
            ? _value.actionUrl
            : actionUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        actionType: freezed == actionType
            ? _value.actionType
            : actionType // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$NotificationModelImpl implements _NotificationModel {
  const _$NotificationModelImpl({
    required this.id,
    required this.userId,
    required this.title,
    required this.body,
    required this.type,
    required this.timestamp,
    required this.status,
    required this.senderId,
    required this.senderName,
    required this.senderAvatarUrl,
    this.postId,
    this.commentId,
    this.messageId,
    this.storyId,
    this.eventId,
    final Map<String, dynamic>? data,
    this.actionUrl,
    this.actionType,
  }) : _data = data;

  factory _$NotificationModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$NotificationModelImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String title;
  @override
  final String body;
  @override
  final NotificationType type;
  @override
  final DateTime timestamp;
  @override
  final NotificationStatus status;
  // User who triggered the notification
  @override
  final String senderId;
  @override
  final String senderName;
  @override
  final String senderAvatarUrl;
  // Related content (optional)
  @override
  final String? postId;
  @override
  final String? commentId;
  @override
  final String? messageId;
  @override
  final String? storyId;
  @override
  final String? eventId;
  // Additional data
  final Map<String, dynamic>? _data;
  // Additional data
  @override
  Map<String, dynamic>? get data {
    final value = _data;
    if (value == null) return null;
    if (_data is EqualUnmodifiableMapView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  // Action data
  @override
  final String? actionUrl;
  @override
  final String? actionType;

  @override
  String toString() {
    return 'NotificationModel(id: $id, userId: $userId, title: $title, body: $body, type: $type, timestamp: $timestamp, status: $status, senderId: $senderId, senderName: $senderName, senderAvatarUrl: $senderAvatarUrl, postId: $postId, commentId: $commentId, messageId: $messageId, storyId: $storyId, eventId: $eventId, data: $data, actionUrl: $actionUrl, actionType: $actionType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotificationModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.body, body) || other.body == body) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.senderId, senderId) ||
                other.senderId == senderId) &&
            (identical(other.senderName, senderName) ||
                other.senderName == senderName) &&
            (identical(other.senderAvatarUrl, senderAvatarUrl) ||
                other.senderAvatarUrl == senderAvatarUrl) &&
            (identical(other.postId, postId) || other.postId == postId) &&
            (identical(other.commentId, commentId) ||
                other.commentId == commentId) &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.storyId, storyId) || other.storyId == storyId) &&
            (identical(other.eventId, eventId) || other.eventId == eventId) &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.actionUrl, actionUrl) ||
                other.actionUrl == actionUrl) &&
            (identical(other.actionType, actionType) ||
                other.actionType == actionType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    userId,
    title,
    body,
    type,
    timestamp,
    status,
    senderId,
    senderName,
    senderAvatarUrl,
    postId,
    commentId,
    messageId,
    storyId,
    eventId,
    const DeepCollectionEquality().hash(_data),
    actionUrl,
    actionType,
  );

  /// Create a copy of NotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NotificationModelImplCopyWith<_$NotificationModelImpl> get copyWith =>
      __$$NotificationModelImplCopyWithImpl<_$NotificationModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$NotificationModelImplToJson(this);
  }
}

abstract class _NotificationModel implements NotificationModel {
  const factory _NotificationModel({
    required final String id,
    required final String userId,
    required final String title,
    required final String body,
    required final NotificationType type,
    required final DateTime timestamp,
    required final NotificationStatus status,
    required final String senderId,
    required final String senderName,
    required final String senderAvatarUrl,
    final String? postId,
    final String? commentId,
    final String? messageId,
    final String? storyId,
    final String? eventId,
    final Map<String, dynamic>? data,
    final String? actionUrl,
    final String? actionType,
  }) = _$NotificationModelImpl;

  factory _NotificationModel.fromJson(Map<String, dynamic> json) =
      _$NotificationModelImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get title;
  @override
  String get body;
  @override
  NotificationType get type;
  @override
  DateTime get timestamp;
  @override
  NotificationStatus get status; // User who triggered the notification
  @override
  String get senderId;
  @override
  String get senderName;
  @override
  String get senderAvatarUrl; // Related content (optional)
  @override
  String? get postId;
  @override
  String? get commentId;
  @override
  String? get messageId;
  @override
  String? get storyId;
  @override
  String? get eventId; // Additional data
  @override
  Map<String, dynamic>? get data; // Action data
  @override
  String? get actionUrl;
  @override
  String? get actionType;

  /// Create a copy of NotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NotificationModelImplCopyWith<_$NotificationModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

NotificationPreferences _$NotificationPreferencesFromJson(
  Map<String, dynamic> json,
) {
  return _NotificationPreferences.fromJson(json);
}

/// @nodoc
mixin _$NotificationPreferences {
  bool get followNotifications => throw _privateConstructorUsedError;
  bool get likeNotifications => throw _privateConstructorUsedError;
  bool get commentNotifications => throw _privateConstructorUsedError;
  bool get mentionNotifications => throw _privateConstructorUsedError;
  bool get messageNotifications => throw _privateConstructorUsedError;
  bool get shareNotifications => throw _privateConstructorUsedError;
  bool get adminNotifications => throw _privateConstructorUsedError;
  bool get storyNotifications => throw _privateConstructorUsedError;
  bool get eventNotifications => throw _privateConstructorUsedError;
  bool get systemNotifications => throw _privateConstructorUsedError;
  bool get pushNotifications => throw _privateConstructorUsedError;
  bool get inAppNotifications => throw _privateConstructorUsedError;
  bool get emailNotifications => throw _privateConstructorUsedError;

  /// Serializes this NotificationPreferences to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of NotificationPreferences
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $NotificationPreferencesCopyWith<NotificationPreferences> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationPreferencesCopyWith<$Res> {
  factory $NotificationPreferencesCopyWith(
    NotificationPreferences value,
    $Res Function(NotificationPreferences) then,
  ) = _$NotificationPreferencesCopyWithImpl<$Res, NotificationPreferences>;
  @useResult
  $Res call({
    bool followNotifications,
    bool likeNotifications,
    bool commentNotifications,
    bool mentionNotifications,
    bool messageNotifications,
    bool shareNotifications,
    bool adminNotifications,
    bool storyNotifications,
    bool eventNotifications,
    bool systemNotifications,
    bool pushNotifications,
    bool inAppNotifications,
    bool emailNotifications,
  });
}

/// @nodoc
class _$NotificationPreferencesCopyWithImpl<
  $Res,
  $Val extends NotificationPreferences
>
    implements $NotificationPreferencesCopyWith<$Res> {
  _$NotificationPreferencesCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NotificationPreferences
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? followNotifications = null,
    Object? likeNotifications = null,
    Object? commentNotifications = null,
    Object? mentionNotifications = null,
    Object? messageNotifications = null,
    Object? shareNotifications = null,
    Object? adminNotifications = null,
    Object? storyNotifications = null,
    Object? eventNotifications = null,
    Object? systemNotifications = null,
    Object? pushNotifications = null,
    Object? inAppNotifications = null,
    Object? emailNotifications = null,
  }) {
    return _then(
      _value.copyWith(
            followNotifications: null == followNotifications
                ? _value.followNotifications
                : followNotifications // ignore: cast_nullable_to_non_nullable
                      as bool,
            likeNotifications: null == likeNotifications
                ? _value.likeNotifications
                : likeNotifications // ignore: cast_nullable_to_non_nullable
                      as bool,
            commentNotifications: null == commentNotifications
                ? _value.commentNotifications
                : commentNotifications // ignore: cast_nullable_to_non_nullable
                      as bool,
            mentionNotifications: null == mentionNotifications
                ? _value.mentionNotifications
                : mentionNotifications // ignore: cast_nullable_to_non_nullable
                      as bool,
            messageNotifications: null == messageNotifications
                ? _value.messageNotifications
                : messageNotifications // ignore: cast_nullable_to_non_nullable
                      as bool,
            shareNotifications: null == shareNotifications
                ? _value.shareNotifications
                : shareNotifications // ignore: cast_nullable_to_non_nullable
                      as bool,
            adminNotifications: null == adminNotifications
                ? _value.adminNotifications
                : adminNotifications // ignore: cast_nullable_to_non_nullable
                      as bool,
            storyNotifications: null == storyNotifications
                ? _value.storyNotifications
                : storyNotifications // ignore: cast_nullable_to_non_nullable
                      as bool,
            eventNotifications: null == eventNotifications
                ? _value.eventNotifications
                : eventNotifications // ignore: cast_nullable_to_non_nullable
                      as bool,
            systemNotifications: null == systemNotifications
                ? _value.systemNotifications
                : systemNotifications // ignore: cast_nullable_to_non_nullable
                      as bool,
            pushNotifications: null == pushNotifications
                ? _value.pushNotifications
                : pushNotifications // ignore: cast_nullable_to_non_nullable
                      as bool,
            inAppNotifications: null == inAppNotifications
                ? _value.inAppNotifications
                : inAppNotifications // ignore: cast_nullable_to_non_nullable
                      as bool,
            emailNotifications: null == emailNotifications
                ? _value.emailNotifications
                : emailNotifications // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$NotificationPreferencesImplCopyWith<$Res>
    implements $NotificationPreferencesCopyWith<$Res> {
  factory _$$NotificationPreferencesImplCopyWith(
    _$NotificationPreferencesImpl value,
    $Res Function(_$NotificationPreferencesImpl) then,
  ) = __$$NotificationPreferencesImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    bool followNotifications,
    bool likeNotifications,
    bool commentNotifications,
    bool mentionNotifications,
    bool messageNotifications,
    bool shareNotifications,
    bool adminNotifications,
    bool storyNotifications,
    bool eventNotifications,
    bool systemNotifications,
    bool pushNotifications,
    bool inAppNotifications,
    bool emailNotifications,
  });
}

/// @nodoc
class __$$NotificationPreferencesImplCopyWithImpl<$Res>
    extends
        _$NotificationPreferencesCopyWithImpl<
          $Res,
          _$NotificationPreferencesImpl
        >
    implements _$$NotificationPreferencesImplCopyWith<$Res> {
  __$$NotificationPreferencesImplCopyWithImpl(
    _$NotificationPreferencesImpl _value,
    $Res Function(_$NotificationPreferencesImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NotificationPreferences
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? followNotifications = null,
    Object? likeNotifications = null,
    Object? commentNotifications = null,
    Object? mentionNotifications = null,
    Object? messageNotifications = null,
    Object? shareNotifications = null,
    Object? adminNotifications = null,
    Object? storyNotifications = null,
    Object? eventNotifications = null,
    Object? systemNotifications = null,
    Object? pushNotifications = null,
    Object? inAppNotifications = null,
    Object? emailNotifications = null,
  }) {
    return _then(
      _$NotificationPreferencesImpl(
        followNotifications: null == followNotifications
            ? _value.followNotifications
            : followNotifications // ignore: cast_nullable_to_non_nullable
                  as bool,
        likeNotifications: null == likeNotifications
            ? _value.likeNotifications
            : likeNotifications // ignore: cast_nullable_to_non_nullable
                  as bool,
        commentNotifications: null == commentNotifications
            ? _value.commentNotifications
            : commentNotifications // ignore: cast_nullable_to_non_nullable
                  as bool,
        mentionNotifications: null == mentionNotifications
            ? _value.mentionNotifications
            : mentionNotifications // ignore: cast_nullable_to_non_nullable
                  as bool,
        messageNotifications: null == messageNotifications
            ? _value.messageNotifications
            : messageNotifications // ignore: cast_nullable_to_non_nullable
                  as bool,
        shareNotifications: null == shareNotifications
            ? _value.shareNotifications
            : shareNotifications // ignore: cast_nullable_to_non_nullable
                  as bool,
        adminNotifications: null == adminNotifications
            ? _value.adminNotifications
            : adminNotifications // ignore: cast_nullable_to_non_nullable
                  as bool,
        storyNotifications: null == storyNotifications
            ? _value.storyNotifications
            : storyNotifications // ignore: cast_nullable_to_non_nullable
                  as bool,
        eventNotifications: null == eventNotifications
            ? _value.eventNotifications
            : eventNotifications // ignore: cast_nullable_to_non_nullable
                  as bool,
        systemNotifications: null == systemNotifications
            ? _value.systemNotifications
            : systemNotifications // ignore: cast_nullable_to_non_nullable
                  as bool,
        pushNotifications: null == pushNotifications
            ? _value.pushNotifications
            : pushNotifications // ignore: cast_nullable_to_non_nullable
                  as bool,
        inAppNotifications: null == inAppNotifications
            ? _value.inAppNotifications
            : inAppNotifications // ignore: cast_nullable_to_non_nullable
                  as bool,
        emailNotifications: null == emailNotifications
            ? _value.emailNotifications
            : emailNotifications // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$NotificationPreferencesImpl implements _NotificationPreferences {
  const _$NotificationPreferencesImpl({
    this.followNotifications = true,
    this.likeNotifications = true,
    this.commentNotifications = true,
    this.mentionNotifications = true,
    this.messageNotifications = true,
    this.shareNotifications = true,
    this.adminNotifications = true,
    this.storyNotifications = true,
    this.eventNotifications = true,
    this.systemNotifications = true,
    this.pushNotifications = true,
    this.inAppNotifications = true,
    this.emailNotifications = true,
  });

  factory _$NotificationPreferencesImpl.fromJson(Map<String, dynamic> json) =>
      _$$NotificationPreferencesImplFromJson(json);

  @override
  @JsonKey()
  final bool followNotifications;
  @override
  @JsonKey()
  final bool likeNotifications;
  @override
  @JsonKey()
  final bool commentNotifications;
  @override
  @JsonKey()
  final bool mentionNotifications;
  @override
  @JsonKey()
  final bool messageNotifications;
  @override
  @JsonKey()
  final bool shareNotifications;
  @override
  @JsonKey()
  final bool adminNotifications;
  @override
  @JsonKey()
  final bool storyNotifications;
  @override
  @JsonKey()
  final bool eventNotifications;
  @override
  @JsonKey()
  final bool systemNotifications;
  @override
  @JsonKey()
  final bool pushNotifications;
  @override
  @JsonKey()
  final bool inAppNotifications;
  @override
  @JsonKey()
  final bool emailNotifications;

  @override
  String toString() {
    return 'NotificationPreferences(followNotifications: $followNotifications, likeNotifications: $likeNotifications, commentNotifications: $commentNotifications, mentionNotifications: $mentionNotifications, messageNotifications: $messageNotifications, shareNotifications: $shareNotifications, adminNotifications: $adminNotifications, storyNotifications: $storyNotifications, eventNotifications: $eventNotifications, systemNotifications: $systemNotifications, pushNotifications: $pushNotifications, inAppNotifications: $inAppNotifications, emailNotifications: $emailNotifications)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotificationPreferencesImpl &&
            (identical(other.followNotifications, followNotifications) ||
                other.followNotifications == followNotifications) &&
            (identical(other.likeNotifications, likeNotifications) ||
                other.likeNotifications == likeNotifications) &&
            (identical(other.commentNotifications, commentNotifications) ||
                other.commentNotifications == commentNotifications) &&
            (identical(other.mentionNotifications, mentionNotifications) ||
                other.mentionNotifications == mentionNotifications) &&
            (identical(other.messageNotifications, messageNotifications) ||
                other.messageNotifications == messageNotifications) &&
            (identical(other.shareNotifications, shareNotifications) ||
                other.shareNotifications == shareNotifications) &&
            (identical(other.adminNotifications, adminNotifications) ||
                other.adminNotifications == adminNotifications) &&
            (identical(other.storyNotifications, storyNotifications) ||
                other.storyNotifications == storyNotifications) &&
            (identical(other.eventNotifications, eventNotifications) ||
                other.eventNotifications == eventNotifications) &&
            (identical(other.systemNotifications, systemNotifications) ||
                other.systemNotifications == systemNotifications) &&
            (identical(other.pushNotifications, pushNotifications) ||
                other.pushNotifications == pushNotifications) &&
            (identical(other.inAppNotifications, inAppNotifications) ||
                other.inAppNotifications == inAppNotifications) &&
            (identical(other.emailNotifications, emailNotifications) ||
                other.emailNotifications == emailNotifications));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    followNotifications,
    likeNotifications,
    commentNotifications,
    mentionNotifications,
    messageNotifications,
    shareNotifications,
    adminNotifications,
    storyNotifications,
    eventNotifications,
    systemNotifications,
    pushNotifications,
    inAppNotifications,
    emailNotifications,
  );

  /// Create a copy of NotificationPreferences
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NotificationPreferencesImplCopyWith<_$NotificationPreferencesImpl>
  get copyWith =>
      __$$NotificationPreferencesImplCopyWithImpl<
        _$NotificationPreferencesImpl
      >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NotificationPreferencesImplToJson(this);
  }
}

abstract class _NotificationPreferences implements NotificationPreferences {
  const factory _NotificationPreferences({
    final bool followNotifications,
    final bool likeNotifications,
    final bool commentNotifications,
    final bool mentionNotifications,
    final bool messageNotifications,
    final bool shareNotifications,
    final bool adminNotifications,
    final bool storyNotifications,
    final bool eventNotifications,
    final bool systemNotifications,
    final bool pushNotifications,
    final bool inAppNotifications,
    final bool emailNotifications,
  }) = _$NotificationPreferencesImpl;

  factory _NotificationPreferences.fromJson(Map<String, dynamic> json) =
      _$NotificationPreferencesImpl.fromJson;

  @override
  bool get followNotifications;
  @override
  bool get likeNotifications;
  @override
  bool get commentNotifications;
  @override
  bool get mentionNotifications;
  @override
  bool get messageNotifications;
  @override
  bool get shareNotifications;
  @override
  bool get adminNotifications;
  @override
  bool get storyNotifications;
  @override
  bool get eventNotifications;
  @override
  bool get systemNotifications;
  @override
  bool get pushNotifications;
  @override
  bool get inAppNotifications;
  @override
  bool get emailNotifications;

  /// Create a copy of NotificationPreferences
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NotificationPreferencesImplCopyWith<_$NotificationPreferencesImpl>
  get copyWith => throw _privateConstructorUsedError;
}
