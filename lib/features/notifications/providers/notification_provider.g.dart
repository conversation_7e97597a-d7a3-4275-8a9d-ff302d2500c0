// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$notificationServiceHash() =>
    r'cda5ea9d196dce85bee56839a4a0f035021752e3';

/// Provider for the notification service
///
/// Copied from [notificationService].
@ProviderFor(notificationService)
final notificationServiceProvider =
    AutoDisposeProvider<NotificationService>.internal(
      notificationService,
      name: r'notificationServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$notificationServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef NotificationServiceRef = AutoDisposeProviderRef<NotificationService>;
String _$notificationsHash() => r'bb49b0ac0338e7a61ab3a5eedd261f7356f62663';

/// Provider for notifications stream
///
/// Copied from [notifications].
@ProviderFor(notifications)
final notificationsProvider =
    AutoDisposeStreamProvider<List<NotificationModel>>.internal(
      notifications,
      name: r'notificationsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$notificationsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef NotificationsRef =
    AutoDisposeStreamProviderRef<List<NotificationModel>>;
String _$notificationNotifierHash() =>
    r'5a656abf01ac58e8d08ab2bbd7a2cb77de48911b';

/// Notification notifier for handling notification actions
///
/// Copied from [NotificationNotifier].
@ProviderFor(NotificationNotifier)
final notificationNotifierProvider =
    AutoDisposeAsyncNotifierProvider<NotificationNotifier, void>.internal(
      NotificationNotifier.new,
      name: r'notificationNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$notificationNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$NotificationNotifier = AutoDisposeAsyncNotifier<void>;
String _$notificationPreferencesNotifierHash() =>
    r'd25f2171cea245ac25455a90ad518edc29dd242f';

/// Notification preferences notifier
///
/// Copied from [NotificationPreferencesNotifier].
@ProviderFor(NotificationPreferencesNotifier)
final notificationPreferencesNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      NotificationPreferencesNotifier,
      NotificationPreferences
    >.internal(
      NotificationPreferencesNotifier.new,
      name: r'notificationPreferencesNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$notificationPreferencesNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$NotificationPreferencesNotifier =
    AutoDisposeAsyncNotifier<NotificationPreferences>;
String _$unreadNotificationCountHash() =>
    r'b71aa1d048156256d78459e7987bf2653231e5ae';

/// Unread notification count provider
///
/// Copied from [UnreadNotificationCount].
@ProviderFor(UnreadNotificationCount)
final unreadNotificationCountProvider =
    AutoDisposeStreamNotifierProvider<UnreadNotificationCount, int>.internal(
      UnreadNotificationCount.new,
      name: r'unreadNotificationCountProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$unreadNotificationCountHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$UnreadNotificationCount = AutoDisposeStreamNotifier<int>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
