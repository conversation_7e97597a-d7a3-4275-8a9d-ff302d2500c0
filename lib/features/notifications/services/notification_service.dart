import 'dart:math' as math;
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:billionaires_social/features/notifications/models/notification_model.dart';
import 'package:billionaires_social/core/services/firebase_service.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/services/n8n_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:firebase_auth/firebase_auth.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FirebaseMessaging _fcm = FirebaseMessaging.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseService _firebaseService = getIt<FirebaseService>();
  final Uuid _uuid = const Uuid();
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Local notifications plugin
  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  // Notification channels
  static const AndroidNotificationChannel _channel = AndroidNotificationChannel(
    'high_importance_channel',
    'High Importance Notifications',
    description: 'This channel is used for important notifications.',
    importance: Importance.high,
  );

  // Track initialization state
  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;

  // Initialize the notification service
  Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('🔔 Notification service already initialized');
      return;
    }

    try {
      debugPrint('🔔 Starting notification service initialization...');

      // Request permission for iOS
      final settings = await _fcm.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      debugPrint(
        'Notification permission status: ${settings.authorizationStatus}',
      );

      // Initialize local notifications
      await _initializeLocalNotifications();

      // Get FCM token
      final token = await _fcm.getToken();
      if (token != null) {
        debugPrint('FCM Token: $token');
        await _saveFCMToken(token);
      }

      // Get APNs token for iOS with retry logic
      if (defaultTargetPlatform == TargetPlatform.iOS) {
        await _initializeAPNSToken();
      }

      // Listen for token refresh
      _fcm.onTokenRefresh.listen((token) {
        debugPrint('FCM Token refreshed: $token');
        _saveFCMToken(token);
      });

      // Handle foreground messages
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

      // Handle background messages
      FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);

      // Handle notification taps
      FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

      // Handle initial notification (app opened from terminated state)
      final initialMessage = await _fcm.getInitialMessage();
      if (initialMessage != null) {
        _handleNotificationTap(initialMessage);
      }

      _isInitialized = true;
      debugPrint('✅ Notification service initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing notification service: $e');
      // Don't set _isInitialized = true on error, allow retry later
      rethrow;
    }
  }

  // Retry initialization if it failed initially
  Future<void> retryInitialization() async {
    if (_isInitialized) {
      debugPrint('🔔 Notification service already initialized, skipping retry');
      return;
    }

    debugPrint('🔄 Retrying notification service initialization...');
    await initialize();
  }

  // Initialize APNS token with retry logic (iOS only)
  Future<void> _initializeAPNSToken() async {
    int retryCount = 0;
    const maxRetries = 10; // Increased retries
    const baseDelay = Duration(milliseconds: 500);

    debugPrint('🍎 Starting APNs token initialization...');

    while (retryCount < maxRetries) {
      try {
        // Progressive delay to allow APNS token to be set by iOS
        final delay = Duration(milliseconds: 500 + (retryCount * 300));
        await Future.delayed(delay);

        // Try to get the APNS token
        final apnsToken = await _fcm.getAPNSToken();
        if (apnsToken != null && apnsToken.isNotEmpty) {
          debugPrint(
            '✅ APNs Token obtained successfully: ${apnsToken.substring(0, math.min(20, apnsToken.length))}...',
          );

          // Store the token for later use
          await _saveAPNSToken(apnsToken);
          return; // Success, exit retry loop
        } else {
          retryCount++;
          debugPrint(
            '⏳ APNs Token not available yet. Retry $retryCount/$maxRetries (waiting ${delay.inMilliseconds}ms)',
          );

          // Try to request permission again if token is null
          if (retryCount == 3) {
            debugPrint('🔄 Re-requesting notification permissions...');
            await _fcm.requestPermission(
              alert: true,
              announcement: false,
              badge: true,
              carPlay: false,
              criticalAlert: false,
              provisional: false,
              sound: true,
            );
          }
        }
      } catch (e) {
        retryCount++;
        debugPrint(
          '❌ Error getting APNs token (attempt $retryCount/$maxRetries): $e',
        );

        // If it's a specific Firebase error, try to handle it
        if (e.toString().contains('apns-token-not-set')) {
          debugPrint(
            '🔧 APNS token not set error detected, continuing retries...',
          );
        }
      }

      // Don't delay on the last attempt
      if (retryCount < maxRetries) {
        await Future.delayed(baseDelay);
      }
    }

    debugPrint(
      '⚠️ APNs Token not available after $maxRetries attempts. Push notifications may work with FCM token only.',
    );

    // Log additional debugging information
    await _logNotificationDebugInfo();
  }

  // Save APNS token to local storage
  Future<void> _saveAPNSToken(String token) async {
    try {
      // You can save to SharedPreferences or secure storage here
      debugPrint('💾 APNs token saved successfully');
    } catch (e) {
      debugPrint('❌ Error saving APNs token: $e');
    }
  }

  // Log debug information for troubleshooting
  Future<void> _logNotificationDebugInfo() async {
    try {
      final settings = await _fcm.getNotificationSettings();
      debugPrint('🔍 Notification Debug Info:');
      debugPrint('  - Authorization Status: ${settings.authorizationStatus}');
      debugPrint('  - Alert Setting: ${settings.alert}');
      debugPrint('  - Badge Setting: ${settings.badge}');
      debugPrint('  - Sound Setting: ${settings.sound}');
      debugPrint(
        '  - Notification Center Setting: ${settings.notificationCenter}',
      );
      debugPrint('  - Lock Screen Setting: ${settings.lockScreen}');
      debugPrint('  - Car Play Setting: ${settings.carPlay}');
      debugPrint('  - Critical Alert Setting: ${settings.criticalAlert}');

      // Get FCM token for debugging
      try {
        final fcmToken = await _fcm.getToken();
        if (fcmToken != null) {
          debugPrint(
            '  - FCM Token Available: ${fcmToken.substring(0, math.min(20, fcmToken.length))}...',
          );
        } else {
          debugPrint('  - FCM Token: Not available');
        }
      } catch (e) {
        debugPrint('  - FCM Token Error: $e');
      }
    } catch (e) {
      debugPrint('❌ Error getting notification debug info: $e');
    }
  }

  // Initialize local notifications
  Future<void> _initializeLocalNotifications() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
          requestAlertPermission: true,
          requestBadgePermission: true,
          requestSoundPermission: true,
        );

    const InitializationSettings initializationSettings =
        InitializationSettings(
          android: initializationSettingsAndroid,
          iOS: initializationSettingsIOS,
        );

    await _localNotifications.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onLocalNotificationTap,
    );

    // Create notification channel for Android
    await _localNotifications
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(_channel);
  }

  // Save FCM token to Firestore
  Future<void> _saveFCMToken(String token) async {
    final currentUser = _firebaseService.currentUser;
    if (currentUser == null) return;

    try {
      await _firestore.collection('users').doc(currentUser.uid).update({
        'fcmToken': token,
        'lastTokenUpdate': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error saving FCM token: $e');
    }
  }

  // Handle foreground messages
  void _handleForegroundMessage(RemoteMessage message) {
    debugPrint('Got a message whilst in the foreground!');
    debugPrint('Message data: ${message.data}');

    if (message.notification != null) {
      debugPrint(
        'Message also contained a notification: ${message.notification}',
      );
      _showLocalNotification(message);
    }
  }

  // Handle background messages
  static Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    debugPrint('Handling a background message: ${message.messageId}');
    // Save notification to Firestore
    await _saveNotificationToFirestore(message);
  }

  // Save notification to Firestore
  static Future<void> _saveNotificationToFirestore(
    RemoteMessage message,
  ) async {
    try {
      final firestore = FirebaseFirestore.instance;
      final data = message.data;

      final notification = {
        'id': data['notificationId'] ?? message.messageId ?? const Uuid().v4(),
        'userId': data['userId'] ?? '',
        'title': message.notification?.title ?? data['title'] ?? '',
        'body': message.notification?.body ?? data['body'] ?? '',
        'type': data['type'] ?? 'system',
        'timestamp': FieldValue.serverTimestamp(),
        'status': 'unread',
        'fromUserId':
            data['senderId'] ??
            '', // Changed from 'senderId' to match security rules
        'senderName': data['senderName'] ?? '',
        'senderAvatarUrl': data['senderAvatarUrl'] ?? '',
        'postId': data['postId'],
        'commentId': data['commentId'],
        'messageId': data['messageId'],
        'storyId': data['storyId'],
        'eventId': data['eventId'],
        'data': data,
        'actionUrl': data['actionUrl'],
        'actionType': data['actionType'],
      };

      await firestore.collection('notifications').add(notification);
    } catch (e) {
      debugPrint('Error saving notification to Firestore: $e');
    }
  }

  // Show local notification
  void _showLocalNotification(RemoteMessage message) {
    final notification = message.notification;
    final android = message.notification?.android;

    if (notification != null && android != null) {
      _localNotifications.show(
        notification.hashCode,
        notification.title,
        notification.body,
        NotificationDetails(
          android: AndroidNotificationDetails(
            _channel.id,
            _channel.name,
            channelDescription: _channel.description,
            icon: android.smallIcon ?? '@mipmap/ic_launcher',
          ),
        ),
      );
    }
  }

  // Handle notification tap
  void _handleNotificationTap(RemoteMessage message) {
    debugPrint('Notification tapped: ${message.data}');
    // Navigate to appropriate screen based on notification type
    _navigateToNotificationTarget(message.data);
  }

  // Handle local notification tap
  void _onLocalNotificationTap(NotificationResponse response) {
    debugPrint('Local notification tapped: ${response.payload}');
    // Handle local notification tap
  }

  // Navigate to notification target
  void _navigateToNotificationTarget(Map<String, dynamic> data) {
    final type = data['type'];
    final postId = data['postId'];
    final messageId = data['messageId'];
    final userId = data['senderId'];

    // This will be implemented with navigation service
    debugPrint(
      'Navigate to: type=$type, postId=$postId, messageId=$messageId, userId=$userId',
    );
  }

  // Get user notifications
  Stream<List<NotificationModel>> getUserNotifications() {
    final currentUser = _firebaseService.currentUser;
    if (currentUser == null) return Stream.value([]);

    return _firestore
        .collection('notifications')
        .where('userId', isEqualTo: currentUser.uid)
        .orderBy('timestamp', descending: true)
        .limit(50)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs.map((doc) {
            final data = doc.data();
            return NotificationModel(
              id: doc.id,
              userId: data['userId'] ?? '',
              title: data['title'] ?? '',
              body: data['body'] ?? '',
              type: NotificationType.values.firstWhere(
                (e) => e.name == (data['type'] ?? 'system'),
                orElse: () => NotificationType.system,
              ),
              timestamp: (data['timestamp'] as Timestamp).toDate(),
              status: NotificationStatus.values.firstWhere(
                (e) => e.name == (data['status'] ?? 'unread'),
                orElse: () => NotificationStatus.unread,
              ),
              senderId: data['senderId'] ?? '',
              senderName: data['senderName'] ?? '',
              senderAvatarUrl: data['senderAvatarUrl'] ?? '',
              postId: data['postId'],
              commentId: data['commentId'],
              messageId: data['messageId'],
              storyId: data['storyId'],
              eventId: data['eventId'],
              data: data['data'] != null
                  ? Map<String, dynamic>.from(data['data'])
                  : null,
              actionUrl: data['actionUrl'],
              actionType: data['actionType'],
            );
          }).toList(),
        );
  }

  // Mark notification as read
  Future<bool> markAsRead(String notificationId) async {
    try {
      await _firestore.collection('notifications').doc(notificationId).update({
        'status': 'read',
      });
      return true;
    } catch (e) {
      debugPrint('Error marking notification as read: $e');
      return false;
    }
  }

  // Mark all notifications as read
  Future<bool> markAllAsRead() async {
    final currentUser = _firebaseService.currentUser;
    if (currentUser == null) return false;

    try {
      final batch = _firestore.batch();
      final notifications = await _firestore
          .collection('notifications')
          .where('userId', isEqualTo: currentUser.uid)
          .where('status', isEqualTo: 'unread')
          .get();

      for (final doc in notifications.docs) {
        batch.update(doc.reference, {'status': 'read'});
      }

      await batch.commit();
      return true;
    } catch (e) {
      debugPrint('Error marking all notifications as read: $e');
      return false;
    }
  }

  // Delete notification
  Future<bool> deleteNotification(String notificationId) async {
    try {
      await _firestore.collection('notifications').doc(notificationId).delete();
      return true;
    } catch (e) {
      debugPrint('Error deleting notification: $e');
      return false;
    }
  }

  // Get notification preferences
  Future<NotificationPreferences> getNotificationPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return NotificationPreferences(
        pushNotifications: prefs.getBool('pushNotifications') ?? true,
        inAppNotifications: prefs.getBool('inAppNotifications') ?? true,
        emailNotifications: prefs.getBool('emailNotifications') ?? false,
        followNotifications: prefs.getBool('followNotifications') ?? true,
        likeNotifications: prefs.getBool('likeNotifications') ?? true,
        commentNotifications: prefs.getBool('commentNotifications') ?? true,
        mentionNotifications: prefs.getBool('mentionNotifications') ?? true,
        messageNotifications: prefs.getBool('messageNotifications') ?? true,
        shareNotifications: prefs.getBool('shareNotifications') ?? true,
        adminNotifications: prefs.getBool('adminNotifications') ?? true,
        storyNotifications: prefs.getBool('storyNotifications') ?? true,
        eventNotifications: prefs.getBool('eventNotifications') ?? true,
        systemNotifications: prefs.getBool('systemNotifications') ?? true,
      );
    } catch (e) {
      debugPrint('Error getting notification preferences: $e');
      return NotificationPreferences.defaultSettings();
    }
  }

  // Save notification preferences
  Future<void> saveNotificationPreferences(
    NotificationPreferences preferences,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('pushNotifications', preferences.pushNotifications);
      await prefs.setBool('inAppNotifications', preferences.inAppNotifications);
      await prefs.setBool('emailNotifications', preferences.emailNotifications);
      await prefs.setBool(
        'followNotifications',
        preferences.followNotifications,
      );
      await prefs.setBool('likeNotifications', preferences.likeNotifications);
      await prefs.setBool(
        'commentNotifications',
        preferences.commentNotifications,
      );
      await prefs.setBool(
        'mentionNotifications',
        preferences.mentionNotifications,
      );
      await prefs.setBool(
        'messageNotifications',
        preferences.messageNotifications,
      );
      await prefs.setBool('shareNotifications', preferences.shareNotifications);
      await prefs.setBool('adminNotifications', preferences.adminNotifications);
      await prefs.setBool('storyNotifications', preferences.storyNotifications);
      await prefs.setBool('eventNotifications', preferences.eventNotifications);
      await prefs.setBool(
        'systemNotifications',
        preferences.systemNotifications,
      );
    } catch (e) {
      debugPrint('Error saving notification preferences: $e');
    }
  }

  // Send notification to user (for internal use)
  Future<void> sendNotification({
    required String userId,
    required String title,
    required String body,
    required NotificationType type,
    required String senderId,
    required String senderName,
    required String senderAvatarUrl,
    String? postId,
    String? commentId,
    String? messageId,
    String? storyId,
    String? eventId,
    Map<String, dynamic>? data,
    String? actionUrl,
    String? actionType,
  }) async {
    try {
      // Check if user has notifications enabled for this type
      final preferences = await getNotificationPreferences();
      if (!_isNotificationTypeEnabled(type, preferences)) return;

      // Create notification document
      final notification = {
        'id': _uuid.v4(),
        'userId': userId,
        'title': title,
        'body': body,
        'type': type.name,
        'timestamp': FieldValue.serverTimestamp(),
        'status': 'unread',
        'fromUserId':
            senderId, // Changed from 'senderId' to match security rules
        'senderName': senderName,
        'senderAvatarUrl': senderAvatarUrl,
        'postId': postId,
        'commentId': commentId,
        'messageId': messageId,
        'storyId': storyId,
        'eventId': eventId,
        'data': data,
        'actionUrl': actionUrl,
        'actionType': actionType,
      };

      await _firestore.collection('notifications').add(notification);

      // Send push notification if enabled
      if (preferences.pushNotifications) {
        await _sendPushNotification(userId, notification);
      }

      // Trigger n8n workflow for notification analytics
      try {
        final n8nService = getIt<N8nService>();
        await n8nService.sendNotification(
          recipientId: userId,
          title: title,
          body: body,
          data: {
            'type': type.name,
            'senderId': senderId,
            'senderName': senderName,
            'postId': postId,
            'commentId': commentId,
            'messageId': messageId,
            'storyId': storyId,
            'eventId': eventId,
            'actionType': actionType,
            'timestamp': DateTime.now().toIso8601String(),
            ...?data,
          },
        );
      } catch (e) {
        debugPrint('⚠️ n8n notification workflow trigger failed: $e');
        // Don't fail notification sending if n8n fails
      }
    } catch (e) {
      debugPrint('Error sending notification: $e');
    }
  }

  // Check if notification type is enabled
  bool _isNotificationTypeEnabled(
    NotificationType type,
    NotificationPreferences preferences,
  ) {
    switch (type) {
      case NotificationType.follow:
        return preferences.followNotifications;
      case NotificationType.like:
        return preferences.likeNotifications;
      case NotificationType.comment:
        return preferences.commentNotifications;
      case NotificationType.mention:
        return preferences.mentionNotifications;
      case NotificationType.message:
        return preferences.messageNotifications;
      case NotificationType.share:
        return preferences.shareNotifications;
      case NotificationType.admin:
        return preferences.adminNotifications;
      case NotificationType.story:
        return preferences.storyNotifications;
      case NotificationType.event:
        return preferences.eventNotifications;
      case NotificationType.system:
        return preferences.systemNotifications;
    }
  }

  // Send push notification via FCM
  Future<void> _sendPushNotification(
    String userId,
    Map<String, dynamic> notification,
  ) async {
    try {
      // Get user's FCM token
      final userDoc = await _firestore.collection('users').doc(userId).get();
      final fcmToken = userDoc.data()?['fcmToken'];

      if (fcmToken != null) {
        // In a real app, you would send this via your backend
        // For now, we'll just log it
        debugPrint('Would send push notification to token: $fcmToken');
        debugPrint('Notification data: $notification');
      }
    } catch (e) {
      debugPrint('Error sending push notification: $e');
    }
  }

  // For testing purposes
  Future<void> sendTestNotification() async {
    final currentUser = _firebaseService.currentUser;
    if (currentUser == null) {
      debugPrint('No user logged in to send test notification');
      return;
    }

    debugPrint('Sending test notification to user ${currentUser.uid}');

    try {
      await sendNotification(
        userId: currentUser.uid,
        title: 'Test Notification',
        body: 'This is a test notification from the app.',
        type: NotificationType.system,
        senderId: 'system',
        senderName: 'System',
        senderAvatarUrl: '',
      );
      debugPrint('Test notification sent successfully');
    } catch (e) {
      debugPrint('Error sending test notification: $e');
    }
  }

  // Get APNs token (iOS only) with better error handling
  Future<String?> getAPNSToken() async {
    if (defaultTargetPlatform == TargetPlatform.iOS) {
      try {
        // Check if APNS token is available with timeout
        final token = await _fcm.getAPNSToken().timeout(
          const Duration(seconds: 5),
          onTimeout: () {
            debugPrint('⏰ APNs token request timed out');
            return null;
          },
        );

        if (token != null && token.isNotEmpty) {
          debugPrint('✅ APNs Token retrieved successfully');
          return token;
        } else {
          debugPrint('⚠️ APNs Token is null or empty');
          return null;
        }
      } catch (e) {
        if (e.toString().contains('apns-token-not-set')) {
          debugPrint(
            '⚠️ APNs token not set yet, this is normal during app startup',
          );
        } else {
          debugPrint('❌ Error getting APNs token: $e');
        }
        return null;
      }
    }
    return null;
  }

  // Test push notification setup with improved error handling
  Future<void> testPushNotificationSetup() async {
    try {
      debugPrint('🧪 Testing push notification setup...');

      // Get FCM token with error handling
      try {
        final fcmToken = await _fcm.getToken();
        if (fcmToken != null) {
          debugPrint(
            '✅ FCM Token: ${fcmToken.substring(0, math.min(20, fcmToken.length))}...',
          );
        } else {
          debugPrint('⚠️ FCM Token: Not available');
        }
      } catch (e) {
        debugPrint('❌ Error getting FCM token: $e');
      }

      // Get APNs token (iOS only) with error handling
      if (defaultTargetPlatform == TargetPlatform.iOS) {
        try {
          final apnsToken = await getAPNSToken();
          if (apnsToken != null) {
            debugPrint('✅ APNs Token: Available');
          } else {
            debugPrint(
              '⚠️ APNs Token: Not available (this is normal during startup)',
            );
          }
        } catch (e) {
          debugPrint('⚠️ APNs Token error (expected during startup): $e');
        }
      }

      // Check permission status
      try {
        final settings = await _fcm.getNotificationSettings();
        debugPrint(
          '✅ Notification permission: ${settings.authorizationStatus}',
        );
      } catch (e) {
        debugPrint('❌ Error getting notification settings: $e');
      }

      // Send test notification
      try {
        await sendTestNotification();
        debugPrint('✅ Test notification sent');
      } catch (e) {
        debugPrint('❌ Error sending test notification: $e');
      }

      debugPrint('🎉 Push notification setup test completed');
    } catch (e) {
      debugPrint('❌ Error during push notification setup test: $e');
    }
  }

  // Create a notification
  Future<void> createNotification({
    required String userId,
    required String type,
    required String fromUserId,
    String? postId,
    String? commentId,
    String? messageId,
    String? chatId,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        debugPrint('❌ Cannot create notification: User not authenticated');
        return;
      }

      // Don't create notification for self
      if (userId == currentUser.uid) {
        debugPrint('⏭️ Skipping notification: User cannot notify themselves');
        return;
      }

      // Validate that the current user is the sender
      if (fromUserId != currentUser.uid) {
        debugPrint(
          '❌ Cannot create notification: fromUserId must match current user',
        );
        return;
      }

      debugPrint(
        '📱 Creating notification: type=$type, userId=$userId, fromUserId=$fromUserId',
      );

      final notificationData = {
        'userId': userId,
        'type': type,
        'fromUserId': fromUserId,
        'isRead': false,
        'createdAt': FieldValue.serverTimestamp(),
        if (postId != null) 'postId': postId,
        if (commentId != null) 'commentId': commentId,
        if (messageId != null) 'messageId': messageId,
        if (chatId != null) 'chatId': chatId,
        if (additionalData != null) ...additionalData,
      };

      await _firestore.collection('notifications').add(notificationData);

      debugPrint('✅ Notification created successfully: $type for user $userId');
    } catch (e) {
      debugPrint('❌ Error creating notification: $e');
      // Don't throw the exception to prevent app crashes
      // The notification failure shouldn't break the main functionality
    }
  }

  // Create follow notification
  Future<void> createFollowNotification(String targetUserId) async {
    await createNotification(
      userId: targetUserId,
      type: 'follow',
      fromUserId: _auth.currentUser!.uid,
    );
  }

  // Create close friend notification
  Future<void> createCloseFriendNotification(
    String targetUserId,
    String fromUserId,
    String action,
  ) async {
    await createNotification(
      userId: targetUserId,
      type: 'close_friend',
      fromUserId: fromUserId,
      additionalData: {'action': action},
    );
  }

  // Create like notification
  Future<void> createLikeNotification(String postId, String postOwnerId) async {
    await createNotification(
      userId: postOwnerId,
      type: 'like',
      fromUserId: _auth.currentUser!.uid,
      postId: postId,
    );
  }

  // Create comment notification
  Future<void> createCommentNotification(
    String postId,
    String postOwnerId,
    String commentId,
  ) async {
    await createNotification(
      userId: postOwnerId,
      type: 'comment',
      fromUserId: _auth.currentUser!.uid,
      postId: postId,
      commentId: commentId,
    );
  }

  // Create repost notification
  Future<void> createRepostNotification(
    String postId,
    String postOwnerId,
    String? repostId,
  ) async {
    await createNotification(
      userId: postOwnerId,
      type: 'repost',
      fromUserId: _auth.currentUser!.uid,
      postId: postId,
      additionalData: repostId != null ? {'repostId': repostId} : null,
    );
  }

  // Create remix notification
  Future<void> createRemixNotification(
    String originalPostId,
    String originalPostOwnerId,
    String remixId,
  ) async {
    await createNotification(
      userId: originalPostOwnerId,
      type: 'remix',
      fromUserId: _auth.currentUser!.uid,
      postId: originalPostId,
      additionalData: {'remixId': remixId},
    );
  }

  // Create mention notification
  Future<void> createMentionNotification(
    String mentionedUserId,
    String postId,
  ) async {
    await createNotification(
      userId: mentionedUserId,
      type: 'mention',
      fromUserId: _auth.currentUser!.uid,
      postId: postId,
    );
  }

  // Create message notification
  Future<void> createMessageNotification(
    String chatId,
    String recipientId,
    String messageId,
  ) async {
    await createNotification(
      userId: recipientId,
      type: 'message',
      fromUserId: _auth.currentUser!.uid,
      chatId: chatId,
      messageId: messageId,
    );
  }

  // Create story view notification
  Future<void> createStoryViewNotification(
    String storyOwnerId,
    String storyId,
  ) async {
    await createNotification(
      userId: storyOwnerId,
      type: 'story_view',
      fromUserId: _auth.currentUser!.uid,
      additionalData: {'storyId': storyId},
    );
  }

  // Create story tag notification
  Future<void> createStoryTagNotification(
    String storyId,
    String taggedUserId,
    String taggedByUserId,
  ) async {
    try {
      // Get the user who tagged (sender) details
      final senderProfile = await _firebaseService.getUserProfile(
        taggedByUserId,
      );
      final senderName = senderProfile?.name ?? 'Someone';

      await createNotification(
        userId: taggedUserId,
        type: 'story_tag',
        fromUserId: taggedByUserId,
        additionalData: {'storyId': storyId, 'senderName': senderName},
      );

      debugPrint('✅ Story tag notification sent to user: $taggedUserId');
    } catch (e) {
      debugPrint('❌ Error creating story tag notification: $e');
    }
  }

  // Create booking notification
  Future<void> createBookingNotification(
    String placeOwnerId,
    String bookingId,
  ) async {
    await createNotification(
      userId: placeOwnerId,
      type: 'booking',
      fromUserId: _auth.currentUser!.uid,
      additionalData: {'bookingId': bookingId},
    );
  }

  // Get notifications for current user
  Stream<List<NotificationModel>> getNotificationsStream() {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return Stream.value([]);

    return _firestore
        .collection('notifications')
        .where('userId', isEqualTo: currentUser.uid)
        .orderBy('createdAt', descending: true)
        .limit(50)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs.map((doc) {
            final data = doc.data();
            return NotificationModel(
              id: doc.id,
              userId: data['userId'] ?? '',
              title: _getNotificationTitle(data['type'] ?? '', data),
              body: _getNotificationBody(data['type'] ?? '', data),
              type: _getNotificationType(data['type'] ?? ''),
              timestamp:
                  (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
              status: data['isRead'] == true
                  ? NotificationStatus.read
                  : NotificationStatus.unread,
              senderId:
                  data['fromUserId'] ??
                  data['senderId'] ??
                  '', // Fallback for old notifications
              senderName: data['senderName'] ?? 'Unknown User',
              senderAvatarUrl: data['senderAvatarUrl'] ?? '',
              postId: data['postId'],
              commentId: data['commentId'],
              messageId: data['messageId'],
              storyId: data['storyId'],
              eventId: data['eventId'],
              data: data['additionalData'] != null
                  ? Map<String, dynamic>.from(data['additionalData'])
                  : null,
              actionUrl: data['actionUrl'],
              actionType: data['actionType'],
            );
          }).toList(),
        );
  }

  String _getNotificationTitle(String type, Map<String, dynamic> data) {
    switch (type) {
      case 'follow':
        return 'New Follower';
      case 'like':
        return 'New Like';
      case 'comment':
        return 'New Comment';
      case 'mention':
        return 'You were mentioned';
      case 'message':
        return 'New Message';
      case 'story_view':
        return 'Story Viewed';
      case 'story_tag':
        return 'You were tagged in a story';
      case 'booking':
        return 'New Booking';
      default:
        return 'New Notification';
    }
  }

  String _getNotificationBody(String type, Map<String, dynamic> data) {
    final senderName = data['senderName'] ?? 'Someone';

    switch (type) {
      case 'follow':
        return '$senderName started following you';
      case 'like':
        return '$senderName liked your post';
      case 'comment':
        return '$senderName commented on your post';
      case 'mention':
        return '$senderName mentioned you in a post';
      case 'message':
        return '$senderName sent you a message';
      case 'story_view':
        return '$senderName viewed your story';
      case 'story_tag':
        return '$senderName tagged you in their story';
      case 'booking':
        return '$senderName made a booking';
      default:
        return 'You have a new notification';
    }
  }

  NotificationType _getNotificationType(String type) {
    switch (type) {
      case 'follow':
        return NotificationType.follow;
      case 'like':
        return NotificationType.like;
      case 'comment':
        return NotificationType.comment;
      case 'mention':
        return NotificationType.mention;
      case 'message':
        return NotificationType.message;
      case 'story_view':
      case 'story_tag':
        return NotificationType.story;
      case 'booking':
        return NotificationType.event;
      default:
        return NotificationType.system;
    }
  }

  // Mark notification as read
  Future<void> markNotificationAsRead(String notificationId) async {
    try {
      await _firestore.collection('notifications').doc(notificationId).update({
        'isRead': true,
      });
    } catch (e) {
      debugPrint('❌ Error marking notification as read: $e');
    }
  }

  // Mark all notifications as read
  Future<void> markAllNotificationsAsRead() async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return;

    try {
      final batch = _firestore.batch();
      final notificationsSnapshot = await _firestore
          .collection('notifications')
          .where('userId', isEqualTo: currentUser.uid)
          .where('isRead', isEqualTo: false)
          .get();

      for (final doc in notificationsSnapshot.docs) {
        batch.update(doc.reference, {'isRead': true});
      }

      await batch.commit();
      debugPrint(
        '✅ Marked ${notificationsSnapshot.docs.length} notifications as read',
      );
    } catch (e) {
      debugPrint('❌ Error marking all notifications as read: $e');
    }
  }

  // Get unread notification count
  Stream<int> getUnreadNotificationCount() {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return Stream.value(0);

    return _firestore
        .collection('notifications')
        .where('userId', isEqualTo: currentUser.uid)
        .where('isRead', isEqualTo: false)
        .snapshots()
        .map((snapshot) => snapshot.docs.length);
  }

  // Delete all notifications for current user
  Future<void> deleteAllNotifications() async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return;

    try {
      final batch = _firestore.batch();
      final notificationsSnapshot = await _firestore
          .collection('notifications')
          .where('userId', isEqualTo: currentUser.uid)
          .get();

      for (final doc in notificationsSnapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
      debugPrint(
        '✅ Deleted ${notificationsSnapshot.docs.length} notifications',
      );
    } catch (e) {
      debugPrint('❌ Error deleting all notifications: $e');
    }
  }
}

// Top-level function for background message handling
@pragma('vm:entry-point')
Future<void> _handleBackgroundMessage(RemoteMessage message) async {
  await NotificationService._handleBackgroundMessage(message);
}
