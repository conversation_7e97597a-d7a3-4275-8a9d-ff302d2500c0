import 'package:flutter/material.dart';
import '../services/enhanced_story_creation_service.dart';

/// Enhanced feedback system for story creation
class StoryCreationFeedback {
  static void showValidationError(BuildContext context, String message) {
    _showCustomSnackBar(
      context,
      message: message,
      icon: Icons.warning_amber_rounded,
      backgroundColor: Colors.orange[700]!,
      duration: const Duration(seconds: 4),
    );
  }

  static void showUploadProgress(BuildContext context, {
    required String message,
    double? progress,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                value: progress,
                color: Colors.white,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
            if (progress != null)
              Text('${(progress * 100).toInt()}%'),
          ],
        ),
        backgroundColor: Colors.blue[700],
        duration: const Duration(seconds: 2),
      ),
    );
  }

  static void showSuccess(BuildContext context, {
    String message = 'Story created successfully!',
    VoidCallback? onViewStory,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green[700],
        duration: const Duration(seconds: 3),
        action: onViewStory != null
            ? SnackBarAction(
                label: 'View',
                textColor: Colors.white,
                onPressed: onViewStory,
              )
            : null,
      ),
    );
  }

  static void showError(BuildContext context, {
    required String message,
    required StoryCreationErrorType errorType,
    VoidCallback? onRetry,
  }) {
    final (icon, actionLabel) = _getErrorDetails(errorType);
    
    _showCustomSnackBar(
      context,
      message: message,
      icon: icon,
      backgroundColor: Colors.red[700]!,
      duration: const Duration(seconds: 6),
      action: onRetry != null
          ? SnackBarAction(
              label: actionLabel,
              textColor: Colors.white,
              onPressed: onRetry,
            )
          : null,
    );
  }

  static void showNetworkError(BuildContext context, {VoidCallback? onRetry}) {
    _showCustomSnackBar(
      context,
      message: 'Network error. Please check your connection and try again.',
      icon: Icons.wifi_off,
      backgroundColor: Colors.red[700]!,
      duration: const Duration(seconds: 5),
      action: onRetry != null
          ? SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: onRetry,
            )
          : null,
    );
  }

  static void showStorageError(BuildContext context) {
    _showCustomSnackBar(
      context,
      message: 'Storage full. Please free up space and try again.',
      icon: Icons.storage,
      backgroundColor: Colors.red[700]!,
      duration: const Duration(seconds: 5),
    );
  }

  static void _showCustomSnackBar(
    BuildContext context, {
    required String message,
    required IconData icon,
    required Color backgroundColor,
    required Duration duration,
    SnackBarAction? action,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(icon, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: backgroundColor,
        duration: duration,
        action: action,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  static (IconData, String) _getErrorDetails(StoryCreationErrorType errorType) {
    switch (errorType) {
      case StoryCreationErrorType.validation:
        return (Icons.warning_amber_rounded, 'Fix');
      case StoryCreationErrorType.upload:
        return (Icons.cloud_upload, 'Retry');
      case StoryCreationErrorType.network:
        return (Icons.wifi_off, 'Retry');
      case StoryCreationErrorType.permission:
        return (Icons.lock, 'Settings');
      case StoryCreationErrorType.storage:
        return (Icons.storage, 'Manage');
      case StoryCreationErrorType.unknown:
        return (Icons.error, 'Retry');
    }
  }
}

/// Loading overlay for story creation
class StoryCreationLoadingOverlay extends StatelessWidget {
  final String message;
  final double? progress;
  final VoidCallback? onCancel;

  const StoryCreationLoadingOverlay({
    super.key,
    required this.message,
    this.progress,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.black.withValues(alpha: 0.7),
      child: Center(
        child: Container(
          margin: const EdgeInsets.all(32),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Progress indicator
              SizedBox(
                width: 60,
                height: 60,
                child: CircularProgressIndicator(
                  strokeWidth: 4,
                  value: progress,
                  backgroundColor: Colors.grey[300],
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Message
              Text(
                message,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
              
              // Progress percentage
              if (progress != null) ...[
                const SizedBox(height: 8),
                Text(
                  '${(progress! * 100).toInt()}%',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
              
              // Cancel button
              if (onCancel != null) ...[
                const SizedBox(height: 16),
                TextButton(
                  onPressed: onCancel,
                  child: const Text('Cancel'),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// Success animation widget
class StoryCreationSuccessAnimation extends StatefulWidget {
  final VoidCallback? onComplete;

  const StoryCreationSuccessAnimation({
    super.key,
    this.onComplete,
  });

  @override
  State<StoryCreationSuccessAnimation> createState() => _StoryCreationSuccessAnimationState();
}

class _StoryCreationSuccessAnimationState extends State<StoryCreationSuccessAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.0, 0.6, curve: Curves.elasticOut),
    ));

    _opacityAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.7, 1.0, curve: Curves.easeOut),
    ));

    _controller.forward().then((_) {
      widget.onComplete?.call();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Container(
          color: Colors.black.withValues(alpha: 0.7 * _opacityAnimation.value),
          child: Center(
            child: Transform.scale(
              scale: _scaleAnimation.value,
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.green.withValues(alpha: 0.3),
                      blurRadius: 20,
                      spreadRadius: 5,
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 60,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Validation helper widget
class StoryValidationHelper extends StatelessWidget {
  final String? error;
  final bool isValid;

  const StoryValidationHelper({
    super.key,
    this.error,
    required this.isValid,
  });

  @override
  Widget build(BuildContext context) {
    if (isValid || error == null) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red[200]!),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red[700],
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              error!,
              style: TextStyle(
                color: Colors.red[700],
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
