import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/story_settings_model.dart';
import '../providers/story_settings_provider.dart';
import '../screens/story_settings_screen.dart';

/// Enhanced header for story creation screens showing current settings
class StoryCreationHeader extends ConsumerWidget {
  final String title;
  final VoidCallback? onClose;
  final VoidCallback? onShare;
  final bool isLoading;
  final bool showSettings;

  const StoryCreationHeader({
    super.key,
    required this.title,
    this.onClose,
    this.onShare,
    this.isLoading = false,
    this.showSettings = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsAsync = ref.watch(storySettingsNotifierProvider);

    return Container(
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top,
        left: 16,
        right: 16,
        bottom: 8,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Colors.black.withValues(alpha: 0.7), Colors.transparent],
        ),
      ),
      child: Column(
        children: [
          // Main header row
          Row(
            children: [
              // Close button
              IconButton(
                onPressed: isLoading ? null : onClose,
                icon: const Icon(Icons.close, color: Colors.white),
              ),

              // Title
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              // Settings button
              if (showSettings)
                IconButton(
                  onPressed: () => _showQuickSettings(context, ref),
                  icon: const Icon(Icons.tune, color: Colors.white),
                ),

              // Share button
              TextButton(
                onPressed: isLoading ? null : onShare,
                child: isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.white,
                        ),
                      )
                    : const Text(
                        'Share',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ],
          ),

          // Settings preview row
          if (showSettings)
            settingsAsync.when(
              data: (settings) => _buildSettingsPreview(settings),
              loading: () => const SizedBox.shrink(),
              error: (_, _) => const SizedBox.shrink(),
            ),
        ],
      ),
    );
  }

  Widget _buildSettingsPreview(StorySettings settings) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Duration indicator
          Icon(
            Icons.schedule,
            size: 14,
            color: Colors.white.withValues(alpha: 0.8),
          ),
          const SizedBox(width: 4),
          Text(
            _getDurationText(settings.duration),
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),

          const SizedBox(width: 12),

          // Visibility indicator
          Icon(
            _getVisibilityIcon(settings.visibility),
            size: 14,
            color: Colors.white.withValues(alpha: 0.8),
          ),
          const SizedBox(width: 4),
          Text(
            _getVisibilityText(settings.visibility),
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  String _getDurationText(StoryDuration duration) {
    switch (duration) {
      case StoryDuration.sixHours:
        return '6h';
      case StoryDuration.twelveHours:
        return '12h';
      case StoryDuration.twentyFourHours:
        return '24h';
    }
  }

  IconData _getVisibilityIcon(StoryVisibility visibility) {
    switch (visibility) {
      case StoryVisibility.public:
        return Icons.public;
      case StoryVisibility.followers:
        return Icons.people;
      case StoryVisibility.specificGroups:
        return Icons.group;
      case StoryVisibility.closeFriends:
        return Icons.favorite;
    }
  }

  String _getVisibilityText(StoryVisibility visibility) {
    switch (visibility) {
      case StoryVisibility.public:
        return 'Public';
      case StoryVisibility.followers:
        return 'Followers';
      case StoryVisibility.specificGroups:
        return 'Groups';
      case StoryVisibility.closeFriends:
        return 'Close Friends';
    }
  }

  void _showQuickSettings(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => const QuickStorySettingsSheet(),
    );
  }
}

/// Quick settings bottom sheet for story creation
class QuickStorySettingsSheet extends ConsumerWidget {
  const QuickStorySettingsSheet({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsAsync = ref.watch(storySettingsNotifierProvider);

    return Container(
      decoration: const BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[600],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                const Text(
                  'Story Settings',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const StorySettingsScreen(),
                      ),
                    );
                  },
                  child: const Text('More Settings'),
                ),
              ],
            ),
          ),

          // Quick settings
          settingsAsync.when(
            data: (settings) => _buildQuickSettings(context, ref, settings),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, _) => Center(
              child: Text(
                'Error loading settings',
                style: TextStyle(color: Colors.red[400]),
              ),
            ),
          ),

          SizedBox(height: MediaQuery.of(context).padding.bottom + 20),
        ],
      ),
    );
  }

  Widget _buildQuickSettings(
    BuildContext context,
    WidgetRef ref,
    StorySettings settings,
  ) {
    return Column(
      children: [
        // Duration setting
        _buildQuickSettingTile(
          icon: Icons.schedule,
          title: 'Duration',
          subtitle: _getDurationText(settings.duration),
          onTap: () => _showDurationPicker(context, ref, settings),
        ),

        // Visibility setting
        _buildQuickSettingTile(
          icon: _getVisibilityIcon(settings.visibility),
          title: 'Who can see',
          subtitle: _getVisibilityText(settings.visibility),
          onTap: () => _showVisibilityPicker(context, ref, settings),
        ),
      ],
    );
  }

  Widget _buildQuickSettingTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: Colors.white),
      title: Text(title, style: const TextStyle(color: Colors.white)),
      subtitle: Text(subtitle, style: TextStyle(color: Colors.grey[400])),
      trailing: const Icon(Icons.chevron_right, color: Colors.grey),
      onTap: onTap,
    );
  }

  void _showDurationPicker(
    BuildContext context,
    WidgetRef ref,
    StorySettings settings,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text(
          'Story Duration',
          style: TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: StoryDuration.values.map((duration) {
            return RadioListTile<StoryDuration>(
              title: Text(
                _getDurationText(duration),
                style: const TextStyle(color: Colors.white),
              ),
              value: duration,
              groupValue: settings.duration,
              onChanged: (value) {
                if (value != null) {
                  ref
                      .read(storySettingsNotifierProvider.notifier)
                      .updateSettings(settings.copyWith(duration: value));
                  Navigator.pop(context);
                }
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showVisibilityPicker(
    BuildContext context,
    WidgetRef ref,
    StorySettings settings,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text(
          'Who can see your story',
          style: TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: StoryVisibility.values.map((visibility) {
            return RadioListTile<StoryVisibility>(
              title: Text(
                _getVisibilityText(visibility),
                style: const TextStyle(color: Colors.white),
              ),
              value: visibility,
              groupValue: settings.visibility,
              onChanged: (value) {
                if (value != null) {
                  ref
                      .read(storySettingsNotifierProvider.notifier)
                      .updateSettings(settings.copyWith(visibility: value));
                  Navigator.pop(context);
                }
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  String _getDurationText(StoryDuration duration) {
    switch (duration) {
      case StoryDuration.sixHours:
        return '6 hours';
      case StoryDuration.twelveHours:
        return '12 hours';
      case StoryDuration.twentyFourHours:
        return '24 hours';
    }
  }

  IconData _getVisibilityIcon(StoryVisibility visibility) {
    switch (visibility) {
      case StoryVisibility.public:
        return Icons.public;
      case StoryVisibility.followers:
        return Icons.people;
      case StoryVisibility.specificGroups:
        return Icons.group;
      case StoryVisibility.closeFriends:
        return Icons.favorite;
    }
  }

  String _getVisibilityText(StoryVisibility visibility) {
    switch (visibility) {
      case StoryVisibility.public:
        return 'Public';
      case StoryVisibility.followers:
        return 'Followers';
      case StoryVisibility.specificGroups:
        return 'Specific Groups';
      case StoryVisibility.closeFriends:
        return 'Close Friends';
    }
  }
}
