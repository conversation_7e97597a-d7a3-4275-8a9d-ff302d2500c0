// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'story_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$StoryImpl _$$StoryImplFromJson(Map<String, dynamic> json) => _$StoryImpl(
  id: json['id'] as String,
  userId: json['userId'] as String,
  userName: json['userName'] as String,
  userAvatarUrl: json['userAvatarUrl'] as String,
  mediaUrl: json['mediaUrl'] as String,
  mediaType: $enumDecode(_$StoryMediaTypeEnumMap, json['mediaType']),
  createdAt: DateTime.parse(json['createdAt'] as String),
  expiresAt: DateTime.parse(json['expiresAt'] as String),
  viewers:
      (json['viewers'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  allowedTo:
      (json['allowedTo'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  reactions:
      (json['reactions'] as List<dynamic>?)
          ?.map((e) => StoryReaction.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  replies:
      (json['replies'] as List<dynamic>?)
          ?.map((e) => StoryReply.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  privacy:
      $enumDecodeNullable(_$StoryPrivacyEnumMap, json['privacy']) ??
      StoryPrivacy.public,
  caption: json['caption'] as String?,
  location: json['location'] as String?,
  hashtags:
      (json['hashtags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  mentions:
      (json['mentions'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  isHighlighted: json['isHighlighted'] as bool? ?? false,
  isArchived: json['isArchived'] as bool? ?? false,
  musicUrl: json['musicUrl'] as String?,
  musicTitle: json['musicTitle'] as String?,
  storyType:
      $enumDecodeNullable(_$StoryTypeEnumMap, json['storyType']) ??
      StoryType.regular,
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$$StoryImplToJson(_$StoryImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'userName': instance.userName,
      'userAvatarUrl': instance.userAvatarUrl,
      'mediaUrl': instance.mediaUrl,
      'mediaType': _$StoryMediaTypeEnumMap[instance.mediaType]!,
      'createdAt': instance.createdAt.toIso8601String(),
      'expiresAt': instance.expiresAt.toIso8601String(),
      'viewers': instance.viewers,
      'allowedTo': instance.allowedTo,
      'reactions': instance.reactions,
      'replies': instance.replies,
      'privacy': _$StoryPrivacyEnumMap[instance.privacy]!,
      'caption': instance.caption,
      'location': instance.location,
      'hashtags': instance.hashtags,
      'mentions': instance.mentions,
      'isHighlighted': instance.isHighlighted,
      'isArchived': instance.isArchived,
      'musicUrl': instance.musicUrl,
      'musicTitle': instance.musicTitle,
      'storyType': _$StoryTypeEnumMap[instance.storyType]!,
      'metadata': instance.metadata,
    };

const _$StoryMediaTypeEnumMap = {
  StoryMediaType.image: 'image',
  StoryMediaType.video: 'video',
  StoryMediaType.text: 'text',
  StoryMediaType.music: 'music',
  StoryMediaType.poll: 'poll',
  StoryMediaType.question: 'question',
  StoryMediaType.countdown: 'countdown',
  StoryMediaType.quiz: 'quiz',
};

const _$StoryPrivacyEnumMap = {
  StoryPrivacy.public: 'public',
  StoryPrivacy.followers: 'followers',
  StoryPrivacy.closeFriends: 'closeFriends',
  StoryPrivacy.custom: 'custom',
  StoryPrivacy.private: 'private',
};

const _$StoryTypeEnumMap = {
  StoryType.regular: 'regular',
  StoryType.highlight: 'highlight',
  StoryType.archive: 'archive',
  StoryType.featured: 'featured',
  StoryType.sponsored: 'sponsored',
};

_$StoryReactionImpl _$$StoryReactionImplFromJson(Map<String, dynamic> json) =>
    _$StoryReactionImpl(
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      userAvatarUrl: json['userAvatarUrl'] as String,
      reactionType: json['reactionType'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$$StoryReactionImplToJson(_$StoryReactionImpl instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'userName': instance.userName,
      'userAvatarUrl': instance.userAvatarUrl,
      'reactionType': instance.reactionType,
      'timestamp': instance.timestamp.toIso8601String(),
    };

_$StoryReplyImpl _$$StoryReplyImplFromJson(Map<String, dynamic> json) =>
    _$StoryReplyImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      userAvatarUrl: json['userAvatarUrl'] as String,
      message: json['message'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      isRead: json['isRead'] as bool? ?? false,
    );

Map<String, dynamic> _$$StoryReplyImplToJson(_$StoryReplyImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'userName': instance.userName,
      'userAvatarUrl': instance.userAvatarUrl,
      'message': instance.message,
      'timestamp': instance.timestamp.toIso8601String(),
      'isRead': instance.isRead,
    };

_$StoryStatsImpl _$$StoryStatsImplFromJson(Map<String, dynamic> json) =>
    _$StoryStatsImpl(
      viewCount: (json['viewCount'] as num).toInt(),
      replyCount: (json['replyCount'] as num).toInt(),
      reactionCount: (json['reactionCount'] as num).toInt(),
      shareCount: (json['shareCount'] as num).toInt(),
      completionRate: (json['completionRate'] as num).toDouble(),
      skipRate: (json['skipRate'] as num).toDouble(),
      topViewers: (json['topViewers'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      reactionBreakdown: Map<String, int>.from(
        json['reactionBreakdown'] as Map,
      ),
    );

Map<String, dynamic> _$$StoryStatsImplToJson(_$StoryStatsImpl instance) =>
    <String, dynamic>{
      'viewCount': instance.viewCount,
      'replyCount': instance.replyCount,
      'reactionCount': instance.reactionCount,
      'shareCount': instance.shareCount,
      'completionRate': instance.completionRate,
      'skipRate': instance.skipRate,
      'topViewers': instance.topViewers,
      'reactionBreakdown': instance.reactionBreakdown,
    };
