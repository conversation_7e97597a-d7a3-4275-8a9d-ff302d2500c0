// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'unified_story_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UnifiedStoryImpl _$$UnifiedStoryImplFromJson(
  Map<String, dynamic> json,
) => _$UnifiedStoryImpl(
  id: json['id'] as String,
  userId: json['userId'] as String,
  userName: json['userName'] as String,
  userAvatarUrl: json['userAvatarUrl'] as String,
  mediaUrl: json['mediaUrl'] as String,
  mediaType: $enumDecode(_$StoryMediaTypeEnumMap, json['mediaType']),
  createdAt: DateTime.parse(json['createdAt'] as String),
  expiresAt: DateTime.parse(json['expiresAt'] as String),
  duration: Duration(microseconds: (json['duration'] as num).toInt()),
  timestamp: DateTime.parse(json['timestamp'] as String),
  privacy:
      $enumDecodeNullable(_$StoryPrivacyEnumMap, json['privacy']) ??
      StoryPrivacy.public,
  visibility:
      $enumDecodeNullable(_$StoryVisibilityEnumMap, json['visibility']) ??
      StoryVisibility.public,
  viewers:
      (json['viewers'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  allowedTo:
      (json['allowedTo'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  hiddenFromUserIds:
      (json['hiddenFromUserIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  allowedGroupIds:
      (json['allowedGroupIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  caption: json['caption'] as String?,
  textOverlay: json['textOverlay'] as String?,
  textColor: (json['textColor'] as num?)?.toInt(),
  textSize: (json['textSize'] as num?)?.toDouble(),
  textPosition: (json['textPosition'] as Map<String, dynamic>?)?.map(
    (k, e) => MapEntry(k, (e as num).toDouble()),
  ),
  backgroundColor: (json['backgroundColor'] as num?)?.toInt(),
  filter: json['filter'] as String?,
  drawingPoints:
      (json['drawingPoints'] as List<dynamic>?)
          ?.map((e) => DrawingPoint.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  drawingColor: (json['drawingColor'] as num?)?.toInt(),
  drawingWidth: (json['drawingWidth'] as num?)?.toDouble(),
  textElements:
      (json['textElements'] as List<dynamic>?)
          ?.map((e) => TextElement.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  tags:
      (json['tags'] as List<dynamic>?)
          ?.map((e) => StoryTag.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  reactions:
      (json['reactions'] as List<dynamic>?)
          ?.map((e) => StoryReaction.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  replies:
      (json['replies'] as List<dynamic>?)
          ?.map((e) => StoryReply.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  hashtags:
      (json['hashtags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  mentions:
      (json['mentions'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  music: json['music'] as Map<String, dynamic>?,
  musicArtist: json['musicArtist'] as String?,
  location: json['location'] as Map<String, dynamic>?,
  locationName: json['locationName'] as String?,
  isHighlighted: json['isHighlighted'] as bool? ?? false,
  isArchived: json['isArchived'] as bool? ?? false,
  isPublic: json['isPublic'] as bool? ?? false,
  isSeen: json['isSeen'] as bool? ?? false,
  isCloseFriend: json['isCloseFriend'] as bool? ?? false,
  storyType:
      $enumDecodeNullable(_$StoryTypeEnumMap, json['storyType']) ??
      StoryType.regular,
  viewCount: (json['viewCount'] as num?)?.toInt() ?? 0,
  replyCount: (json['replyCount'] as num?)?.toInt() ?? 0,
  reactionCount: (json['reactionCount'] as num?)?.toInt() ?? 0,
  shareCount: (json['shareCount'] as num?)?.toInt() ?? 0,
  completionRate: (json['completionRate'] as num?)?.toDouble() ?? 0.0,
  skipRate: (json['skipRate'] as num?)?.toDouble() ?? 0.0,
  topViewers:
      (json['topViewers'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  reactionBreakdown:
      (json['reactionBreakdown'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toInt()),
      ) ??
      const {},
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$$UnifiedStoryImplToJson(_$UnifiedStoryImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'userName': instance.userName,
      'userAvatarUrl': instance.userAvatarUrl,
      'mediaUrl': instance.mediaUrl,
      'mediaType': _$StoryMediaTypeEnumMap[instance.mediaType]!,
      'createdAt': instance.createdAt.toIso8601String(),
      'expiresAt': instance.expiresAt.toIso8601String(),
      'duration': instance.duration.inMicroseconds,
      'timestamp': instance.timestamp.toIso8601String(),
      'privacy': _$StoryPrivacyEnumMap[instance.privacy]!,
      'visibility': _$StoryVisibilityEnumMap[instance.visibility]!,
      'viewers': instance.viewers,
      'allowedTo': instance.allowedTo,
      'hiddenFromUserIds': instance.hiddenFromUserIds,
      'allowedGroupIds': instance.allowedGroupIds,
      'caption': instance.caption,
      'textOverlay': instance.textOverlay,
      'textColor': instance.textColor,
      'textSize': instance.textSize,
      'textPosition': instance.textPosition,
      'backgroundColor': instance.backgroundColor,
      'filter': instance.filter,
      'drawingPoints': instance.drawingPoints,
      'drawingColor': instance.drawingColor,
      'drawingWidth': instance.drawingWidth,
      'textElements': instance.textElements,
      'tags': instance.tags,
      'reactions': instance.reactions,
      'replies': instance.replies,
      'hashtags': instance.hashtags,
      'mentions': instance.mentions,
      'music': instance.music,
      'musicArtist': instance.musicArtist,
      'location': instance.location,
      'locationName': instance.locationName,
      'isHighlighted': instance.isHighlighted,
      'isArchived': instance.isArchived,
      'isPublic': instance.isPublic,
      'isSeen': instance.isSeen,
      'isCloseFriend': instance.isCloseFriend,
      'storyType': _$StoryTypeEnumMap[instance.storyType]!,
      'viewCount': instance.viewCount,
      'replyCount': instance.replyCount,
      'reactionCount': instance.reactionCount,
      'shareCount': instance.shareCount,
      'completionRate': instance.completionRate,
      'skipRate': instance.skipRate,
      'topViewers': instance.topViewers,
      'reactionBreakdown': instance.reactionBreakdown,
      'metadata': instance.metadata,
    };

const _$StoryMediaTypeEnumMap = {
  StoryMediaType.image: 'image',
  StoryMediaType.video: 'video',
  StoryMediaType.text: 'text',
  StoryMediaType.music: 'music',
  StoryMediaType.poll: 'poll',
  StoryMediaType.question: 'question',
  StoryMediaType.countdown: 'countdown',
  StoryMediaType.quiz: 'quiz',
};

const _$StoryPrivacyEnumMap = {
  StoryPrivacy.public: 'public',
  StoryPrivacy.followers: 'followers',
  StoryPrivacy.closeFriends: 'closeFriends',
  StoryPrivacy.custom: 'custom',
  StoryPrivacy.private: 'private',
};

const _$StoryVisibilityEnumMap = {
  StoryVisibility.public: 'public',
  StoryVisibility.followers: 'followers',
  StoryVisibility.specificGroups: 'specificGroups',
  StoryVisibility.closeFriends: 'closeFriends',
};

const _$StoryTypeEnumMap = {
  StoryType.regular: 'regular',
  StoryType.highlight: 'highlight',
  StoryType.archive: 'archive',
  StoryType.featured: 'featured',
  StoryType.sponsored: 'sponsored',
};

_$StoryTagImpl _$$StoryTagImplFromJson(Map<String, dynamic> json) =>
    _$StoryTagImpl(
      userId: json['userId'] as String,
      username: json['username'] as String,
      name: json['name'] as String?,
      x: (json['x'] as num).toDouble(),
      y: (json['y'] as num).toDouble(),
    );

Map<String, dynamic> _$$StoryTagImplToJson(_$StoryTagImpl instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'username': instance.username,
      'name': instance.name,
      'x': instance.x,
      'y': instance.y,
    };

_$StoryReactionImpl _$$StoryReactionImplFromJson(Map<String, dynamic> json) =>
    _$StoryReactionImpl(
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      userAvatarUrl: json['userAvatarUrl'] as String,
      reactionType: json['reactionType'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$$StoryReactionImplToJson(_$StoryReactionImpl instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'userName': instance.userName,
      'userAvatarUrl': instance.userAvatarUrl,
      'reactionType': instance.reactionType,
      'timestamp': instance.timestamp.toIso8601String(),
    };

_$StoryReplyImpl _$$StoryReplyImplFromJson(Map<String, dynamic> json) =>
    _$StoryReplyImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      userAvatarUrl: json['userAvatarUrl'] as String,
      message: json['message'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      isRead: json['isRead'] as bool? ?? false,
    );

Map<String, dynamic> _$$StoryReplyImplToJson(_$StoryReplyImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'userName': instance.userName,
      'userAvatarUrl': instance.userAvatarUrl,
      'message': instance.message,
      'timestamp': instance.timestamp.toIso8601String(),
      'isRead': instance.isRead,
    };

_$StoryReelImpl _$$StoryReelImplFromJson(Map<String, dynamic> json) =>
    _$StoryReelImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      username: json['username'] as String,
      userAvatarUrl: json['userAvatarUrl'] as String,
      stories: (json['stories'] as List<dynamic>)
          .map((e) => UnifiedStory.fromJson(e as Map<String, dynamic>))
          .toList(),
      isAllViewed: json['isAllViewed'] as bool? ?? false,
      isCloseFriend: json['isCloseFriend'] as bool? ?? false,
    );

Map<String, dynamic> _$$StoryReelImplToJson(_$StoryReelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'username': instance.username,
      'userAvatarUrl': instance.userAvatarUrl,
      'stories': instance.stories,
      'isAllViewed': instance.isAllViewed,
      'isCloseFriend': instance.isCloseFriend,
    };

_$StoryStatsImpl _$$StoryStatsImplFromJson(Map<String, dynamic> json) =>
    _$StoryStatsImpl(
      viewCount: (json['viewCount'] as num).toInt(),
      replyCount: (json['replyCount'] as num).toInt(),
      reactionCount: (json['reactionCount'] as num).toInt(),
      shareCount: (json['shareCount'] as num).toInt(),
      completionRate: (json['completionRate'] as num).toDouble(),
      skipRate: (json['skipRate'] as num).toDouble(),
      topViewers: (json['topViewers'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      reactionBreakdown: Map<String, int>.from(
        json['reactionBreakdown'] as Map,
      ),
    );

Map<String, dynamic> _$$StoryStatsImplToJson(_$StoryStatsImpl instance) =>
    <String, dynamic>{
      'viewCount': instance.viewCount,
      'replyCount': instance.replyCount,
      'reactionCount': instance.reactionCount,
      'shareCount': instance.shareCount,
      'completionRate': instance.completionRate,
      'skipRate': instance.skipRate,
      'topViewers': instance.topViewers,
      'reactionBreakdown': instance.reactionBreakdown,
    };
