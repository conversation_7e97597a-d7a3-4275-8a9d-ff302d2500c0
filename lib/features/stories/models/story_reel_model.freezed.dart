// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'story_reel_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

StoryTag _$StoryTagFromJson(Map<String, dynamic> json) {
  return _StoryTag.fromJson(json);
}

/// @nodoc
mixin _$StoryTag {
  String get userId => throw _privateConstructorUsedError;
  String get username => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  double get x => throw _privateConstructorUsedError;
  double get y => throw _privateConstructorUsedError;

  /// Serializes this StoryTag to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StoryTag
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StoryTagCopyWith<StoryTag> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StoryTagCopyWith<$Res> {
  factory $StoryTagCopyWith(StoryTag value, $Res Function(StoryTag) then) =
      _$StoryTagCopyWithImpl<$Res, StoryTag>;
  @useResult
  $Res call({String userId, String username, String? name, double x, double y});
}

/// @nodoc
class _$StoryTagCopyWithImpl<$Res, $Val extends StoryTag>
    implements $StoryTagCopyWith<$Res> {
  _$StoryTagCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StoryTag
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? username = null,
    Object? name = freezed,
    Object? x = null,
    Object? y = null,
  }) {
    return _then(
      _value.copyWith(
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            username: null == username
                ? _value.username
                : username // ignore: cast_nullable_to_non_nullable
                      as String,
            name: freezed == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String?,
            x: null == x
                ? _value.x
                : x // ignore: cast_nullable_to_non_nullable
                      as double,
            y: null == y
                ? _value.y
                : y // ignore: cast_nullable_to_non_nullable
                      as double,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$StoryTagImplCopyWith<$Res>
    implements $StoryTagCopyWith<$Res> {
  factory _$$StoryTagImplCopyWith(
    _$StoryTagImpl value,
    $Res Function(_$StoryTagImpl) then,
  ) = __$$StoryTagImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String userId, String username, String? name, double x, double y});
}

/// @nodoc
class __$$StoryTagImplCopyWithImpl<$Res>
    extends _$StoryTagCopyWithImpl<$Res, _$StoryTagImpl>
    implements _$$StoryTagImplCopyWith<$Res> {
  __$$StoryTagImplCopyWithImpl(
    _$StoryTagImpl _value,
    $Res Function(_$StoryTagImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of StoryTag
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? username = null,
    Object? name = freezed,
    Object? x = null,
    Object? y = null,
  }) {
    return _then(
      _$StoryTagImpl(
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        username: null == username
            ? _value.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        name: freezed == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String?,
        x: null == x
            ? _value.x
            : x // ignore: cast_nullable_to_non_nullable
                  as double,
        y: null == y
            ? _value.y
            : y // ignore: cast_nullable_to_non_nullable
                  as double,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$StoryTagImpl implements _StoryTag {
  const _$StoryTagImpl({
    required this.userId,
    required this.username,
    this.name,
    required this.x,
    required this.y,
  });

  factory _$StoryTagImpl.fromJson(Map<String, dynamic> json) =>
      _$$StoryTagImplFromJson(json);

  @override
  final String userId;
  @override
  final String username;
  @override
  final String? name;
  @override
  final double x;
  @override
  final double y;

  @override
  String toString() {
    return 'StoryTag(userId: $userId, username: $username, name: $name, x: $x, y: $y)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StoryTagImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.x, x) || other.x == x) &&
            (identical(other.y, y) || other.y == y));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, userId, username, name, x, y);

  /// Create a copy of StoryTag
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StoryTagImplCopyWith<_$StoryTagImpl> get copyWith =>
      __$$StoryTagImplCopyWithImpl<_$StoryTagImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StoryTagImplToJson(this);
  }
}

abstract class _StoryTag implements StoryTag {
  const factory _StoryTag({
    required final String userId,
    required final String username,
    final String? name,
    required final double x,
    required final double y,
  }) = _$StoryTagImpl;

  factory _StoryTag.fromJson(Map<String, dynamic> json) =
      _$StoryTagImpl.fromJson;

  @override
  String get userId;
  @override
  String get username;
  @override
  String? get name;
  @override
  double get x;
  @override
  double get y;

  /// Create a copy of StoryTag
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StoryTagImplCopyWith<_$StoryTagImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StoryReel _$StoryReelFromJson(Map<String, dynamic> json) {
  return _StoryReel.fromJson(json);
}

/// @nodoc
mixin _$StoryReel {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get username => throw _privateConstructorUsedError;
  String get userAvatarUrl => throw _privateConstructorUsedError;
  List<StoryItem> get stories => throw _privateConstructorUsedError;
  bool get isAllViewed => throw _privateConstructorUsedError;
  bool get isCloseFriend => throw _privateConstructorUsedError;

  /// Serializes this StoryReel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StoryReel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StoryReelCopyWith<StoryReel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StoryReelCopyWith<$Res> {
  factory $StoryReelCopyWith(StoryReel value, $Res Function(StoryReel) then) =
      _$StoryReelCopyWithImpl<$Res, StoryReel>;
  @useResult
  $Res call({
    String id,
    String userId,
    String username,
    String userAvatarUrl,
    List<StoryItem> stories,
    bool isAllViewed,
    bool isCloseFriend,
  });
}

/// @nodoc
class _$StoryReelCopyWithImpl<$Res, $Val extends StoryReel>
    implements $StoryReelCopyWith<$Res> {
  _$StoryReelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StoryReel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? username = null,
    Object? userAvatarUrl = null,
    Object? stories = null,
    Object? isAllViewed = null,
    Object? isCloseFriend = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            username: null == username
                ? _value.username
                : username // ignore: cast_nullable_to_non_nullable
                      as String,
            userAvatarUrl: null == userAvatarUrl
                ? _value.userAvatarUrl
                : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            stories: null == stories
                ? _value.stories
                : stories // ignore: cast_nullable_to_non_nullable
                      as List<StoryItem>,
            isAllViewed: null == isAllViewed
                ? _value.isAllViewed
                : isAllViewed // ignore: cast_nullable_to_non_nullable
                      as bool,
            isCloseFriend: null == isCloseFriend
                ? _value.isCloseFriend
                : isCloseFriend // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$StoryReelImplCopyWith<$Res>
    implements $StoryReelCopyWith<$Res> {
  factory _$$StoryReelImplCopyWith(
    _$StoryReelImpl value,
    $Res Function(_$StoryReelImpl) then,
  ) = __$$StoryReelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    String username,
    String userAvatarUrl,
    List<StoryItem> stories,
    bool isAllViewed,
    bool isCloseFriend,
  });
}

/// @nodoc
class __$$StoryReelImplCopyWithImpl<$Res>
    extends _$StoryReelCopyWithImpl<$Res, _$StoryReelImpl>
    implements _$$StoryReelImplCopyWith<$Res> {
  __$$StoryReelImplCopyWithImpl(
    _$StoryReelImpl _value,
    $Res Function(_$StoryReelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of StoryReel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? username = null,
    Object? userAvatarUrl = null,
    Object? stories = null,
    Object? isAllViewed = null,
    Object? isCloseFriend = null,
  }) {
    return _then(
      _$StoryReelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        username: null == username
            ? _value.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        userAvatarUrl: null == userAvatarUrl
            ? _value.userAvatarUrl
            : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        stories: null == stories
            ? _value._stories
            : stories // ignore: cast_nullable_to_non_nullable
                  as List<StoryItem>,
        isAllViewed: null == isAllViewed
            ? _value.isAllViewed
            : isAllViewed // ignore: cast_nullable_to_non_nullable
                  as bool,
        isCloseFriend: null == isCloseFriend
            ? _value.isCloseFriend
            : isCloseFriend // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$StoryReelImpl implements _StoryReel {
  const _$StoryReelImpl({
    required this.id,
    required this.userId,
    required this.username,
    required this.userAvatarUrl,
    required final List<StoryItem> stories,
    this.isAllViewed = false,
    this.isCloseFriend = false,
  }) : _stories = stories;

  factory _$StoryReelImpl.fromJson(Map<String, dynamic> json) =>
      _$$StoryReelImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String username;
  @override
  final String userAvatarUrl;
  final List<StoryItem> _stories;
  @override
  List<StoryItem> get stories {
    if (_stories is EqualUnmodifiableListView) return _stories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_stories);
  }

  @override
  @JsonKey()
  final bool isAllViewed;
  @override
  @JsonKey()
  final bool isCloseFriend;

  @override
  String toString() {
    return 'StoryReel(id: $id, userId: $userId, username: $username, userAvatarUrl: $userAvatarUrl, stories: $stories, isAllViewed: $isAllViewed, isCloseFriend: $isCloseFriend)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StoryReelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userAvatarUrl, userAvatarUrl) ||
                other.userAvatarUrl == userAvatarUrl) &&
            const DeepCollectionEquality().equals(other._stories, _stories) &&
            (identical(other.isAllViewed, isAllViewed) ||
                other.isAllViewed == isAllViewed) &&
            (identical(other.isCloseFriend, isCloseFriend) ||
                other.isCloseFriend == isCloseFriend));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    userId,
    username,
    userAvatarUrl,
    const DeepCollectionEquality().hash(_stories),
    isAllViewed,
    isCloseFriend,
  );

  /// Create a copy of StoryReel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StoryReelImplCopyWith<_$StoryReelImpl> get copyWith =>
      __$$StoryReelImplCopyWithImpl<_$StoryReelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StoryReelImplToJson(this);
  }
}

abstract class _StoryReel implements StoryReel {
  const factory _StoryReel({
    required final String id,
    required final String userId,
    required final String username,
    required final String userAvatarUrl,
    required final List<StoryItem> stories,
    final bool isAllViewed,
    final bool isCloseFriend,
  }) = _$StoryReelImpl;

  factory _StoryReel.fromJson(Map<String, dynamic> json) =
      _$StoryReelImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get username;
  @override
  String get userAvatarUrl;
  @override
  List<StoryItem> get stories;
  @override
  bool get isAllViewed;
  @override
  bool get isCloseFriend;

  /// Create a copy of StoryReel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StoryReelImplCopyWith<_$StoryReelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StoryItem _$StoryItemFromJson(Map<String, dynamic> json) {
  return _StoryItem.fromJson(json);
}

/// @nodoc
mixin _$StoryItem {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get mediaUrl => throw _privateConstructorUsedError;
  MediaType get mediaType => throw _privateConstructorUsedError;
  Duration get duration => throw _privateConstructorUsedError;
  DateTime get timestamp =>
      throw _privateConstructorUsedError; // Story metadata
  String? get textOverlay => throw _privateConstructorUsedError;
  int? get textColor => throw _privateConstructorUsedError;
  double? get textSize => throw _privateConstructorUsedError;
  Map<String, double>? get textPosition => throw _privateConstructorUsedError;
  int? get backgroundColor => throw _privateConstructorUsedError;
  String? get filter => throw _privateConstructorUsedError;
  List<Map<String, dynamic>>? get drawingPoints =>
      throw _privateConstructorUsedError;
  int? get drawingColor => throw _privateConstructorUsedError;
  double? get drawingWidth => throw _privateConstructorUsedError;
  List<TextElement> get textElements => throw _privateConstructorUsedError;
  List<StoryTag>? get tags => throw _privateConstructorUsedError;
  Map<String, dynamic>? get music => throw _privateConstructorUsedError;
  String? get musicArtist => throw _privateConstructorUsedError;
  Map<String, dynamic>? get location => throw _privateConstructorUsedError;
  String? get privacy => throw _privateConstructorUsedError;
  bool? get isPublic => throw _privateConstructorUsedError;
  bool? get isSeen => throw _privateConstructorUsedError;
  bool? get isCloseFriend => throw _privateConstructorUsedError;

  /// Serializes this StoryItem to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StoryItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StoryItemCopyWith<StoryItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StoryItemCopyWith<$Res> {
  factory $StoryItemCopyWith(StoryItem value, $Res Function(StoryItem) then) =
      _$StoryItemCopyWithImpl<$Res, StoryItem>;
  @useResult
  $Res call({
    String id,
    String userId,
    String mediaUrl,
    MediaType mediaType,
    Duration duration,
    DateTime timestamp,
    String? textOverlay,
    int? textColor,
    double? textSize,
    Map<String, double>? textPosition,
    int? backgroundColor,
    String? filter,
    List<Map<String, dynamic>>? drawingPoints,
    int? drawingColor,
    double? drawingWidth,
    List<TextElement> textElements,
    List<StoryTag>? tags,
    Map<String, dynamic>? music,
    String? musicArtist,
    Map<String, dynamic>? location,
    String? privacy,
    bool? isPublic,
    bool? isSeen,
    bool? isCloseFriend,
  });
}

/// @nodoc
class _$StoryItemCopyWithImpl<$Res, $Val extends StoryItem>
    implements $StoryItemCopyWith<$Res> {
  _$StoryItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StoryItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? mediaUrl = null,
    Object? mediaType = null,
    Object? duration = null,
    Object? timestamp = null,
    Object? textOverlay = freezed,
    Object? textColor = freezed,
    Object? textSize = freezed,
    Object? textPosition = freezed,
    Object? backgroundColor = freezed,
    Object? filter = freezed,
    Object? drawingPoints = freezed,
    Object? drawingColor = freezed,
    Object? drawingWidth = freezed,
    Object? textElements = null,
    Object? tags = freezed,
    Object? music = freezed,
    Object? musicArtist = freezed,
    Object? location = freezed,
    Object? privacy = freezed,
    Object? isPublic = freezed,
    Object? isSeen = freezed,
    Object? isCloseFriend = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            mediaUrl: null == mediaUrl
                ? _value.mediaUrl
                : mediaUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            mediaType: null == mediaType
                ? _value.mediaType
                : mediaType // ignore: cast_nullable_to_non_nullable
                      as MediaType,
            duration: null == duration
                ? _value.duration
                : duration // ignore: cast_nullable_to_non_nullable
                      as Duration,
            timestamp: null == timestamp
                ? _value.timestamp
                : timestamp // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            textOverlay: freezed == textOverlay
                ? _value.textOverlay
                : textOverlay // ignore: cast_nullable_to_non_nullable
                      as String?,
            textColor: freezed == textColor
                ? _value.textColor
                : textColor // ignore: cast_nullable_to_non_nullable
                      as int?,
            textSize: freezed == textSize
                ? _value.textSize
                : textSize // ignore: cast_nullable_to_non_nullable
                      as double?,
            textPosition: freezed == textPosition
                ? _value.textPosition
                : textPosition // ignore: cast_nullable_to_non_nullable
                      as Map<String, double>?,
            backgroundColor: freezed == backgroundColor
                ? _value.backgroundColor
                : backgroundColor // ignore: cast_nullable_to_non_nullable
                      as int?,
            filter: freezed == filter
                ? _value.filter
                : filter // ignore: cast_nullable_to_non_nullable
                      as String?,
            drawingPoints: freezed == drawingPoints
                ? _value.drawingPoints
                : drawingPoints // ignore: cast_nullable_to_non_nullable
                      as List<Map<String, dynamic>>?,
            drawingColor: freezed == drawingColor
                ? _value.drawingColor
                : drawingColor // ignore: cast_nullable_to_non_nullable
                      as int?,
            drawingWidth: freezed == drawingWidth
                ? _value.drawingWidth
                : drawingWidth // ignore: cast_nullable_to_non_nullable
                      as double?,
            textElements: null == textElements
                ? _value.textElements
                : textElements // ignore: cast_nullable_to_non_nullable
                      as List<TextElement>,
            tags: freezed == tags
                ? _value.tags
                : tags // ignore: cast_nullable_to_non_nullable
                      as List<StoryTag>?,
            music: freezed == music
                ? _value.music
                : music // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
            musicArtist: freezed == musicArtist
                ? _value.musicArtist
                : musicArtist // ignore: cast_nullable_to_non_nullable
                      as String?,
            location: freezed == location
                ? _value.location
                : location // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
            privacy: freezed == privacy
                ? _value.privacy
                : privacy // ignore: cast_nullable_to_non_nullable
                      as String?,
            isPublic: freezed == isPublic
                ? _value.isPublic
                : isPublic // ignore: cast_nullable_to_non_nullable
                      as bool?,
            isSeen: freezed == isSeen
                ? _value.isSeen
                : isSeen // ignore: cast_nullable_to_non_nullable
                      as bool?,
            isCloseFriend: freezed == isCloseFriend
                ? _value.isCloseFriend
                : isCloseFriend // ignore: cast_nullable_to_non_nullable
                      as bool?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$StoryItemImplCopyWith<$Res>
    implements $StoryItemCopyWith<$Res> {
  factory _$$StoryItemImplCopyWith(
    _$StoryItemImpl value,
    $Res Function(_$StoryItemImpl) then,
  ) = __$$StoryItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    String mediaUrl,
    MediaType mediaType,
    Duration duration,
    DateTime timestamp,
    String? textOverlay,
    int? textColor,
    double? textSize,
    Map<String, double>? textPosition,
    int? backgroundColor,
    String? filter,
    List<Map<String, dynamic>>? drawingPoints,
    int? drawingColor,
    double? drawingWidth,
    List<TextElement> textElements,
    List<StoryTag>? tags,
    Map<String, dynamic>? music,
    String? musicArtist,
    Map<String, dynamic>? location,
    String? privacy,
    bool? isPublic,
    bool? isSeen,
    bool? isCloseFriend,
  });
}

/// @nodoc
class __$$StoryItemImplCopyWithImpl<$Res>
    extends _$StoryItemCopyWithImpl<$Res, _$StoryItemImpl>
    implements _$$StoryItemImplCopyWith<$Res> {
  __$$StoryItemImplCopyWithImpl(
    _$StoryItemImpl _value,
    $Res Function(_$StoryItemImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of StoryItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? mediaUrl = null,
    Object? mediaType = null,
    Object? duration = null,
    Object? timestamp = null,
    Object? textOverlay = freezed,
    Object? textColor = freezed,
    Object? textSize = freezed,
    Object? textPosition = freezed,
    Object? backgroundColor = freezed,
    Object? filter = freezed,
    Object? drawingPoints = freezed,
    Object? drawingColor = freezed,
    Object? drawingWidth = freezed,
    Object? textElements = null,
    Object? tags = freezed,
    Object? music = freezed,
    Object? musicArtist = freezed,
    Object? location = freezed,
    Object? privacy = freezed,
    Object? isPublic = freezed,
    Object? isSeen = freezed,
    Object? isCloseFriend = freezed,
  }) {
    return _then(
      _$StoryItemImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        mediaUrl: null == mediaUrl
            ? _value.mediaUrl
            : mediaUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        mediaType: null == mediaType
            ? _value.mediaType
            : mediaType // ignore: cast_nullable_to_non_nullable
                  as MediaType,
        duration: null == duration
            ? _value.duration
            : duration // ignore: cast_nullable_to_non_nullable
                  as Duration,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        textOverlay: freezed == textOverlay
            ? _value.textOverlay
            : textOverlay // ignore: cast_nullable_to_non_nullable
                  as String?,
        textColor: freezed == textColor
            ? _value.textColor
            : textColor // ignore: cast_nullable_to_non_nullable
                  as int?,
        textSize: freezed == textSize
            ? _value.textSize
            : textSize // ignore: cast_nullable_to_non_nullable
                  as double?,
        textPosition: freezed == textPosition
            ? _value._textPosition
            : textPosition // ignore: cast_nullable_to_non_nullable
                  as Map<String, double>?,
        backgroundColor: freezed == backgroundColor
            ? _value.backgroundColor
            : backgroundColor // ignore: cast_nullable_to_non_nullable
                  as int?,
        filter: freezed == filter
            ? _value.filter
            : filter // ignore: cast_nullable_to_non_nullable
                  as String?,
        drawingPoints: freezed == drawingPoints
            ? _value._drawingPoints
            : drawingPoints // ignore: cast_nullable_to_non_nullable
                  as List<Map<String, dynamic>>?,
        drawingColor: freezed == drawingColor
            ? _value.drawingColor
            : drawingColor // ignore: cast_nullable_to_non_nullable
                  as int?,
        drawingWidth: freezed == drawingWidth
            ? _value.drawingWidth
            : drawingWidth // ignore: cast_nullable_to_non_nullable
                  as double?,
        textElements: null == textElements
            ? _value._textElements
            : textElements // ignore: cast_nullable_to_non_nullable
                  as List<TextElement>,
        tags: freezed == tags
            ? _value._tags
            : tags // ignore: cast_nullable_to_non_nullable
                  as List<StoryTag>?,
        music: freezed == music
            ? _value._music
            : music // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
        musicArtist: freezed == musicArtist
            ? _value.musicArtist
            : musicArtist // ignore: cast_nullable_to_non_nullable
                  as String?,
        location: freezed == location
            ? _value._location
            : location // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
        privacy: freezed == privacy
            ? _value.privacy
            : privacy // ignore: cast_nullable_to_non_nullable
                  as String?,
        isPublic: freezed == isPublic
            ? _value.isPublic
            : isPublic // ignore: cast_nullable_to_non_nullable
                  as bool?,
        isSeen: freezed == isSeen
            ? _value.isSeen
            : isSeen // ignore: cast_nullable_to_non_nullable
                  as bool?,
        isCloseFriend: freezed == isCloseFriend
            ? _value.isCloseFriend
            : isCloseFriend // ignore: cast_nullable_to_non_nullable
                  as bool?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$StoryItemImpl implements _StoryItem {
  const _$StoryItemImpl({
    required this.id,
    required this.userId,
    required this.mediaUrl,
    required this.mediaType,
    required this.duration,
    required this.timestamp,
    this.textOverlay,
    this.textColor,
    this.textSize,
    final Map<String, double>? textPosition,
    this.backgroundColor,
    this.filter,
    final List<Map<String, dynamic>>? drawingPoints,
    this.drawingColor,
    this.drawingWidth,
    final List<TextElement> textElements = const [],
    final List<StoryTag>? tags,
    final Map<String, dynamic>? music,
    this.musicArtist,
    final Map<String, dynamic>? location,
    this.privacy,
    this.isPublic,
    this.isSeen,
    this.isCloseFriend,
  }) : _textPosition = textPosition,
       _drawingPoints = drawingPoints,
       _textElements = textElements,
       _tags = tags,
       _music = music,
       _location = location;

  factory _$StoryItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$StoryItemImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String mediaUrl;
  @override
  final MediaType mediaType;
  @override
  final Duration duration;
  @override
  final DateTime timestamp;
  // Story metadata
  @override
  final String? textOverlay;
  @override
  final int? textColor;
  @override
  final double? textSize;
  final Map<String, double>? _textPosition;
  @override
  Map<String, double>? get textPosition {
    final value = _textPosition;
    if (value == null) return null;
    if (_textPosition is EqualUnmodifiableMapView) return _textPosition;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final int? backgroundColor;
  @override
  final String? filter;
  final List<Map<String, dynamic>>? _drawingPoints;
  @override
  List<Map<String, dynamic>>? get drawingPoints {
    final value = _drawingPoints;
    if (value == null) return null;
    if (_drawingPoints is EqualUnmodifiableListView) return _drawingPoints;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? drawingColor;
  @override
  final double? drawingWidth;
  final List<TextElement> _textElements;
  @override
  @JsonKey()
  List<TextElement> get textElements {
    if (_textElements is EqualUnmodifiableListView) return _textElements;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_textElements);
  }

  final List<StoryTag>? _tags;
  @override
  List<StoryTag>? get tags {
    final value = _tags;
    if (value == null) return null;
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final Map<String, dynamic>? _music;
  @override
  Map<String, dynamic>? get music {
    final value = _music;
    if (value == null) return null;
    if (_music is EqualUnmodifiableMapView) return _music;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final String? musicArtist;
  final Map<String, dynamic>? _location;
  @override
  Map<String, dynamic>? get location {
    final value = _location;
    if (value == null) return null;
    if (_location is EqualUnmodifiableMapView) return _location;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final String? privacy;
  @override
  final bool? isPublic;
  @override
  final bool? isSeen;
  @override
  final bool? isCloseFriend;

  @override
  String toString() {
    return 'StoryItem(id: $id, userId: $userId, mediaUrl: $mediaUrl, mediaType: $mediaType, duration: $duration, timestamp: $timestamp, textOverlay: $textOverlay, textColor: $textColor, textSize: $textSize, textPosition: $textPosition, backgroundColor: $backgroundColor, filter: $filter, drawingPoints: $drawingPoints, drawingColor: $drawingColor, drawingWidth: $drawingWidth, textElements: $textElements, tags: $tags, music: $music, musicArtist: $musicArtist, location: $location, privacy: $privacy, isPublic: $isPublic, isSeen: $isSeen, isCloseFriend: $isCloseFriend)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StoryItemImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.mediaUrl, mediaUrl) ||
                other.mediaUrl == mediaUrl) &&
            (identical(other.mediaType, mediaType) ||
                other.mediaType == mediaType) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.textOverlay, textOverlay) ||
                other.textOverlay == textOverlay) &&
            (identical(other.textColor, textColor) ||
                other.textColor == textColor) &&
            (identical(other.textSize, textSize) ||
                other.textSize == textSize) &&
            const DeepCollectionEquality().equals(
              other._textPosition,
              _textPosition,
            ) &&
            (identical(other.backgroundColor, backgroundColor) ||
                other.backgroundColor == backgroundColor) &&
            (identical(other.filter, filter) || other.filter == filter) &&
            const DeepCollectionEquality().equals(
              other._drawingPoints,
              _drawingPoints,
            ) &&
            (identical(other.drawingColor, drawingColor) ||
                other.drawingColor == drawingColor) &&
            (identical(other.drawingWidth, drawingWidth) ||
                other.drawingWidth == drawingWidth) &&
            const DeepCollectionEquality().equals(
              other._textElements,
              _textElements,
            ) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            const DeepCollectionEquality().equals(other._music, _music) &&
            (identical(other.musicArtist, musicArtist) ||
                other.musicArtist == musicArtist) &&
            const DeepCollectionEquality().equals(other._location, _location) &&
            (identical(other.privacy, privacy) || other.privacy == privacy) &&
            (identical(other.isPublic, isPublic) ||
                other.isPublic == isPublic) &&
            (identical(other.isSeen, isSeen) || other.isSeen == isSeen) &&
            (identical(other.isCloseFriend, isCloseFriend) ||
                other.isCloseFriend == isCloseFriend));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    userId,
    mediaUrl,
    mediaType,
    duration,
    timestamp,
    textOverlay,
    textColor,
    textSize,
    const DeepCollectionEquality().hash(_textPosition),
    backgroundColor,
    filter,
    const DeepCollectionEquality().hash(_drawingPoints),
    drawingColor,
    drawingWidth,
    const DeepCollectionEquality().hash(_textElements),
    const DeepCollectionEquality().hash(_tags),
    const DeepCollectionEquality().hash(_music),
    musicArtist,
    const DeepCollectionEquality().hash(_location),
    privacy,
    isPublic,
    isSeen,
    isCloseFriend,
  ]);

  /// Create a copy of StoryItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StoryItemImplCopyWith<_$StoryItemImpl> get copyWith =>
      __$$StoryItemImplCopyWithImpl<_$StoryItemImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StoryItemImplToJson(this);
  }
}

abstract class _StoryItem implements StoryItem {
  const factory _StoryItem({
    required final String id,
    required final String userId,
    required final String mediaUrl,
    required final MediaType mediaType,
    required final Duration duration,
    required final DateTime timestamp,
    final String? textOverlay,
    final int? textColor,
    final double? textSize,
    final Map<String, double>? textPosition,
    final int? backgroundColor,
    final String? filter,
    final List<Map<String, dynamic>>? drawingPoints,
    final int? drawingColor,
    final double? drawingWidth,
    final List<TextElement> textElements,
    final List<StoryTag>? tags,
    final Map<String, dynamic>? music,
    final String? musicArtist,
    final Map<String, dynamic>? location,
    final String? privacy,
    final bool? isPublic,
    final bool? isSeen,
    final bool? isCloseFriend,
  }) = _$StoryItemImpl;

  factory _StoryItem.fromJson(Map<String, dynamic> json) =
      _$StoryItemImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get mediaUrl;
  @override
  MediaType get mediaType;
  @override
  Duration get duration;
  @override
  DateTime get timestamp; // Story metadata
  @override
  String? get textOverlay;
  @override
  int? get textColor;
  @override
  double? get textSize;
  @override
  Map<String, double>? get textPosition;
  @override
  int? get backgroundColor;
  @override
  String? get filter;
  @override
  List<Map<String, dynamic>>? get drawingPoints;
  @override
  int? get drawingColor;
  @override
  double? get drawingWidth;
  @override
  List<TextElement> get textElements;
  @override
  List<StoryTag>? get tags;
  @override
  Map<String, dynamic>? get music;
  @override
  String? get musicArtist;
  @override
  Map<String, dynamic>? get location;
  @override
  String? get privacy;
  @override
  bool? get isPublic;
  @override
  bool? get isSeen;
  @override
  bool? get isCloseFriend;

  /// Create a copy of StoryItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StoryItemImplCopyWith<_$StoryItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
