// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'story_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

Story _$StoryFromJson(Map<String, dynamic> json) {
  return _Story.fromJson(json);
}

/// @nodoc
mixin _$Story {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get userName => throw _privateConstructorUsedError;
  String get userAvatarUrl => throw _privateConstructorUsedError;
  String get mediaUrl => throw _privateConstructorUsedError;
  StoryMediaType get mediaType => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get expiresAt => throw _privateConstructorUsedError;
  List<String> get viewers => throw _privateConstructorUsedError;
  List<String> get allowedTo => throw _privateConstructorUsedError;
  List<StoryReaction> get reactions => throw _privateConstructorUsedError;
  List<StoryReply> get replies => throw _privateConstructorUsedError;
  StoryPrivacy get privacy => throw _privateConstructorUsedError;
  String? get caption => throw _privateConstructorUsedError;
  String? get location => throw _privateConstructorUsedError;
  List<String> get hashtags => throw _privateConstructorUsedError;
  List<String> get mentions => throw _privateConstructorUsedError;
  bool get isHighlighted => throw _privateConstructorUsedError;
  bool get isArchived => throw _privateConstructorUsedError;
  String? get musicUrl => throw _privateConstructorUsedError;
  String? get musicTitle => throw _privateConstructorUsedError;
  StoryType get storyType => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this Story to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Story
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StoryCopyWith<Story> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StoryCopyWith<$Res> {
  factory $StoryCopyWith(Story value, $Res Function(Story) then) =
      _$StoryCopyWithImpl<$Res, Story>;
  @useResult
  $Res call({
    String id,
    String userId,
    String userName,
    String userAvatarUrl,
    String mediaUrl,
    StoryMediaType mediaType,
    DateTime createdAt,
    DateTime expiresAt,
    List<String> viewers,
    List<String> allowedTo,
    List<StoryReaction> reactions,
    List<StoryReply> replies,
    StoryPrivacy privacy,
    String? caption,
    String? location,
    List<String> hashtags,
    List<String> mentions,
    bool isHighlighted,
    bool isArchived,
    String? musicUrl,
    String? musicTitle,
    StoryType storyType,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class _$StoryCopyWithImpl<$Res, $Val extends Story>
    implements $StoryCopyWith<$Res> {
  _$StoryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Story
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? userName = null,
    Object? userAvatarUrl = null,
    Object? mediaUrl = null,
    Object? mediaType = null,
    Object? createdAt = null,
    Object? expiresAt = null,
    Object? viewers = null,
    Object? allowedTo = null,
    Object? reactions = null,
    Object? replies = null,
    Object? privacy = null,
    Object? caption = freezed,
    Object? location = freezed,
    Object? hashtags = null,
    Object? mentions = null,
    Object? isHighlighted = null,
    Object? isArchived = null,
    Object? musicUrl = freezed,
    Object? musicTitle = freezed,
    Object? storyType = null,
    Object? metadata = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            userName: null == userName
                ? _value.userName
                : userName // ignore: cast_nullable_to_non_nullable
                      as String,
            userAvatarUrl: null == userAvatarUrl
                ? _value.userAvatarUrl
                : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            mediaUrl: null == mediaUrl
                ? _value.mediaUrl
                : mediaUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            mediaType: null == mediaType
                ? _value.mediaType
                : mediaType // ignore: cast_nullable_to_non_nullable
                      as StoryMediaType,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            expiresAt: null == expiresAt
                ? _value.expiresAt
                : expiresAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            viewers: null == viewers
                ? _value.viewers
                : viewers // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            allowedTo: null == allowedTo
                ? _value.allowedTo
                : allowedTo // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            reactions: null == reactions
                ? _value.reactions
                : reactions // ignore: cast_nullable_to_non_nullable
                      as List<StoryReaction>,
            replies: null == replies
                ? _value.replies
                : replies // ignore: cast_nullable_to_non_nullable
                      as List<StoryReply>,
            privacy: null == privacy
                ? _value.privacy
                : privacy // ignore: cast_nullable_to_non_nullable
                      as StoryPrivacy,
            caption: freezed == caption
                ? _value.caption
                : caption // ignore: cast_nullable_to_non_nullable
                      as String?,
            location: freezed == location
                ? _value.location
                : location // ignore: cast_nullable_to_non_nullable
                      as String?,
            hashtags: null == hashtags
                ? _value.hashtags
                : hashtags // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            mentions: null == mentions
                ? _value.mentions
                : mentions // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            isHighlighted: null == isHighlighted
                ? _value.isHighlighted
                : isHighlighted // ignore: cast_nullable_to_non_nullable
                      as bool,
            isArchived: null == isArchived
                ? _value.isArchived
                : isArchived // ignore: cast_nullable_to_non_nullable
                      as bool,
            musicUrl: freezed == musicUrl
                ? _value.musicUrl
                : musicUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            musicTitle: freezed == musicTitle
                ? _value.musicTitle
                : musicTitle // ignore: cast_nullable_to_non_nullable
                      as String?,
            storyType: null == storyType
                ? _value.storyType
                : storyType // ignore: cast_nullable_to_non_nullable
                      as StoryType,
            metadata: freezed == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$StoryImplCopyWith<$Res> implements $StoryCopyWith<$Res> {
  factory _$$StoryImplCopyWith(
    _$StoryImpl value,
    $Res Function(_$StoryImpl) then,
  ) = __$$StoryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    String userName,
    String userAvatarUrl,
    String mediaUrl,
    StoryMediaType mediaType,
    DateTime createdAt,
    DateTime expiresAt,
    List<String> viewers,
    List<String> allowedTo,
    List<StoryReaction> reactions,
    List<StoryReply> replies,
    StoryPrivacy privacy,
    String? caption,
    String? location,
    List<String> hashtags,
    List<String> mentions,
    bool isHighlighted,
    bool isArchived,
    String? musicUrl,
    String? musicTitle,
    StoryType storyType,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class __$$StoryImplCopyWithImpl<$Res>
    extends _$StoryCopyWithImpl<$Res, _$StoryImpl>
    implements _$$StoryImplCopyWith<$Res> {
  __$$StoryImplCopyWithImpl(
    _$StoryImpl _value,
    $Res Function(_$StoryImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Story
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? userName = null,
    Object? userAvatarUrl = null,
    Object? mediaUrl = null,
    Object? mediaType = null,
    Object? createdAt = null,
    Object? expiresAt = null,
    Object? viewers = null,
    Object? allowedTo = null,
    Object? reactions = null,
    Object? replies = null,
    Object? privacy = null,
    Object? caption = freezed,
    Object? location = freezed,
    Object? hashtags = null,
    Object? mentions = null,
    Object? isHighlighted = null,
    Object? isArchived = null,
    Object? musicUrl = freezed,
    Object? musicTitle = freezed,
    Object? storyType = null,
    Object? metadata = freezed,
  }) {
    return _then(
      _$StoryImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        userName: null == userName
            ? _value.userName
            : userName // ignore: cast_nullable_to_non_nullable
                  as String,
        userAvatarUrl: null == userAvatarUrl
            ? _value.userAvatarUrl
            : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        mediaUrl: null == mediaUrl
            ? _value.mediaUrl
            : mediaUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        mediaType: null == mediaType
            ? _value.mediaType
            : mediaType // ignore: cast_nullable_to_non_nullable
                  as StoryMediaType,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        expiresAt: null == expiresAt
            ? _value.expiresAt
            : expiresAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        viewers: null == viewers
            ? _value._viewers
            : viewers // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        allowedTo: null == allowedTo
            ? _value._allowedTo
            : allowedTo // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        reactions: null == reactions
            ? _value._reactions
            : reactions // ignore: cast_nullable_to_non_nullable
                  as List<StoryReaction>,
        replies: null == replies
            ? _value._replies
            : replies // ignore: cast_nullable_to_non_nullable
                  as List<StoryReply>,
        privacy: null == privacy
            ? _value.privacy
            : privacy // ignore: cast_nullable_to_non_nullable
                  as StoryPrivacy,
        caption: freezed == caption
            ? _value.caption
            : caption // ignore: cast_nullable_to_non_nullable
                  as String?,
        location: freezed == location
            ? _value.location
            : location // ignore: cast_nullable_to_non_nullable
                  as String?,
        hashtags: null == hashtags
            ? _value._hashtags
            : hashtags // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        mentions: null == mentions
            ? _value._mentions
            : mentions // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        isHighlighted: null == isHighlighted
            ? _value.isHighlighted
            : isHighlighted // ignore: cast_nullable_to_non_nullable
                  as bool,
        isArchived: null == isArchived
            ? _value.isArchived
            : isArchived // ignore: cast_nullable_to_non_nullable
                  as bool,
        musicUrl: freezed == musicUrl
            ? _value.musicUrl
            : musicUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        musicTitle: freezed == musicTitle
            ? _value.musicTitle
            : musicTitle // ignore: cast_nullable_to_non_nullable
                  as String?,
        storyType: null == storyType
            ? _value.storyType
            : storyType // ignore: cast_nullable_to_non_nullable
                  as StoryType,
        metadata: freezed == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$StoryImpl implements _Story {
  const _$StoryImpl({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userAvatarUrl,
    required this.mediaUrl,
    required this.mediaType,
    required this.createdAt,
    required this.expiresAt,
    final List<String> viewers = const [],
    final List<String> allowedTo = const [],
    final List<StoryReaction> reactions = const [],
    final List<StoryReply> replies = const [],
    this.privacy = StoryPrivacy.public,
    this.caption,
    this.location,
    final List<String> hashtags = const [],
    final List<String> mentions = const [],
    this.isHighlighted = false,
    this.isArchived = false,
    this.musicUrl,
    this.musicTitle,
    this.storyType = StoryType.regular,
    final Map<String, dynamic>? metadata,
  }) : _viewers = viewers,
       _allowedTo = allowedTo,
       _reactions = reactions,
       _replies = replies,
       _hashtags = hashtags,
       _mentions = mentions,
       _metadata = metadata;

  factory _$StoryImpl.fromJson(Map<String, dynamic> json) =>
      _$$StoryImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String userName;
  @override
  final String userAvatarUrl;
  @override
  final String mediaUrl;
  @override
  final StoryMediaType mediaType;
  @override
  final DateTime createdAt;
  @override
  final DateTime expiresAt;
  final List<String> _viewers;
  @override
  @JsonKey()
  List<String> get viewers {
    if (_viewers is EqualUnmodifiableListView) return _viewers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_viewers);
  }

  final List<String> _allowedTo;
  @override
  @JsonKey()
  List<String> get allowedTo {
    if (_allowedTo is EqualUnmodifiableListView) return _allowedTo;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_allowedTo);
  }

  final List<StoryReaction> _reactions;
  @override
  @JsonKey()
  List<StoryReaction> get reactions {
    if (_reactions is EqualUnmodifiableListView) return _reactions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_reactions);
  }

  final List<StoryReply> _replies;
  @override
  @JsonKey()
  List<StoryReply> get replies {
    if (_replies is EqualUnmodifiableListView) return _replies;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_replies);
  }

  @override
  @JsonKey()
  final StoryPrivacy privacy;
  @override
  final String? caption;
  @override
  final String? location;
  final List<String> _hashtags;
  @override
  @JsonKey()
  List<String> get hashtags {
    if (_hashtags is EqualUnmodifiableListView) return _hashtags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_hashtags);
  }

  final List<String> _mentions;
  @override
  @JsonKey()
  List<String> get mentions {
    if (_mentions is EqualUnmodifiableListView) return _mentions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_mentions);
  }

  @override
  @JsonKey()
  final bool isHighlighted;
  @override
  @JsonKey()
  final bool isArchived;
  @override
  final String? musicUrl;
  @override
  final String? musicTitle;
  @override
  @JsonKey()
  final StoryType storyType;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'Story(id: $id, userId: $userId, userName: $userName, userAvatarUrl: $userAvatarUrl, mediaUrl: $mediaUrl, mediaType: $mediaType, createdAt: $createdAt, expiresAt: $expiresAt, viewers: $viewers, allowedTo: $allowedTo, reactions: $reactions, replies: $replies, privacy: $privacy, caption: $caption, location: $location, hashtags: $hashtags, mentions: $mentions, isHighlighted: $isHighlighted, isArchived: $isArchived, musicUrl: $musicUrl, musicTitle: $musicTitle, storyType: $storyType, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StoryImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userName, userName) ||
                other.userName == userName) &&
            (identical(other.userAvatarUrl, userAvatarUrl) ||
                other.userAvatarUrl == userAvatarUrl) &&
            (identical(other.mediaUrl, mediaUrl) ||
                other.mediaUrl == mediaUrl) &&
            (identical(other.mediaType, mediaType) ||
                other.mediaType == mediaType) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt) &&
            const DeepCollectionEquality().equals(other._viewers, _viewers) &&
            const DeepCollectionEquality().equals(
              other._allowedTo,
              _allowedTo,
            ) &&
            const DeepCollectionEquality().equals(
              other._reactions,
              _reactions,
            ) &&
            const DeepCollectionEquality().equals(other._replies, _replies) &&
            (identical(other.privacy, privacy) || other.privacy == privacy) &&
            (identical(other.caption, caption) || other.caption == caption) &&
            (identical(other.location, location) ||
                other.location == location) &&
            const DeepCollectionEquality().equals(other._hashtags, _hashtags) &&
            const DeepCollectionEquality().equals(other._mentions, _mentions) &&
            (identical(other.isHighlighted, isHighlighted) ||
                other.isHighlighted == isHighlighted) &&
            (identical(other.isArchived, isArchived) ||
                other.isArchived == isArchived) &&
            (identical(other.musicUrl, musicUrl) ||
                other.musicUrl == musicUrl) &&
            (identical(other.musicTitle, musicTitle) ||
                other.musicTitle == musicTitle) &&
            (identical(other.storyType, storyType) ||
                other.storyType == storyType) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    userId,
    userName,
    userAvatarUrl,
    mediaUrl,
    mediaType,
    createdAt,
    expiresAt,
    const DeepCollectionEquality().hash(_viewers),
    const DeepCollectionEquality().hash(_allowedTo),
    const DeepCollectionEquality().hash(_reactions),
    const DeepCollectionEquality().hash(_replies),
    privacy,
    caption,
    location,
    const DeepCollectionEquality().hash(_hashtags),
    const DeepCollectionEquality().hash(_mentions),
    isHighlighted,
    isArchived,
    musicUrl,
    musicTitle,
    storyType,
    const DeepCollectionEquality().hash(_metadata),
  ]);

  /// Create a copy of Story
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StoryImplCopyWith<_$StoryImpl> get copyWith =>
      __$$StoryImplCopyWithImpl<_$StoryImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StoryImplToJson(this);
  }
}

abstract class _Story implements Story {
  const factory _Story({
    required final String id,
    required final String userId,
    required final String userName,
    required final String userAvatarUrl,
    required final String mediaUrl,
    required final StoryMediaType mediaType,
    required final DateTime createdAt,
    required final DateTime expiresAt,
    final List<String> viewers,
    final List<String> allowedTo,
    final List<StoryReaction> reactions,
    final List<StoryReply> replies,
    final StoryPrivacy privacy,
    final String? caption,
    final String? location,
    final List<String> hashtags,
    final List<String> mentions,
    final bool isHighlighted,
    final bool isArchived,
    final String? musicUrl,
    final String? musicTitle,
    final StoryType storyType,
    final Map<String, dynamic>? metadata,
  }) = _$StoryImpl;

  factory _Story.fromJson(Map<String, dynamic> json) = _$StoryImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get userName;
  @override
  String get userAvatarUrl;
  @override
  String get mediaUrl;
  @override
  StoryMediaType get mediaType;
  @override
  DateTime get createdAt;
  @override
  DateTime get expiresAt;
  @override
  List<String> get viewers;
  @override
  List<String> get allowedTo;
  @override
  List<StoryReaction> get reactions;
  @override
  List<StoryReply> get replies;
  @override
  StoryPrivacy get privacy;
  @override
  String? get caption;
  @override
  String? get location;
  @override
  List<String> get hashtags;
  @override
  List<String> get mentions;
  @override
  bool get isHighlighted;
  @override
  bool get isArchived;
  @override
  String? get musicUrl;
  @override
  String? get musicTitle;
  @override
  StoryType get storyType;
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of Story
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StoryImplCopyWith<_$StoryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StoryReaction _$StoryReactionFromJson(Map<String, dynamic> json) {
  return _StoryReaction.fromJson(json);
}

/// @nodoc
mixin _$StoryReaction {
  String get userId => throw _privateConstructorUsedError;
  String get userName => throw _privateConstructorUsedError;
  String get userAvatarUrl => throw _privateConstructorUsedError;
  String get reactionType => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;

  /// Serializes this StoryReaction to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StoryReaction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StoryReactionCopyWith<StoryReaction> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StoryReactionCopyWith<$Res> {
  factory $StoryReactionCopyWith(
    StoryReaction value,
    $Res Function(StoryReaction) then,
  ) = _$StoryReactionCopyWithImpl<$Res, StoryReaction>;
  @useResult
  $Res call({
    String userId,
    String userName,
    String userAvatarUrl,
    String reactionType,
    DateTime timestamp,
  });
}

/// @nodoc
class _$StoryReactionCopyWithImpl<$Res, $Val extends StoryReaction>
    implements $StoryReactionCopyWith<$Res> {
  _$StoryReactionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StoryReaction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? userName = null,
    Object? userAvatarUrl = null,
    Object? reactionType = null,
    Object? timestamp = null,
  }) {
    return _then(
      _value.copyWith(
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            userName: null == userName
                ? _value.userName
                : userName // ignore: cast_nullable_to_non_nullable
                      as String,
            userAvatarUrl: null == userAvatarUrl
                ? _value.userAvatarUrl
                : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            reactionType: null == reactionType
                ? _value.reactionType
                : reactionType // ignore: cast_nullable_to_non_nullable
                      as String,
            timestamp: null == timestamp
                ? _value.timestamp
                : timestamp // ignore: cast_nullable_to_non_nullable
                      as DateTime,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$StoryReactionImplCopyWith<$Res>
    implements $StoryReactionCopyWith<$Res> {
  factory _$$StoryReactionImplCopyWith(
    _$StoryReactionImpl value,
    $Res Function(_$StoryReactionImpl) then,
  ) = __$$StoryReactionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String userId,
    String userName,
    String userAvatarUrl,
    String reactionType,
    DateTime timestamp,
  });
}

/// @nodoc
class __$$StoryReactionImplCopyWithImpl<$Res>
    extends _$StoryReactionCopyWithImpl<$Res, _$StoryReactionImpl>
    implements _$$StoryReactionImplCopyWith<$Res> {
  __$$StoryReactionImplCopyWithImpl(
    _$StoryReactionImpl _value,
    $Res Function(_$StoryReactionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of StoryReaction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? userName = null,
    Object? userAvatarUrl = null,
    Object? reactionType = null,
    Object? timestamp = null,
  }) {
    return _then(
      _$StoryReactionImpl(
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        userName: null == userName
            ? _value.userName
            : userName // ignore: cast_nullable_to_non_nullable
                  as String,
        userAvatarUrl: null == userAvatarUrl
            ? _value.userAvatarUrl
            : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        reactionType: null == reactionType
            ? _value.reactionType
            : reactionType // ignore: cast_nullable_to_non_nullable
                  as String,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$StoryReactionImpl implements _StoryReaction {
  const _$StoryReactionImpl({
    required this.userId,
    required this.userName,
    required this.userAvatarUrl,
    required this.reactionType,
    required this.timestamp,
  });

  factory _$StoryReactionImpl.fromJson(Map<String, dynamic> json) =>
      _$$StoryReactionImplFromJson(json);

  @override
  final String userId;
  @override
  final String userName;
  @override
  final String userAvatarUrl;
  @override
  final String reactionType;
  @override
  final DateTime timestamp;

  @override
  String toString() {
    return 'StoryReaction(userId: $userId, userName: $userName, userAvatarUrl: $userAvatarUrl, reactionType: $reactionType, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StoryReactionImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userName, userName) ||
                other.userName == userName) &&
            (identical(other.userAvatarUrl, userAvatarUrl) ||
                other.userAvatarUrl == userAvatarUrl) &&
            (identical(other.reactionType, reactionType) ||
                other.reactionType == reactionType) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    userId,
    userName,
    userAvatarUrl,
    reactionType,
    timestamp,
  );

  /// Create a copy of StoryReaction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StoryReactionImplCopyWith<_$StoryReactionImpl> get copyWith =>
      __$$StoryReactionImplCopyWithImpl<_$StoryReactionImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StoryReactionImplToJson(this);
  }
}

abstract class _StoryReaction implements StoryReaction {
  const factory _StoryReaction({
    required final String userId,
    required final String userName,
    required final String userAvatarUrl,
    required final String reactionType,
    required final DateTime timestamp,
  }) = _$StoryReactionImpl;

  factory _StoryReaction.fromJson(Map<String, dynamic> json) =
      _$StoryReactionImpl.fromJson;

  @override
  String get userId;
  @override
  String get userName;
  @override
  String get userAvatarUrl;
  @override
  String get reactionType;
  @override
  DateTime get timestamp;

  /// Create a copy of StoryReaction
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StoryReactionImplCopyWith<_$StoryReactionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StoryReply _$StoryReplyFromJson(Map<String, dynamic> json) {
  return _StoryReply.fromJson(json);
}

/// @nodoc
mixin _$StoryReply {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get userName => throw _privateConstructorUsedError;
  String get userAvatarUrl => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;
  bool get isRead => throw _privateConstructorUsedError;

  /// Serializes this StoryReply to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StoryReply
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StoryReplyCopyWith<StoryReply> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StoryReplyCopyWith<$Res> {
  factory $StoryReplyCopyWith(
    StoryReply value,
    $Res Function(StoryReply) then,
  ) = _$StoryReplyCopyWithImpl<$Res, StoryReply>;
  @useResult
  $Res call({
    String id,
    String userId,
    String userName,
    String userAvatarUrl,
    String message,
    DateTime timestamp,
    bool isRead,
  });
}

/// @nodoc
class _$StoryReplyCopyWithImpl<$Res, $Val extends StoryReply>
    implements $StoryReplyCopyWith<$Res> {
  _$StoryReplyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StoryReply
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? userName = null,
    Object? userAvatarUrl = null,
    Object? message = null,
    Object? timestamp = null,
    Object? isRead = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            userName: null == userName
                ? _value.userName
                : userName // ignore: cast_nullable_to_non_nullable
                      as String,
            userAvatarUrl: null == userAvatarUrl
                ? _value.userAvatarUrl
                : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            message: null == message
                ? _value.message
                : message // ignore: cast_nullable_to_non_nullable
                      as String,
            timestamp: null == timestamp
                ? _value.timestamp
                : timestamp // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isRead: null == isRead
                ? _value.isRead
                : isRead // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$StoryReplyImplCopyWith<$Res>
    implements $StoryReplyCopyWith<$Res> {
  factory _$$StoryReplyImplCopyWith(
    _$StoryReplyImpl value,
    $Res Function(_$StoryReplyImpl) then,
  ) = __$$StoryReplyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    String userName,
    String userAvatarUrl,
    String message,
    DateTime timestamp,
    bool isRead,
  });
}

/// @nodoc
class __$$StoryReplyImplCopyWithImpl<$Res>
    extends _$StoryReplyCopyWithImpl<$Res, _$StoryReplyImpl>
    implements _$$StoryReplyImplCopyWith<$Res> {
  __$$StoryReplyImplCopyWithImpl(
    _$StoryReplyImpl _value,
    $Res Function(_$StoryReplyImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of StoryReply
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? userName = null,
    Object? userAvatarUrl = null,
    Object? message = null,
    Object? timestamp = null,
    Object? isRead = null,
  }) {
    return _then(
      _$StoryReplyImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        userName: null == userName
            ? _value.userName
            : userName // ignore: cast_nullable_to_non_nullable
                  as String,
        userAvatarUrl: null == userAvatarUrl
            ? _value.userAvatarUrl
            : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        message: null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isRead: null == isRead
            ? _value.isRead
            : isRead // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$StoryReplyImpl implements _StoryReply {
  const _$StoryReplyImpl({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userAvatarUrl,
    required this.message,
    required this.timestamp,
    this.isRead = false,
  });

  factory _$StoryReplyImpl.fromJson(Map<String, dynamic> json) =>
      _$$StoryReplyImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String userName;
  @override
  final String userAvatarUrl;
  @override
  final String message;
  @override
  final DateTime timestamp;
  @override
  @JsonKey()
  final bool isRead;

  @override
  String toString() {
    return 'StoryReply(id: $id, userId: $userId, userName: $userName, userAvatarUrl: $userAvatarUrl, message: $message, timestamp: $timestamp, isRead: $isRead)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StoryReplyImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userName, userName) ||
                other.userName == userName) &&
            (identical(other.userAvatarUrl, userAvatarUrl) ||
                other.userAvatarUrl == userAvatarUrl) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.isRead, isRead) || other.isRead == isRead));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    userId,
    userName,
    userAvatarUrl,
    message,
    timestamp,
    isRead,
  );

  /// Create a copy of StoryReply
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StoryReplyImplCopyWith<_$StoryReplyImpl> get copyWith =>
      __$$StoryReplyImplCopyWithImpl<_$StoryReplyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StoryReplyImplToJson(this);
  }
}

abstract class _StoryReply implements StoryReply {
  const factory _StoryReply({
    required final String id,
    required final String userId,
    required final String userName,
    required final String userAvatarUrl,
    required final String message,
    required final DateTime timestamp,
    final bool isRead,
  }) = _$StoryReplyImpl;

  factory _StoryReply.fromJson(Map<String, dynamic> json) =
      _$StoryReplyImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get userName;
  @override
  String get userAvatarUrl;
  @override
  String get message;
  @override
  DateTime get timestamp;
  @override
  bool get isRead;

  /// Create a copy of StoryReply
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StoryReplyImplCopyWith<_$StoryReplyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StoryStats _$StoryStatsFromJson(Map<String, dynamic> json) {
  return _StoryStats.fromJson(json);
}

/// @nodoc
mixin _$StoryStats {
  int get viewCount => throw _privateConstructorUsedError;
  int get replyCount => throw _privateConstructorUsedError;
  int get reactionCount => throw _privateConstructorUsedError;
  int get shareCount => throw _privateConstructorUsedError;
  double get completionRate => throw _privateConstructorUsedError;
  double get skipRate => throw _privateConstructorUsedError;
  List<String> get topViewers => throw _privateConstructorUsedError;
  Map<String, int> get reactionBreakdown => throw _privateConstructorUsedError;

  /// Serializes this StoryStats to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StoryStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StoryStatsCopyWith<StoryStats> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StoryStatsCopyWith<$Res> {
  factory $StoryStatsCopyWith(
    StoryStats value,
    $Res Function(StoryStats) then,
  ) = _$StoryStatsCopyWithImpl<$Res, StoryStats>;
  @useResult
  $Res call({
    int viewCount,
    int replyCount,
    int reactionCount,
    int shareCount,
    double completionRate,
    double skipRate,
    List<String> topViewers,
    Map<String, int> reactionBreakdown,
  });
}

/// @nodoc
class _$StoryStatsCopyWithImpl<$Res, $Val extends StoryStats>
    implements $StoryStatsCopyWith<$Res> {
  _$StoryStatsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StoryStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? viewCount = null,
    Object? replyCount = null,
    Object? reactionCount = null,
    Object? shareCount = null,
    Object? completionRate = null,
    Object? skipRate = null,
    Object? topViewers = null,
    Object? reactionBreakdown = null,
  }) {
    return _then(
      _value.copyWith(
            viewCount: null == viewCount
                ? _value.viewCount
                : viewCount // ignore: cast_nullable_to_non_nullable
                      as int,
            replyCount: null == replyCount
                ? _value.replyCount
                : replyCount // ignore: cast_nullable_to_non_nullable
                      as int,
            reactionCount: null == reactionCount
                ? _value.reactionCount
                : reactionCount // ignore: cast_nullable_to_non_nullable
                      as int,
            shareCount: null == shareCount
                ? _value.shareCount
                : shareCount // ignore: cast_nullable_to_non_nullable
                      as int,
            completionRate: null == completionRate
                ? _value.completionRate
                : completionRate // ignore: cast_nullable_to_non_nullable
                      as double,
            skipRate: null == skipRate
                ? _value.skipRate
                : skipRate // ignore: cast_nullable_to_non_nullable
                      as double,
            topViewers: null == topViewers
                ? _value.topViewers
                : topViewers // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            reactionBreakdown: null == reactionBreakdown
                ? _value.reactionBreakdown
                : reactionBreakdown // ignore: cast_nullable_to_non_nullable
                      as Map<String, int>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$StoryStatsImplCopyWith<$Res>
    implements $StoryStatsCopyWith<$Res> {
  factory _$$StoryStatsImplCopyWith(
    _$StoryStatsImpl value,
    $Res Function(_$StoryStatsImpl) then,
  ) = __$$StoryStatsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int viewCount,
    int replyCount,
    int reactionCount,
    int shareCount,
    double completionRate,
    double skipRate,
    List<String> topViewers,
    Map<String, int> reactionBreakdown,
  });
}

/// @nodoc
class __$$StoryStatsImplCopyWithImpl<$Res>
    extends _$StoryStatsCopyWithImpl<$Res, _$StoryStatsImpl>
    implements _$$StoryStatsImplCopyWith<$Res> {
  __$$StoryStatsImplCopyWithImpl(
    _$StoryStatsImpl _value,
    $Res Function(_$StoryStatsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of StoryStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? viewCount = null,
    Object? replyCount = null,
    Object? reactionCount = null,
    Object? shareCount = null,
    Object? completionRate = null,
    Object? skipRate = null,
    Object? topViewers = null,
    Object? reactionBreakdown = null,
  }) {
    return _then(
      _$StoryStatsImpl(
        viewCount: null == viewCount
            ? _value.viewCount
            : viewCount // ignore: cast_nullable_to_non_nullable
                  as int,
        replyCount: null == replyCount
            ? _value.replyCount
            : replyCount // ignore: cast_nullable_to_non_nullable
                  as int,
        reactionCount: null == reactionCount
            ? _value.reactionCount
            : reactionCount // ignore: cast_nullable_to_non_nullable
                  as int,
        shareCount: null == shareCount
            ? _value.shareCount
            : shareCount // ignore: cast_nullable_to_non_nullable
                  as int,
        completionRate: null == completionRate
            ? _value.completionRate
            : completionRate // ignore: cast_nullable_to_non_nullable
                  as double,
        skipRate: null == skipRate
            ? _value.skipRate
            : skipRate // ignore: cast_nullable_to_non_nullable
                  as double,
        topViewers: null == topViewers
            ? _value._topViewers
            : topViewers // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        reactionBreakdown: null == reactionBreakdown
            ? _value._reactionBreakdown
            : reactionBreakdown // ignore: cast_nullable_to_non_nullable
                  as Map<String, int>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$StoryStatsImpl implements _StoryStats {
  const _$StoryStatsImpl({
    required this.viewCount,
    required this.replyCount,
    required this.reactionCount,
    required this.shareCount,
    required this.completionRate,
    required this.skipRate,
    required final List<String> topViewers,
    required final Map<String, int> reactionBreakdown,
  }) : _topViewers = topViewers,
       _reactionBreakdown = reactionBreakdown;

  factory _$StoryStatsImpl.fromJson(Map<String, dynamic> json) =>
      _$$StoryStatsImplFromJson(json);

  @override
  final int viewCount;
  @override
  final int replyCount;
  @override
  final int reactionCount;
  @override
  final int shareCount;
  @override
  final double completionRate;
  @override
  final double skipRate;
  final List<String> _topViewers;
  @override
  List<String> get topViewers {
    if (_topViewers is EqualUnmodifiableListView) return _topViewers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_topViewers);
  }

  final Map<String, int> _reactionBreakdown;
  @override
  Map<String, int> get reactionBreakdown {
    if (_reactionBreakdown is EqualUnmodifiableMapView)
      return _reactionBreakdown;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_reactionBreakdown);
  }

  @override
  String toString() {
    return 'StoryStats(viewCount: $viewCount, replyCount: $replyCount, reactionCount: $reactionCount, shareCount: $shareCount, completionRate: $completionRate, skipRate: $skipRate, topViewers: $topViewers, reactionBreakdown: $reactionBreakdown)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StoryStatsImpl &&
            (identical(other.viewCount, viewCount) ||
                other.viewCount == viewCount) &&
            (identical(other.replyCount, replyCount) ||
                other.replyCount == replyCount) &&
            (identical(other.reactionCount, reactionCount) ||
                other.reactionCount == reactionCount) &&
            (identical(other.shareCount, shareCount) ||
                other.shareCount == shareCount) &&
            (identical(other.completionRate, completionRate) ||
                other.completionRate == completionRate) &&
            (identical(other.skipRate, skipRate) ||
                other.skipRate == skipRate) &&
            const DeepCollectionEquality().equals(
              other._topViewers,
              _topViewers,
            ) &&
            const DeepCollectionEquality().equals(
              other._reactionBreakdown,
              _reactionBreakdown,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    viewCount,
    replyCount,
    reactionCount,
    shareCount,
    completionRate,
    skipRate,
    const DeepCollectionEquality().hash(_topViewers),
    const DeepCollectionEquality().hash(_reactionBreakdown),
  );

  /// Create a copy of StoryStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StoryStatsImplCopyWith<_$StoryStatsImpl> get copyWith =>
      __$$StoryStatsImplCopyWithImpl<_$StoryStatsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StoryStatsImplToJson(this);
  }
}

abstract class _StoryStats implements StoryStats {
  const factory _StoryStats({
    required final int viewCount,
    required final int replyCount,
    required final int reactionCount,
    required final int shareCount,
    required final double completionRate,
    required final double skipRate,
    required final List<String> topViewers,
    required final Map<String, int> reactionBreakdown,
  }) = _$StoryStatsImpl;

  factory _StoryStats.fromJson(Map<String, dynamic> json) =
      _$StoryStatsImpl.fromJson;

  @override
  int get viewCount;
  @override
  int get replyCount;
  @override
  int get reactionCount;
  @override
  int get shareCount;
  @override
  double get completionRate;
  @override
  double get skipRate;
  @override
  List<String> get topViewers;
  @override
  Map<String, int> get reactionBreakdown;

  /// Create a copy of StoryStats
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StoryStatsImplCopyWith<_$StoryStatsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
