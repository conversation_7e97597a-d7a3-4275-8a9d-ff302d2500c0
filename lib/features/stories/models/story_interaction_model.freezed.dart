// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'story_interaction_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

StoryReaction _$StoryReactionFromJson(Map<String, dynamic> json) {
  return _StoryReaction.fromJson(json);
}

/// @nodoc
mixin _$StoryReaction {
  String get id => throw _privateConstructorUsedError;
  String get storyId => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get username => throw _privateConstructorUsedError;
  String get userAvatarUrl => throw _privateConstructorUsedError;
  StoryReactionType get type => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;

  /// Serializes this StoryReaction to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StoryReaction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StoryReactionCopyWith<StoryReaction> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StoryReactionCopyWith<$Res> {
  factory $StoryReactionCopyWith(
    StoryReaction value,
    $Res Function(StoryReaction) then,
  ) = _$StoryReactionCopyWithImpl<$Res, StoryReaction>;
  @useResult
  $Res call({
    String id,
    String storyId,
    String userId,
    String username,
    String userAvatarUrl,
    StoryReactionType type,
    DateTime timestamp,
  });
}

/// @nodoc
class _$StoryReactionCopyWithImpl<$Res, $Val extends StoryReaction>
    implements $StoryReactionCopyWith<$Res> {
  _$StoryReactionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StoryReaction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? storyId = null,
    Object? userId = null,
    Object? username = null,
    Object? userAvatarUrl = null,
    Object? type = null,
    Object? timestamp = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            storyId: null == storyId
                ? _value.storyId
                : storyId // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            username: null == username
                ? _value.username
                : username // ignore: cast_nullable_to_non_nullable
                      as String,
            userAvatarUrl: null == userAvatarUrl
                ? _value.userAvatarUrl
                : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as StoryReactionType,
            timestamp: null == timestamp
                ? _value.timestamp
                : timestamp // ignore: cast_nullable_to_non_nullable
                      as DateTime,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$StoryReactionImplCopyWith<$Res>
    implements $StoryReactionCopyWith<$Res> {
  factory _$$StoryReactionImplCopyWith(
    _$StoryReactionImpl value,
    $Res Function(_$StoryReactionImpl) then,
  ) = __$$StoryReactionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String storyId,
    String userId,
    String username,
    String userAvatarUrl,
    StoryReactionType type,
    DateTime timestamp,
  });
}

/// @nodoc
class __$$StoryReactionImplCopyWithImpl<$Res>
    extends _$StoryReactionCopyWithImpl<$Res, _$StoryReactionImpl>
    implements _$$StoryReactionImplCopyWith<$Res> {
  __$$StoryReactionImplCopyWithImpl(
    _$StoryReactionImpl _value,
    $Res Function(_$StoryReactionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of StoryReaction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? storyId = null,
    Object? userId = null,
    Object? username = null,
    Object? userAvatarUrl = null,
    Object? type = null,
    Object? timestamp = null,
  }) {
    return _then(
      _$StoryReactionImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        storyId: null == storyId
            ? _value.storyId
            : storyId // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        username: null == username
            ? _value.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        userAvatarUrl: null == userAvatarUrl
            ? _value.userAvatarUrl
            : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as StoryReactionType,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$StoryReactionImpl implements _StoryReaction {
  const _$StoryReactionImpl({
    required this.id,
    required this.storyId,
    required this.userId,
    required this.username,
    required this.userAvatarUrl,
    required this.type,
    required this.timestamp,
  });

  factory _$StoryReactionImpl.fromJson(Map<String, dynamic> json) =>
      _$$StoryReactionImplFromJson(json);

  @override
  final String id;
  @override
  final String storyId;
  @override
  final String userId;
  @override
  final String username;
  @override
  final String userAvatarUrl;
  @override
  final StoryReactionType type;
  @override
  final DateTime timestamp;

  @override
  String toString() {
    return 'StoryReaction(id: $id, storyId: $storyId, userId: $userId, username: $username, userAvatarUrl: $userAvatarUrl, type: $type, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StoryReactionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.storyId, storyId) || other.storyId == storyId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userAvatarUrl, userAvatarUrl) ||
                other.userAvatarUrl == userAvatarUrl) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    storyId,
    userId,
    username,
    userAvatarUrl,
    type,
    timestamp,
  );

  /// Create a copy of StoryReaction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StoryReactionImplCopyWith<_$StoryReactionImpl> get copyWith =>
      __$$StoryReactionImplCopyWithImpl<_$StoryReactionImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StoryReactionImplToJson(this);
  }
}

abstract class _StoryReaction implements StoryReaction {
  const factory _StoryReaction({
    required final String id,
    required final String storyId,
    required final String userId,
    required final String username,
    required final String userAvatarUrl,
    required final StoryReactionType type,
    required final DateTime timestamp,
  }) = _$StoryReactionImpl;

  factory _StoryReaction.fromJson(Map<String, dynamic> json) =
      _$StoryReactionImpl.fromJson;

  @override
  String get id;
  @override
  String get storyId;
  @override
  String get userId;
  @override
  String get username;
  @override
  String get userAvatarUrl;
  @override
  StoryReactionType get type;
  @override
  DateTime get timestamp;

  /// Create a copy of StoryReaction
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StoryReactionImplCopyWith<_$StoryReactionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StoryReply _$StoryReplyFromJson(Map<String, dynamic> json) {
  return _StoryReply.fromJson(json);
}

/// @nodoc
mixin _$StoryReply {
  String get id => throw _privateConstructorUsedError;
  String get storyId => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get username => throw _privateConstructorUsedError;
  String get userAvatarUrl => throw _privateConstructorUsedError;
  String get content => throw _privateConstructorUsedError;
  String? get mediaUrl => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;
  bool get isRead => throw _privateConstructorUsedError;
  bool get isReplied => throw _privateConstructorUsedError;

  /// Serializes this StoryReply to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StoryReply
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StoryReplyCopyWith<StoryReply> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StoryReplyCopyWith<$Res> {
  factory $StoryReplyCopyWith(
    StoryReply value,
    $Res Function(StoryReply) then,
  ) = _$StoryReplyCopyWithImpl<$Res, StoryReply>;
  @useResult
  $Res call({
    String id,
    String storyId,
    String userId,
    String username,
    String userAvatarUrl,
    String content,
    String? mediaUrl,
    DateTime timestamp,
    bool isRead,
    bool isReplied,
  });
}

/// @nodoc
class _$StoryReplyCopyWithImpl<$Res, $Val extends StoryReply>
    implements $StoryReplyCopyWith<$Res> {
  _$StoryReplyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StoryReply
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? storyId = null,
    Object? userId = null,
    Object? username = null,
    Object? userAvatarUrl = null,
    Object? content = null,
    Object? mediaUrl = freezed,
    Object? timestamp = null,
    Object? isRead = null,
    Object? isReplied = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            storyId: null == storyId
                ? _value.storyId
                : storyId // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            username: null == username
                ? _value.username
                : username // ignore: cast_nullable_to_non_nullable
                      as String,
            userAvatarUrl: null == userAvatarUrl
                ? _value.userAvatarUrl
                : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            content: null == content
                ? _value.content
                : content // ignore: cast_nullable_to_non_nullable
                      as String,
            mediaUrl: freezed == mediaUrl
                ? _value.mediaUrl
                : mediaUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            timestamp: null == timestamp
                ? _value.timestamp
                : timestamp // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isRead: null == isRead
                ? _value.isRead
                : isRead // ignore: cast_nullable_to_non_nullable
                      as bool,
            isReplied: null == isReplied
                ? _value.isReplied
                : isReplied // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$StoryReplyImplCopyWith<$Res>
    implements $StoryReplyCopyWith<$Res> {
  factory _$$StoryReplyImplCopyWith(
    _$StoryReplyImpl value,
    $Res Function(_$StoryReplyImpl) then,
  ) = __$$StoryReplyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String storyId,
    String userId,
    String username,
    String userAvatarUrl,
    String content,
    String? mediaUrl,
    DateTime timestamp,
    bool isRead,
    bool isReplied,
  });
}

/// @nodoc
class __$$StoryReplyImplCopyWithImpl<$Res>
    extends _$StoryReplyCopyWithImpl<$Res, _$StoryReplyImpl>
    implements _$$StoryReplyImplCopyWith<$Res> {
  __$$StoryReplyImplCopyWithImpl(
    _$StoryReplyImpl _value,
    $Res Function(_$StoryReplyImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of StoryReply
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? storyId = null,
    Object? userId = null,
    Object? username = null,
    Object? userAvatarUrl = null,
    Object? content = null,
    Object? mediaUrl = freezed,
    Object? timestamp = null,
    Object? isRead = null,
    Object? isReplied = null,
  }) {
    return _then(
      _$StoryReplyImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        storyId: null == storyId
            ? _value.storyId
            : storyId // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        username: null == username
            ? _value.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        userAvatarUrl: null == userAvatarUrl
            ? _value.userAvatarUrl
            : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        content: null == content
            ? _value.content
            : content // ignore: cast_nullable_to_non_nullable
                  as String,
        mediaUrl: freezed == mediaUrl
            ? _value.mediaUrl
            : mediaUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isRead: null == isRead
            ? _value.isRead
            : isRead // ignore: cast_nullable_to_non_nullable
                  as bool,
        isReplied: null == isReplied
            ? _value.isReplied
            : isReplied // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$StoryReplyImpl implements _StoryReply {
  const _$StoryReplyImpl({
    required this.id,
    required this.storyId,
    required this.userId,
    required this.username,
    required this.userAvatarUrl,
    required this.content,
    this.mediaUrl,
    required this.timestamp,
    this.isRead = false,
    this.isReplied = false,
  });

  factory _$StoryReplyImpl.fromJson(Map<String, dynamic> json) =>
      _$$StoryReplyImplFromJson(json);

  @override
  final String id;
  @override
  final String storyId;
  @override
  final String userId;
  @override
  final String username;
  @override
  final String userAvatarUrl;
  @override
  final String content;
  @override
  final String? mediaUrl;
  @override
  final DateTime timestamp;
  @override
  @JsonKey()
  final bool isRead;
  @override
  @JsonKey()
  final bool isReplied;

  @override
  String toString() {
    return 'StoryReply(id: $id, storyId: $storyId, userId: $userId, username: $username, userAvatarUrl: $userAvatarUrl, content: $content, mediaUrl: $mediaUrl, timestamp: $timestamp, isRead: $isRead, isReplied: $isReplied)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StoryReplyImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.storyId, storyId) || other.storyId == storyId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userAvatarUrl, userAvatarUrl) ||
                other.userAvatarUrl == userAvatarUrl) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.mediaUrl, mediaUrl) ||
                other.mediaUrl == mediaUrl) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.isRead, isRead) || other.isRead == isRead) &&
            (identical(other.isReplied, isReplied) ||
                other.isReplied == isReplied));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    storyId,
    userId,
    username,
    userAvatarUrl,
    content,
    mediaUrl,
    timestamp,
    isRead,
    isReplied,
  );

  /// Create a copy of StoryReply
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StoryReplyImplCopyWith<_$StoryReplyImpl> get copyWith =>
      __$$StoryReplyImplCopyWithImpl<_$StoryReplyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StoryReplyImplToJson(this);
  }
}

abstract class _StoryReply implements StoryReply {
  const factory _StoryReply({
    required final String id,
    required final String storyId,
    required final String userId,
    required final String username,
    required final String userAvatarUrl,
    required final String content,
    final String? mediaUrl,
    required final DateTime timestamp,
    final bool isRead,
    final bool isReplied,
  }) = _$StoryReplyImpl;

  factory _StoryReply.fromJson(Map<String, dynamic> json) =
      _$StoryReplyImpl.fromJson;

  @override
  String get id;
  @override
  String get storyId;
  @override
  String get userId;
  @override
  String get username;
  @override
  String get userAvatarUrl;
  @override
  String get content;
  @override
  String? get mediaUrl;
  @override
  DateTime get timestamp;
  @override
  bool get isRead;
  @override
  bool get isReplied;

  /// Create a copy of StoryReply
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StoryReplyImplCopyWith<_$StoryReplyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StoryPrivacySettings _$StoryPrivacySettingsFromJson(Map<String, dynamic> json) {
  return _StoryPrivacySettings.fromJson(json);
}

/// @nodoc
mixin _$StoryPrivacySettings {
  StoryPrivacy get privacy => throw _privateConstructorUsedError;
  List<String> get allowedViewers => throw _privateConstructorUsedError;
  List<String> get blockedViewers => throw _privateConstructorUsedError;
  bool get allowReplies => throw _privateConstructorUsedError;
  bool get allowReactions => throw _privateConstructorUsedError;
  bool get allowScreenshots => throw _privateConstructorUsedError;
  bool get hideFromStory => throw _privateConstructorUsedError;
  bool get muteStory => throw _privateConstructorUsedError;

  /// Serializes this StoryPrivacySettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StoryPrivacySettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StoryPrivacySettingsCopyWith<StoryPrivacySettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StoryPrivacySettingsCopyWith<$Res> {
  factory $StoryPrivacySettingsCopyWith(
    StoryPrivacySettings value,
    $Res Function(StoryPrivacySettings) then,
  ) = _$StoryPrivacySettingsCopyWithImpl<$Res, StoryPrivacySettings>;
  @useResult
  $Res call({
    StoryPrivacy privacy,
    List<String> allowedViewers,
    List<String> blockedViewers,
    bool allowReplies,
    bool allowReactions,
    bool allowScreenshots,
    bool hideFromStory,
    bool muteStory,
  });
}

/// @nodoc
class _$StoryPrivacySettingsCopyWithImpl<
  $Res,
  $Val extends StoryPrivacySettings
>
    implements $StoryPrivacySettingsCopyWith<$Res> {
  _$StoryPrivacySettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StoryPrivacySettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? privacy = null,
    Object? allowedViewers = null,
    Object? blockedViewers = null,
    Object? allowReplies = null,
    Object? allowReactions = null,
    Object? allowScreenshots = null,
    Object? hideFromStory = null,
    Object? muteStory = null,
  }) {
    return _then(
      _value.copyWith(
            privacy: null == privacy
                ? _value.privacy
                : privacy // ignore: cast_nullable_to_non_nullable
                      as StoryPrivacy,
            allowedViewers: null == allowedViewers
                ? _value.allowedViewers
                : allowedViewers // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            blockedViewers: null == blockedViewers
                ? _value.blockedViewers
                : blockedViewers // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            allowReplies: null == allowReplies
                ? _value.allowReplies
                : allowReplies // ignore: cast_nullable_to_non_nullable
                      as bool,
            allowReactions: null == allowReactions
                ? _value.allowReactions
                : allowReactions // ignore: cast_nullable_to_non_nullable
                      as bool,
            allowScreenshots: null == allowScreenshots
                ? _value.allowScreenshots
                : allowScreenshots // ignore: cast_nullable_to_non_nullable
                      as bool,
            hideFromStory: null == hideFromStory
                ? _value.hideFromStory
                : hideFromStory // ignore: cast_nullable_to_non_nullable
                      as bool,
            muteStory: null == muteStory
                ? _value.muteStory
                : muteStory // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$StoryPrivacySettingsImplCopyWith<$Res>
    implements $StoryPrivacySettingsCopyWith<$Res> {
  factory _$$StoryPrivacySettingsImplCopyWith(
    _$StoryPrivacySettingsImpl value,
    $Res Function(_$StoryPrivacySettingsImpl) then,
  ) = __$$StoryPrivacySettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    StoryPrivacy privacy,
    List<String> allowedViewers,
    List<String> blockedViewers,
    bool allowReplies,
    bool allowReactions,
    bool allowScreenshots,
    bool hideFromStory,
    bool muteStory,
  });
}

/// @nodoc
class __$$StoryPrivacySettingsImplCopyWithImpl<$Res>
    extends _$StoryPrivacySettingsCopyWithImpl<$Res, _$StoryPrivacySettingsImpl>
    implements _$$StoryPrivacySettingsImplCopyWith<$Res> {
  __$$StoryPrivacySettingsImplCopyWithImpl(
    _$StoryPrivacySettingsImpl _value,
    $Res Function(_$StoryPrivacySettingsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of StoryPrivacySettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? privacy = null,
    Object? allowedViewers = null,
    Object? blockedViewers = null,
    Object? allowReplies = null,
    Object? allowReactions = null,
    Object? allowScreenshots = null,
    Object? hideFromStory = null,
    Object? muteStory = null,
  }) {
    return _then(
      _$StoryPrivacySettingsImpl(
        privacy: null == privacy
            ? _value.privacy
            : privacy // ignore: cast_nullable_to_non_nullable
                  as StoryPrivacy,
        allowedViewers: null == allowedViewers
            ? _value._allowedViewers
            : allowedViewers // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        blockedViewers: null == blockedViewers
            ? _value._blockedViewers
            : blockedViewers // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        allowReplies: null == allowReplies
            ? _value.allowReplies
            : allowReplies // ignore: cast_nullable_to_non_nullable
                  as bool,
        allowReactions: null == allowReactions
            ? _value.allowReactions
            : allowReactions // ignore: cast_nullable_to_non_nullable
                  as bool,
        allowScreenshots: null == allowScreenshots
            ? _value.allowScreenshots
            : allowScreenshots // ignore: cast_nullable_to_non_nullable
                  as bool,
        hideFromStory: null == hideFromStory
            ? _value.hideFromStory
            : hideFromStory // ignore: cast_nullable_to_non_nullable
                  as bool,
        muteStory: null == muteStory
            ? _value.muteStory
            : muteStory // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$StoryPrivacySettingsImpl implements _StoryPrivacySettings {
  const _$StoryPrivacySettingsImpl({
    this.privacy = StoryPrivacy.public,
    final List<String> allowedViewers = const [],
    final List<String> blockedViewers = const [],
    this.allowReplies = true,
    this.allowReactions = true,
    this.allowScreenshots = true,
    this.hideFromStory = false,
    this.muteStory = false,
  }) : _allowedViewers = allowedViewers,
       _blockedViewers = blockedViewers;

  factory _$StoryPrivacySettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$StoryPrivacySettingsImplFromJson(json);

  @override
  @JsonKey()
  final StoryPrivacy privacy;
  final List<String> _allowedViewers;
  @override
  @JsonKey()
  List<String> get allowedViewers {
    if (_allowedViewers is EqualUnmodifiableListView) return _allowedViewers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_allowedViewers);
  }

  final List<String> _blockedViewers;
  @override
  @JsonKey()
  List<String> get blockedViewers {
    if (_blockedViewers is EqualUnmodifiableListView) return _blockedViewers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_blockedViewers);
  }

  @override
  @JsonKey()
  final bool allowReplies;
  @override
  @JsonKey()
  final bool allowReactions;
  @override
  @JsonKey()
  final bool allowScreenshots;
  @override
  @JsonKey()
  final bool hideFromStory;
  @override
  @JsonKey()
  final bool muteStory;

  @override
  String toString() {
    return 'StoryPrivacySettings(privacy: $privacy, allowedViewers: $allowedViewers, blockedViewers: $blockedViewers, allowReplies: $allowReplies, allowReactions: $allowReactions, allowScreenshots: $allowScreenshots, hideFromStory: $hideFromStory, muteStory: $muteStory)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StoryPrivacySettingsImpl &&
            (identical(other.privacy, privacy) || other.privacy == privacy) &&
            const DeepCollectionEquality().equals(
              other._allowedViewers,
              _allowedViewers,
            ) &&
            const DeepCollectionEquality().equals(
              other._blockedViewers,
              _blockedViewers,
            ) &&
            (identical(other.allowReplies, allowReplies) ||
                other.allowReplies == allowReplies) &&
            (identical(other.allowReactions, allowReactions) ||
                other.allowReactions == allowReactions) &&
            (identical(other.allowScreenshots, allowScreenshots) ||
                other.allowScreenshots == allowScreenshots) &&
            (identical(other.hideFromStory, hideFromStory) ||
                other.hideFromStory == hideFromStory) &&
            (identical(other.muteStory, muteStory) ||
                other.muteStory == muteStory));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    privacy,
    const DeepCollectionEquality().hash(_allowedViewers),
    const DeepCollectionEquality().hash(_blockedViewers),
    allowReplies,
    allowReactions,
    allowScreenshots,
    hideFromStory,
    muteStory,
  );

  /// Create a copy of StoryPrivacySettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StoryPrivacySettingsImplCopyWith<_$StoryPrivacySettingsImpl>
  get copyWith =>
      __$$StoryPrivacySettingsImplCopyWithImpl<_$StoryPrivacySettingsImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$StoryPrivacySettingsImplToJson(this);
  }
}

abstract class _StoryPrivacySettings implements StoryPrivacySettings {
  const factory _StoryPrivacySettings({
    final StoryPrivacy privacy,
    final List<String> allowedViewers,
    final List<String> blockedViewers,
    final bool allowReplies,
    final bool allowReactions,
    final bool allowScreenshots,
    final bool hideFromStory,
    final bool muteStory,
  }) = _$StoryPrivacySettingsImpl;

  factory _StoryPrivacySettings.fromJson(Map<String, dynamic> json) =
      _$StoryPrivacySettingsImpl.fromJson;

  @override
  StoryPrivacy get privacy;
  @override
  List<String> get allowedViewers;
  @override
  List<String> get blockedViewers;
  @override
  bool get allowReplies;
  @override
  bool get allowReactions;
  @override
  bool get allowScreenshots;
  @override
  bool get hideFromStory;
  @override
  bool get muteStory;

  /// Create a copy of StoryPrivacySettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StoryPrivacySettingsImplCopyWith<_$StoryPrivacySettingsImpl>
  get copyWith => throw _privateConstructorUsedError;
}

StoryAnalytics _$StoryAnalyticsFromJson(Map<String, dynamic> json) {
  return _StoryAnalytics.fromJson(json);
}

/// @nodoc
mixin _$StoryAnalytics {
  String get storyId => throw _privateConstructorUsedError;
  int get viewCount => throw _privateConstructorUsedError;
  int get uniqueViewers => throw _privateConstructorUsedError;
  int get replyCount => throw _privateConstructorUsedError;
  int get reactionCount => throw _privateConstructorUsedError;
  Map<StoryReactionType, int> get reactionBreakdown =>
      throw _privateConstructorUsedError;
  Duration get averageViewDuration => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime? get expiresAt => throw _privateConstructorUsedError;
  List<String> get viewedBy => throw _privateConstructorUsedError;
  List<String> get repliedBy => throw _privateConstructorUsedError;
  List<String> get reactedBy => throw _privateConstructorUsedError;

  /// Serializes this StoryAnalytics to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StoryAnalytics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StoryAnalyticsCopyWith<StoryAnalytics> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StoryAnalyticsCopyWith<$Res> {
  factory $StoryAnalyticsCopyWith(
    StoryAnalytics value,
    $Res Function(StoryAnalytics) then,
  ) = _$StoryAnalyticsCopyWithImpl<$Res, StoryAnalytics>;
  @useResult
  $Res call({
    String storyId,
    int viewCount,
    int uniqueViewers,
    int replyCount,
    int reactionCount,
    Map<StoryReactionType, int> reactionBreakdown,
    Duration averageViewDuration,
    DateTime createdAt,
    DateTime? expiresAt,
    List<String> viewedBy,
    List<String> repliedBy,
    List<String> reactedBy,
  });
}

/// @nodoc
class _$StoryAnalyticsCopyWithImpl<$Res, $Val extends StoryAnalytics>
    implements $StoryAnalyticsCopyWith<$Res> {
  _$StoryAnalyticsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StoryAnalytics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? storyId = null,
    Object? viewCount = null,
    Object? uniqueViewers = null,
    Object? replyCount = null,
    Object? reactionCount = null,
    Object? reactionBreakdown = null,
    Object? averageViewDuration = null,
    Object? createdAt = null,
    Object? expiresAt = freezed,
    Object? viewedBy = null,
    Object? repliedBy = null,
    Object? reactedBy = null,
  }) {
    return _then(
      _value.copyWith(
            storyId: null == storyId
                ? _value.storyId
                : storyId // ignore: cast_nullable_to_non_nullable
                      as String,
            viewCount: null == viewCount
                ? _value.viewCount
                : viewCount // ignore: cast_nullable_to_non_nullable
                      as int,
            uniqueViewers: null == uniqueViewers
                ? _value.uniqueViewers
                : uniqueViewers // ignore: cast_nullable_to_non_nullable
                      as int,
            replyCount: null == replyCount
                ? _value.replyCount
                : replyCount // ignore: cast_nullable_to_non_nullable
                      as int,
            reactionCount: null == reactionCount
                ? _value.reactionCount
                : reactionCount // ignore: cast_nullable_to_non_nullable
                      as int,
            reactionBreakdown: null == reactionBreakdown
                ? _value.reactionBreakdown
                : reactionBreakdown // ignore: cast_nullable_to_non_nullable
                      as Map<StoryReactionType, int>,
            averageViewDuration: null == averageViewDuration
                ? _value.averageViewDuration
                : averageViewDuration // ignore: cast_nullable_to_non_nullable
                      as Duration,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            expiresAt: freezed == expiresAt
                ? _value.expiresAt
                : expiresAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            viewedBy: null == viewedBy
                ? _value.viewedBy
                : viewedBy // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            repliedBy: null == repliedBy
                ? _value.repliedBy
                : repliedBy // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            reactedBy: null == reactedBy
                ? _value.reactedBy
                : reactedBy // ignore: cast_nullable_to_non_nullable
                      as List<String>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$StoryAnalyticsImplCopyWith<$Res>
    implements $StoryAnalyticsCopyWith<$Res> {
  factory _$$StoryAnalyticsImplCopyWith(
    _$StoryAnalyticsImpl value,
    $Res Function(_$StoryAnalyticsImpl) then,
  ) = __$$StoryAnalyticsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String storyId,
    int viewCount,
    int uniqueViewers,
    int replyCount,
    int reactionCount,
    Map<StoryReactionType, int> reactionBreakdown,
    Duration averageViewDuration,
    DateTime createdAt,
    DateTime? expiresAt,
    List<String> viewedBy,
    List<String> repliedBy,
    List<String> reactedBy,
  });
}

/// @nodoc
class __$$StoryAnalyticsImplCopyWithImpl<$Res>
    extends _$StoryAnalyticsCopyWithImpl<$Res, _$StoryAnalyticsImpl>
    implements _$$StoryAnalyticsImplCopyWith<$Res> {
  __$$StoryAnalyticsImplCopyWithImpl(
    _$StoryAnalyticsImpl _value,
    $Res Function(_$StoryAnalyticsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of StoryAnalytics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? storyId = null,
    Object? viewCount = null,
    Object? uniqueViewers = null,
    Object? replyCount = null,
    Object? reactionCount = null,
    Object? reactionBreakdown = null,
    Object? averageViewDuration = null,
    Object? createdAt = null,
    Object? expiresAt = freezed,
    Object? viewedBy = null,
    Object? repliedBy = null,
    Object? reactedBy = null,
  }) {
    return _then(
      _$StoryAnalyticsImpl(
        storyId: null == storyId
            ? _value.storyId
            : storyId // ignore: cast_nullable_to_non_nullable
                  as String,
        viewCount: null == viewCount
            ? _value.viewCount
            : viewCount // ignore: cast_nullable_to_non_nullable
                  as int,
        uniqueViewers: null == uniqueViewers
            ? _value.uniqueViewers
            : uniqueViewers // ignore: cast_nullable_to_non_nullable
                  as int,
        replyCount: null == replyCount
            ? _value.replyCount
            : replyCount // ignore: cast_nullable_to_non_nullable
                  as int,
        reactionCount: null == reactionCount
            ? _value.reactionCount
            : reactionCount // ignore: cast_nullable_to_non_nullable
                  as int,
        reactionBreakdown: null == reactionBreakdown
            ? _value._reactionBreakdown
            : reactionBreakdown // ignore: cast_nullable_to_non_nullable
                  as Map<StoryReactionType, int>,
        averageViewDuration: null == averageViewDuration
            ? _value.averageViewDuration
            : averageViewDuration // ignore: cast_nullable_to_non_nullable
                  as Duration,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        expiresAt: freezed == expiresAt
            ? _value.expiresAt
            : expiresAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        viewedBy: null == viewedBy
            ? _value._viewedBy
            : viewedBy // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        repliedBy: null == repliedBy
            ? _value._repliedBy
            : repliedBy // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        reactedBy: null == reactedBy
            ? _value._reactedBy
            : reactedBy // ignore: cast_nullable_to_non_nullable
                  as List<String>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$StoryAnalyticsImpl implements _StoryAnalytics {
  const _$StoryAnalyticsImpl({
    required this.storyId,
    required this.viewCount,
    required this.uniqueViewers,
    required this.replyCount,
    required this.reactionCount,
    required final Map<StoryReactionType, int> reactionBreakdown,
    required this.averageViewDuration,
    required this.createdAt,
    required this.expiresAt,
    final List<String> viewedBy = const [],
    final List<String> repliedBy = const [],
    final List<String> reactedBy = const [],
  }) : _reactionBreakdown = reactionBreakdown,
       _viewedBy = viewedBy,
       _repliedBy = repliedBy,
       _reactedBy = reactedBy;

  factory _$StoryAnalyticsImpl.fromJson(Map<String, dynamic> json) =>
      _$$StoryAnalyticsImplFromJson(json);

  @override
  final String storyId;
  @override
  final int viewCount;
  @override
  final int uniqueViewers;
  @override
  final int replyCount;
  @override
  final int reactionCount;
  final Map<StoryReactionType, int> _reactionBreakdown;
  @override
  Map<StoryReactionType, int> get reactionBreakdown {
    if (_reactionBreakdown is EqualUnmodifiableMapView)
      return _reactionBreakdown;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_reactionBreakdown);
  }

  @override
  final Duration averageViewDuration;
  @override
  final DateTime createdAt;
  @override
  final DateTime? expiresAt;
  final List<String> _viewedBy;
  @override
  @JsonKey()
  List<String> get viewedBy {
    if (_viewedBy is EqualUnmodifiableListView) return _viewedBy;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_viewedBy);
  }

  final List<String> _repliedBy;
  @override
  @JsonKey()
  List<String> get repliedBy {
    if (_repliedBy is EqualUnmodifiableListView) return _repliedBy;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_repliedBy);
  }

  final List<String> _reactedBy;
  @override
  @JsonKey()
  List<String> get reactedBy {
    if (_reactedBy is EqualUnmodifiableListView) return _reactedBy;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_reactedBy);
  }

  @override
  String toString() {
    return 'StoryAnalytics(storyId: $storyId, viewCount: $viewCount, uniqueViewers: $uniqueViewers, replyCount: $replyCount, reactionCount: $reactionCount, reactionBreakdown: $reactionBreakdown, averageViewDuration: $averageViewDuration, createdAt: $createdAt, expiresAt: $expiresAt, viewedBy: $viewedBy, repliedBy: $repliedBy, reactedBy: $reactedBy)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StoryAnalyticsImpl &&
            (identical(other.storyId, storyId) || other.storyId == storyId) &&
            (identical(other.viewCount, viewCount) ||
                other.viewCount == viewCount) &&
            (identical(other.uniqueViewers, uniqueViewers) ||
                other.uniqueViewers == uniqueViewers) &&
            (identical(other.replyCount, replyCount) ||
                other.replyCount == replyCount) &&
            (identical(other.reactionCount, reactionCount) ||
                other.reactionCount == reactionCount) &&
            const DeepCollectionEquality().equals(
              other._reactionBreakdown,
              _reactionBreakdown,
            ) &&
            (identical(other.averageViewDuration, averageViewDuration) ||
                other.averageViewDuration == averageViewDuration) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt) &&
            const DeepCollectionEquality().equals(other._viewedBy, _viewedBy) &&
            const DeepCollectionEquality().equals(
              other._repliedBy,
              _repliedBy,
            ) &&
            const DeepCollectionEquality().equals(
              other._reactedBy,
              _reactedBy,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    storyId,
    viewCount,
    uniqueViewers,
    replyCount,
    reactionCount,
    const DeepCollectionEquality().hash(_reactionBreakdown),
    averageViewDuration,
    createdAt,
    expiresAt,
    const DeepCollectionEquality().hash(_viewedBy),
    const DeepCollectionEquality().hash(_repliedBy),
    const DeepCollectionEquality().hash(_reactedBy),
  );

  /// Create a copy of StoryAnalytics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StoryAnalyticsImplCopyWith<_$StoryAnalyticsImpl> get copyWith =>
      __$$StoryAnalyticsImplCopyWithImpl<_$StoryAnalyticsImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$StoryAnalyticsImplToJson(this);
  }
}

abstract class _StoryAnalytics implements StoryAnalytics {
  const factory _StoryAnalytics({
    required final String storyId,
    required final int viewCount,
    required final int uniqueViewers,
    required final int replyCount,
    required final int reactionCount,
    required final Map<StoryReactionType, int> reactionBreakdown,
    required final Duration averageViewDuration,
    required final DateTime createdAt,
    required final DateTime? expiresAt,
    final List<String> viewedBy,
    final List<String> repliedBy,
    final List<String> reactedBy,
  }) = _$StoryAnalyticsImpl;

  factory _StoryAnalytics.fromJson(Map<String, dynamic> json) =
      _$StoryAnalyticsImpl.fromJson;

  @override
  String get storyId;
  @override
  int get viewCount;
  @override
  int get uniqueViewers;
  @override
  int get replyCount;
  @override
  int get reactionCount;
  @override
  Map<StoryReactionType, int> get reactionBreakdown;
  @override
  Duration get averageViewDuration;
  @override
  DateTime get createdAt;
  @override
  DateTime? get expiresAt;
  @override
  List<String> get viewedBy;
  @override
  List<String> get repliedBy;
  @override
  List<String> get reactedBy;

  /// Create a copy of StoryAnalytics
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StoryAnalyticsImplCopyWith<_$StoryAnalyticsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StoryReport _$StoryReportFromJson(Map<String, dynamic> json) {
  return _StoryReport.fromJson(json);
}

/// @nodoc
mixin _$StoryReport {
  String get id => throw _privateConstructorUsedError;
  String get storyId => throw _privateConstructorUsedError;
  String get reportedBy => throw _privateConstructorUsedError;
  String get reason => throw _privateConstructorUsedError;
  String? get additionalInfo => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;
  String get status =>
      throw _privateConstructorUsedError; // pending, reviewed, resolved, dismissed
  String? get reviewedBy => throw _privateConstructorUsedError;
  DateTime? get reviewedAt => throw _privateConstructorUsedError;
  String? get action => throw _privateConstructorUsedError;

  /// Serializes this StoryReport to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StoryReport
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StoryReportCopyWith<StoryReport> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StoryReportCopyWith<$Res> {
  factory $StoryReportCopyWith(
    StoryReport value,
    $Res Function(StoryReport) then,
  ) = _$StoryReportCopyWithImpl<$Res, StoryReport>;
  @useResult
  $Res call({
    String id,
    String storyId,
    String reportedBy,
    String reason,
    String? additionalInfo,
    DateTime timestamp,
    String status,
    String? reviewedBy,
    DateTime? reviewedAt,
    String? action,
  });
}

/// @nodoc
class _$StoryReportCopyWithImpl<$Res, $Val extends StoryReport>
    implements $StoryReportCopyWith<$Res> {
  _$StoryReportCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StoryReport
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? storyId = null,
    Object? reportedBy = null,
    Object? reason = null,
    Object? additionalInfo = freezed,
    Object? timestamp = null,
    Object? status = null,
    Object? reviewedBy = freezed,
    Object? reviewedAt = freezed,
    Object? action = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            storyId: null == storyId
                ? _value.storyId
                : storyId // ignore: cast_nullable_to_non_nullable
                      as String,
            reportedBy: null == reportedBy
                ? _value.reportedBy
                : reportedBy // ignore: cast_nullable_to_non_nullable
                      as String,
            reason: null == reason
                ? _value.reason
                : reason // ignore: cast_nullable_to_non_nullable
                      as String,
            additionalInfo: freezed == additionalInfo
                ? _value.additionalInfo
                : additionalInfo // ignore: cast_nullable_to_non_nullable
                      as String?,
            timestamp: null == timestamp
                ? _value.timestamp
                : timestamp // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as String,
            reviewedBy: freezed == reviewedBy
                ? _value.reviewedBy
                : reviewedBy // ignore: cast_nullable_to_non_nullable
                      as String?,
            reviewedAt: freezed == reviewedAt
                ? _value.reviewedAt
                : reviewedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            action: freezed == action
                ? _value.action
                : action // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$StoryReportImplCopyWith<$Res>
    implements $StoryReportCopyWith<$Res> {
  factory _$$StoryReportImplCopyWith(
    _$StoryReportImpl value,
    $Res Function(_$StoryReportImpl) then,
  ) = __$$StoryReportImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String storyId,
    String reportedBy,
    String reason,
    String? additionalInfo,
    DateTime timestamp,
    String status,
    String? reviewedBy,
    DateTime? reviewedAt,
    String? action,
  });
}

/// @nodoc
class __$$StoryReportImplCopyWithImpl<$Res>
    extends _$StoryReportCopyWithImpl<$Res, _$StoryReportImpl>
    implements _$$StoryReportImplCopyWith<$Res> {
  __$$StoryReportImplCopyWithImpl(
    _$StoryReportImpl _value,
    $Res Function(_$StoryReportImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of StoryReport
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? storyId = null,
    Object? reportedBy = null,
    Object? reason = null,
    Object? additionalInfo = freezed,
    Object? timestamp = null,
    Object? status = null,
    Object? reviewedBy = freezed,
    Object? reviewedAt = freezed,
    Object? action = freezed,
  }) {
    return _then(
      _$StoryReportImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        storyId: null == storyId
            ? _value.storyId
            : storyId // ignore: cast_nullable_to_non_nullable
                  as String,
        reportedBy: null == reportedBy
            ? _value.reportedBy
            : reportedBy // ignore: cast_nullable_to_non_nullable
                  as String,
        reason: null == reason
            ? _value.reason
            : reason // ignore: cast_nullable_to_non_nullable
                  as String,
        additionalInfo: freezed == additionalInfo
            ? _value.additionalInfo
            : additionalInfo // ignore: cast_nullable_to_non_nullable
                  as String?,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as String,
        reviewedBy: freezed == reviewedBy
            ? _value.reviewedBy
            : reviewedBy // ignore: cast_nullable_to_non_nullable
                  as String?,
        reviewedAt: freezed == reviewedAt
            ? _value.reviewedAt
            : reviewedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        action: freezed == action
            ? _value.action
            : action // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$StoryReportImpl implements _StoryReport {
  const _$StoryReportImpl({
    required this.id,
    required this.storyId,
    required this.reportedBy,
    required this.reason,
    this.additionalInfo,
    required this.timestamp,
    this.status = 'pending',
    this.reviewedBy,
    this.reviewedAt,
    this.action,
  });

  factory _$StoryReportImpl.fromJson(Map<String, dynamic> json) =>
      _$$StoryReportImplFromJson(json);

  @override
  final String id;
  @override
  final String storyId;
  @override
  final String reportedBy;
  @override
  final String reason;
  @override
  final String? additionalInfo;
  @override
  final DateTime timestamp;
  @override
  @JsonKey()
  final String status;
  // pending, reviewed, resolved, dismissed
  @override
  final String? reviewedBy;
  @override
  final DateTime? reviewedAt;
  @override
  final String? action;

  @override
  String toString() {
    return 'StoryReport(id: $id, storyId: $storyId, reportedBy: $reportedBy, reason: $reason, additionalInfo: $additionalInfo, timestamp: $timestamp, status: $status, reviewedBy: $reviewedBy, reviewedAt: $reviewedAt, action: $action)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StoryReportImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.storyId, storyId) || other.storyId == storyId) &&
            (identical(other.reportedBy, reportedBy) ||
                other.reportedBy == reportedBy) &&
            (identical(other.reason, reason) || other.reason == reason) &&
            (identical(other.additionalInfo, additionalInfo) ||
                other.additionalInfo == additionalInfo) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.reviewedBy, reviewedBy) ||
                other.reviewedBy == reviewedBy) &&
            (identical(other.reviewedAt, reviewedAt) ||
                other.reviewedAt == reviewedAt) &&
            (identical(other.action, action) || other.action == action));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    storyId,
    reportedBy,
    reason,
    additionalInfo,
    timestamp,
    status,
    reviewedBy,
    reviewedAt,
    action,
  );

  /// Create a copy of StoryReport
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StoryReportImplCopyWith<_$StoryReportImpl> get copyWith =>
      __$$StoryReportImplCopyWithImpl<_$StoryReportImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StoryReportImplToJson(this);
  }
}

abstract class _StoryReport implements StoryReport {
  const factory _StoryReport({
    required final String id,
    required final String storyId,
    required final String reportedBy,
    required final String reason,
    final String? additionalInfo,
    required final DateTime timestamp,
    final String status,
    final String? reviewedBy,
    final DateTime? reviewedAt,
    final String? action,
  }) = _$StoryReportImpl;

  factory _StoryReport.fromJson(Map<String, dynamic> json) =
      _$StoryReportImpl.fromJson;

  @override
  String get id;
  @override
  String get storyId;
  @override
  String get reportedBy;
  @override
  String get reason;
  @override
  String? get additionalInfo;
  @override
  DateTime get timestamp;
  @override
  String get status; // pending, reviewed, resolved, dismissed
  @override
  String? get reviewedBy;
  @override
  DateTime? get reviewedAt;
  @override
  String? get action;

  /// Create a copy of StoryReport
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StoryReportImplCopyWith<_$StoryReportImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
