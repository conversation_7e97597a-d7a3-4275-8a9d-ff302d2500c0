// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'story_settings_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

StorySettings _$StorySettingsFromJson(Map<String, dynamic> json) {
  return _StorySettings.fromJson(json);
}

/// @nodoc
mixin _$StorySettings {
  String get id => throw _privateConstructorUsedError;
  StoryDuration get duration => throw _privateConstructorUsedError;
  StoryVisibility get visibility => throw _privateConstructorUsedError;
  List<String> get hiddenFromUserIds => throw _privateConstructorUsedError;
  bool get allowEditing => throw _privateConstructorUsedError;
  bool get allowComments => throw _privateConstructorUsedError;
  bool get allowReactions => throw _privateConstructorUsedError;
  bool get allowMentions => throw _privateConstructorUsedError;
  bool get allowMusic => throw _privateConstructorUsedError;
  bool get allowFilters => throw _privateConstructorUsedError;
  bool get allowTextOverlays => throw _privateConstructorUsedError;
  bool get allowVideoUpload => throw _privateConstructorUsedError;
  List<String> get allowedGroupIds => throw _privateConstructorUsedError;

  /// Serializes this StorySettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StorySettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StorySettingsCopyWith<StorySettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StorySettingsCopyWith<$Res> {
  factory $StorySettingsCopyWith(
    StorySettings value,
    $Res Function(StorySettings) then,
  ) = _$StorySettingsCopyWithImpl<$Res, StorySettings>;
  @useResult
  $Res call({
    String id,
    StoryDuration duration,
    StoryVisibility visibility,
    List<String> hiddenFromUserIds,
    bool allowEditing,
    bool allowComments,
    bool allowReactions,
    bool allowMentions,
    bool allowMusic,
    bool allowFilters,
    bool allowTextOverlays,
    bool allowVideoUpload,
    List<String> allowedGroupIds,
  });
}

/// @nodoc
class _$StorySettingsCopyWithImpl<$Res, $Val extends StorySettings>
    implements $StorySettingsCopyWith<$Res> {
  _$StorySettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StorySettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? duration = null,
    Object? visibility = null,
    Object? hiddenFromUserIds = null,
    Object? allowEditing = null,
    Object? allowComments = null,
    Object? allowReactions = null,
    Object? allowMentions = null,
    Object? allowMusic = null,
    Object? allowFilters = null,
    Object? allowTextOverlays = null,
    Object? allowVideoUpload = null,
    Object? allowedGroupIds = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            duration: null == duration
                ? _value.duration
                : duration // ignore: cast_nullable_to_non_nullable
                      as StoryDuration,
            visibility: null == visibility
                ? _value.visibility
                : visibility // ignore: cast_nullable_to_non_nullable
                      as StoryVisibility,
            hiddenFromUserIds: null == hiddenFromUserIds
                ? _value.hiddenFromUserIds
                : hiddenFromUserIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            allowEditing: null == allowEditing
                ? _value.allowEditing
                : allowEditing // ignore: cast_nullable_to_non_nullable
                      as bool,
            allowComments: null == allowComments
                ? _value.allowComments
                : allowComments // ignore: cast_nullable_to_non_nullable
                      as bool,
            allowReactions: null == allowReactions
                ? _value.allowReactions
                : allowReactions // ignore: cast_nullable_to_non_nullable
                      as bool,
            allowMentions: null == allowMentions
                ? _value.allowMentions
                : allowMentions // ignore: cast_nullable_to_non_nullable
                      as bool,
            allowMusic: null == allowMusic
                ? _value.allowMusic
                : allowMusic // ignore: cast_nullable_to_non_nullable
                      as bool,
            allowFilters: null == allowFilters
                ? _value.allowFilters
                : allowFilters // ignore: cast_nullable_to_non_nullable
                      as bool,
            allowTextOverlays: null == allowTextOverlays
                ? _value.allowTextOverlays
                : allowTextOverlays // ignore: cast_nullable_to_non_nullable
                      as bool,
            allowVideoUpload: null == allowVideoUpload
                ? _value.allowVideoUpload
                : allowVideoUpload // ignore: cast_nullable_to_non_nullable
                      as bool,
            allowedGroupIds: null == allowedGroupIds
                ? _value.allowedGroupIds
                : allowedGroupIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$StorySettingsImplCopyWith<$Res>
    implements $StorySettingsCopyWith<$Res> {
  factory _$$StorySettingsImplCopyWith(
    _$StorySettingsImpl value,
    $Res Function(_$StorySettingsImpl) then,
  ) = __$$StorySettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    StoryDuration duration,
    StoryVisibility visibility,
    List<String> hiddenFromUserIds,
    bool allowEditing,
    bool allowComments,
    bool allowReactions,
    bool allowMentions,
    bool allowMusic,
    bool allowFilters,
    bool allowTextOverlays,
    bool allowVideoUpload,
    List<String> allowedGroupIds,
  });
}

/// @nodoc
class __$$StorySettingsImplCopyWithImpl<$Res>
    extends _$StorySettingsCopyWithImpl<$Res, _$StorySettingsImpl>
    implements _$$StorySettingsImplCopyWith<$Res> {
  __$$StorySettingsImplCopyWithImpl(
    _$StorySettingsImpl _value,
    $Res Function(_$StorySettingsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of StorySettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? duration = null,
    Object? visibility = null,
    Object? hiddenFromUserIds = null,
    Object? allowEditing = null,
    Object? allowComments = null,
    Object? allowReactions = null,
    Object? allowMentions = null,
    Object? allowMusic = null,
    Object? allowFilters = null,
    Object? allowTextOverlays = null,
    Object? allowVideoUpload = null,
    Object? allowedGroupIds = null,
  }) {
    return _then(
      _$StorySettingsImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        duration: null == duration
            ? _value.duration
            : duration // ignore: cast_nullable_to_non_nullable
                  as StoryDuration,
        visibility: null == visibility
            ? _value.visibility
            : visibility // ignore: cast_nullable_to_non_nullable
                  as StoryVisibility,
        hiddenFromUserIds: null == hiddenFromUserIds
            ? _value._hiddenFromUserIds
            : hiddenFromUserIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        allowEditing: null == allowEditing
            ? _value.allowEditing
            : allowEditing // ignore: cast_nullable_to_non_nullable
                  as bool,
        allowComments: null == allowComments
            ? _value.allowComments
            : allowComments // ignore: cast_nullable_to_non_nullable
                  as bool,
        allowReactions: null == allowReactions
            ? _value.allowReactions
            : allowReactions // ignore: cast_nullable_to_non_nullable
                  as bool,
        allowMentions: null == allowMentions
            ? _value.allowMentions
            : allowMentions // ignore: cast_nullable_to_non_nullable
                  as bool,
        allowMusic: null == allowMusic
            ? _value.allowMusic
            : allowMusic // ignore: cast_nullable_to_non_nullable
                  as bool,
        allowFilters: null == allowFilters
            ? _value.allowFilters
            : allowFilters // ignore: cast_nullable_to_non_nullable
                  as bool,
        allowTextOverlays: null == allowTextOverlays
            ? _value.allowTextOverlays
            : allowTextOverlays // ignore: cast_nullable_to_non_nullable
                  as bool,
        allowVideoUpload: null == allowVideoUpload
            ? _value.allowVideoUpload
            : allowVideoUpload // ignore: cast_nullable_to_non_nullable
                  as bool,
        allowedGroupIds: null == allowedGroupIds
            ? _value._allowedGroupIds
            : allowedGroupIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$StorySettingsImpl implements _StorySettings {
  const _$StorySettingsImpl({
    required this.id,
    required this.duration,
    required this.visibility,
    required final List<String> hiddenFromUserIds,
    required this.allowEditing,
    required this.allowComments,
    required this.allowReactions,
    required this.allowMentions,
    required this.allowMusic,
    required this.allowFilters,
    required this.allowTextOverlays,
    required this.allowVideoUpload,
    required final List<String> allowedGroupIds,
  }) : _hiddenFromUserIds = hiddenFromUserIds,
       _allowedGroupIds = allowedGroupIds;

  factory _$StorySettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$StorySettingsImplFromJson(json);

  @override
  final String id;
  @override
  final StoryDuration duration;
  @override
  final StoryVisibility visibility;
  final List<String> _hiddenFromUserIds;
  @override
  List<String> get hiddenFromUserIds {
    if (_hiddenFromUserIds is EqualUnmodifiableListView)
      return _hiddenFromUserIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_hiddenFromUserIds);
  }

  @override
  final bool allowEditing;
  @override
  final bool allowComments;
  @override
  final bool allowReactions;
  @override
  final bool allowMentions;
  @override
  final bool allowMusic;
  @override
  final bool allowFilters;
  @override
  final bool allowTextOverlays;
  @override
  final bool allowVideoUpload;
  final List<String> _allowedGroupIds;
  @override
  List<String> get allowedGroupIds {
    if (_allowedGroupIds is EqualUnmodifiableListView) return _allowedGroupIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_allowedGroupIds);
  }

  @override
  String toString() {
    return 'StorySettings(id: $id, duration: $duration, visibility: $visibility, hiddenFromUserIds: $hiddenFromUserIds, allowEditing: $allowEditing, allowComments: $allowComments, allowReactions: $allowReactions, allowMentions: $allowMentions, allowMusic: $allowMusic, allowFilters: $allowFilters, allowTextOverlays: $allowTextOverlays, allowVideoUpload: $allowVideoUpload, allowedGroupIds: $allowedGroupIds)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StorySettingsImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.visibility, visibility) ||
                other.visibility == visibility) &&
            const DeepCollectionEquality().equals(
              other._hiddenFromUserIds,
              _hiddenFromUserIds,
            ) &&
            (identical(other.allowEditing, allowEditing) ||
                other.allowEditing == allowEditing) &&
            (identical(other.allowComments, allowComments) ||
                other.allowComments == allowComments) &&
            (identical(other.allowReactions, allowReactions) ||
                other.allowReactions == allowReactions) &&
            (identical(other.allowMentions, allowMentions) ||
                other.allowMentions == allowMentions) &&
            (identical(other.allowMusic, allowMusic) ||
                other.allowMusic == allowMusic) &&
            (identical(other.allowFilters, allowFilters) ||
                other.allowFilters == allowFilters) &&
            (identical(other.allowTextOverlays, allowTextOverlays) ||
                other.allowTextOverlays == allowTextOverlays) &&
            (identical(other.allowVideoUpload, allowVideoUpload) ||
                other.allowVideoUpload == allowVideoUpload) &&
            const DeepCollectionEquality().equals(
              other._allowedGroupIds,
              _allowedGroupIds,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    duration,
    visibility,
    const DeepCollectionEquality().hash(_hiddenFromUserIds),
    allowEditing,
    allowComments,
    allowReactions,
    allowMentions,
    allowMusic,
    allowFilters,
    allowTextOverlays,
    allowVideoUpload,
    const DeepCollectionEquality().hash(_allowedGroupIds),
  );

  /// Create a copy of StorySettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StorySettingsImplCopyWith<_$StorySettingsImpl> get copyWith =>
      __$$StorySettingsImplCopyWithImpl<_$StorySettingsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StorySettingsImplToJson(this);
  }
}

abstract class _StorySettings implements StorySettings {
  const factory _StorySettings({
    required final String id,
    required final StoryDuration duration,
    required final StoryVisibility visibility,
    required final List<String> hiddenFromUserIds,
    required final bool allowEditing,
    required final bool allowComments,
    required final bool allowReactions,
    required final bool allowMentions,
    required final bool allowMusic,
    required final bool allowFilters,
    required final bool allowTextOverlays,
    required final bool allowVideoUpload,
    required final List<String> allowedGroupIds,
  }) = _$StorySettingsImpl;

  factory _StorySettings.fromJson(Map<String, dynamic> json) =
      _$StorySettingsImpl.fromJson;

  @override
  String get id;
  @override
  StoryDuration get duration;
  @override
  StoryVisibility get visibility;
  @override
  List<String> get hiddenFromUserIds;
  @override
  bool get allowEditing;
  @override
  bool get allowComments;
  @override
  bool get allowReactions;
  @override
  bool get allowMentions;
  @override
  bool get allowMusic;
  @override
  bool get allowFilters;
  @override
  bool get allowTextOverlays;
  @override
  bool get allowVideoUpload;
  @override
  List<String> get allowedGroupIds;

  /// Create a copy of StorySettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StorySettingsImplCopyWith<_$StorySettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
