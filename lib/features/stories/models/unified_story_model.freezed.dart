// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'unified_story_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

UnifiedStory _$UnifiedStoryFromJson(Map<String, dynamic> json) {
  return _UnifiedStory.fromJson(json);
}

/// @nodoc
mixin _$UnifiedStory {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get userName => throw _privateConstructorUsedError;
  String get userAvatarUrl => throw _privateConstructorUsedError;
  String get mediaUrl => throw _privateConstructorUsedError;
  StoryMediaType get mediaType => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get expiresAt => throw _privateConstructorUsedError;
  Duration get duration => throw _privateConstructorUsedError;
  DateTime get timestamp =>
      throw _privateConstructorUsedError; // Privacy and visibility
  StoryPrivacy get privacy => throw _privateConstructorUsedError;
  StoryVisibility get visibility => throw _privateConstructorUsedError;
  List<String> get viewers => throw _privateConstructorUsedError;
  List<String> get allowedTo => throw _privateConstructorUsedError;
  List<String> get hiddenFromUserIds => throw _privateConstructorUsedError;
  List<String> get allowedGroupIds =>
      throw _privateConstructorUsedError; // Content metadata
  String? get caption => throw _privateConstructorUsedError;
  String? get textOverlay => throw _privateConstructorUsedError;
  int? get textColor => throw _privateConstructorUsedError;
  double? get textSize => throw _privateConstructorUsedError;
  Map<String, double>? get textPosition => throw _privateConstructorUsedError;
  int? get backgroundColor => throw _privateConstructorUsedError;
  String? get filter =>
      throw _privateConstructorUsedError; // Drawing and text elements
  List<DrawingPoint> get drawingPoints => throw _privateConstructorUsedError;
  int? get drawingColor => throw _privateConstructorUsedError;
  double? get drawingWidth => throw _privateConstructorUsedError;
  List<TextElement> get textElements =>
      throw _privateConstructorUsedError; // User interactions
  List<StoryTag> get tags => throw _privateConstructorUsedError;
  List<StoryReaction> get reactions => throw _privateConstructorUsedError;
  List<StoryReply> get replies => throw _privateConstructorUsedError;
  List<String> get hashtags => throw _privateConstructorUsedError;
  List<String> get mentions =>
      throw _privateConstructorUsedError; // Media and location
  Map<String, dynamic>? get music => throw _privateConstructorUsedError;
  String? get musicArtist => throw _privateConstructorUsedError;
  Map<String, dynamic>? get location => throw _privateConstructorUsedError;
  String? get locationName =>
      throw _privateConstructorUsedError; // Story features
  bool get isHighlighted => throw _privateConstructorUsedError;
  bool get isArchived => throw _privateConstructorUsedError;
  bool get isPublic => throw _privateConstructorUsedError;
  bool get isSeen => throw _privateConstructorUsedError;
  bool get isCloseFriend => throw _privateConstructorUsedError;
  StoryType get storyType =>
      throw _privateConstructorUsedError; // Analytics and tracking
  int get viewCount => throw _privateConstructorUsedError;
  int get replyCount => throw _privateConstructorUsedError;
  int get reactionCount => throw _privateConstructorUsedError;
  int get shareCount => throw _privateConstructorUsedError;
  double get completionRate => throw _privateConstructorUsedError;
  double get skipRate => throw _privateConstructorUsedError;
  List<String> get topViewers => throw _privateConstructorUsedError;
  Map<String, int> get reactionBreakdown =>
      throw _privateConstructorUsedError; // Additional metadata
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this UnifiedStory to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UnifiedStory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UnifiedStoryCopyWith<UnifiedStory> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UnifiedStoryCopyWith<$Res> {
  factory $UnifiedStoryCopyWith(
    UnifiedStory value,
    $Res Function(UnifiedStory) then,
  ) = _$UnifiedStoryCopyWithImpl<$Res, UnifiedStory>;
  @useResult
  $Res call({
    String id,
    String userId,
    String userName,
    String userAvatarUrl,
    String mediaUrl,
    StoryMediaType mediaType,
    DateTime createdAt,
    DateTime expiresAt,
    Duration duration,
    DateTime timestamp,
    StoryPrivacy privacy,
    StoryVisibility visibility,
    List<String> viewers,
    List<String> allowedTo,
    List<String> hiddenFromUserIds,
    List<String> allowedGroupIds,
    String? caption,
    String? textOverlay,
    int? textColor,
    double? textSize,
    Map<String, double>? textPosition,
    int? backgroundColor,
    String? filter,
    List<DrawingPoint> drawingPoints,
    int? drawingColor,
    double? drawingWidth,
    List<TextElement> textElements,
    List<StoryTag> tags,
    List<StoryReaction> reactions,
    List<StoryReply> replies,
    List<String> hashtags,
    List<String> mentions,
    Map<String, dynamic>? music,
    String? musicArtist,
    Map<String, dynamic>? location,
    String? locationName,
    bool isHighlighted,
    bool isArchived,
    bool isPublic,
    bool isSeen,
    bool isCloseFriend,
    StoryType storyType,
    int viewCount,
    int replyCount,
    int reactionCount,
    int shareCount,
    double completionRate,
    double skipRate,
    List<String> topViewers,
    Map<String, int> reactionBreakdown,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class _$UnifiedStoryCopyWithImpl<$Res, $Val extends UnifiedStory>
    implements $UnifiedStoryCopyWith<$Res> {
  _$UnifiedStoryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UnifiedStory
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? userName = null,
    Object? userAvatarUrl = null,
    Object? mediaUrl = null,
    Object? mediaType = null,
    Object? createdAt = null,
    Object? expiresAt = null,
    Object? duration = null,
    Object? timestamp = null,
    Object? privacy = null,
    Object? visibility = null,
    Object? viewers = null,
    Object? allowedTo = null,
    Object? hiddenFromUserIds = null,
    Object? allowedGroupIds = null,
    Object? caption = freezed,
    Object? textOverlay = freezed,
    Object? textColor = freezed,
    Object? textSize = freezed,
    Object? textPosition = freezed,
    Object? backgroundColor = freezed,
    Object? filter = freezed,
    Object? drawingPoints = null,
    Object? drawingColor = freezed,
    Object? drawingWidth = freezed,
    Object? textElements = null,
    Object? tags = null,
    Object? reactions = null,
    Object? replies = null,
    Object? hashtags = null,
    Object? mentions = null,
    Object? music = freezed,
    Object? musicArtist = freezed,
    Object? location = freezed,
    Object? locationName = freezed,
    Object? isHighlighted = null,
    Object? isArchived = null,
    Object? isPublic = null,
    Object? isSeen = null,
    Object? isCloseFriend = null,
    Object? storyType = null,
    Object? viewCount = null,
    Object? replyCount = null,
    Object? reactionCount = null,
    Object? shareCount = null,
    Object? completionRate = null,
    Object? skipRate = null,
    Object? topViewers = null,
    Object? reactionBreakdown = null,
    Object? metadata = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            userName: null == userName
                ? _value.userName
                : userName // ignore: cast_nullable_to_non_nullable
                      as String,
            userAvatarUrl: null == userAvatarUrl
                ? _value.userAvatarUrl
                : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            mediaUrl: null == mediaUrl
                ? _value.mediaUrl
                : mediaUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            mediaType: null == mediaType
                ? _value.mediaType
                : mediaType // ignore: cast_nullable_to_non_nullable
                      as StoryMediaType,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            expiresAt: null == expiresAt
                ? _value.expiresAt
                : expiresAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            duration: null == duration
                ? _value.duration
                : duration // ignore: cast_nullable_to_non_nullable
                      as Duration,
            timestamp: null == timestamp
                ? _value.timestamp
                : timestamp // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            privacy: null == privacy
                ? _value.privacy
                : privacy // ignore: cast_nullable_to_non_nullable
                      as StoryPrivacy,
            visibility: null == visibility
                ? _value.visibility
                : visibility // ignore: cast_nullable_to_non_nullable
                      as StoryVisibility,
            viewers: null == viewers
                ? _value.viewers
                : viewers // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            allowedTo: null == allowedTo
                ? _value.allowedTo
                : allowedTo // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            hiddenFromUserIds: null == hiddenFromUserIds
                ? _value.hiddenFromUserIds
                : hiddenFromUserIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            allowedGroupIds: null == allowedGroupIds
                ? _value.allowedGroupIds
                : allowedGroupIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            caption: freezed == caption
                ? _value.caption
                : caption // ignore: cast_nullable_to_non_nullable
                      as String?,
            textOverlay: freezed == textOverlay
                ? _value.textOverlay
                : textOverlay // ignore: cast_nullable_to_non_nullable
                      as String?,
            textColor: freezed == textColor
                ? _value.textColor
                : textColor // ignore: cast_nullable_to_non_nullable
                      as int?,
            textSize: freezed == textSize
                ? _value.textSize
                : textSize // ignore: cast_nullable_to_non_nullable
                      as double?,
            textPosition: freezed == textPosition
                ? _value.textPosition
                : textPosition // ignore: cast_nullable_to_non_nullable
                      as Map<String, double>?,
            backgroundColor: freezed == backgroundColor
                ? _value.backgroundColor
                : backgroundColor // ignore: cast_nullable_to_non_nullable
                      as int?,
            filter: freezed == filter
                ? _value.filter
                : filter // ignore: cast_nullable_to_non_nullable
                      as String?,
            drawingPoints: null == drawingPoints
                ? _value.drawingPoints
                : drawingPoints // ignore: cast_nullable_to_non_nullable
                      as List<DrawingPoint>,
            drawingColor: freezed == drawingColor
                ? _value.drawingColor
                : drawingColor // ignore: cast_nullable_to_non_nullable
                      as int?,
            drawingWidth: freezed == drawingWidth
                ? _value.drawingWidth
                : drawingWidth // ignore: cast_nullable_to_non_nullable
                      as double?,
            textElements: null == textElements
                ? _value.textElements
                : textElements // ignore: cast_nullable_to_non_nullable
                      as List<TextElement>,
            tags: null == tags
                ? _value.tags
                : tags // ignore: cast_nullable_to_non_nullable
                      as List<StoryTag>,
            reactions: null == reactions
                ? _value.reactions
                : reactions // ignore: cast_nullable_to_non_nullable
                      as List<StoryReaction>,
            replies: null == replies
                ? _value.replies
                : replies // ignore: cast_nullable_to_non_nullable
                      as List<StoryReply>,
            hashtags: null == hashtags
                ? _value.hashtags
                : hashtags // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            mentions: null == mentions
                ? _value.mentions
                : mentions // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            music: freezed == music
                ? _value.music
                : music // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
            musicArtist: freezed == musicArtist
                ? _value.musicArtist
                : musicArtist // ignore: cast_nullable_to_non_nullable
                      as String?,
            location: freezed == location
                ? _value.location
                : location // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
            locationName: freezed == locationName
                ? _value.locationName
                : locationName // ignore: cast_nullable_to_non_nullable
                      as String?,
            isHighlighted: null == isHighlighted
                ? _value.isHighlighted
                : isHighlighted // ignore: cast_nullable_to_non_nullable
                      as bool,
            isArchived: null == isArchived
                ? _value.isArchived
                : isArchived // ignore: cast_nullable_to_non_nullable
                      as bool,
            isPublic: null == isPublic
                ? _value.isPublic
                : isPublic // ignore: cast_nullable_to_non_nullable
                      as bool,
            isSeen: null == isSeen
                ? _value.isSeen
                : isSeen // ignore: cast_nullable_to_non_nullable
                      as bool,
            isCloseFriend: null == isCloseFriend
                ? _value.isCloseFriend
                : isCloseFriend // ignore: cast_nullable_to_non_nullable
                      as bool,
            storyType: null == storyType
                ? _value.storyType
                : storyType // ignore: cast_nullable_to_non_nullable
                      as StoryType,
            viewCount: null == viewCount
                ? _value.viewCount
                : viewCount // ignore: cast_nullable_to_non_nullable
                      as int,
            replyCount: null == replyCount
                ? _value.replyCount
                : replyCount // ignore: cast_nullable_to_non_nullable
                      as int,
            reactionCount: null == reactionCount
                ? _value.reactionCount
                : reactionCount // ignore: cast_nullable_to_non_nullable
                      as int,
            shareCount: null == shareCount
                ? _value.shareCount
                : shareCount // ignore: cast_nullable_to_non_nullable
                      as int,
            completionRate: null == completionRate
                ? _value.completionRate
                : completionRate // ignore: cast_nullable_to_non_nullable
                      as double,
            skipRate: null == skipRate
                ? _value.skipRate
                : skipRate // ignore: cast_nullable_to_non_nullable
                      as double,
            topViewers: null == topViewers
                ? _value.topViewers
                : topViewers // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            reactionBreakdown: null == reactionBreakdown
                ? _value.reactionBreakdown
                : reactionBreakdown // ignore: cast_nullable_to_non_nullable
                      as Map<String, int>,
            metadata: freezed == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$UnifiedStoryImplCopyWith<$Res>
    implements $UnifiedStoryCopyWith<$Res> {
  factory _$$UnifiedStoryImplCopyWith(
    _$UnifiedStoryImpl value,
    $Res Function(_$UnifiedStoryImpl) then,
  ) = __$$UnifiedStoryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    String userName,
    String userAvatarUrl,
    String mediaUrl,
    StoryMediaType mediaType,
    DateTime createdAt,
    DateTime expiresAt,
    Duration duration,
    DateTime timestamp,
    StoryPrivacy privacy,
    StoryVisibility visibility,
    List<String> viewers,
    List<String> allowedTo,
    List<String> hiddenFromUserIds,
    List<String> allowedGroupIds,
    String? caption,
    String? textOverlay,
    int? textColor,
    double? textSize,
    Map<String, double>? textPosition,
    int? backgroundColor,
    String? filter,
    List<DrawingPoint> drawingPoints,
    int? drawingColor,
    double? drawingWidth,
    List<TextElement> textElements,
    List<StoryTag> tags,
    List<StoryReaction> reactions,
    List<StoryReply> replies,
    List<String> hashtags,
    List<String> mentions,
    Map<String, dynamic>? music,
    String? musicArtist,
    Map<String, dynamic>? location,
    String? locationName,
    bool isHighlighted,
    bool isArchived,
    bool isPublic,
    bool isSeen,
    bool isCloseFriend,
    StoryType storyType,
    int viewCount,
    int replyCount,
    int reactionCount,
    int shareCount,
    double completionRate,
    double skipRate,
    List<String> topViewers,
    Map<String, int> reactionBreakdown,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class __$$UnifiedStoryImplCopyWithImpl<$Res>
    extends _$UnifiedStoryCopyWithImpl<$Res, _$UnifiedStoryImpl>
    implements _$$UnifiedStoryImplCopyWith<$Res> {
  __$$UnifiedStoryImplCopyWithImpl(
    _$UnifiedStoryImpl _value,
    $Res Function(_$UnifiedStoryImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of UnifiedStory
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? userName = null,
    Object? userAvatarUrl = null,
    Object? mediaUrl = null,
    Object? mediaType = null,
    Object? createdAt = null,
    Object? expiresAt = null,
    Object? duration = null,
    Object? timestamp = null,
    Object? privacy = null,
    Object? visibility = null,
    Object? viewers = null,
    Object? allowedTo = null,
    Object? hiddenFromUserIds = null,
    Object? allowedGroupIds = null,
    Object? caption = freezed,
    Object? textOverlay = freezed,
    Object? textColor = freezed,
    Object? textSize = freezed,
    Object? textPosition = freezed,
    Object? backgroundColor = freezed,
    Object? filter = freezed,
    Object? drawingPoints = null,
    Object? drawingColor = freezed,
    Object? drawingWidth = freezed,
    Object? textElements = null,
    Object? tags = null,
    Object? reactions = null,
    Object? replies = null,
    Object? hashtags = null,
    Object? mentions = null,
    Object? music = freezed,
    Object? musicArtist = freezed,
    Object? location = freezed,
    Object? locationName = freezed,
    Object? isHighlighted = null,
    Object? isArchived = null,
    Object? isPublic = null,
    Object? isSeen = null,
    Object? isCloseFriend = null,
    Object? storyType = null,
    Object? viewCount = null,
    Object? replyCount = null,
    Object? reactionCount = null,
    Object? shareCount = null,
    Object? completionRate = null,
    Object? skipRate = null,
    Object? topViewers = null,
    Object? reactionBreakdown = null,
    Object? metadata = freezed,
  }) {
    return _then(
      _$UnifiedStoryImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        userName: null == userName
            ? _value.userName
            : userName // ignore: cast_nullable_to_non_nullable
                  as String,
        userAvatarUrl: null == userAvatarUrl
            ? _value.userAvatarUrl
            : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        mediaUrl: null == mediaUrl
            ? _value.mediaUrl
            : mediaUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        mediaType: null == mediaType
            ? _value.mediaType
            : mediaType // ignore: cast_nullable_to_non_nullable
                  as StoryMediaType,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        expiresAt: null == expiresAt
            ? _value.expiresAt
            : expiresAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        duration: null == duration
            ? _value.duration
            : duration // ignore: cast_nullable_to_non_nullable
                  as Duration,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        privacy: null == privacy
            ? _value.privacy
            : privacy // ignore: cast_nullable_to_non_nullable
                  as StoryPrivacy,
        visibility: null == visibility
            ? _value.visibility
            : visibility // ignore: cast_nullable_to_non_nullable
                  as StoryVisibility,
        viewers: null == viewers
            ? _value._viewers
            : viewers // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        allowedTo: null == allowedTo
            ? _value._allowedTo
            : allowedTo // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        hiddenFromUserIds: null == hiddenFromUserIds
            ? _value._hiddenFromUserIds
            : hiddenFromUserIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        allowedGroupIds: null == allowedGroupIds
            ? _value._allowedGroupIds
            : allowedGroupIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        caption: freezed == caption
            ? _value.caption
            : caption // ignore: cast_nullable_to_non_nullable
                  as String?,
        textOverlay: freezed == textOverlay
            ? _value.textOverlay
            : textOverlay // ignore: cast_nullable_to_non_nullable
                  as String?,
        textColor: freezed == textColor
            ? _value.textColor
            : textColor // ignore: cast_nullable_to_non_nullable
                  as int?,
        textSize: freezed == textSize
            ? _value.textSize
            : textSize // ignore: cast_nullable_to_non_nullable
                  as double?,
        textPosition: freezed == textPosition
            ? _value._textPosition
            : textPosition // ignore: cast_nullable_to_non_nullable
                  as Map<String, double>?,
        backgroundColor: freezed == backgroundColor
            ? _value.backgroundColor
            : backgroundColor // ignore: cast_nullable_to_non_nullable
                  as int?,
        filter: freezed == filter
            ? _value.filter
            : filter // ignore: cast_nullable_to_non_nullable
                  as String?,
        drawingPoints: null == drawingPoints
            ? _value._drawingPoints
            : drawingPoints // ignore: cast_nullable_to_non_nullable
                  as List<DrawingPoint>,
        drawingColor: freezed == drawingColor
            ? _value.drawingColor
            : drawingColor // ignore: cast_nullable_to_non_nullable
                  as int?,
        drawingWidth: freezed == drawingWidth
            ? _value.drawingWidth
            : drawingWidth // ignore: cast_nullable_to_non_nullable
                  as double?,
        textElements: null == textElements
            ? _value._textElements
            : textElements // ignore: cast_nullable_to_non_nullable
                  as List<TextElement>,
        tags: null == tags
            ? _value._tags
            : tags // ignore: cast_nullable_to_non_nullable
                  as List<StoryTag>,
        reactions: null == reactions
            ? _value._reactions
            : reactions // ignore: cast_nullable_to_non_nullable
                  as List<StoryReaction>,
        replies: null == replies
            ? _value._replies
            : replies // ignore: cast_nullable_to_non_nullable
                  as List<StoryReply>,
        hashtags: null == hashtags
            ? _value._hashtags
            : hashtags // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        mentions: null == mentions
            ? _value._mentions
            : mentions // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        music: freezed == music
            ? _value._music
            : music // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
        musicArtist: freezed == musicArtist
            ? _value.musicArtist
            : musicArtist // ignore: cast_nullable_to_non_nullable
                  as String?,
        location: freezed == location
            ? _value._location
            : location // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
        locationName: freezed == locationName
            ? _value.locationName
            : locationName // ignore: cast_nullable_to_non_nullable
                  as String?,
        isHighlighted: null == isHighlighted
            ? _value.isHighlighted
            : isHighlighted // ignore: cast_nullable_to_non_nullable
                  as bool,
        isArchived: null == isArchived
            ? _value.isArchived
            : isArchived // ignore: cast_nullable_to_non_nullable
                  as bool,
        isPublic: null == isPublic
            ? _value.isPublic
            : isPublic // ignore: cast_nullable_to_non_nullable
                  as bool,
        isSeen: null == isSeen
            ? _value.isSeen
            : isSeen // ignore: cast_nullable_to_non_nullable
                  as bool,
        isCloseFriend: null == isCloseFriend
            ? _value.isCloseFriend
            : isCloseFriend // ignore: cast_nullable_to_non_nullable
                  as bool,
        storyType: null == storyType
            ? _value.storyType
            : storyType // ignore: cast_nullable_to_non_nullable
                  as StoryType,
        viewCount: null == viewCount
            ? _value.viewCount
            : viewCount // ignore: cast_nullable_to_non_nullable
                  as int,
        replyCount: null == replyCount
            ? _value.replyCount
            : replyCount // ignore: cast_nullable_to_non_nullable
                  as int,
        reactionCount: null == reactionCount
            ? _value.reactionCount
            : reactionCount // ignore: cast_nullable_to_non_nullable
                  as int,
        shareCount: null == shareCount
            ? _value.shareCount
            : shareCount // ignore: cast_nullable_to_non_nullable
                  as int,
        completionRate: null == completionRate
            ? _value.completionRate
            : completionRate // ignore: cast_nullable_to_non_nullable
                  as double,
        skipRate: null == skipRate
            ? _value.skipRate
            : skipRate // ignore: cast_nullable_to_non_nullable
                  as double,
        topViewers: null == topViewers
            ? _value._topViewers
            : topViewers // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        reactionBreakdown: null == reactionBreakdown
            ? _value._reactionBreakdown
            : reactionBreakdown // ignore: cast_nullable_to_non_nullable
                  as Map<String, int>,
        metadata: freezed == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$UnifiedStoryImpl implements _UnifiedStory {
  const _$UnifiedStoryImpl({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userAvatarUrl,
    required this.mediaUrl,
    required this.mediaType,
    required this.createdAt,
    required this.expiresAt,
    required this.duration,
    required this.timestamp,
    this.privacy = StoryPrivacy.public,
    this.visibility = StoryVisibility.public,
    final List<String> viewers = const [],
    final List<String> allowedTo = const [],
    final List<String> hiddenFromUserIds = const [],
    final List<String> allowedGroupIds = const [],
    this.caption,
    this.textOverlay,
    this.textColor,
    this.textSize,
    final Map<String, double>? textPosition,
    this.backgroundColor,
    this.filter,
    final List<DrawingPoint> drawingPoints = const [],
    this.drawingColor,
    this.drawingWidth,
    final List<TextElement> textElements = const [],
    final List<StoryTag> tags = const [],
    final List<StoryReaction> reactions = const [],
    final List<StoryReply> replies = const [],
    final List<String> hashtags = const [],
    final List<String> mentions = const [],
    final Map<String, dynamic>? music,
    this.musicArtist,
    final Map<String, dynamic>? location,
    this.locationName,
    this.isHighlighted = false,
    this.isArchived = false,
    this.isPublic = false,
    this.isSeen = false,
    this.isCloseFriend = false,
    this.storyType = StoryType.regular,
    this.viewCount = 0,
    this.replyCount = 0,
    this.reactionCount = 0,
    this.shareCount = 0,
    this.completionRate = 0.0,
    this.skipRate = 0.0,
    final List<String> topViewers = const [],
    final Map<String, int> reactionBreakdown = const {},
    final Map<String, dynamic>? metadata,
  }) : _viewers = viewers,
       _allowedTo = allowedTo,
       _hiddenFromUserIds = hiddenFromUserIds,
       _allowedGroupIds = allowedGroupIds,
       _textPosition = textPosition,
       _drawingPoints = drawingPoints,
       _textElements = textElements,
       _tags = tags,
       _reactions = reactions,
       _replies = replies,
       _hashtags = hashtags,
       _mentions = mentions,
       _music = music,
       _location = location,
       _topViewers = topViewers,
       _reactionBreakdown = reactionBreakdown,
       _metadata = metadata;

  factory _$UnifiedStoryImpl.fromJson(Map<String, dynamic> json) =>
      _$$UnifiedStoryImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String userName;
  @override
  final String userAvatarUrl;
  @override
  final String mediaUrl;
  @override
  final StoryMediaType mediaType;
  @override
  final DateTime createdAt;
  @override
  final DateTime expiresAt;
  @override
  final Duration duration;
  @override
  final DateTime timestamp;
  // Privacy and visibility
  @override
  @JsonKey()
  final StoryPrivacy privacy;
  @override
  @JsonKey()
  final StoryVisibility visibility;
  final List<String> _viewers;
  @override
  @JsonKey()
  List<String> get viewers {
    if (_viewers is EqualUnmodifiableListView) return _viewers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_viewers);
  }

  final List<String> _allowedTo;
  @override
  @JsonKey()
  List<String> get allowedTo {
    if (_allowedTo is EqualUnmodifiableListView) return _allowedTo;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_allowedTo);
  }

  final List<String> _hiddenFromUserIds;
  @override
  @JsonKey()
  List<String> get hiddenFromUserIds {
    if (_hiddenFromUserIds is EqualUnmodifiableListView)
      return _hiddenFromUserIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_hiddenFromUserIds);
  }

  final List<String> _allowedGroupIds;
  @override
  @JsonKey()
  List<String> get allowedGroupIds {
    if (_allowedGroupIds is EqualUnmodifiableListView) return _allowedGroupIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_allowedGroupIds);
  }

  // Content metadata
  @override
  final String? caption;
  @override
  final String? textOverlay;
  @override
  final int? textColor;
  @override
  final double? textSize;
  final Map<String, double>? _textPosition;
  @override
  Map<String, double>? get textPosition {
    final value = _textPosition;
    if (value == null) return null;
    if (_textPosition is EqualUnmodifiableMapView) return _textPosition;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final int? backgroundColor;
  @override
  final String? filter;
  // Drawing and text elements
  final List<DrawingPoint> _drawingPoints;
  // Drawing and text elements
  @override
  @JsonKey()
  List<DrawingPoint> get drawingPoints {
    if (_drawingPoints is EqualUnmodifiableListView) return _drawingPoints;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_drawingPoints);
  }

  @override
  final int? drawingColor;
  @override
  final double? drawingWidth;
  final List<TextElement> _textElements;
  @override
  @JsonKey()
  List<TextElement> get textElements {
    if (_textElements is EqualUnmodifiableListView) return _textElements;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_textElements);
  }

  // User interactions
  final List<StoryTag> _tags;
  // User interactions
  @override
  @JsonKey()
  List<StoryTag> get tags {
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tags);
  }

  final List<StoryReaction> _reactions;
  @override
  @JsonKey()
  List<StoryReaction> get reactions {
    if (_reactions is EqualUnmodifiableListView) return _reactions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_reactions);
  }

  final List<StoryReply> _replies;
  @override
  @JsonKey()
  List<StoryReply> get replies {
    if (_replies is EqualUnmodifiableListView) return _replies;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_replies);
  }

  final List<String> _hashtags;
  @override
  @JsonKey()
  List<String> get hashtags {
    if (_hashtags is EqualUnmodifiableListView) return _hashtags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_hashtags);
  }

  final List<String> _mentions;
  @override
  @JsonKey()
  List<String> get mentions {
    if (_mentions is EqualUnmodifiableListView) return _mentions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_mentions);
  }

  // Media and location
  final Map<String, dynamic>? _music;
  // Media and location
  @override
  Map<String, dynamic>? get music {
    final value = _music;
    if (value == null) return null;
    if (_music is EqualUnmodifiableMapView) return _music;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final String? musicArtist;
  final Map<String, dynamic>? _location;
  @override
  Map<String, dynamic>? get location {
    final value = _location;
    if (value == null) return null;
    if (_location is EqualUnmodifiableMapView) return _location;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final String? locationName;
  // Story features
  @override
  @JsonKey()
  final bool isHighlighted;
  @override
  @JsonKey()
  final bool isArchived;
  @override
  @JsonKey()
  final bool isPublic;
  @override
  @JsonKey()
  final bool isSeen;
  @override
  @JsonKey()
  final bool isCloseFriend;
  @override
  @JsonKey()
  final StoryType storyType;
  // Analytics and tracking
  @override
  @JsonKey()
  final int viewCount;
  @override
  @JsonKey()
  final int replyCount;
  @override
  @JsonKey()
  final int reactionCount;
  @override
  @JsonKey()
  final int shareCount;
  @override
  @JsonKey()
  final double completionRate;
  @override
  @JsonKey()
  final double skipRate;
  final List<String> _topViewers;
  @override
  @JsonKey()
  List<String> get topViewers {
    if (_topViewers is EqualUnmodifiableListView) return _topViewers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_topViewers);
  }

  final Map<String, int> _reactionBreakdown;
  @override
  @JsonKey()
  Map<String, int> get reactionBreakdown {
    if (_reactionBreakdown is EqualUnmodifiableMapView)
      return _reactionBreakdown;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_reactionBreakdown);
  }

  // Additional metadata
  final Map<String, dynamic>? _metadata;
  // Additional metadata
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'UnifiedStory(id: $id, userId: $userId, userName: $userName, userAvatarUrl: $userAvatarUrl, mediaUrl: $mediaUrl, mediaType: $mediaType, createdAt: $createdAt, expiresAt: $expiresAt, duration: $duration, timestamp: $timestamp, privacy: $privacy, visibility: $visibility, viewers: $viewers, allowedTo: $allowedTo, hiddenFromUserIds: $hiddenFromUserIds, allowedGroupIds: $allowedGroupIds, caption: $caption, textOverlay: $textOverlay, textColor: $textColor, textSize: $textSize, textPosition: $textPosition, backgroundColor: $backgroundColor, filter: $filter, drawingPoints: $drawingPoints, drawingColor: $drawingColor, drawingWidth: $drawingWidth, textElements: $textElements, tags: $tags, reactions: $reactions, replies: $replies, hashtags: $hashtags, mentions: $mentions, music: $music, musicArtist: $musicArtist, location: $location, locationName: $locationName, isHighlighted: $isHighlighted, isArchived: $isArchived, isPublic: $isPublic, isSeen: $isSeen, isCloseFriend: $isCloseFriend, storyType: $storyType, viewCount: $viewCount, replyCount: $replyCount, reactionCount: $reactionCount, shareCount: $shareCount, completionRate: $completionRate, skipRate: $skipRate, topViewers: $topViewers, reactionBreakdown: $reactionBreakdown, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UnifiedStoryImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userName, userName) ||
                other.userName == userName) &&
            (identical(other.userAvatarUrl, userAvatarUrl) ||
                other.userAvatarUrl == userAvatarUrl) &&
            (identical(other.mediaUrl, mediaUrl) ||
                other.mediaUrl == mediaUrl) &&
            (identical(other.mediaType, mediaType) ||
                other.mediaType == mediaType) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.privacy, privacy) || other.privacy == privacy) &&
            (identical(other.visibility, visibility) ||
                other.visibility == visibility) &&
            const DeepCollectionEquality().equals(other._viewers, _viewers) &&
            const DeepCollectionEquality().equals(
              other._allowedTo,
              _allowedTo,
            ) &&
            const DeepCollectionEquality().equals(
              other._hiddenFromUserIds,
              _hiddenFromUserIds,
            ) &&
            const DeepCollectionEquality().equals(
              other._allowedGroupIds,
              _allowedGroupIds,
            ) &&
            (identical(other.caption, caption) || other.caption == caption) &&
            (identical(other.textOverlay, textOverlay) ||
                other.textOverlay == textOverlay) &&
            (identical(other.textColor, textColor) ||
                other.textColor == textColor) &&
            (identical(other.textSize, textSize) ||
                other.textSize == textSize) &&
            const DeepCollectionEquality().equals(
              other._textPosition,
              _textPosition,
            ) &&
            (identical(other.backgroundColor, backgroundColor) ||
                other.backgroundColor == backgroundColor) &&
            (identical(other.filter, filter) || other.filter == filter) &&
            const DeepCollectionEquality().equals(
              other._drawingPoints,
              _drawingPoints,
            ) &&
            (identical(other.drawingColor, drawingColor) ||
                other.drawingColor == drawingColor) &&
            (identical(other.drawingWidth, drawingWidth) ||
                other.drawingWidth == drawingWidth) &&
            const DeepCollectionEquality().equals(
              other._textElements,
              _textElements,
            ) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            const DeepCollectionEquality().equals(
              other._reactions,
              _reactions,
            ) &&
            const DeepCollectionEquality().equals(other._replies, _replies) &&
            const DeepCollectionEquality().equals(other._hashtags, _hashtags) &&
            const DeepCollectionEquality().equals(other._mentions, _mentions) &&
            const DeepCollectionEquality().equals(other._music, _music) &&
            (identical(other.musicArtist, musicArtist) ||
                other.musicArtist == musicArtist) &&
            const DeepCollectionEquality().equals(other._location, _location) &&
            (identical(other.locationName, locationName) ||
                other.locationName == locationName) &&
            (identical(other.isHighlighted, isHighlighted) ||
                other.isHighlighted == isHighlighted) &&
            (identical(other.isArchived, isArchived) ||
                other.isArchived == isArchived) &&
            (identical(other.isPublic, isPublic) ||
                other.isPublic == isPublic) &&
            (identical(other.isSeen, isSeen) || other.isSeen == isSeen) &&
            (identical(other.isCloseFriend, isCloseFriend) ||
                other.isCloseFriend == isCloseFriend) &&
            (identical(other.storyType, storyType) ||
                other.storyType == storyType) &&
            (identical(other.viewCount, viewCount) ||
                other.viewCount == viewCount) &&
            (identical(other.replyCount, replyCount) ||
                other.replyCount == replyCount) &&
            (identical(other.reactionCount, reactionCount) ||
                other.reactionCount == reactionCount) &&
            (identical(other.shareCount, shareCount) ||
                other.shareCount == shareCount) &&
            (identical(other.completionRate, completionRate) ||
                other.completionRate == completionRate) &&
            (identical(other.skipRate, skipRate) ||
                other.skipRate == skipRate) &&
            const DeepCollectionEquality().equals(
              other._topViewers,
              _topViewers,
            ) &&
            const DeepCollectionEquality().equals(
              other._reactionBreakdown,
              _reactionBreakdown,
            ) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    userId,
    userName,
    userAvatarUrl,
    mediaUrl,
    mediaType,
    createdAt,
    expiresAt,
    duration,
    timestamp,
    privacy,
    visibility,
    const DeepCollectionEquality().hash(_viewers),
    const DeepCollectionEquality().hash(_allowedTo),
    const DeepCollectionEquality().hash(_hiddenFromUserIds),
    const DeepCollectionEquality().hash(_allowedGroupIds),
    caption,
    textOverlay,
    textColor,
    textSize,
    const DeepCollectionEquality().hash(_textPosition),
    backgroundColor,
    filter,
    const DeepCollectionEquality().hash(_drawingPoints),
    drawingColor,
    drawingWidth,
    const DeepCollectionEquality().hash(_textElements),
    const DeepCollectionEquality().hash(_tags),
    const DeepCollectionEquality().hash(_reactions),
    const DeepCollectionEquality().hash(_replies),
    const DeepCollectionEquality().hash(_hashtags),
    const DeepCollectionEquality().hash(_mentions),
    const DeepCollectionEquality().hash(_music),
    musicArtist,
    const DeepCollectionEquality().hash(_location),
    locationName,
    isHighlighted,
    isArchived,
    isPublic,
    isSeen,
    isCloseFriend,
    storyType,
    viewCount,
    replyCount,
    reactionCount,
    shareCount,
    completionRate,
    skipRate,
    const DeepCollectionEquality().hash(_topViewers),
    const DeepCollectionEquality().hash(_reactionBreakdown),
    const DeepCollectionEquality().hash(_metadata),
  ]);

  /// Create a copy of UnifiedStory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UnifiedStoryImplCopyWith<_$UnifiedStoryImpl> get copyWith =>
      __$$UnifiedStoryImplCopyWithImpl<_$UnifiedStoryImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UnifiedStoryImplToJson(this);
  }
}

abstract class _UnifiedStory implements UnifiedStory {
  const factory _UnifiedStory({
    required final String id,
    required final String userId,
    required final String userName,
    required final String userAvatarUrl,
    required final String mediaUrl,
    required final StoryMediaType mediaType,
    required final DateTime createdAt,
    required final DateTime expiresAt,
    required final Duration duration,
    required final DateTime timestamp,
    final StoryPrivacy privacy,
    final StoryVisibility visibility,
    final List<String> viewers,
    final List<String> allowedTo,
    final List<String> hiddenFromUserIds,
    final List<String> allowedGroupIds,
    final String? caption,
    final String? textOverlay,
    final int? textColor,
    final double? textSize,
    final Map<String, double>? textPosition,
    final int? backgroundColor,
    final String? filter,
    final List<DrawingPoint> drawingPoints,
    final int? drawingColor,
    final double? drawingWidth,
    final List<TextElement> textElements,
    final List<StoryTag> tags,
    final List<StoryReaction> reactions,
    final List<StoryReply> replies,
    final List<String> hashtags,
    final List<String> mentions,
    final Map<String, dynamic>? music,
    final String? musicArtist,
    final Map<String, dynamic>? location,
    final String? locationName,
    final bool isHighlighted,
    final bool isArchived,
    final bool isPublic,
    final bool isSeen,
    final bool isCloseFriend,
    final StoryType storyType,
    final int viewCount,
    final int replyCount,
    final int reactionCount,
    final int shareCount,
    final double completionRate,
    final double skipRate,
    final List<String> topViewers,
    final Map<String, int> reactionBreakdown,
    final Map<String, dynamic>? metadata,
  }) = _$UnifiedStoryImpl;

  factory _UnifiedStory.fromJson(Map<String, dynamic> json) =
      _$UnifiedStoryImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get userName;
  @override
  String get userAvatarUrl;
  @override
  String get mediaUrl;
  @override
  StoryMediaType get mediaType;
  @override
  DateTime get createdAt;
  @override
  DateTime get expiresAt;
  @override
  Duration get duration;
  @override
  DateTime get timestamp; // Privacy and visibility
  @override
  StoryPrivacy get privacy;
  @override
  StoryVisibility get visibility;
  @override
  List<String> get viewers;
  @override
  List<String> get allowedTo;
  @override
  List<String> get hiddenFromUserIds;
  @override
  List<String> get allowedGroupIds; // Content metadata
  @override
  String? get caption;
  @override
  String? get textOverlay;
  @override
  int? get textColor;
  @override
  double? get textSize;
  @override
  Map<String, double>? get textPosition;
  @override
  int? get backgroundColor;
  @override
  String? get filter; // Drawing and text elements
  @override
  List<DrawingPoint> get drawingPoints;
  @override
  int? get drawingColor;
  @override
  double? get drawingWidth;
  @override
  List<TextElement> get textElements; // User interactions
  @override
  List<StoryTag> get tags;
  @override
  List<StoryReaction> get reactions;
  @override
  List<StoryReply> get replies;
  @override
  List<String> get hashtags;
  @override
  List<String> get mentions; // Media and location
  @override
  Map<String, dynamic>? get music;
  @override
  String? get musicArtist;
  @override
  Map<String, dynamic>? get location;
  @override
  String? get locationName; // Story features
  @override
  bool get isHighlighted;
  @override
  bool get isArchived;
  @override
  bool get isPublic;
  @override
  bool get isSeen;
  @override
  bool get isCloseFriend;
  @override
  StoryType get storyType; // Analytics and tracking
  @override
  int get viewCount;
  @override
  int get replyCount;
  @override
  int get reactionCount;
  @override
  int get shareCount;
  @override
  double get completionRate;
  @override
  double get skipRate;
  @override
  List<String> get topViewers;
  @override
  Map<String, int> get reactionBreakdown; // Additional metadata
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of UnifiedStory
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UnifiedStoryImplCopyWith<_$UnifiedStoryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StoryTag _$StoryTagFromJson(Map<String, dynamic> json) {
  return _StoryTag.fromJson(json);
}

/// @nodoc
mixin _$StoryTag {
  String get userId => throw _privateConstructorUsedError;
  String get username => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  double get x => throw _privateConstructorUsedError;
  double get y => throw _privateConstructorUsedError;

  /// Serializes this StoryTag to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StoryTag
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StoryTagCopyWith<StoryTag> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StoryTagCopyWith<$Res> {
  factory $StoryTagCopyWith(StoryTag value, $Res Function(StoryTag) then) =
      _$StoryTagCopyWithImpl<$Res, StoryTag>;
  @useResult
  $Res call({String userId, String username, String? name, double x, double y});
}

/// @nodoc
class _$StoryTagCopyWithImpl<$Res, $Val extends StoryTag>
    implements $StoryTagCopyWith<$Res> {
  _$StoryTagCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StoryTag
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? username = null,
    Object? name = freezed,
    Object? x = null,
    Object? y = null,
  }) {
    return _then(
      _value.copyWith(
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            username: null == username
                ? _value.username
                : username // ignore: cast_nullable_to_non_nullable
                      as String,
            name: freezed == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String?,
            x: null == x
                ? _value.x
                : x // ignore: cast_nullable_to_non_nullable
                      as double,
            y: null == y
                ? _value.y
                : y // ignore: cast_nullable_to_non_nullable
                      as double,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$StoryTagImplCopyWith<$Res>
    implements $StoryTagCopyWith<$Res> {
  factory _$$StoryTagImplCopyWith(
    _$StoryTagImpl value,
    $Res Function(_$StoryTagImpl) then,
  ) = __$$StoryTagImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String userId, String username, String? name, double x, double y});
}

/// @nodoc
class __$$StoryTagImplCopyWithImpl<$Res>
    extends _$StoryTagCopyWithImpl<$Res, _$StoryTagImpl>
    implements _$$StoryTagImplCopyWith<$Res> {
  __$$StoryTagImplCopyWithImpl(
    _$StoryTagImpl _value,
    $Res Function(_$StoryTagImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of StoryTag
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? username = null,
    Object? name = freezed,
    Object? x = null,
    Object? y = null,
  }) {
    return _then(
      _$StoryTagImpl(
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        username: null == username
            ? _value.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        name: freezed == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String?,
        x: null == x
            ? _value.x
            : x // ignore: cast_nullable_to_non_nullable
                  as double,
        y: null == y
            ? _value.y
            : y // ignore: cast_nullable_to_non_nullable
                  as double,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$StoryTagImpl implements _StoryTag {
  const _$StoryTagImpl({
    required this.userId,
    required this.username,
    this.name,
    required this.x,
    required this.y,
  });

  factory _$StoryTagImpl.fromJson(Map<String, dynamic> json) =>
      _$$StoryTagImplFromJson(json);

  @override
  final String userId;
  @override
  final String username;
  @override
  final String? name;
  @override
  final double x;
  @override
  final double y;

  @override
  String toString() {
    return 'StoryTag(userId: $userId, username: $username, name: $name, x: $x, y: $y)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StoryTagImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.x, x) || other.x == x) &&
            (identical(other.y, y) || other.y == y));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, userId, username, name, x, y);

  /// Create a copy of StoryTag
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StoryTagImplCopyWith<_$StoryTagImpl> get copyWith =>
      __$$StoryTagImplCopyWithImpl<_$StoryTagImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StoryTagImplToJson(this);
  }
}

abstract class _StoryTag implements StoryTag {
  const factory _StoryTag({
    required final String userId,
    required final String username,
    final String? name,
    required final double x,
    required final double y,
  }) = _$StoryTagImpl;

  factory _StoryTag.fromJson(Map<String, dynamic> json) =
      _$StoryTagImpl.fromJson;

  @override
  String get userId;
  @override
  String get username;
  @override
  String? get name;
  @override
  double get x;
  @override
  double get y;

  /// Create a copy of StoryTag
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StoryTagImplCopyWith<_$StoryTagImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StoryReaction _$StoryReactionFromJson(Map<String, dynamic> json) {
  return _StoryReaction.fromJson(json);
}

/// @nodoc
mixin _$StoryReaction {
  String get userId => throw _privateConstructorUsedError;
  String get userName => throw _privateConstructorUsedError;
  String get userAvatarUrl => throw _privateConstructorUsedError;
  String get reactionType => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;

  /// Serializes this StoryReaction to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StoryReaction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StoryReactionCopyWith<StoryReaction> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StoryReactionCopyWith<$Res> {
  factory $StoryReactionCopyWith(
    StoryReaction value,
    $Res Function(StoryReaction) then,
  ) = _$StoryReactionCopyWithImpl<$Res, StoryReaction>;
  @useResult
  $Res call({
    String userId,
    String userName,
    String userAvatarUrl,
    String reactionType,
    DateTime timestamp,
  });
}

/// @nodoc
class _$StoryReactionCopyWithImpl<$Res, $Val extends StoryReaction>
    implements $StoryReactionCopyWith<$Res> {
  _$StoryReactionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StoryReaction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? userName = null,
    Object? userAvatarUrl = null,
    Object? reactionType = null,
    Object? timestamp = null,
  }) {
    return _then(
      _value.copyWith(
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            userName: null == userName
                ? _value.userName
                : userName // ignore: cast_nullable_to_non_nullable
                      as String,
            userAvatarUrl: null == userAvatarUrl
                ? _value.userAvatarUrl
                : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            reactionType: null == reactionType
                ? _value.reactionType
                : reactionType // ignore: cast_nullable_to_non_nullable
                      as String,
            timestamp: null == timestamp
                ? _value.timestamp
                : timestamp // ignore: cast_nullable_to_non_nullable
                      as DateTime,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$StoryReactionImplCopyWith<$Res>
    implements $StoryReactionCopyWith<$Res> {
  factory _$$StoryReactionImplCopyWith(
    _$StoryReactionImpl value,
    $Res Function(_$StoryReactionImpl) then,
  ) = __$$StoryReactionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String userId,
    String userName,
    String userAvatarUrl,
    String reactionType,
    DateTime timestamp,
  });
}

/// @nodoc
class __$$StoryReactionImplCopyWithImpl<$Res>
    extends _$StoryReactionCopyWithImpl<$Res, _$StoryReactionImpl>
    implements _$$StoryReactionImplCopyWith<$Res> {
  __$$StoryReactionImplCopyWithImpl(
    _$StoryReactionImpl _value,
    $Res Function(_$StoryReactionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of StoryReaction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? userName = null,
    Object? userAvatarUrl = null,
    Object? reactionType = null,
    Object? timestamp = null,
  }) {
    return _then(
      _$StoryReactionImpl(
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        userName: null == userName
            ? _value.userName
            : userName // ignore: cast_nullable_to_non_nullable
                  as String,
        userAvatarUrl: null == userAvatarUrl
            ? _value.userAvatarUrl
            : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        reactionType: null == reactionType
            ? _value.reactionType
            : reactionType // ignore: cast_nullable_to_non_nullable
                  as String,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$StoryReactionImpl implements _StoryReaction {
  const _$StoryReactionImpl({
    required this.userId,
    required this.userName,
    required this.userAvatarUrl,
    required this.reactionType,
    required this.timestamp,
  });

  factory _$StoryReactionImpl.fromJson(Map<String, dynamic> json) =>
      _$$StoryReactionImplFromJson(json);

  @override
  final String userId;
  @override
  final String userName;
  @override
  final String userAvatarUrl;
  @override
  final String reactionType;
  @override
  final DateTime timestamp;

  @override
  String toString() {
    return 'StoryReaction(userId: $userId, userName: $userName, userAvatarUrl: $userAvatarUrl, reactionType: $reactionType, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StoryReactionImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userName, userName) ||
                other.userName == userName) &&
            (identical(other.userAvatarUrl, userAvatarUrl) ||
                other.userAvatarUrl == userAvatarUrl) &&
            (identical(other.reactionType, reactionType) ||
                other.reactionType == reactionType) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    userId,
    userName,
    userAvatarUrl,
    reactionType,
    timestamp,
  );

  /// Create a copy of StoryReaction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StoryReactionImplCopyWith<_$StoryReactionImpl> get copyWith =>
      __$$StoryReactionImplCopyWithImpl<_$StoryReactionImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StoryReactionImplToJson(this);
  }
}

abstract class _StoryReaction implements StoryReaction {
  const factory _StoryReaction({
    required final String userId,
    required final String userName,
    required final String userAvatarUrl,
    required final String reactionType,
    required final DateTime timestamp,
  }) = _$StoryReactionImpl;

  factory _StoryReaction.fromJson(Map<String, dynamic> json) =
      _$StoryReactionImpl.fromJson;

  @override
  String get userId;
  @override
  String get userName;
  @override
  String get userAvatarUrl;
  @override
  String get reactionType;
  @override
  DateTime get timestamp;

  /// Create a copy of StoryReaction
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StoryReactionImplCopyWith<_$StoryReactionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StoryReply _$StoryReplyFromJson(Map<String, dynamic> json) {
  return _StoryReply.fromJson(json);
}

/// @nodoc
mixin _$StoryReply {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get userName => throw _privateConstructorUsedError;
  String get userAvatarUrl => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;
  bool get isRead => throw _privateConstructorUsedError;

  /// Serializes this StoryReply to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StoryReply
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StoryReplyCopyWith<StoryReply> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StoryReplyCopyWith<$Res> {
  factory $StoryReplyCopyWith(
    StoryReply value,
    $Res Function(StoryReply) then,
  ) = _$StoryReplyCopyWithImpl<$Res, StoryReply>;
  @useResult
  $Res call({
    String id,
    String userId,
    String userName,
    String userAvatarUrl,
    String message,
    DateTime timestamp,
    bool isRead,
  });
}

/// @nodoc
class _$StoryReplyCopyWithImpl<$Res, $Val extends StoryReply>
    implements $StoryReplyCopyWith<$Res> {
  _$StoryReplyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StoryReply
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? userName = null,
    Object? userAvatarUrl = null,
    Object? message = null,
    Object? timestamp = null,
    Object? isRead = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            userName: null == userName
                ? _value.userName
                : userName // ignore: cast_nullable_to_non_nullable
                      as String,
            userAvatarUrl: null == userAvatarUrl
                ? _value.userAvatarUrl
                : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            message: null == message
                ? _value.message
                : message // ignore: cast_nullable_to_non_nullable
                      as String,
            timestamp: null == timestamp
                ? _value.timestamp
                : timestamp // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isRead: null == isRead
                ? _value.isRead
                : isRead // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$StoryReplyImplCopyWith<$Res>
    implements $StoryReplyCopyWith<$Res> {
  factory _$$StoryReplyImplCopyWith(
    _$StoryReplyImpl value,
    $Res Function(_$StoryReplyImpl) then,
  ) = __$$StoryReplyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    String userName,
    String userAvatarUrl,
    String message,
    DateTime timestamp,
    bool isRead,
  });
}

/// @nodoc
class __$$StoryReplyImplCopyWithImpl<$Res>
    extends _$StoryReplyCopyWithImpl<$Res, _$StoryReplyImpl>
    implements _$$StoryReplyImplCopyWith<$Res> {
  __$$StoryReplyImplCopyWithImpl(
    _$StoryReplyImpl _value,
    $Res Function(_$StoryReplyImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of StoryReply
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? userName = null,
    Object? userAvatarUrl = null,
    Object? message = null,
    Object? timestamp = null,
    Object? isRead = null,
  }) {
    return _then(
      _$StoryReplyImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        userName: null == userName
            ? _value.userName
            : userName // ignore: cast_nullable_to_non_nullable
                  as String,
        userAvatarUrl: null == userAvatarUrl
            ? _value.userAvatarUrl
            : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        message: null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isRead: null == isRead
            ? _value.isRead
            : isRead // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$StoryReplyImpl implements _StoryReply {
  const _$StoryReplyImpl({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userAvatarUrl,
    required this.message,
    required this.timestamp,
    this.isRead = false,
  });

  factory _$StoryReplyImpl.fromJson(Map<String, dynamic> json) =>
      _$$StoryReplyImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String userName;
  @override
  final String userAvatarUrl;
  @override
  final String message;
  @override
  final DateTime timestamp;
  @override
  @JsonKey()
  final bool isRead;

  @override
  String toString() {
    return 'StoryReply(id: $id, userId: $userId, userName: $userName, userAvatarUrl: $userAvatarUrl, message: $message, timestamp: $timestamp, isRead: $isRead)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StoryReplyImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userName, userName) ||
                other.userName == userName) &&
            (identical(other.userAvatarUrl, userAvatarUrl) ||
                other.userAvatarUrl == userAvatarUrl) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.isRead, isRead) || other.isRead == isRead));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    userId,
    userName,
    userAvatarUrl,
    message,
    timestamp,
    isRead,
  );

  /// Create a copy of StoryReply
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StoryReplyImplCopyWith<_$StoryReplyImpl> get copyWith =>
      __$$StoryReplyImplCopyWithImpl<_$StoryReplyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StoryReplyImplToJson(this);
  }
}

abstract class _StoryReply implements StoryReply {
  const factory _StoryReply({
    required final String id,
    required final String userId,
    required final String userName,
    required final String userAvatarUrl,
    required final String message,
    required final DateTime timestamp,
    final bool isRead,
  }) = _$StoryReplyImpl;

  factory _StoryReply.fromJson(Map<String, dynamic> json) =
      _$StoryReplyImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get userName;
  @override
  String get userAvatarUrl;
  @override
  String get message;
  @override
  DateTime get timestamp;
  @override
  bool get isRead;

  /// Create a copy of StoryReply
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StoryReplyImplCopyWith<_$StoryReplyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StoryReel _$StoryReelFromJson(Map<String, dynamic> json) {
  return _StoryReel.fromJson(json);
}

/// @nodoc
mixin _$StoryReel {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get username => throw _privateConstructorUsedError;
  String get userAvatarUrl => throw _privateConstructorUsedError;
  List<UnifiedStory> get stories => throw _privateConstructorUsedError;
  bool get isAllViewed => throw _privateConstructorUsedError;
  bool get isCloseFriend => throw _privateConstructorUsedError;

  /// Serializes this StoryReel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StoryReel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StoryReelCopyWith<StoryReel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StoryReelCopyWith<$Res> {
  factory $StoryReelCopyWith(StoryReel value, $Res Function(StoryReel) then) =
      _$StoryReelCopyWithImpl<$Res, StoryReel>;
  @useResult
  $Res call({
    String id,
    String userId,
    String username,
    String userAvatarUrl,
    List<UnifiedStory> stories,
    bool isAllViewed,
    bool isCloseFriend,
  });
}

/// @nodoc
class _$StoryReelCopyWithImpl<$Res, $Val extends StoryReel>
    implements $StoryReelCopyWith<$Res> {
  _$StoryReelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StoryReel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? username = null,
    Object? userAvatarUrl = null,
    Object? stories = null,
    Object? isAllViewed = null,
    Object? isCloseFriend = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            username: null == username
                ? _value.username
                : username // ignore: cast_nullable_to_non_nullable
                      as String,
            userAvatarUrl: null == userAvatarUrl
                ? _value.userAvatarUrl
                : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            stories: null == stories
                ? _value.stories
                : stories // ignore: cast_nullable_to_non_nullable
                      as List<UnifiedStory>,
            isAllViewed: null == isAllViewed
                ? _value.isAllViewed
                : isAllViewed // ignore: cast_nullable_to_non_nullable
                      as bool,
            isCloseFriend: null == isCloseFriend
                ? _value.isCloseFriend
                : isCloseFriend // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$StoryReelImplCopyWith<$Res>
    implements $StoryReelCopyWith<$Res> {
  factory _$$StoryReelImplCopyWith(
    _$StoryReelImpl value,
    $Res Function(_$StoryReelImpl) then,
  ) = __$$StoryReelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    String username,
    String userAvatarUrl,
    List<UnifiedStory> stories,
    bool isAllViewed,
    bool isCloseFriend,
  });
}

/// @nodoc
class __$$StoryReelImplCopyWithImpl<$Res>
    extends _$StoryReelCopyWithImpl<$Res, _$StoryReelImpl>
    implements _$$StoryReelImplCopyWith<$Res> {
  __$$StoryReelImplCopyWithImpl(
    _$StoryReelImpl _value,
    $Res Function(_$StoryReelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of StoryReel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? username = null,
    Object? userAvatarUrl = null,
    Object? stories = null,
    Object? isAllViewed = null,
    Object? isCloseFriend = null,
  }) {
    return _then(
      _$StoryReelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        username: null == username
            ? _value.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        userAvatarUrl: null == userAvatarUrl
            ? _value.userAvatarUrl
            : userAvatarUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        stories: null == stories
            ? _value._stories
            : stories // ignore: cast_nullable_to_non_nullable
                  as List<UnifiedStory>,
        isAllViewed: null == isAllViewed
            ? _value.isAllViewed
            : isAllViewed // ignore: cast_nullable_to_non_nullable
                  as bool,
        isCloseFriend: null == isCloseFriend
            ? _value.isCloseFriend
            : isCloseFriend // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$StoryReelImpl implements _StoryReel {
  const _$StoryReelImpl({
    required this.id,
    required this.userId,
    required this.username,
    required this.userAvatarUrl,
    required final List<UnifiedStory> stories,
    this.isAllViewed = false,
    this.isCloseFriend = false,
  }) : _stories = stories;

  factory _$StoryReelImpl.fromJson(Map<String, dynamic> json) =>
      _$$StoryReelImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String username;
  @override
  final String userAvatarUrl;
  final List<UnifiedStory> _stories;
  @override
  List<UnifiedStory> get stories {
    if (_stories is EqualUnmodifiableListView) return _stories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_stories);
  }

  @override
  @JsonKey()
  final bool isAllViewed;
  @override
  @JsonKey()
  final bool isCloseFriend;

  @override
  String toString() {
    return 'StoryReel(id: $id, userId: $userId, username: $username, userAvatarUrl: $userAvatarUrl, stories: $stories, isAllViewed: $isAllViewed, isCloseFriend: $isCloseFriend)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StoryReelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userAvatarUrl, userAvatarUrl) ||
                other.userAvatarUrl == userAvatarUrl) &&
            const DeepCollectionEquality().equals(other._stories, _stories) &&
            (identical(other.isAllViewed, isAllViewed) ||
                other.isAllViewed == isAllViewed) &&
            (identical(other.isCloseFriend, isCloseFriend) ||
                other.isCloseFriend == isCloseFriend));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    userId,
    username,
    userAvatarUrl,
    const DeepCollectionEquality().hash(_stories),
    isAllViewed,
    isCloseFriend,
  );

  /// Create a copy of StoryReel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StoryReelImplCopyWith<_$StoryReelImpl> get copyWith =>
      __$$StoryReelImplCopyWithImpl<_$StoryReelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StoryReelImplToJson(this);
  }
}

abstract class _StoryReel implements StoryReel {
  const factory _StoryReel({
    required final String id,
    required final String userId,
    required final String username,
    required final String userAvatarUrl,
    required final List<UnifiedStory> stories,
    final bool isAllViewed,
    final bool isCloseFriend,
  }) = _$StoryReelImpl;

  factory _StoryReel.fromJson(Map<String, dynamic> json) =
      _$StoryReelImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get username;
  @override
  String get userAvatarUrl;
  @override
  List<UnifiedStory> get stories;
  @override
  bool get isAllViewed;
  @override
  bool get isCloseFriend;

  /// Create a copy of StoryReel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StoryReelImplCopyWith<_$StoryReelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StoryStats _$StoryStatsFromJson(Map<String, dynamic> json) {
  return _StoryStats.fromJson(json);
}

/// @nodoc
mixin _$StoryStats {
  int get viewCount => throw _privateConstructorUsedError;
  int get replyCount => throw _privateConstructorUsedError;
  int get reactionCount => throw _privateConstructorUsedError;
  int get shareCount => throw _privateConstructorUsedError;
  double get completionRate => throw _privateConstructorUsedError;
  double get skipRate => throw _privateConstructorUsedError;
  List<String> get topViewers => throw _privateConstructorUsedError;
  Map<String, int> get reactionBreakdown => throw _privateConstructorUsedError;

  /// Serializes this StoryStats to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StoryStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StoryStatsCopyWith<StoryStats> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StoryStatsCopyWith<$Res> {
  factory $StoryStatsCopyWith(
    StoryStats value,
    $Res Function(StoryStats) then,
  ) = _$StoryStatsCopyWithImpl<$Res, StoryStats>;
  @useResult
  $Res call({
    int viewCount,
    int replyCount,
    int reactionCount,
    int shareCount,
    double completionRate,
    double skipRate,
    List<String> topViewers,
    Map<String, int> reactionBreakdown,
  });
}

/// @nodoc
class _$StoryStatsCopyWithImpl<$Res, $Val extends StoryStats>
    implements $StoryStatsCopyWith<$Res> {
  _$StoryStatsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StoryStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? viewCount = null,
    Object? replyCount = null,
    Object? reactionCount = null,
    Object? shareCount = null,
    Object? completionRate = null,
    Object? skipRate = null,
    Object? topViewers = null,
    Object? reactionBreakdown = null,
  }) {
    return _then(
      _value.copyWith(
            viewCount: null == viewCount
                ? _value.viewCount
                : viewCount // ignore: cast_nullable_to_non_nullable
                      as int,
            replyCount: null == replyCount
                ? _value.replyCount
                : replyCount // ignore: cast_nullable_to_non_nullable
                      as int,
            reactionCount: null == reactionCount
                ? _value.reactionCount
                : reactionCount // ignore: cast_nullable_to_non_nullable
                      as int,
            shareCount: null == shareCount
                ? _value.shareCount
                : shareCount // ignore: cast_nullable_to_non_nullable
                      as int,
            completionRate: null == completionRate
                ? _value.completionRate
                : completionRate // ignore: cast_nullable_to_non_nullable
                      as double,
            skipRate: null == skipRate
                ? _value.skipRate
                : skipRate // ignore: cast_nullable_to_non_nullable
                      as double,
            topViewers: null == topViewers
                ? _value.topViewers
                : topViewers // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            reactionBreakdown: null == reactionBreakdown
                ? _value.reactionBreakdown
                : reactionBreakdown // ignore: cast_nullable_to_non_nullable
                      as Map<String, int>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$StoryStatsImplCopyWith<$Res>
    implements $StoryStatsCopyWith<$Res> {
  factory _$$StoryStatsImplCopyWith(
    _$StoryStatsImpl value,
    $Res Function(_$StoryStatsImpl) then,
  ) = __$$StoryStatsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int viewCount,
    int replyCount,
    int reactionCount,
    int shareCount,
    double completionRate,
    double skipRate,
    List<String> topViewers,
    Map<String, int> reactionBreakdown,
  });
}

/// @nodoc
class __$$StoryStatsImplCopyWithImpl<$Res>
    extends _$StoryStatsCopyWithImpl<$Res, _$StoryStatsImpl>
    implements _$$StoryStatsImplCopyWith<$Res> {
  __$$StoryStatsImplCopyWithImpl(
    _$StoryStatsImpl _value,
    $Res Function(_$StoryStatsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of StoryStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? viewCount = null,
    Object? replyCount = null,
    Object? reactionCount = null,
    Object? shareCount = null,
    Object? completionRate = null,
    Object? skipRate = null,
    Object? topViewers = null,
    Object? reactionBreakdown = null,
  }) {
    return _then(
      _$StoryStatsImpl(
        viewCount: null == viewCount
            ? _value.viewCount
            : viewCount // ignore: cast_nullable_to_non_nullable
                  as int,
        replyCount: null == replyCount
            ? _value.replyCount
            : replyCount // ignore: cast_nullable_to_non_nullable
                  as int,
        reactionCount: null == reactionCount
            ? _value.reactionCount
            : reactionCount // ignore: cast_nullable_to_non_nullable
                  as int,
        shareCount: null == shareCount
            ? _value.shareCount
            : shareCount // ignore: cast_nullable_to_non_nullable
                  as int,
        completionRate: null == completionRate
            ? _value.completionRate
            : completionRate // ignore: cast_nullable_to_non_nullable
                  as double,
        skipRate: null == skipRate
            ? _value.skipRate
            : skipRate // ignore: cast_nullable_to_non_nullable
                  as double,
        topViewers: null == topViewers
            ? _value._topViewers
            : topViewers // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        reactionBreakdown: null == reactionBreakdown
            ? _value._reactionBreakdown
            : reactionBreakdown // ignore: cast_nullable_to_non_nullable
                  as Map<String, int>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$StoryStatsImpl implements _StoryStats {
  const _$StoryStatsImpl({
    required this.viewCount,
    required this.replyCount,
    required this.reactionCount,
    required this.shareCount,
    required this.completionRate,
    required this.skipRate,
    required final List<String> topViewers,
    required final Map<String, int> reactionBreakdown,
  }) : _topViewers = topViewers,
       _reactionBreakdown = reactionBreakdown;

  factory _$StoryStatsImpl.fromJson(Map<String, dynamic> json) =>
      _$$StoryStatsImplFromJson(json);

  @override
  final int viewCount;
  @override
  final int replyCount;
  @override
  final int reactionCount;
  @override
  final int shareCount;
  @override
  final double completionRate;
  @override
  final double skipRate;
  final List<String> _topViewers;
  @override
  List<String> get topViewers {
    if (_topViewers is EqualUnmodifiableListView) return _topViewers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_topViewers);
  }

  final Map<String, int> _reactionBreakdown;
  @override
  Map<String, int> get reactionBreakdown {
    if (_reactionBreakdown is EqualUnmodifiableMapView)
      return _reactionBreakdown;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_reactionBreakdown);
  }

  @override
  String toString() {
    return 'StoryStats(viewCount: $viewCount, replyCount: $replyCount, reactionCount: $reactionCount, shareCount: $shareCount, completionRate: $completionRate, skipRate: $skipRate, topViewers: $topViewers, reactionBreakdown: $reactionBreakdown)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StoryStatsImpl &&
            (identical(other.viewCount, viewCount) ||
                other.viewCount == viewCount) &&
            (identical(other.replyCount, replyCount) ||
                other.replyCount == replyCount) &&
            (identical(other.reactionCount, reactionCount) ||
                other.reactionCount == reactionCount) &&
            (identical(other.shareCount, shareCount) ||
                other.shareCount == shareCount) &&
            (identical(other.completionRate, completionRate) ||
                other.completionRate == completionRate) &&
            (identical(other.skipRate, skipRate) ||
                other.skipRate == skipRate) &&
            const DeepCollectionEquality().equals(
              other._topViewers,
              _topViewers,
            ) &&
            const DeepCollectionEquality().equals(
              other._reactionBreakdown,
              _reactionBreakdown,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    viewCount,
    replyCount,
    reactionCount,
    shareCount,
    completionRate,
    skipRate,
    const DeepCollectionEquality().hash(_topViewers),
    const DeepCollectionEquality().hash(_reactionBreakdown),
  );

  /// Create a copy of StoryStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StoryStatsImplCopyWith<_$StoryStatsImpl> get copyWith =>
      __$$StoryStatsImplCopyWithImpl<_$StoryStatsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StoryStatsImplToJson(this);
  }
}

abstract class _StoryStats implements StoryStats {
  const factory _StoryStats({
    required final int viewCount,
    required final int replyCount,
    required final int reactionCount,
    required final int shareCount,
    required final double completionRate,
    required final double skipRate,
    required final List<String> topViewers,
    required final Map<String, int> reactionBreakdown,
  }) = _$StoryStatsImpl;

  factory _StoryStats.fromJson(Map<String, dynamic> json) =
      _$StoryStatsImpl.fromJson;

  @override
  int get viewCount;
  @override
  int get replyCount;
  @override
  int get reactionCount;
  @override
  int get shareCount;
  @override
  double get completionRate;
  @override
  double get skipRate;
  @override
  List<String> get topViewers;
  @override
  Map<String, int> get reactionBreakdown;

  /// Create a copy of StoryStats
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StoryStatsImplCopyWith<_$StoryStatsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
