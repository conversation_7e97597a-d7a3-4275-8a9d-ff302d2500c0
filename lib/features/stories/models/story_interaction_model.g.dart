// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'story_interaction_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$StoryReactionImpl _$$StoryReactionImplFromJson(Map<String, dynamic> json) =>
    _$StoryReactionImpl(
      id: json['id'] as String,
      storyId: json['storyId'] as String,
      userId: json['userId'] as String,
      username: json['username'] as String,
      userAvatarUrl: json['userAvatarUrl'] as String,
      type: $enumDecode(_$StoryReactionTypeEnumMap, json['type']),
      timestamp: DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$$StoryReactionImplToJson(_$StoryReactionImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'storyId': instance.storyId,
      'userId': instance.userId,
      'username': instance.username,
      'userAvatarUrl': instance.userAvatarUrl,
      'type': _$StoryReactionTypeEnumMap[instance.type]!,
      'timestamp': instance.timestamp.toIso8601String(),
    };

const _$StoryReactionTypeEnumMap = {
  StoryReactionType.like: 'like',
  StoryReactionType.love: 'love',
  StoryReactionType.haha: 'haha',
  StoryReactionType.wow: 'wow',
  StoryReactionType.sad: 'sad',
  StoryReactionType.angry: 'angry',
};

_$StoryReplyImpl _$$StoryReplyImplFromJson(Map<String, dynamic> json) =>
    _$StoryReplyImpl(
      id: json['id'] as String,
      storyId: json['storyId'] as String,
      userId: json['userId'] as String,
      username: json['username'] as String,
      userAvatarUrl: json['userAvatarUrl'] as String,
      content: json['content'] as String,
      mediaUrl: json['mediaUrl'] as String?,
      timestamp: DateTime.parse(json['timestamp'] as String),
      isRead: json['isRead'] as bool? ?? false,
      isReplied: json['isReplied'] as bool? ?? false,
    );

Map<String, dynamic> _$$StoryReplyImplToJson(_$StoryReplyImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'storyId': instance.storyId,
      'userId': instance.userId,
      'username': instance.username,
      'userAvatarUrl': instance.userAvatarUrl,
      'content': instance.content,
      'mediaUrl': instance.mediaUrl,
      'timestamp': instance.timestamp.toIso8601String(),
      'isRead': instance.isRead,
      'isReplied': instance.isReplied,
    };

_$StoryPrivacySettingsImpl _$$StoryPrivacySettingsImplFromJson(
  Map<String, dynamic> json,
) => _$StoryPrivacySettingsImpl(
  privacy:
      $enumDecodeNullable(_$StoryPrivacyEnumMap, json['privacy']) ??
      StoryPrivacy.public,
  allowedViewers:
      (json['allowedViewers'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  blockedViewers:
      (json['blockedViewers'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  allowReplies: json['allowReplies'] as bool? ?? true,
  allowReactions: json['allowReactions'] as bool? ?? true,
  allowScreenshots: json['allowScreenshots'] as bool? ?? true,
  hideFromStory: json['hideFromStory'] as bool? ?? false,
  muteStory: json['muteStory'] as bool? ?? false,
);

Map<String, dynamic> _$$StoryPrivacySettingsImplToJson(
  _$StoryPrivacySettingsImpl instance,
) => <String, dynamic>{
  'privacy': _$StoryPrivacyEnumMap[instance.privacy]!,
  'allowedViewers': instance.allowedViewers,
  'blockedViewers': instance.blockedViewers,
  'allowReplies': instance.allowReplies,
  'allowReactions': instance.allowReactions,
  'allowScreenshots': instance.allowScreenshots,
  'hideFromStory': instance.hideFromStory,
  'muteStory': instance.muteStory,
};

const _$StoryPrivacyEnumMap = {
  StoryPrivacy.public: 'public',
  StoryPrivacy.followers: 'followers',
  StoryPrivacy.closeFriends: 'closeFriends',
  StoryPrivacy.custom: 'custom',
  StoryPrivacy.private: 'private',
};

_$StoryAnalyticsImpl _$$StoryAnalyticsImplFromJson(
  Map<String, dynamic> json,
) => _$StoryAnalyticsImpl(
  storyId: json['storyId'] as String,
  viewCount: (json['viewCount'] as num).toInt(),
  uniqueViewers: (json['uniqueViewers'] as num).toInt(),
  replyCount: (json['replyCount'] as num).toInt(),
  reactionCount: (json['reactionCount'] as num).toInt(),
  reactionBreakdown: (json['reactionBreakdown'] as Map<String, dynamic>).map(
    (k, e) => MapEntry(
      $enumDecode(_$StoryReactionTypeEnumMap, k),
      (e as num).toInt(),
    ),
  ),
  averageViewDuration: Duration(
    microseconds: (json['averageViewDuration'] as num).toInt(),
  ),
  createdAt: DateTime.parse(json['createdAt'] as String),
  expiresAt: json['expiresAt'] == null
      ? null
      : DateTime.parse(json['expiresAt'] as String),
  viewedBy:
      (json['viewedBy'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  repliedBy:
      (json['repliedBy'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  reactedBy:
      (json['reactedBy'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
);

Map<String, dynamic> _$$StoryAnalyticsImplToJson(
  _$StoryAnalyticsImpl instance,
) => <String, dynamic>{
  'storyId': instance.storyId,
  'viewCount': instance.viewCount,
  'uniqueViewers': instance.uniqueViewers,
  'replyCount': instance.replyCount,
  'reactionCount': instance.reactionCount,
  'reactionBreakdown': instance.reactionBreakdown.map(
    (k, e) => MapEntry(_$StoryReactionTypeEnumMap[k]!, e),
  ),
  'averageViewDuration': instance.averageViewDuration.inMicroseconds,
  'createdAt': instance.createdAt.toIso8601String(),
  'expiresAt': instance.expiresAt?.toIso8601String(),
  'viewedBy': instance.viewedBy,
  'repliedBy': instance.repliedBy,
  'reactedBy': instance.reactedBy,
};

_$StoryReportImpl _$$StoryReportImplFromJson(Map<String, dynamic> json) =>
    _$StoryReportImpl(
      id: json['id'] as String,
      storyId: json['storyId'] as String,
      reportedBy: json['reportedBy'] as String,
      reason: json['reason'] as String,
      additionalInfo: json['additionalInfo'] as String?,
      timestamp: DateTime.parse(json['timestamp'] as String),
      status: json['status'] as String? ?? 'pending',
      reviewedBy: json['reviewedBy'] as String?,
      reviewedAt: json['reviewedAt'] == null
          ? null
          : DateTime.parse(json['reviewedAt'] as String),
      action: json['action'] as String?,
    );

Map<String, dynamic> _$$StoryReportImplToJson(_$StoryReportImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'storyId': instance.storyId,
      'reportedBy': instance.reportedBy,
      'reason': instance.reason,
      'additionalInfo': instance.additionalInfo,
      'timestamp': instance.timestamp.toIso8601String(),
      'status': instance.status,
      'reviewedBy': instance.reviewedBy,
      'reviewedAt': instance.reviewedAt?.toIso8601String(),
      'action': instance.action,
    };
