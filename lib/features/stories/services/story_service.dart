import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:billionaires_social/features/feed/models/post_model.dart';
import 'package:billionaires_social/features/profile/models/profile_model.dart';
import 'package:billionaires_social/features/stories/models/story_reel_model.dart';
import 'package:billionaires_social/features/stories/models/shared/story_shared_models.dart';
import 'package:billionaires_social/features/stories/services/story_settings_service.dart';
import 'package:billionaires_social/features/stories/models/story_settings_model.dart'
    as story_settings_model;
import 'package:billionaires_social/core/services/firebase_service.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/features/notifications/services/notification_service.dart';
import 'package:uuid/uuid.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:geolocator/geolocator.dart';
import 'dart:async';

final storyServiceProvider = Provider<StoryService>((ref) {
  return getIt<StoryService>();
});

class StoryService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseService _firebaseService = FirebaseService();
  final NotificationService _notificationService = getIt<NotificationService>();
  final Uuid _uuid = const Uuid();

  // Music preview functionality
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isPlaying = false;
  String? _currentMusicPath;

  // Location search functionality
  List<LocationResult> _locationResults = [];
  bool _isSearchingLocation = false;

  Future<List<StoryReel>> getStoryReels() async {
    try {
      debugPrint('🎬 Fetching story reels...');

      // Temporarily remove 24-hour filter to debug
      // final twentyFourHoursAgo = DateTime.now().subtract(
      //   const Duration(hours: 24),
      // );

      final storiesSnapshot = await _firestore
          .collection('stories')
          // .where(
          //   'createdAt',
          //   isGreaterThan: Timestamp.fromDate(twentyFourHoursAgo),
          // )
          .orderBy('createdAt', descending: true)
          .get();

      debugPrint(
        '📊 Found ${storiesSnapshot.docs.length} stories in Firestore',
      );

      if (storiesSnapshot.docs.isEmpty) {
        debugPrint('📭 No stories found, returning empty list');
        return [];
      }

      // Debug: Print first few stories
      final debugCount = storiesSnapshot.docs.length < 3
          ? storiesSnapshot.docs.length
          : 3;
      for (int i = 0; i < debugCount; i++) {
        final doc = storiesSnapshot.docs[i];
        final data = doc.data();
        debugPrint(
          '📖 Story ${i + 1}: userId=${data['userId']}, createdAt=${data['createdAt']}, mediaUrl=${data['mediaUrl']}',
        );
      }

      // Group stories by user, but only include stories with valid mediaUrl
      final Map<String, List<QueryDocumentSnapshot>> userStories = {};

      for (final doc in storiesSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>?;
        final userId = data?['userId'] as String?;
        final mediaUrl = data?['mediaUrl'] as String?;

        // Only include stories with a non-empty mediaUrl
        if (userId != null && mediaUrl != null && mediaUrl.trim().isNotEmpty) {
          userStories.putIfAbsent(userId, () => []).add(doc);
        }
      }

      debugPrint('👥 Grouped stories by ${userStories.length} users');

      // Convert to StoryReel objects
      final List<StoryReel> reels = [];

      for (final entry in userStories.entries) {
        final userId = entry.key;
        final userStoriesDocs = entry.value;

        try {
          // Get user profile for username and avatar
          final userProfile = await _firebaseService.getUserProfile(userId);
          final username = getUsernameFromProfile(userProfile);
          final userAvatarUrl = getValidAvatarUrl(
            userProfile?.profilePictureUrl,
            userId: userId,
          );

          // Convert stories to StoryItem objects
          final stories = userStoriesDocs
              .map((doc) {
                final data = doc.data() as Map<String, dynamic>?;
                if (data == null) {
                  debugPrint('⚠️ Skipping story with null data: ${doc.id}');
                  return null;
                }

                // Validate required fields
                final mediaUrl = data['mediaUrl'] as String?;
                if (mediaUrl == null || mediaUrl.isEmpty) {
                  debugPrint(
                    '⚠️ Skipping story with empty mediaUrl: ${doc.id}',
                  );
                  return null;
                }

                final mediaType = data['mediaType'] as String? ?? 'image';
                final textPosition = data['textPosition'];

                // Safely parse textPosition with better type handling
                Map<String, double>? parsedTextPosition;
                if (textPosition is Map<String, dynamic>) {
                  try {
                    parsedTextPosition = Map<String, double>.from(
                      textPosition.map(
                        (key, value) => MapEntry(key, _safeToDouble(value)),
                      ),
                    );
                  } catch (e) {
                    debugPrint(
                      '⚠️ Invalid textPosition format in story ${doc.id}: $e',
                    );
                    parsedTextPosition = {'x': 100.0, 'y': 100.0};
                  }
                } else if (textPosition is Map) {
                  try {
                    parsedTextPosition = Map<String, double>.from(
                      textPosition.map(
                        (key, value) =>
                            MapEntry(key.toString(), _safeToDouble(value)),
                      ),
                    );
                  } catch (e) {
                    debugPrint(
                      '⚠️ Invalid textPosition format in story ${doc.id}: $e',
                    );
                    parsedTextPosition = {'x': 100.0, 'y': 100.0};
                  }
                } else {
                  parsedTextPosition = {'x': 100.0, 'y': 100.0};
                }

                // Log timestamp parsing failures for monitoring
                if (data['createdAt'] != null &&
                    _parseTimestamp(data['createdAt']) == null) {
                  // Use unawaited since logging is not critical for main flow
                  unawaited(
                    logTimestampParsingFailure(
                      storyId: doc.id,
                      originalTimestamp: data['createdAt'],
                      error: 'Failed to parse timestamp: ${data['createdAt']}',
                    ),
                  );
                }

                // Story metadata
                final textOverlay = data['textOverlay'] as String?;
                final textColor = data['textColor'] as int?;
                final textSize = (data['textSize'] as num?)?.toDouble();
                final backgroundColor = data['backgroundColor'] as int?;
                final filter = data['filter'] as String?;

                // Safely parse drawingPoints
                List<Map<String, dynamic>>? drawingPoints;
                final rawDrawingPoints = data['drawingPoints'];
                if (rawDrawingPoints is List) {
                  try {
                    drawingPoints = rawDrawingPoints
                        .whereType<Map<String, dynamic>>()
                        .toList();
                  } catch (e) {
                    debugPrint(
                      '⚠️ Invalid drawingPoints format in story ${doc.id}: $e',
                    );
                  }
                }

                final drawingColor = data['drawingColor'] as int?;
                final drawingWidth = (data['drawingWidth'] as num?)?.toDouble();

                // Safely parse tags
                List<StoryTag>? tags;
                final rawTags = data['tags'];
                if (rawTags is List) {
                  try {
                    tags = rawTags
                        .whereType<Map<String, dynamic>>()
                        .map((tag) => StoryTag.fromJson(tag))
                        .whereType<StoryTag>()
                        .toList();
                  } catch (e) {
                    debugPrint('⚠️ Invalid tags format in story ${doc.id}: $e');
                  }
                }

                // Safely parse text elements
                List<TextElement> textElements = [];
                final rawTextElements = data['textElements'];
                if (rawTextElements is List) {
                  try {
                    textElements = rawTextElements
                        .whereType<Map<String, dynamic>>()
                        .map((element) => TextElement.fromJson(element))
                        .whereType<TextElement>()
                        .toList();
                  } catch (e) {
                    debugPrint(
                      '⚠️ Invalid textElements format in story ${doc.id}: $e',
                    );
                  }
                }

                final music = data['music'] as Map<String, dynamic>?;
                final musicArtist = data['musicArtist'] as String?;
                final location = data['location'] as Map<String, dynamic>?;
                final privacy = data['privacy'] as String?;
                final isPublic = data['isPublic'] as bool? ?? true;
                final isSeen = data['isSeen'] as bool? ?? false;
                final isCloseFriend = data['isCloseFriend'] as bool? ?? false;

                return StoryItem(
                  id: doc.id,
                  userId: data['userId'] as String? ?? '',
                  mediaUrl: getValidImageUrl(mediaUrl, fallbackSeed: doc.id),
                  mediaType: MediaType.values.firstWhere(
                    (e) => e.name == mediaType,
                    orElse: () => MediaType.image,
                  ),
                  duration: const Duration(seconds: 5),
                  timestamp:
                      _parseTimestamp(data['createdAt']) ?? DateTime.now(),
                  // Story metadata
                  textOverlay: textOverlay,
                  textColor: textColor,
                  textSize: textSize,
                  textPosition: parsedTextPosition,
                  backgroundColor: backgroundColor,
                  filter: filter,
                  drawingPoints: drawingPoints,
                  drawingColor: drawingColor,
                  drawingWidth: drawingWidth,
                  textElements: textElements,
                  tags: tags,
                  music: music,
                  musicArtist: musicArtist,
                  location: location,
                  privacy: privacy,
                  isPublic: isPublic,
                  isSeen: isSeen,
                  isCloseFriend: isCloseFriend,
                );
              })
              .whereType<StoryItem>()
              .toList();

          // Check if all stories are seen
          final isAllViewed = userStoriesDocs.every((doc) {
            final data = doc.data() as Map<String, dynamic>?;
            return data?['isSeen'] == true;
          });

          final isCloseFriend = await _isCloseFriend(userId);
          debugPrint('👥 User $userId isCloseFriend: $isCloseFriend');

          reels.add(
            StoryReel(
              id: userId, // Use userId as reel ID
              userId: userId,
              username: username,
              userAvatarUrl: userAvatarUrl,
              stories: stories,
              isAllViewed: isAllViewed,
              isCloseFriend: isCloseFriend,
            ),
          );

          debugPrint(
            '✅ Created reel for user $username with ${stories.length} stories',
          );
          debugPrint('  - isAllViewed: $isAllViewed');
          debugPrint('  - isCloseFriend: $isCloseFriend');
        } catch (e) {
          debugPrint('❌ Error creating reel for user $userId: $e');
        }
      }

      debugPrint('🎬 Returning ${reels.length} story reels');
      return reels;
    } catch (e) {
      debugPrint('❌ Error fetching stories: $e');
      throw Exception('Failed to fetch stories: $e');
    }
  }

  // Check if a user is a close friend (simplified implementation)
  Future<bool> _isCloseFriend(String userId) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return false;

      // Check if the user is in the current user's close friends list
      final closeFriendsDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('closeFriends')
          .doc(userId)
          .get();

      return closeFriendsDoc.exists;
    } catch (e) {
      debugPrint('❌ Error checking close friend status: $e');
      throw Exception('Failed to check close friend status: $e');
    }
  }

  // Debug method to add a user as close friend
  Future<void> addCloseFriend(String userId) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return;

      await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('closeFriends')
          .doc(userId)
          .set({'addedAt': FieldValue.serverTimestamp()});

      debugPrint('✅ Added $userId as close friend');
    } catch (e) {
      debugPrint('❌ Error adding close friend: $e');
      throw Exception('Failed to add close friend: $e');
    }
  }

  // Debug method to remove a user from close friends
  Future<void> removeCloseFriend(String userId) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return;

      await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .collection('closeFriends')
          .doc(userId)
          .delete();

      debugPrint('✅ Removed $userId from close friends');
    } catch (e) {
      debugPrint('❌ Error removing close friend: $e');
      throw Exception('Failed to remove close friend: $e');
    }
  }

  // Debug method to create a test story with specific privacy settings
  Future<void> createTestStoryWithPrivacy({
    required bool isCloseFriend,
    required bool isSeen,
    required String title,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return;

      // Get user's story duration preference
      final storySettingsService = getIt<StorySettingsService>();
      final storySettings = await storySettingsService.getSettings();
      final sharedDuration = _convertToSharedDuration(storySettings.duration);
      final userDuration =
          StoryConstants.expirationDurations[sharedDuration] ??
          const Duration(hours: 24);

      // Debug logging to verify duration
      debugPrint('🕐 Story Duration Settings:');
      debugPrint('   User selected: ${storySettings.duration}');
      debugPrint('   Converted to: $sharedDuration');
      debugPrint('   Duration: ${userDuration.inHours} hours');
      debugPrint('   Expires at: ${DateTime.now().add(userDuration)}');

      final storyPayload = {
        'userId': currentUser.uid,
        'mediaUrl':
            'https://picsum.photos/seed/${DateTime.now().millisecondsSinceEpoch}/400/600',
        'mediaType': 'image',
        'filter': 'Normal',
        'textOverlay': title,
        'textColor': 4294967295, // White
        'textSize': 24.0,
        'textPosition': {'x': 100, 'y': 100},
        'isPublic': !isCloseFriend, // Public if not close friend
        'isSeen': isSeen,
        'isCloseFriend': isCloseFriend,
        'createdAt': FieldValue.serverTimestamp(),
        'expiresAt': DateTime.now().add(userDuration),
      };

      await _firestore.collection('stories').add(storyPayload);
      debugPrint(
        '✅ Created test story: $title (isCloseFriend: $isCloseFriend, isSeen: $isSeen)',
      );
    } catch (e) {
      debugPrint('❌ Error creating test story: $e');
      throw Exception('Failed to create test story: $e');
    }
  }

  // Debug method to create test stories for different users
  Future<void> createTestStoriesForUsers() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return;

      // Create a test story for current user (public)
      await createTestStoryWithPrivacy(
        isCloseFriend: false,
        isSeen: false,
        title: 'My Public Story',
      );

      // Create a test story for a close friend
      final closeFriendId = 'close_friend_123';
      await createTestStoryWithPrivacy(
        isCloseFriend: true,
        isSeen: false,
        title: 'Close Friend Story',
      );

      // Add the close friend to current user's close friends list
      await addCloseFriend(closeFriendId);

      debugPrint('✅ Created test stories for different privacy levels');
    } catch (e) {
      debugPrint('❌ Error creating test stories: $e');
      throw Exception('Failed to create test stories: $e');
    }
  }

  // Mark stories as viewed for a specific user
  Future<void> markStoryAsViewed(String userId) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        debugPrint('❌ User not authenticated');
        return;
      }

      debugPrint('👁️ Marking stories as viewed for user: $userId');

      final storiesSnapshot = await _firestore
          .collection('stories')
          .where('userId', isEqualTo: userId)
          .get();

      if (storiesSnapshot.docs.isEmpty) {
        debugPrint('📭 No stories found for user: $userId');
        return;
      }

      debugPrint(
        '📊 Found ${storiesSnapshot.docs.length} stories to mark as viewed',
      );

      // Mark all stories from this user as seen
      final batch = _firestore.batch();
      for (final doc in storiesSnapshot.docs) {
        final storyData = doc.data();
        final storyOwnerId = storyData['userId'] as String?;

        // Only update if the story belongs to someone else (not own stories)
        if (storyOwnerId != null && storyOwnerId != currentUser.uid) {
          batch.update(doc.reference, {
            'isSeen': true,
            'viewCount': FieldValue.increment(1),
            'lastViewedAt': FieldValue.serverTimestamp(),
          });

          debugPrint('✅ Marked story ${doc.id} as viewed');
        } else {
          debugPrint('⏭️ Skipping own story: ${doc.id}');
        }
      }

      await batch.commit();
      debugPrint('✅ Successfully marked stories for user $userId as seen');

      // Send story view notification if not viewing own stories
      if (userId != currentUser.uid) {
        for (final doc in storiesSnapshot.docs) {
          try {
            await _notificationService.createStoryViewNotification(
              userId,
              doc.id,
            );
            debugPrint('📱 Story view notification sent for story: ${doc.id}');
          } catch (e) {
            debugPrint('⚠️ Failed to send story view notification: $e');
            // Continue with other notifications even if one fails
            // Don't let notification errors break the story viewing functionality
          }
        }
      }
    } catch (e) {
      debugPrint('❌ Error marking stories as seen: $e');
      // Don't throw the exception, just log it to prevent app crashes
      // The UI can handle the case where stories aren't marked as viewed
    }
  }

  // Mark a single story item as viewed
  Future<void> markSingleStoryAsViewed(String storyId) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        debugPrint('❌ User not authenticated');
        return;
      }

      debugPrint('👁️ Marking single story as viewed: $storyId');

      final storyDoc = await _firestore
          .collection('stories')
          .doc(storyId)
          .get();

      if (!storyDoc.exists) {
        debugPrint('❌ Story not found: $storyId');
        return;
      }

      final storyData = storyDoc.data()!;
      final storyOwnerId = storyData['userId'] as String?;

      // Only update if the story belongs to someone else (not own story)
      if (storyOwnerId != null && storyOwnerId != currentUser.uid) {
        await _firestore.collection('stories').doc(storyId).update({
          'isSeen': true,
          'viewCount': FieldValue.increment(1),
          'lastViewedAt': FieldValue.serverTimestamp(),
        });

        debugPrint('✅ Marked story $storyId as seen');

        // Send story view notification
        try {
          await _notificationService.createStoryViewNotification(
            storyOwnerId,
            storyId,
          );
          debugPrint('📱 Story view notification sent for story: $storyId');
        } catch (e) {
          debugPrint('⚠️ Failed to send story view notification: $e');
          // Continue even if notification fails
          // Don't let notification errors break the story viewing functionality
        }
      } else {
        debugPrint('⏭️ Skipping own story: $storyId');
      }
    } catch (e) {
      debugPrint('❌ Error marking story $storyId as seen: $e');
      // Don't throw the exception, just log it to prevent app crashes
    }
  }

  Future<StoryItem?> getStoryById(String storyId) async {
    try {
      final doc = await _firestore.collection('stories').doc(storyId).get();
      if (!doc.exists) return null;
      final data = doc.data()!;

      // Parse text elements
      List<TextElement> textElements = [];
      final rawTextElements = data['textElements'];
      if (rawTextElements is List) {
        try {
          textElements = rawTextElements
              .whereType<Map<String, dynamic>>()
              .map((element) => TextElement.fromJson(element))
              .whereType<TextElement>()
              .toList();
        } catch (e) {
          debugPrint('⚠️ Invalid textElements format in story $storyId: $e');
        }
      }

      return StoryItem(
        id: doc.id,
        userId: data['userId'] ?? '',
        mediaUrl: getValidImageUrl(data['mediaUrl'], fallbackSeed: doc.id),
        mediaType: MediaType.values.firstWhere(
          (e) => e.name == (data['mediaType'] ?? 'image'),
          orElse: () => MediaType.image,
        ),
        duration: const Duration(seconds: 5),
        timestamp: _parseTimestamp(data['createdAt']) ?? DateTime.now(),
        textElements: textElements,
      );
    } catch (e) {
      debugPrint('Error getting story by ID: $e');
      throw Exception('Failed to get story by ID: $e');
    }
  }

  Future<Map<String, String>> getUserDetails(String userId) async {
    try {
      final userProfile = await _firebaseService.getUserProfile(userId);
      return {
        'name': getUsernameFromProfile(userProfile),
        'profilePictureUrl': getValidAvatarUrl(
          userProfile?.profilePictureUrl,
          userId: userId,
        ),
      };
    } catch (e) {
      debugPrint('Error getting user details: $e');
      throw Exception('Failed to get user details: $e');
    }
  }

  // Track story view for analytics
  Future<void> trackStoryView(String storyId, String viewerId) async {
    try {
      final viewData = {
        'storyId': storyId,
        'viewerId': viewerId,
        'timestamp': FieldValue.serverTimestamp(),
        'deviceId': await _getDeviceId(),
        'viewCount': 1,
      };

      // Use storyId/viewerId as document ID to prevent duplicate views
      final viewRef = _firestore
          .collection('story_views')
          .doc(storyId)
          .collection('viewers')
          .doc(viewerId);

      await viewRef.set(viewData);

      // Update story view count
      await _firestore.collection('stories').doc(storyId).update({
        'viewCount': FieldValue.increment(1),
        'lastViewedAt': FieldValue.serverTimestamp(),
      });

      debugPrint('✅ Story view tracked: $storyId by $viewerId');
    } catch (e) {
      debugPrint('❌ Error tracking story view: $e');
      throw Exception('Failed to track story view: $e');
    }
  }

  // Get story analytics
  Future<Map<String, dynamic>> getStoryAnalytics(String storyId) async {
    try {
      final viewsSnapshot = await _firestore
          .collection('story_views')
          .doc(storyId)
          .collection('viewers')
          .get();

      final storyDoc = await _firestore
          .collection('stories')
          .doc(storyId)
          .get();
      final storyData = storyDoc.data();

      return {
        'totalViews': viewsSnapshot.docs.length,
        'uniqueViewers': viewsSnapshot.docs.length,
        'createdAt': storyData?['createdAt'],
        'expiresAt': storyData?['expiresAt'],
        'viewers': viewsSnapshot.docs.map((doc) => doc.data()).toList(),
      };
    } catch (e) {
      debugPrint('❌ Error getting story analytics: $e');
      throw Exception('Failed to get story analytics: $e');
    }
  }

  // Get device ID for analytics
  Future<String> _getDeviceId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      String? deviceId = prefs.getString('device_id');

      if (deviceId == null) {
        deviceId = _uuid.v4();
        await prefs.setString('device_id', deviceId);
      }

      return deviceId;
    } catch (e) {
      return _uuid.v4();
    }
  }

  // Preview music track
  Future<void> previewMusic(String musicPath) async {
    try {
      if (_isPlaying && _currentMusicPath == musicPath) {
        // Stop current preview
        await _audioPlayer.stop();
        _isPlaying = false;
        _currentMusicPath = null;
        return;
      }

      // Stop any currently playing music
      if (_isPlaying) {
        await _audioPlayer.stop();
      }

      // Start new preview
      await _audioPlayer.play(AssetSource(musicPath));
      _isPlaying = true;
      _currentMusicPath = musicPath;

      debugPrint('✅ Playing music preview: $musicPath');
    } catch (e) {
      debugPrint('❌ Error playing music preview: $e');
      throw Exception('Failed to play music preview: $e');
    }
  }

  // Stop music preview
  Future<void> stopMusicPreview() async {
    try {
      await _audioPlayer.stop();
      _isPlaying = false;
      _currentMusicPath = null;
      debugPrint('✅ Music preview stopped');
    } catch (e) {
      debugPrint('❌ Error stopping music preview: $e');
      throw Exception('Failed to stop music preview: $e');
    }
  }

  // Get music preview status
  bool get isPlaying => _isPlaying;
  String? get currentMusicPath => _currentMusicPath;

  // Dispose audio player
  void dispose() {
    _audioPlayer.dispose();
  }

  // Search locations using reverse geocoding
  Future<List<LocationResult>> searchLocations(String query) async {
    try {
      _isSearchingLocation = true;

      // For now, we'll use a simple approach with predefined locations
      // In a real app, you'd integrate with Google Places API or similar
      final predefinedLocations = [
        LocationResult(
          name: 'New York, NY',
          address: 'New York, New York, United States',
          latitude: 40.7128,
          longitude: -74.0060,
        ),
        LocationResult(
          name: 'Los Angeles, CA',
          address: 'Los Angeles, California, United States',
          latitude: 34.0522,
          longitude: -118.2437,
        ),
        LocationResult(
          name: 'London, UK',
          address: 'London, England, United Kingdom',
          latitude: 51.5074,
          longitude: -0.1278,
        ),
        LocationResult(
          name: 'Paris, France',
          address: 'Paris, Île-de-France, France',
          latitude: 48.8566,
          longitude: 2.3522,
        ),
        LocationResult(
          name: 'Tokyo, Japan',
          address: 'Tokyo, Japan',
          latitude: 35.6762,
          longitude: 139.6503,
        ),
      ];

      // Filter locations based on query
      final filteredLocations = predefinedLocations
          .where(
            (location) =>
                location.name.toLowerCase().contains(query.toLowerCase()) ||
                location.address.toLowerCase().contains(query.toLowerCase()),
          )
          .toList();

      _locationResults = filteredLocations;
      return filteredLocations;
    } catch (e) {
      debugPrint('❌ Error searching locations: $e');
      throw Exception('Failed to search locations: $e');
    } finally {
      _isSearchingLocation = false;
    }
  }

  // Get current location
  Future<LocationResult?> getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        debugPrint('❌ Location services are disabled');
        return null;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          debugPrint('❌ Location permissions denied');
          return null;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        debugPrint('❌ Location permissions permanently denied');
        return null;
      }

      Position position = await Geolocator.getCurrentPosition();

      // For now, return a simple location result
      // In a real app, you'd use reverse geocoding to get the address
      return LocationResult(
        name: 'Current Location',
        address:
            '${position.latitude.toStringAsFixed(4)}, ${position.longitude.toStringAsFixed(4)}',
        latitude: position.latitude,
        longitude: position.longitude,
      );
    } catch (e) {
      debugPrint('❌ Error getting current location: $e');
      throw Exception('Failed to get current location: $e');
    }
  }

  // Get location search status
  bool get isSearchingLocation => _isSearchingLocation;
  List<LocationResult> get locationResults => _locationResults;

  // Story expiration logic
  static const Duration _storyLifetime = Duration(hours: 24);

  // Check if a story is expired
  bool isStoryExpired(DateTime createdAt) {
    final now = DateTime.now();
    final expirationTime = createdAt.add(_storyLifetime);
    return now.isAfter(expirationTime);
  }

  // Get time remaining until story expires
  Duration getTimeRemaining(DateTime createdAt) {
    final now = DateTime.now();
    final expirationTime = createdAt.add(_storyLifetime);
    return expirationTime.difference(now);
  }

  // Filter out expired stories
  List<StoryItem> filterExpiredStories(List<StoryItem> stories) {
    return stories.where((story) => !isStoryExpired(story.timestamp)).toList();
  }

  // Delete expired stories (for cleanup)
  Future<void> deleteExpiredStories() async {
    try {
      final twentyFourHoursAgo = DateTime.now().subtract(_storyLifetime);

      final expiredStoriesSnapshot = await _firestore
          .collection('stories')
          .where(
            'createdAt',
            isLessThan: Timestamp.fromDate(twentyFourHoursAgo),
          )
          .get();

      if (expiredStoriesSnapshot.docs.isNotEmpty) {
        final batch = _firestore.batch();

        for (final doc in expiredStoriesSnapshot.docs) {
          batch.delete(doc.reference);
        }

        await batch.commit();
        debugPrint(
          '✅ Deleted ${expiredStoriesSnapshot.docs.length} expired stories',
        );
      }
    } catch (e) {
      debugPrint('❌ Error deleting expired stories: $e');
      throw Exception('Failed to delete expired stories: $e');
    }
  }

  // Delete a story
  Future<void> deleteStory(String storyId) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Get the story document to extract media URL
      final storyDoc = await _firestore
          .collection('stories')
          .doc(storyId)
          .get();
      if (!storyDoc.exists) {
        throw Exception('Story not found');
      }

      final storyData = storyDoc.data()!;
      final mediaUrl = storyData['mediaUrl'] as String?;

      // Delete from Firestore
      await _firestore.collection('stories').doc(storyId).delete();

      // Delete from Firebase Storage if media URL exists
      if (mediaUrl != null && mediaUrl.isNotEmpty) {
        try {
          final storageRef = FirebaseStorage.instance.refFromURL(mediaUrl);
          await storageRef.delete();
          debugPrint('✅ Deleted media from Firebase Storage');
        } catch (e) {
          debugPrint('⚠️ Could not delete media from Storage: $e');
          // Continue even if storage deletion fails
        }
      }

      debugPrint('✅ Story $storyId deleted successfully');
    } catch (e) {
      debugPrint('❌ Error deleting story: $e');
      throw Exception('Failed to delete story: $e');
    }
  }

  // Create a story with fallback media handling
  Future<void> createStoryWithFallback({
    required String mediaUrl,
    required String mediaType,
    String? filter,
    String? textOverlay,
    int? textColor,
    double? textSize,
    Map<String, dynamic>? textPosition,
    bool isPublic = true,
    bool isCloseFriend = false,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Validate and provide fallback for mediaUrl
      final validatedMediaUrl = await _validateAndGetFallbackMediaUrl(
        mediaUrl,
        mediaType,
      );

      // Get user's story duration preference
      final storySettingsService = getIt<StorySettingsService>();
      final storySettings = await storySettingsService.getSettings();

      // Convert settings duration to shared duration enum
      final sharedDuration = _convertToSharedDuration(storySettings.duration);
      final userDuration =
          StoryConstants.expirationDurations[sharedDuration] ??
          const Duration(hours: 24);

      debugPrint(
        '⏰ Using user duration preference: ${storySettings.duration} (${userDuration.inHours}h)',
      );

      final storyPayload = {
        'userId': currentUser.uid,
        'mediaUrl': validatedMediaUrl,
        'mediaType': mediaType,
        'filter': filter ?? 'Normal',
        'textOverlay': textOverlay ?? '',
        'textColor': textColor ?? 4294967295, // White
        'textSize': textSize ?? 24.0,
        'textPosition': textPosition ?? {'x': 100, 'y': 100},
        'isPublic': isPublic,
        'isSeen': false,
        'isCloseFriend': isCloseFriend,
        'createdAt': FieldValue.serverTimestamp(),
        'expiresAt': DateTime.now().add(userDuration),
        'viewCount': 0,
        'viewedBy': [],
        'reactions': [],
        'comments': [],
        'shares': 0,
        'isActive': true,
        'metadata': {
          'originalMediaUrl': mediaUrl,
          'hasFallback': mediaUrl != validatedMediaUrl,
          'createdVia': 'app',
          'version': '1.0',
          'userDurationPreference': storySettings.duration.name,
        },
      };

      await _firestore.collection('stories').add(storyPayload);

      debugPrint('✅ Story created successfully with fallback handling');
      debugPrint('   Original URL: $mediaUrl');
      debugPrint('   Final URL: $validatedMediaUrl');
      debugPrint('   Has fallback: ${mediaUrl != validatedMediaUrl}');
    } catch (e) {
      debugPrint('❌ Error creating story with fallback: $e');
      throw Exception('Failed to create story: $e');
    }
  }

  // Validate media URL and provide fallback if needed
  Future<String> _validateAndGetFallbackMediaUrl(
    String mediaUrl,
    String mediaType,
  ) async {
    if (mediaUrl.isEmpty) {
      debugPrint('⚠️ Empty mediaUrl provided, using fallback');
      return _getFallbackMediaUrl(mediaType);
    }

    // Check if URL is valid
    if (!_isValidUrl(mediaUrl)) {
      debugPrint('⚠️ Invalid URL format: $mediaUrl, using fallback');
      return _getFallbackMediaUrl(mediaType);
    }

    // For now, we'll assume the URL is valid
    // In production, you might want to make a HEAD request to verify the URL
    return mediaUrl;
  }

  // Get fallback media URL based on media type
  String _getFallbackMediaUrl(String mediaType) {
    switch (mediaType.toLowerCase()) {
      case 'image':
        return 'https://picsum.photos/seed/${DateTime.now().millisecondsSinceEpoch}/400/600';
      case 'video':
        return 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4';
      default:
        return 'https://picsum.photos/seed/${DateTime.now().millisecondsSinceEpoch}/400/600';
    }
  }

  // Validate URL format
  bool _isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && uri.hasAuthority;
    } catch (e) {
      return false;
    }
  }

  // Clean up invalid stories (remove stories with empty mediaUrl)
  Future<void> cleanupInvalidStories() async {
    try {
      debugPrint('🧹 Starting cleanup of invalid stories...');

      final storiesSnapshot = await _firestore
          .collection('stories')
          .where('mediaUrl', isEqualTo: '')
          .get();

      final batch = _firestore.batch();
      int deletedCount = 0;

      for (final doc in storiesSnapshot.docs) {
        batch.delete(doc.reference);
        deletedCount++;
      }

      if (deletedCount > 0) {
        await batch.commit();
        debugPrint('✅ Cleaned up $deletedCount invalid stories');
      } else {
        debugPrint('✅ No invalid stories found');
      }
    } catch (e) {
      debugPrint('❌ Error cleaning up invalid stories: $e');
    }
  }

  // Monitor and log timestamp parsing failures
  Future<void> logTimestampParsingFailure({
    required String storyId,
    required dynamic originalTimestamp,
    required String error,
  }) async {
    try {
      await _firestore.collection('error_logs').add({
        'type': 'timestamp_parsing_failure',
        'storyId': storyId,
        'originalTimestamp': originalTimestamp.toString(),
        'error': error,
        'timestamp': FieldValue.serverTimestamp(),
        'severity': 'warning',
      });

      debugPrint('📝 Logged timestamp parsing failure for story $storyId');
    } catch (e) {
      debugPrint('❌ Error logging timestamp parsing failure: $e');
    }
  }

  // Utility method to validate image URLs and provide fallbacks
  String getValidImageUrl(String? url, {String? fallbackSeed}) {
    if (url == null || url.isEmpty) {
      final seed =
          fallbackSeed ?? DateTime.now().millisecondsSinceEpoch.toString();
      return 'https://picsum.photos/seed/$seed/400/600';
    }

    // Check if URL has a valid scheme
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      final seed =
          fallbackSeed ?? DateTime.now().millisecondsSinceEpoch.toString();
      return 'https://picsum.photos/seed/$seed/400/600';
    }

    return url;
  }

  // Utility method to validate avatar URLs and provide fallbacks
  String getValidAvatarUrl(String? url, {String? userId}) {
    if (url == null || url.isEmpty) {
      final seed = userId ?? DateTime.now().millisecondsSinceEpoch.toString();
      return 'https://i.pravatar.cc/150?u=$seed';
    }

    // Check if URL has a valid scheme
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      final seed = userId ?? DateTime.now().millisecondsSinceEpoch.toString();
      return 'https://i.pravatar.cc/150?u=$seed';
    }

    return url;
  }

  // Utility method to get username from user profile with fallback
  String getUsernameFromProfile(ProfileModel? userProfile) {
    return userProfile?.name ?? userProfile?.username ?? 'Unknown User';
  }

  DateTime? _parseTimestamp(dynamic timestamp) {
    if (timestamp == null) return null;

    if (timestamp is Timestamp) {
      return timestamp.toDate();
    } else if (timestamp is DateTime) {
      return timestamp;
    } else if (timestamp is String) {
      try {
        return DateTime.parse(timestamp);
      } catch (e) {
        debugPrint('⚠️ Could not parse timestamp string: $timestamp');
        return null;
      }
    } else {
      debugPrint('⚠️ Unknown timestamp type: ${timestamp.runtimeType}');
      return null;
    }
  }

  // Simple test method to create a story for current user
  Future<void> createSimpleTestStory() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return;

      final storyPayload = {
        'userId': currentUser.uid,
        'mediaUrl':
            'https://picsum.photos/seed/${DateTime.now().millisecondsSinceEpoch}/400/600',
        'mediaType': 'image',
        'filter': 'Normal',
        'textOverlay': 'Test Story - ${DateTime.now().millisecondsSinceEpoch}',
        'textColor': 4294967295, // White
        'textSize': 24.0,
        'textPosition': {'x': 100, 'y': 100},
        'isPublic': true,
        'isSeen': false,
        'isCloseFriend': false,
        'createdAt': FieldValue.serverTimestamp(),
        'expiresAt': DateTime.now().add(const Duration(hours: 24)),
      };

      await _firestore.collection('stories').add(storyPayload);
      debugPrint('✅ Created simple test story for current user');
    } catch (e) {
      debugPrint('❌ Error creating simple test story: $e');
    }
  }

  double _safeToDouble(dynamic value) {
    if (value == null) {
      return 0.0;
    } else if (value is num) {
      return value.toDouble();
    } else if (value is String) {
      try {
        return double.parse(value);
      } catch (e) {
        debugPrint('⚠️ Could not parse string to double: $value');
        return 0.0;
      }
    } else {
      debugPrint('⚠️ Invalid type for double conversion: ${value.runtimeType}');
      return 0.0;
    }
  }

  // Migrate existing stories with incorrect textPosition data types
  Future<void> migrateTextPositionDataTypes() async {
    try {
      debugPrint('🔄 Starting textPosition data type migration...');

      final storiesSnapshot = await _firestore.collection('stories').get();
      int migratedCount = 0;

      for (final doc in storiesSnapshot.docs) {
        final data = doc.data();
        final textPosition = data['textPosition'];

        if (textPosition is Map) {
          bool needsMigration = false;
          Map<String, double> correctedPosition = {};

          // Check if any values are not doubles
          for (final entry in textPosition.entries) {
            if (entry.value is int) {
              needsMigration = true;
              correctedPosition[entry.key] = entry.value.toDouble();
            } else if (entry.value is double) {
              correctedPosition[entry.key] = entry.value;
            } else {
              needsMigration = true;
              correctedPosition[entry.key] = _safeToDouble(entry.value);
            }
          }

          if (needsMigration) {
            await doc.reference.update({'textPosition': correctedPosition});
            migratedCount++;
            debugPrint('✅ Migrated story ${doc.id}');
          }
        }
      }

      debugPrint('✅ Migration completed. Migrated $migratedCount stories');
    } catch (e) {
      debugPrint('❌ Error during textPosition migration: $e');
    }
  }

  // Test method to create a story with mixed data types to verify parsing
  Future<void> createTestStoryWithMixedDataTypes() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return;

      final storyPayload = {
        'userId': currentUser.uid,
        'mediaUrl':
            'https://picsum.photos/seed/${DateTime.now().millisecondsSinceEpoch}/400/600',
        'mediaType': 'image',
        'filter': 'Normal',
        'textOverlay': 'Test Story with Mixed Data Types',
        'textColor': 4294967295, // White
        'textSize': 24, // Int instead of double
        'textPosition': {'x': 100, 'y': 200}, // Ints instead of doubles
        'backgroundColor': 0x00000000, // Transparent
        'isPublic': true,
        'isSeen': false,
        'isCloseFriend': false,
        'createdAt': FieldValue.serverTimestamp(),
        'expiresAt': DateTime.now().add(const Duration(hours: 24)),
      };

      await _firestore.collection('stories').add(storyPayload);
      debugPrint('✅ Created test story with mixed data types');
    } catch (e) {
      debugPrint('❌ Error creating test story with mixed data types: $e');
    }
  }

  /// Convert settings StoryDuration to shared StoryDuration enum
  StoryDuration _convertToSharedDuration(
    story_settings_model.StoryDuration settingsDuration,
  ) {
    switch (settingsDuration) {
      case story_settings_model.StoryDuration.sixHours:
        return StoryDuration.sixHours;
      case story_settings_model.StoryDuration.twelveHours:
        return StoryDuration.twelveHours;
      case story_settings_model.StoryDuration.twentyFourHours:
        return StoryDuration.twentyFourHours;
    }
  }
}
