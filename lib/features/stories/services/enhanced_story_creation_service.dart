import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/shared/story_shared_models.dart';
import '../models/story_settings_model.dart' as settings;
import '../models/unified_story_model.dart';
import '../services/story_settings_service.dart';
import '../../../core/service_locator.dart';

/// Enhanced story creation service with validation, retry logic, and better error handling
class EnhancedStoryCreationService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Validation constants
  static const int maxFileSizeMB = 50;
  static const int maxImageFileSizeMB = 25; // Separate limit for images
  static const int maxVideoFileSizeMB = 100; // Higher limit for videos
  static const int maxTextLength = 2000;
  static const int minImageWidth = 100; // Minimum image dimensions
  static const int minImageHeight = 100;
  static const int maxImageWidth = 4096; // Maximum image dimensions
  static const int maxImageHeight = 4096;
  static const int maxVideoWidth = 1920; // Maximum video dimensions
  static const int maxVideoHeight = 1080;
  static const Duration minVideoDuration = Duration(seconds: 1);
  static const Duration maxVideoDuration = Duration(minutes: 5);
  static const int maxDrawingPoints = 10000; // Limit drawing complexity
  static const int maxTextElements = 50; // Limit text overlays
  static const int maxMentions = 20; // Limit mentions per story
  static const int maxHashtags = 30; // Limit hashtags per story
  static const List<String> supportedImageFormats = [
    'jpg',
    'jpeg',
    'png',
    'gif',
    'webp',
    'bmp', // Bitmap format
    'tiff', // Tagged Image File Format
    'tif', // TIFF alternative extension
    'svg', // Scalable Vector Graphics (if supported by your image processing)
    'heic', // High Efficiency Image Container (iOS)
    'heif', // High Efficiency Image Format
  ];
  static const List<String> supportedVideoFormats = [
    'mp4',
    'mov',
    'avi',
    'mkv',
    'webm', // Web video format
    'flv', // Flash video (still used)
    'm4v', // iTunes video format
    '3gp', // Mobile video format
  ];
  static const List<String> supportedAudioFormats = [
    'mp3', // MPEG Audio Layer 3
    'aac', // Advanced Audio Coding
    'wav', // Waveform Audio File Format
    'm4a', // MPEG-4 Audio
    'ogg', // Ogg Vorbis
    'flac', // Free Lossless Audio Codec
    'wma', // Windows Media Audio
  ];
  static const int maxAudioFileSizeMB = 10; // Audio file size limit
  static const Duration maxAudioDuration = Duration(
    minutes: 2,
  ); // Audio duration limit

  /// Public static method to validate media file
  static Future<ValidationResult> validateMediaFile(File file) async {
    try {
      // Check if file exists
      if (!await file.exists()) {
        return ValidationResult.invalid('Selected file does not exist');
      }

      // Check file size with format-specific limits
      final fileSizeBytes = await file.length();
      final fileSizeMB = fileSizeBytes / (1024 * 1024);
      final extension = file.path.split('.').last.toLowerCase();
      final isImage = supportedImageFormats.contains(extension);
      final isVideo = supportedVideoFormats.contains(extension);
      final isAudio = supportedAudioFormats.contains(extension);

      if (isImage && fileSizeMB > maxImageFileSizeMB) {
        return ValidationResult.invalid(
          'Image file size must be less than ${maxImageFileSizeMB}MB',
        );
      } else if (isVideo && fileSizeMB > maxVideoFileSizeMB) {
        return ValidationResult.invalid(
          'Video file size must be less than ${maxVideoFileSizeMB}MB',
        );
      } else if (isAudio && fileSizeMB > maxAudioFileSizeMB) {
        return ValidationResult.invalid(
          'Audio file size must be less than ${maxAudioFileSizeMB}MB',
        );
      } else if (fileSizeMB > maxFileSizeMB) {
        return ValidationResult.invalid(
          'File size must be less than ${maxFileSizeMB}MB',
        );
      }

      // Check if format is supported
      if (!isImage && !isVideo && !isAudio) {
        return ValidationResult.invalid(
          'Unsupported file format. Supported formats: ${[...supportedImageFormats, ...supportedVideoFormats, ...supportedAudioFormats].join(', ')}',
        );
      }

      return ValidationResult.valid();
    } catch (e) {
      return ValidationResult.invalid('Error validating file: $e');
    }
  }

  /// Public static method to validate text content
  static ValidationResult validateTextContent(String textContent) {
    if (textContent.length > maxTextLength) {
      return ValidationResult.invalid(
        'Text content is too long (max $maxTextLength characters)',
      );
    }

    if (textContent.trim().isEmpty) {
      return ValidationResult.invalid('Text content cannot be empty');
    }

    return ValidationResult.valid();
  }

  /// Create story with comprehensive validation and error handling
  Future<StoryCreationResult> createStory({
    File? mediaFile,
    String? textContent,
    required StoryMediaType mediaType,
    String? caption,
    List<TextElement>? textElements,
    Color? backgroundColor,
    String? filter,
    List<DrawingPoint>? drawingPoints,
    List<String>? mentions,
    List<String>? hashtags,
  }) async {
    // Validate input limits early
    if (textElements != null && textElements.length > maxTextElements) {
      return StoryCreationResult.failure(
        error: 'Too many text elements (max $maxTextElements)',
        errorType: StoryCreationErrorType.validation,
      );
    }

    if (drawingPoints != null && drawingPoints.length > maxDrawingPoints) {
      return StoryCreationResult.failure(
        error: 'Drawing is too complex (max $maxDrawingPoints points)',
        errorType: StoryCreationErrorType.validation,
      );
    }

    if (mentions != null && mentions.length > maxMentions) {
      return StoryCreationResult.failure(
        error: 'Too many mentions (max $maxMentions)',
        errorType: StoryCreationErrorType.validation,
      );
    }

    if (hashtags != null && hashtags.length > maxHashtags) {
      return StoryCreationResult.failure(
        error: 'Too many hashtags (max $maxHashtags)',
        errorType: StoryCreationErrorType.validation,
      );
    }
    try {
      // 1. Validate inputs
      final validationResult = await _validateInputs(
        mediaFile: mediaFile,
        textContent: textContent,
        mediaType: mediaType,
        textElements: textElements,
      );

      if (!validationResult.isValid) {
        return StoryCreationResult.failure(
          error: validationResult.error!,
          errorType: StoryCreationErrorType.validation,
        );
      }

      // 2. Get user settings
      final settings = await _getUserSettings();

      // 3. Process media if needed
      String? mediaUrl;
      if (mediaFile != null) {
        final uploadResult = await _uploadMediaWithRetry(
          mediaFile,
          maxRetries: 3,
        );
        if (!uploadResult.success) {
          return StoryCreationResult.failure(
            error: uploadResult.error!,
            errorType: StoryCreationErrorType.upload,
          );
        }
        mediaUrl = uploadResult.url;
      }

      // 4. Create story document
      final story = await _createStoryDocument(
        mediaUrl: mediaUrl,
        textContent: textContent,
        mediaType: mediaType,
        caption: caption,
        textElements: textElements ?? [],
        backgroundColor: backgroundColor,
        filter: filter,
        drawingPoints: drawingPoints ?? [],
        mentions: mentions ?? [],
        hashtags: hashtags ?? [],
        settings: settings,
      );

      // 5. Process mentions and notifications
      await _processMentionsAsync(story.id, mentions ?? []);

      return StoryCreationResult.success(story: story);
    } catch (e) {
      debugPrint('❌ Error creating story: $e');
      return StoryCreationResult.failure(
        error: 'Failed to create story: ${e.toString()}',
        errorType: StoryCreationErrorType.unknown,
      );
    }
  }

  /// Validate all inputs before processing
  Future<ValidationResult> _validateInputs({
    File? mediaFile,
    String? textContent,
    required StoryMediaType mediaType,
    List<TextElement>? textElements,
  }) async {
    // Check authentication
    if (_auth.currentUser == null) {
      return ValidationResult.invalid(
        'You must be logged in to create stories',
      );
    }

    // Validate based on media type
    switch (mediaType) {
      case StoryMediaType.image:
      case StoryMediaType.video:
        if (mediaFile == null) {
          return ValidationResult.invalid('Media file is required');
        }
        return await _validateMediaFile(mediaFile, mediaType);

      case StoryMediaType.text:
        if (textContent == null || textContent.trim().isEmpty) {
          if (textElements == null || textElements.isEmpty) {
            return ValidationResult.invalid(
              'Text content is required for text stories',
            );
          }
        }
        return _validateTextContent(textContent, textElements);

      case StoryMediaType.music:
      case StoryMediaType.poll:
      case StoryMediaType.question:
      case StoryMediaType.countdown:
      case StoryMediaType.quiz:
        return ValidationResult.invalid('This story type is not yet supported');
    }
  }

  /// Validate media file
  Future<ValidationResult> _validateMediaFile(
    File file,
    StoryMediaType mediaType,
  ) async {
    try {
      // Check if file exists
      if (!await file.exists()) {
        return ValidationResult.invalid('Selected file does not exist');
      }

      // Check file size
      final fileSizeBytes = await file.length();
      final fileSizeMB = fileSizeBytes / (1024 * 1024);
      if (fileSizeMB > maxFileSizeMB) {
        return ValidationResult.invalid(
          'File size must be less than ${maxFileSizeMB}MB',
        );
      }

      // Check file format
      final extension = file.path.split('.').last.toLowerCase();
      final supportedFormats = mediaType == StoryMediaType.image
          ? supportedImageFormats
          : supportedVideoFormats;

      if (!supportedFormats.contains(extension)) {
        return ValidationResult.invalid(
          'Unsupported file format. Supported formats: ${supportedFormats.join(', ')}',
        );
      }

      // Additional video validation
      if (mediaType == StoryMediaType.video) {
        // Note: You might want to add video duration validation here
        // This would require a video processing library
      }

      return ValidationResult.valid();
    } catch (e) {
      return ValidationResult.invalid('Error validating file: $e');
    }
  }

  /// Validate text content
  ValidationResult _validateTextContent(
    String? textContent,
    List<TextElement>? textElements,
  ) {
    final totalTextLength =
        (textContent?.length ?? 0) +
        (textElements?.fold<int>(
              0,
              (total, element) => total + element.text.length,
            ) ??
            0);

    if (totalTextLength > maxTextLength) {
      return ValidationResult.invalid(
        'Text content is too long (max $maxTextLength characters)',
      );
    }

    return ValidationResult.valid();
  }

  /// Upload media with retry logic
  Future<UploadResult> _uploadMediaWithRetry(
    File file, {
    int maxRetries = 3,
  }) async {
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        debugPrint('📤 Upload attempt $attempt/$maxRetries');

        final user = _auth.currentUser!;
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final extension = file.path.split('.').last;
        final fileName = '${timestamp}_story.$extension';

        final ref = _storage.ref().child('stories/${user.uid}/$fileName');

        // Upload with progress tracking
        final uploadTask = ref.putFile(file);

        // You can add progress tracking here if needed
        uploadTask.snapshotEvents.listen((snapshot) {
          final progress = snapshot.bytesTransferred / snapshot.totalBytes;
          debugPrint(
            '📤 Upload progress: ${(progress * 100).toStringAsFixed(1)}%',
          );
        });

        final snapshot = await uploadTask;
        final downloadUrl = await snapshot.ref.getDownloadURL();

        debugPrint('✅ Media uploaded successfully: $downloadUrl');
        return UploadResult.success(url: downloadUrl);
      } catch (e) {
        debugPrint('❌ Upload attempt $attempt failed: $e');

        if (attempt == maxRetries) {
          return UploadResult.failure(
            error: 'Failed to upload media after $maxRetries attempts: $e',
          );
        }

        // Wait before retry (exponential backoff)
        await Future.delayed(Duration(seconds: attempt * 2));
      }
    }

    return UploadResult.failure(error: 'Upload failed after all retries');
  }

  /// Get user story settings
  Future<settings.StorySettings> _getUserSettings() async {
    try {
      final settingsService = getIt<StorySettingsService>();
      return await settingsService.getSettings();
    } catch (e) {
      debugPrint('⚠️ Error getting user settings, using defaults: $e');
      // Return default settings if there's an error
      return settings.StorySettings(
        id: _auth.currentUser?.uid ?? 'default',
        duration: settings.StoryDuration.twentyFourHours,
        visibility: settings.StoryVisibility.public,
        hiddenFromUserIds: [],
        allowEditing: true,
        allowComments: true,
        allowReactions: true,
        allowMentions: true,
        allowMusic: true,
        allowFilters: true,
        allowTextOverlays: true,
        allowVideoUpload: true,
        allowedGroupIds: [],
      );
    }
  }

  /// Create story document in Firestore
  Future<UnifiedStory> _createStoryDocument({
    String? mediaUrl,
    String? textContent,
    required StoryMediaType mediaType,
    String? caption,
    required List<TextElement> textElements,
    Color? backgroundColor,
    String? filter,
    required List<DrawingPoint> drawingPoints,
    required List<String> mentions,
    required List<String> hashtags,
    required settings.StorySettings settings,
  }) async {
    final user = _auth.currentUser!;
    final now = DateTime.now();
    final storyId = _firestore.collection('stories').doc().id;

    // Calculate expiration based on user settings
    final duration =
        StoryConstants.expirationDurations[_convertToSharedDuration(
          settings.duration,
        )] ??
        const Duration(hours: 24);

    final expiresAt = now.add(duration);

    final story = UnifiedStory(
      id: storyId,
      userId: user.uid,
      userName: user.displayName ?? 'Unknown',
      userAvatarUrl: user.photoURL ?? '',
      mediaUrl: mediaUrl ?? '',
      mediaType: mediaType,
      textOverlay: textContent,
      caption: caption,
      textElements: textElements,
      backgroundColor: backgroundColor?.toARGB32(),
      filter: filter,
      drawingPoints: drawingPoints,
      mentions: mentions,
      hashtags: hashtags,
      createdAt: now,
      expiresAt: expiresAt,
      duration: duration,
      timestamp: now,
      privacy: _convertToStoryPrivacy(settings.visibility),
      visibility: _convertToSharedVisibility(settings.visibility),
      allowedTo: [],
      hiddenFromUserIds: settings.hiddenFromUserIds,
    );

    // Save to Firestore
    await _firestore
        .collection('stories')
        .doc(storyId)
        .set(story.toFirestore());

    debugPrint('✅ Story created successfully: $storyId');
    debugPrint('   Duration: ${duration.inHours}h');
    debugPrint('   Expires: $expiresAt');

    return story;
  }

  /// Process mentions asynchronously
  Future<void> _processMentionsAsync(
    String storyId,
    List<String> mentions,
  ) async {
    if (mentions.isEmpty) return;

    try {
      // Process mentions in background
      // This could include sending notifications, etc.
      debugPrint(
        '📧 Processing ${mentions.length} mentions for story $storyId',
      );
      // Implementation depends on your notification system
    } catch (e) {
      debugPrint('⚠️ Error processing mentions: $e');
      // Don't fail story creation if mentions fail
    }
  }

  /// Convert settings duration to shared duration
  StoryDuration _convertToSharedDuration(
    settings.StoryDuration settingsDuration,
  ) {
    switch (settingsDuration) {
      case settings.StoryDuration.sixHours:
        return StoryDuration.sixHours;
      case settings.StoryDuration.twelveHours:
        return StoryDuration.twelveHours;
      case settings.StoryDuration.twentyFourHours:
        return StoryDuration.twentyFourHours;
    }
  }

  /// Convert settings visibility to story privacy
  StoryPrivacy _convertToStoryPrivacy(settings.StoryVisibility visibility) {
    switch (visibility) {
      case settings.StoryVisibility.public:
        return StoryPrivacy.public;
      case settings.StoryVisibility.followers:
        return StoryPrivacy.followers;
      case settings.StoryVisibility.specificGroups:
        return StoryPrivacy.custom; // Map specificGroups to custom
      case settings.StoryVisibility.closeFriends:
        return StoryPrivacy.closeFriends;
    }
  }

  /// Convert settings visibility to shared visibility
  StoryVisibility _convertToSharedVisibility(
    settings.StoryVisibility visibility,
  ) {
    switch (visibility) {
      case settings.StoryVisibility.public:
        return StoryVisibility.public;
      case settings.StoryVisibility.followers:
        return StoryVisibility.followers;
      case settings.StoryVisibility.specificGroups:
        return StoryVisibility.specificGroups;
      case settings.StoryVisibility.closeFriends:
        return StoryVisibility.closeFriends;
    }
  }
}

/// Result classes for better error handling
class StoryCreationResult {
  final bool success;
  final UnifiedStory? story;
  final String? error;
  final StoryCreationErrorType? errorType;

  StoryCreationResult._({
    required this.success,
    this.story,
    this.error,
    this.errorType,
  });

  factory StoryCreationResult.success({required UnifiedStory story}) {
    return StoryCreationResult._(success: true, story: story);
  }

  factory StoryCreationResult.failure({
    required String error,
    required StoryCreationErrorType errorType,
  }) {
    return StoryCreationResult._(
      success: false,
      error: error,
      errorType: errorType,
    );
  }
}

class ValidationResult {
  final bool isValid;
  final String? error;

  ValidationResult._(this.isValid, this.error);

  factory ValidationResult.valid() => ValidationResult._(true, null);
  factory ValidationResult.invalid(String error) =>
      ValidationResult._(false, error);
}

class UploadResult {
  final bool success;
  final String? url;
  final String? error;

  UploadResult._({required this.success, this.url, this.error});

  factory UploadResult.success({required String url}) {
    return UploadResult._(success: true, url: url);
  }

  factory UploadResult.failure({required String error}) {
    return UploadResult._(success: false, error: error);
  }
}

enum StoryCreationErrorType {
  validation,
  upload,
  network,
  permission,
  storage,
  unknown,
}
