import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';
import '../models/shared/story_shared_models.dart';

/// Service for managing story drafts
class StoryDraftService {
  static const String _draftsKey = 'story_drafts';
  static const String _draftMediaDir = 'story_drafts';

  /// Save a story draft
  Future<String> saveDraft({
    File? mediaFile,
    String? textContent,
    required StoryMediaType mediaType,
    String? caption,
    List<TextElement>? textElements,
    Color? backgroundColor,
    String? filter,
    List<DrawingPoint>? drawingPoints,
    List<String>? mentions,
    List<String>? hashtags,
  }) async {
    try {
      final draftId = DateTime.now().millisecondsSinceEpoch.toString();

      // Save media file locally if exists
      String? localMediaPath;
      if (mediaFile != null) {
        localMediaPath = await _saveMediaLocally(mediaFile, draftId);
      }

      final draft = StoryDraft(
        id: draftId,
        localMediaPath: localMediaPath,
        textContent: textContent,
        mediaType: mediaType,
        caption: caption,
        textElements: textElements ?? [],
        backgroundColor: backgroundColor?.toARGB32(),
        filter: filter,
        drawingPoints: drawingPoints ?? [],
        mentions: mentions ?? [],
        hashtags: hashtags ?? [],
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );

      await _saveDraftToStorage(draft);
      debugPrint('✅ Draft saved: $draftId');
      return draftId;
    } catch (e) {
      debugPrint('❌ Error saving draft: $e');
      rethrow;
    }
  }

  /// Get all drafts
  Future<List<StoryDraft>> getDrafts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final draftsJson = prefs.getStringList(_draftsKey) ?? [];

      final drafts = draftsJson
          .map((json) => StoryDraft.fromJson(jsonDecode(json)))
          .toList();

      // Sort by last modified (newest first)
      drafts.sort((a, b) => b.lastModified.compareTo(a.lastModified));

      return drafts;
    } catch (e) {
      debugPrint('❌ Error loading drafts: $e');
      return [];
    }
  }

  /// Get a specific draft
  Future<StoryDraft?> getDraft(String draftId) async {
    try {
      final drafts = await getDrafts();
      return drafts.firstWhere(
        (draft) => draft.id == draftId,
        orElse: () => throw StateError('Draft not found'),
      );
    } catch (e) {
      debugPrint('❌ Error loading draft $draftId: $e');
      return null;
    }
  }

  /// Update an existing draft
  Future<void> updateDraft(StoryDraft draft) async {
    try {
      final updatedDraft = draft.copyWith(lastModified: DateTime.now());
      await _saveDraftToStorage(updatedDraft);
      debugPrint('✅ Draft updated: ${draft.id}');
    } catch (e) {
      debugPrint('❌ Error updating draft: $e');
      rethrow;
    }
  }

  /// Delete a draft
  Future<void> deleteDraft(String draftId) async {
    try {
      final drafts = await getDrafts();
      final draftIndex = drafts.indexWhere((d) => d.id == draftId);

      if (draftIndex == -1) return;

      final draft = drafts[draftIndex];

      // Delete local media file if exists
      if (draft.localMediaPath != null) {
        await _deleteLocalMedia(draft.localMediaPath!);
      }

      // Remove from list
      drafts.removeAt(draftIndex);

      // Save updated list
      await _saveDraftsList(drafts);

      debugPrint('✅ Draft deleted: $draftId');
    } catch (e) {
      debugPrint('❌ Error deleting draft: $e');
      rethrow;
    }
  }

  /// Clean up old drafts (older than 30 days)
  Future<void> cleanupOldDrafts() async {
    try {
      final drafts = await getDrafts();
      final cutoffDate = DateTime.now().subtract(const Duration(days: 30));

      final oldDrafts = drafts
          .where((draft) => draft.createdAt.isBefore(cutoffDate))
          .toList();

      for (final draft in oldDrafts) {
        await deleteDraft(draft.id);
      }

      if (oldDrafts.isNotEmpty) {
        debugPrint('🧹 Cleaned up ${oldDrafts.length} old drafts');
      }
    } catch (e) {
      debugPrint('❌ Error cleaning up drafts: $e');
    }
  }

  /// Save media file locally
  Future<String> _saveMediaLocally(File mediaFile, String draftId) async {
    final appDir = await getApplicationDocumentsDirectory();
    final draftDir = Directory('${appDir.path}/$_draftMediaDir');

    if (!await draftDir.exists()) {
      await draftDir.create(recursive: true);
    }

    final extension = mediaFile.path.split('.').last;
    final localPath = '${draftDir.path}/${draftId}_media.$extension';

    await mediaFile.copy(localPath);
    return localPath;
  }

  /// Delete local media file
  Future<void> _deleteLocalMedia(String localPath) async {
    try {
      final file = File(localPath);
      if (await file.exists()) {
        await file.delete();
      }
    } catch (e) {
      debugPrint('⚠️ Error deleting local media: $e');
    }
  }

  /// Save draft to storage
  Future<void> _saveDraftToStorage(StoryDraft draft) async {
    final drafts = await getDrafts();

    // Remove existing draft with same ID
    drafts.removeWhere((d) => d.id == draft.id);

    // Add updated draft
    drafts.add(draft);

    await _saveDraftsList(drafts);
  }

  /// Save drafts list to storage
  Future<void> _saveDraftsList(List<StoryDraft> drafts) async {
    final prefs = await SharedPreferences.getInstance();
    final draftsJson = drafts
        .map((draft) => jsonEncode(draft.toJson()))
        .toList();
    await prefs.setStringList(_draftsKey, draftsJson);
  }
}

/// Story draft model
class StoryDraft {
  final String id;
  final String? localMediaPath;
  final String? textContent;
  final StoryMediaType mediaType;
  final String? caption;
  final List<TextElement> textElements;
  final int? backgroundColor;
  final String? filter;
  final List<DrawingPoint> drawingPoints;
  final List<String> mentions;
  final List<String> hashtags;
  final DateTime createdAt;
  final DateTime lastModified;

  const StoryDraft({
    required this.id,
    this.localMediaPath,
    this.textContent,
    required this.mediaType,
    this.caption,
    required this.textElements,
    this.backgroundColor,
    this.filter,
    required this.drawingPoints,
    required this.mentions,
    required this.hashtags,
    required this.createdAt,
    required this.lastModified,
  });

  StoryDraft copyWith({
    String? id,
    String? localMediaPath,
    String? textContent,
    StoryMediaType? mediaType,
    String? caption,
    List<TextElement>? textElements,
    int? backgroundColor,
    String? filter,
    List<DrawingPoint>? drawingPoints,
    List<String>? mentions,
    List<String>? hashtags,
    DateTime? createdAt,
    DateTime? lastModified,
  }) {
    return StoryDraft(
      id: id ?? this.id,
      localMediaPath: localMediaPath ?? this.localMediaPath,
      textContent: textContent ?? this.textContent,
      mediaType: mediaType ?? this.mediaType,
      caption: caption ?? this.caption,
      textElements: textElements ?? this.textElements,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      filter: filter ?? this.filter,
      drawingPoints: drawingPoints ?? this.drawingPoints,
      mentions: mentions ?? this.mentions,
      hashtags: hashtags ?? this.hashtags,
      createdAt: createdAt ?? this.createdAt,
      lastModified: lastModified ?? this.lastModified,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'localMediaPath': localMediaPath,
      'textContent': textContent,
      'mediaType': mediaType.toString(),
      'caption': caption,
      'textElements': textElements.map((e) => e.toJson()).toList(),
      'backgroundColor': backgroundColor,
      'filter': filter,
      'drawingPoints': drawingPoints.map((p) => p.toJson()).toList(),
      'mentions': mentions,
      'hashtags': hashtags,
      'createdAt': createdAt.toIso8601String(),
      'lastModified': lastModified.toIso8601String(),
    };
  }

  factory StoryDraft.fromJson(Map<String, dynamic> json) {
    return StoryDraft(
      id: json['id'],
      localMediaPath: json['localMediaPath'],
      textContent: json['textContent'],
      mediaType: StoryMediaType.values.firstWhere(
        (e) => e.toString() == json['mediaType'],
      ),
      caption: json['caption'],
      textElements:
          (json['textElements'] as List?)
              ?.map((e) => TextElement.fromJson(e))
              .toList() ??
          [],
      backgroundColor: json['backgroundColor'],
      filter: json['filter'],
      drawingPoints:
          (json['drawingPoints'] as List?)
              ?.map((p) => DrawingPoint.fromJson(p))
              .toList() ??
          [],
      mentions: List<String>.from(json['mentions'] ?? []),
      hashtags: List<String>.from(json['hashtags'] ?? []),
      createdAt: DateTime.parse(json['createdAt']),
      lastModified: DateTime.parse(json['lastModified']),
    );
  }

  /// Get preview text for the draft
  String get previewText {
    if (textContent != null && textContent!.isNotEmpty) {
      return textContent!.length > 50
          ? '${textContent!.substring(0, 50)}...'
          : textContent!;
    }

    if (textElements.isNotEmpty) {
      final allText = textElements.map((e) => e.text).join(' ');
      return allText.length > 50 ? '${allText.substring(0, 50)}...' : allText;
    }

    return caption ?? 'No text content';
  }

  /// Check if draft has media
  bool get hasMedia => localMediaPath != null;

  /// Get file for local media
  File? get mediaFile => localMediaPath != null ? File(localMediaPath!) : null;
}
