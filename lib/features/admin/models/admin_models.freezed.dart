// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'admin_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

AdminStats _$AdminStatsFromJson(Map<String, dynamic> json) {
  return _AdminStats.fromJson(json);
}

/// @nodoc
mixin _$AdminStats {
  int get totalUsers => throw _privateConstructorUsedError;
  int get businessAccounts => throw _privateConstructorUsedError;
  int get totalProducts => throw _privateConstructorUsedError;
  int get totalEvents => throw _privateConstructorUsedError;
  int get totalPosts => throw _privateConstructorUsedError;
  int get pendingVerifications => throw _privateConstructorUsedError;
  int get flaggedContent => throw _privateConstructorUsedError;

  /// Serializes this AdminStats to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AdminStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AdminStatsCopyWith<AdminStats> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AdminStatsCopyWith<$Res> {
  factory $AdminStatsCopyWith(
    AdminStats value,
    $Res Function(AdminStats) then,
  ) = _$AdminStatsCopyWithImpl<$Res, AdminStats>;
  @useResult
  $Res call({
    int totalUsers,
    int businessAccounts,
    int totalProducts,
    int totalEvents,
    int totalPosts,
    int pendingVerifications,
    int flaggedContent,
  });
}

/// @nodoc
class _$AdminStatsCopyWithImpl<$Res, $Val extends AdminStats>
    implements $AdminStatsCopyWith<$Res> {
  _$AdminStatsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AdminStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalUsers = null,
    Object? businessAccounts = null,
    Object? totalProducts = null,
    Object? totalEvents = null,
    Object? totalPosts = null,
    Object? pendingVerifications = null,
    Object? flaggedContent = null,
  }) {
    return _then(
      _value.copyWith(
            totalUsers: null == totalUsers
                ? _value.totalUsers
                : totalUsers // ignore: cast_nullable_to_non_nullable
                      as int,
            businessAccounts: null == businessAccounts
                ? _value.businessAccounts
                : businessAccounts // ignore: cast_nullable_to_non_nullable
                      as int,
            totalProducts: null == totalProducts
                ? _value.totalProducts
                : totalProducts // ignore: cast_nullable_to_non_nullable
                      as int,
            totalEvents: null == totalEvents
                ? _value.totalEvents
                : totalEvents // ignore: cast_nullable_to_non_nullable
                      as int,
            totalPosts: null == totalPosts
                ? _value.totalPosts
                : totalPosts // ignore: cast_nullable_to_non_nullable
                      as int,
            pendingVerifications: null == pendingVerifications
                ? _value.pendingVerifications
                : pendingVerifications // ignore: cast_nullable_to_non_nullable
                      as int,
            flaggedContent: null == flaggedContent
                ? _value.flaggedContent
                : flaggedContent // ignore: cast_nullable_to_non_nullable
                      as int,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$AdminStatsImplCopyWith<$Res>
    implements $AdminStatsCopyWith<$Res> {
  factory _$$AdminStatsImplCopyWith(
    _$AdminStatsImpl value,
    $Res Function(_$AdminStatsImpl) then,
  ) = __$$AdminStatsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int totalUsers,
    int businessAccounts,
    int totalProducts,
    int totalEvents,
    int totalPosts,
    int pendingVerifications,
    int flaggedContent,
  });
}

/// @nodoc
class __$$AdminStatsImplCopyWithImpl<$Res>
    extends _$AdminStatsCopyWithImpl<$Res, _$AdminStatsImpl>
    implements _$$AdminStatsImplCopyWith<$Res> {
  __$$AdminStatsImplCopyWithImpl(
    _$AdminStatsImpl _value,
    $Res Function(_$AdminStatsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AdminStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalUsers = null,
    Object? businessAccounts = null,
    Object? totalProducts = null,
    Object? totalEvents = null,
    Object? totalPosts = null,
    Object? pendingVerifications = null,
    Object? flaggedContent = null,
  }) {
    return _then(
      _$AdminStatsImpl(
        totalUsers: null == totalUsers
            ? _value.totalUsers
            : totalUsers // ignore: cast_nullable_to_non_nullable
                  as int,
        businessAccounts: null == businessAccounts
            ? _value.businessAccounts
            : businessAccounts // ignore: cast_nullable_to_non_nullable
                  as int,
        totalProducts: null == totalProducts
            ? _value.totalProducts
            : totalProducts // ignore: cast_nullable_to_non_nullable
                  as int,
        totalEvents: null == totalEvents
            ? _value.totalEvents
            : totalEvents // ignore: cast_nullable_to_non_nullable
                  as int,
        totalPosts: null == totalPosts
            ? _value.totalPosts
            : totalPosts // ignore: cast_nullable_to_non_nullable
                  as int,
        pendingVerifications: null == pendingVerifications
            ? _value.pendingVerifications
            : pendingVerifications // ignore: cast_nullable_to_non_nullable
                  as int,
        flaggedContent: null == flaggedContent
            ? _value.flaggedContent
            : flaggedContent // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$AdminStatsImpl implements _AdminStats {
  const _$AdminStatsImpl({
    required this.totalUsers,
    required this.businessAccounts,
    required this.totalProducts,
    required this.totalEvents,
    required this.totalPosts,
    required this.pendingVerifications,
    required this.flaggedContent,
  });

  factory _$AdminStatsImpl.fromJson(Map<String, dynamic> json) =>
      _$$AdminStatsImplFromJson(json);

  @override
  final int totalUsers;
  @override
  final int businessAccounts;
  @override
  final int totalProducts;
  @override
  final int totalEvents;
  @override
  final int totalPosts;
  @override
  final int pendingVerifications;
  @override
  final int flaggedContent;

  @override
  String toString() {
    return 'AdminStats(totalUsers: $totalUsers, businessAccounts: $businessAccounts, totalProducts: $totalProducts, totalEvents: $totalEvents, totalPosts: $totalPosts, pendingVerifications: $pendingVerifications, flaggedContent: $flaggedContent)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AdminStatsImpl &&
            (identical(other.totalUsers, totalUsers) ||
                other.totalUsers == totalUsers) &&
            (identical(other.businessAccounts, businessAccounts) ||
                other.businessAccounts == businessAccounts) &&
            (identical(other.totalProducts, totalProducts) ||
                other.totalProducts == totalProducts) &&
            (identical(other.totalEvents, totalEvents) ||
                other.totalEvents == totalEvents) &&
            (identical(other.totalPosts, totalPosts) ||
                other.totalPosts == totalPosts) &&
            (identical(other.pendingVerifications, pendingVerifications) ||
                other.pendingVerifications == pendingVerifications) &&
            (identical(other.flaggedContent, flaggedContent) ||
                other.flaggedContent == flaggedContent));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    totalUsers,
    businessAccounts,
    totalProducts,
    totalEvents,
    totalPosts,
    pendingVerifications,
    flaggedContent,
  );

  /// Create a copy of AdminStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AdminStatsImplCopyWith<_$AdminStatsImpl> get copyWith =>
      __$$AdminStatsImplCopyWithImpl<_$AdminStatsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AdminStatsImplToJson(this);
  }
}

abstract class _AdminStats implements AdminStats {
  const factory _AdminStats({
    required final int totalUsers,
    required final int businessAccounts,
    required final int totalProducts,
    required final int totalEvents,
    required final int totalPosts,
    required final int pendingVerifications,
    required final int flaggedContent,
  }) = _$AdminStatsImpl;

  factory _AdminStats.fromJson(Map<String, dynamic> json) =
      _$AdminStatsImpl.fromJson;

  @override
  int get totalUsers;
  @override
  int get businessAccounts;
  @override
  int get totalProducts;
  @override
  int get totalEvents;
  @override
  int get totalPosts;
  @override
  int get pendingVerifications;
  @override
  int get flaggedContent;

  /// Create a copy of AdminStats
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AdminStatsImplCopyWith<_$AdminStatsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
