// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'place_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PlaceUserImpl _$$PlaceUserImplFromJson(Map<String, dynamic> json) =>
    _$PlaceUserImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      avatarUrl: json['avatarUrl'] as String,
    );

Map<String, dynamic> _$$PlaceUserImplToJson(_$PlaceUserImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'avatarUrl': instance.avatarUrl,
    };

_$PlaceImpl _$$PlaceImplFromJson(Map<String, dynamic> json) => _$PlaceImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  imageUrl: json['imageUrl'] as String,
  category: $enumDecode(_$PlaceCategoryEnumMap, json['category']),
  eventDate: DateTime.parse(json['eventDate'] as String),
  attendees:
      (json['attendees'] as List<dynamic>?)
          ?.map((e) => PlaceUser.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  isTrending: json['isTrending'] as bool? ?? false,
  isNew: json['isNew'] as bool? ?? false,
  type:
      $enumDecodeNullable(_$PlaceTypeEnumMap, json['type']) ?? PlaceType.event,
  description: json['description'] as String?,
  location: json['location'] as String?,
  latitude: (json['latitude'] as num?)?.toDouble(),
  longitude: (json['longitude'] as num?)?.toDouble(),
  price: (json['price'] as num?)?.toDouble(),
  currency: json['currency'] as String?,
  imageUrls:
      (json['imageUrls'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  amenities:
      (json['amenities'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  isVerified: json['isVerified'] as bool? ?? false,
  isExclusive: json['isExclusive'] as bool? ?? false,
  bookingStatus:
      $enumDecodeNullable(_$BookingStatusEnumMap, json['bookingStatus']) ??
      BookingStatus.available,
  capacity: (json['capacity'] as num?)?.toInt(),
  currentBookings: (json['currentBookings'] as num?)?.toInt(),
  ownerId: json['ownerId'] as String?,
  ownerName: json['ownerName'] as String?,
  ownerAvatarUrl: json['ownerAvatarUrl'] as String?,
  rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
  reviewCount: (json['reviewCount'] as num?)?.toInt(),
  lastUpdated: json['lastUpdated'] == null
      ? null
      : DateTime.parse(json['lastUpdated'] as String),
);

Map<String, dynamic> _$$PlaceImplToJson(_$PlaceImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'imageUrl': instance.imageUrl,
      'category': _$PlaceCategoryEnumMap[instance.category]!,
      'eventDate': instance.eventDate.toIso8601String(),
      'attendees': instance.attendees,
      'isTrending': instance.isTrending,
      'isNew': instance.isNew,
      'type': _$PlaceTypeEnumMap[instance.type]!,
      'description': instance.description,
      'location': instance.location,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'price': instance.price,
      'currency': instance.currency,
      'imageUrls': instance.imageUrls,
      'amenities': instance.amenities,
      'isVerified': instance.isVerified,
      'isExclusive': instance.isExclusive,
      'bookingStatus': _$BookingStatusEnumMap[instance.bookingStatus]!,
      'capacity': instance.capacity,
      'currentBookings': instance.currentBookings,
      'ownerId': instance.ownerId,
      'ownerName': instance.ownerName,
      'ownerAvatarUrl': instance.ownerAvatarUrl,
      'rating': instance.rating,
      'reviewCount': instance.reviewCount,
      'lastUpdated': instance.lastUpdated?.toIso8601String(),
    };

const _$PlaceCategoryEnumMap = {
  PlaceCategory.restaurants: 'restaurants',
  PlaceCategory.events: 'events',
  PlaceCategory.nightlife: 'nightlife',
  PlaceCategory.cafes: 'cafes',
  PlaceCategory.resorts: 'resorts',
  PlaceCategory.concerts: 'concerts',
  PlaceCategory.shopping: 'shopping',
  PlaceCategory.shows: 'shows',
  PlaceCategory.villas: 'villas',
  PlaceCategory.yachts: 'yachts',
  PlaceCategory.privateJets: 'privateJets',
  PlaceCategory.islands: 'islands',
  PlaceCategory.luxuryCars: 'luxuryCars',
  PlaceCategory.artGalleries: 'artGalleries',
  PlaceCategory.exclusiveClubs: 'exclusiveClubs',
  PlaceCategory.spas: 'spas',
};

const _$PlaceTypeEnumMap = {
  PlaceType.event: 'event',
  PlaceType.location: 'location',
  PlaceType.experience: 'experience',
  PlaceType.accommodation: 'accommodation',
};

const _$BookingStatusEnumMap = {
  BookingStatus.available: 'available',
  BookingStatus.booked: 'booked',
  BookingStatus.pending: 'pending',
  BookingStatus.confirmed: 'confirmed',
  BookingStatus.cancelled: 'cancelled',
};

_$PlaceJoinImpl _$$PlaceJoinImplFromJson(Map<String, dynamic> json) =>
    _$PlaceJoinImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      placeId: json['placeId'] as String,
      joinDate: DateTime.parse(json['joinDate'] as String),
      visitDate: DateTime.parse(json['visitDate'] as String),
      status:
          $enumDecodeNullable(_$JoinStatusEnumMap, json['status']) ??
          JoinStatus.joined,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$$PlaceJoinImplToJson(_$PlaceJoinImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'placeId': instance.placeId,
      'joinDate': instance.joinDate.toIso8601String(),
      'visitDate': instance.visitDate.toIso8601String(),
      'status': _$JoinStatusEnumMap[instance.status]!,
      'notes': instance.notes,
    };

const _$JoinStatusEnumMap = {
  JoinStatus.joined: 'joined',
  JoinStatus.attended: 'attended',
  JoinStatus.missed: 'missed',
  JoinStatus.cancelled: 'cancelled',
};

_$UserJoinStatsImpl _$$UserJoinStatsImplFromJson(Map<String, dynamic> json) =>
    _$UserJoinStatsImpl(
      userId: json['userId'] as String,
      missedJoins: (json['missedJoins'] as num?)?.toInt() ?? 0,
      restrictionEndDate: json['restrictionEndDate'] == null
          ? null
          : DateTime.parse(json['restrictionEndDate'] as String),
    );

Map<String, dynamic> _$$UserJoinStatsImplToJson(_$UserJoinStatsImpl instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'missedJoins': instance.missedJoins,
      'restrictionEndDate': instance.restrictionEndDate?.toIso8601String(),
    };

_$BookingImpl _$$BookingImplFromJson(Map<String, dynamic> json) =>
    _$BookingImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      placeId: json['placeId'] as String,
      bookingDate: DateTime.parse(json['bookingDate'] as String),
      visitDate: DateTime.parse(json['visitDate'] as String),
      guestCount: (json['guestCount'] as num).toInt(),
      status:
          $enumDecodeNullable(_$BookingStatusEnumMap, json['status']) ??
          BookingStatus.pending,
      totalPrice: (json['totalPrice'] as num?)?.toDouble(),
      currency: json['currency'] as String?,
      specialRequests: json['specialRequests'] as String?,
      contactPhone: json['contactPhone'] as String?,
      contactEmail: json['contactEmail'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$BookingImplToJson(_$BookingImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'placeId': instance.placeId,
      'bookingDate': instance.bookingDate.toIso8601String(),
      'visitDate': instance.visitDate.toIso8601String(),
      'guestCount': instance.guestCount,
      'status': _$BookingStatusEnumMap[instance.status]!,
      'totalPrice': instance.totalPrice,
      'currency': instance.currency,
      'specialRequests': instance.specialRequests,
      'contactPhone': instance.contactPhone,
      'contactEmail': instance.contactEmail,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

_$BookingRequestImpl _$$BookingRequestImplFromJson(Map<String, dynamic> json) =>
    _$BookingRequestImpl(
      placeId: json['placeId'] as String,
      visitDate: DateTime.parse(json['visitDate'] as String),
      guestCount: (json['guestCount'] as num).toInt(),
      specialRequests: json['specialRequests'] as String?,
      contactPhone: json['contactPhone'] as String?,
      contactEmail: json['contactEmail'] as String?,
    );

Map<String, dynamic> _$$BookingRequestImplToJson(
  _$BookingRequestImpl instance,
) => <String, dynamic>{
  'placeId': instance.placeId,
  'visitDate': instance.visitDate.toIso8601String(),
  'guestCount': instance.guestCount,
  'specialRequests': instance.specialRequests,
  'contactPhone': instance.contactPhone,
  'contactEmail': instance.contactEmail,
};

_$PlacesFilterImpl _$$PlacesFilterImplFromJson(Map<String, dynamic> json) =>
    _$PlacesFilterImpl(
      categories:
          (json['categories'] as List<dynamic>?)
              ?.map((e) => $enumDecode(_$PlaceCategoryEnumMap, e))
              .toSet() ??
          const {},
      types:
          (json['types'] as List<dynamic>?)
              ?.map((e) => $enumDecode(_$PlaceTypeEnumMap, e))
              .toSet() ??
          const {},
      startDate: json['startDate'] == null
          ? null
          : DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
      minPrice: (json['minPrice'] as num?)?.toDouble(),
      maxPrice: (json['maxPrice'] as num?)?.toDouble(),
      verifiedOnly: json['verifiedOnly'] as bool? ?? false,
      exclusiveOnly: json['exclusiveOnly'] as bool? ?? false,
      trendingOnly: json['trendingOnly'] as bool? ?? false,
      searchQuery: json['searchQuery'] as String?,
      location: json['location'] as String?,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      radius: (json['radius'] as num?)?.toDouble(),
      minRating: (json['minRating'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$$PlacesFilterImplToJson(_$PlacesFilterImpl instance) =>
    <String, dynamic>{
      'categories': instance.categories
          .map((e) => _$PlaceCategoryEnumMap[e]!)
          .toList(),
      'types': instance.types.map((e) => _$PlaceTypeEnumMap[e]!).toList(),
      'startDate': instance.startDate?.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
      'minPrice': instance.minPrice,
      'maxPrice': instance.maxPrice,
      'verifiedOnly': instance.verifiedOnly,
      'exclusiveOnly': instance.exclusiveOnly,
      'trendingOnly': instance.trendingOnly,
      'searchQuery': instance.searchQuery,
      'location': instance.location,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'radius': instance.radius,
      'minRating': instance.minRating,
    };
