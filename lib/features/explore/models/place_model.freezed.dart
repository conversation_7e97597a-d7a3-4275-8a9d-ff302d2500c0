// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'place_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

PlaceUser _$PlaceUserFromJson(Map<String, dynamic> json) {
  return _PlaceUser.fromJson(json);
}

/// @nodoc
mixin _$PlaceUser {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get avatarUrl => throw _privateConstructorUsedError;

  /// Serializes this PlaceUser to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PlaceUser
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PlaceUserCopyWith<PlaceUser> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlaceUserCopyWith<$Res> {
  factory $PlaceUserCopyWith(PlaceUser value, $Res Function(PlaceUser) then) =
      _$PlaceUserCopyWithImpl<$Res, PlaceUser>;
  @useResult
  $Res call({String id, String name, String avatarUrl});
}

/// @nodoc
class _$PlaceUserCopyWithImpl<$Res, $Val extends PlaceUser>
    implements $PlaceUserCopyWith<$Res> {
  _$PlaceUserCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PlaceUser
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? avatarUrl = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            avatarUrl: null == avatarUrl
                ? _value.avatarUrl
                : avatarUrl // ignore: cast_nullable_to_non_nullable
                      as String,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PlaceUserImplCopyWith<$Res>
    implements $PlaceUserCopyWith<$Res> {
  factory _$$PlaceUserImplCopyWith(
    _$PlaceUserImpl value,
    $Res Function(_$PlaceUserImpl) then,
  ) = __$$PlaceUserImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String name, String avatarUrl});
}

/// @nodoc
class __$$PlaceUserImplCopyWithImpl<$Res>
    extends _$PlaceUserCopyWithImpl<$Res, _$PlaceUserImpl>
    implements _$$PlaceUserImplCopyWith<$Res> {
  __$$PlaceUserImplCopyWithImpl(
    _$PlaceUserImpl _value,
    $Res Function(_$PlaceUserImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PlaceUser
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? avatarUrl = null,
  }) {
    return _then(
      _$PlaceUserImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        avatarUrl: null == avatarUrl
            ? _value.avatarUrl
            : avatarUrl // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PlaceUserImpl implements _PlaceUser {
  const _$PlaceUserImpl({
    required this.id,
    required this.name,
    required this.avatarUrl,
  });

  factory _$PlaceUserImpl.fromJson(Map<String, dynamic> json) =>
      _$$PlaceUserImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String avatarUrl;

  @override
  String toString() {
    return 'PlaceUser(id: $id, name: $name, avatarUrl: $avatarUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlaceUserImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.avatarUrl, avatarUrl) ||
                other.avatarUrl == avatarUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, avatarUrl);

  /// Create a copy of PlaceUser
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PlaceUserImplCopyWith<_$PlaceUserImpl> get copyWith =>
      __$$PlaceUserImplCopyWithImpl<_$PlaceUserImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PlaceUserImplToJson(this);
  }
}

abstract class _PlaceUser implements PlaceUser {
  const factory _PlaceUser({
    required final String id,
    required final String name,
    required final String avatarUrl,
  }) = _$PlaceUserImpl;

  factory _PlaceUser.fromJson(Map<String, dynamic> json) =
      _$PlaceUserImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get avatarUrl;

  /// Create a copy of PlaceUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PlaceUserImplCopyWith<_$PlaceUserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Place _$PlaceFromJson(Map<String, dynamic> json) {
  return _Place.fromJson(json);
}

/// @nodoc
mixin _$Place {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get imageUrl => throw _privateConstructorUsedError;
  PlaceCategory get category => throw _privateConstructorUsedError;
  DateTime get eventDate => throw _privateConstructorUsedError;
  List<PlaceUser> get attendees => throw _privateConstructorUsedError;
  bool get isTrending => throw _privateConstructorUsedError;
  bool get isNew => throw _privateConstructorUsedError;
  PlaceType get type => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get location => throw _privateConstructorUsedError;
  double? get latitude => throw _privateConstructorUsedError;
  double? get longitude => throw _privateConstructorUsedError;
  double? get price => throw _privateConstructorUsedError;
  String? get currency => throw _privateConstructorUsedError;
  List<String> get imageUrls => throw _privateConstructorUsedError;
  List<String> get amenities => throw _privateConstructorUsedError;
  bool get isVerified => throw _privateConstructorUsedError;
  bool get isExclusive => throw _privateConstructorUsedError;
  BookingStatus get bookingStatus => throw _privateConstructorUsedError;
  int? get capacity => throw _privateConstructorUsedError;
  int? get currentBookings => throw _privateConstructorUsedError;
  String? get ownerId => throw _privateConstructorUsedError;
  String? get ownerName => throw _privateConstructorUsedError;
  String? get ownerAvatarUrl => throw _privateConstructorUsedError;
  double get rating => throw _privateConstructorUsedError;
  int? get reviewCount => throw _privateConstructorUsedError;
  DateTime? get lastUpdated => throw _privateConstructorUsedError;

  /// Serializes this Place to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Place
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PlaceCopyWith<Place> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlaceCopyWith<$Res> {
  factory $PlaceCopyWith(Place value, $Res Function(Place) then) =
      _$PlaceCopyWithImpl<$Res, Place>;
  @useResult
  $Res call({
    String id,
    String name,
    String imageUrl,
    PlaceCategory category,
    DateTime eventDate,
    List<PlaceUser> attendees,
    bool isTrending,
    bool isNew,
    PlaceType type,
    String? description,
    String? location,
    double? latitude,
    double? longitude,
    double? price,
    String? currency,
    List<String> imageUrls,
    List<String> amenities,
    bool isVerified,
    bool isExclusive,
    BookingStatus bookingStatus,
    int? capacity,
    int? currentBookings,
    String? ownerId,
    String? ownerName,
    String? ownerAvatarUrl,
    double rating,
    int? reviewCount,
    DateTime? lastUpdated,
  });
}

/// @nodoc
class _$PlaceCopyWithImpl<$Res, $Val extends Place>
    implements $PlaceCopyWith<$Res> {
  _$PlaceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Place
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? imageUrl = null,
    Object? category = null,
    Object? eventDate = null,
    Object? attendees = null,
    Object? isTrending = null,
    Object? isNew = null,
    Object? type = null,
    Object? description = freezed,
    Object? location = freezed,
    Object? latitude = freezed,
    Object? longitude = freezed,
    Object? price = freezed,
    Object? currency = freezed,
    Object? imageUrls = null,
    Object? amenities = null,
    Object? isVerified = null,
    Object? isExclusive = null,
    Object? bookingStatus = null,
    Object? capacity = freezed,
    Object? currentBookings = freezed,
    Object? ownerId = freezed,
    Object? ownerName = freezed,
    Object? ownerAvatarUrl = freezed,
    Object? rating = null,
    Object? reviewCount = freezed,
    Object? lastUpdated = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            imageUrl: null == imageUrl
                ? _value.imageUrl
                : imageUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            category: null == category
                ? _value.category
                : category // ignore: cast_nullable_to_non_nullable
                      as PlaceCategory,
            eventDate: null == eventDate
                ? _value.eventDate
                : eventDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            attendees: null == attendees
                ? _value.attendees
                : attendees // ignore: cast_nullable_to_non_nullable
                      as List<PlaceUser>,
            isTrending: null == isTrending
                ? _value.isTrending
                : isTrending // ignore: cast_nullable_to_non_nullable
                      as bool,
            isNew: null == isNew
                ? _value.isNew
                : isNew // ignore: cast_nullable_to_non_nullable
                      as bool,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as PlaceType,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            location: freezed == location
                ? _value.location
                : location // ignore: cast_nullable_to_non_nullable
                      as String?,
            latitude: freezed == latitude
                ? _value.latitude
                : latitude // ignore: cast_nullable_to_non_nullable
                      as double?,
            longitude: freezed == longitude
                ? _value.longitude
                : longitude // ignore: cast_nullable_to_non_nullable
                      as double?,
            price: freezed == price
                ? _value.price
                : price // ignore: cast_nullable_to_non_nullable
                      as double?,
            currency: freezed == currency
                ? _value.currency
                : currency // ignore: cast_nullable_to_non_nullable
                      as String?,
            imageUrls: null == imageUrls
                ? _value.imageUrls
                : imageUrls // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            amenities: null == amenities
                ? _value.amenities
                : amenities // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            isVerified: null == isVerified
                ? _value.isVerified
                : isVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            isExclusive: null == isExclusive
                ? _value.isExclusive
                : isExclusive // ignore: cast_nullable_to_non_nullable
                      as bool,
            bookingStatus: null == bookingStatus
                ? _value.bookingStatus
                : bookingStatus // ignore: cast_nullable_to_non_nullable
                      as BookingStatus,
            capacity: freezed == capacity
                ? _value.capacity
                : capacity // ignore: cast_nullable_to_non_nullable
                      as int?,
            currentBookings: freezed == currentBookings
                ? _value.currentBookings
                : currentBookings // ignore: cast_nullable_to_non_nullable
                      as int?,
            ownerId: freezed == ownerId
                ? _value.ownerId
                : ownerId // ignore: cast_nullable_to_non_nullable
                      as String?,
            ownerName: freezed == ownerName
                ? _value.ownerName
                : ownerName // ignore: cast_nullable_to_non_nullable
                      as String?,
            ownerAvatarUrl: freezed == ownerAvatarUrl
                ? _value.ownerAvatarUrl
                : ownerAvatarUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            rating: null == rating
                ? _value.rating
                : rating // ignore: cast_nullable_to_non_nullable
                      as double,
            reviewCount: freezed == reviewCount
                ? _value.reviewCount
                : reviewCount // ignore: cast_nullable_to_non_nullable
                      as int?,
            lastUpdated: freezed == lastUpdated
                ? _value.lastUpdated
                : lastUpdated // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PlaceImplCopyWith<$Res> implements $PlaceCopyWith<$Res> {
  factory _$$PlaceImplCopyWith(
    _$PlaceImpl value,
    $Res Function(_$PlaceImpl) then,
  ) = __$$PlaceImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String imageUrl,
    PlaceCategory category,
    DateTime eventDate,
    List<PlaceUser> attendees,
    bool isTrending,
    bool isNew,
    PlaceType type,
    String? description,
    String? location,
    double? latitude,
    double? longitude,
    double? price,
    String? currency,
    List<String> imageUrls,
    List<String> amenities,
    bool isVerified,
    bool isExclusive,
    BookingStatus bookingStatus,
    int? capacity,
    int? currentBookings,
    String? ownerId,
    String? ownerName,
    String? ownerAvatarUrl,
    double rating,
    int? reviewCount,
    DateTime? lastUpdated,
  });
}

/// @nodoc
class __$$PlaceImplCopyWithImpl<$Res>
    extends _$PlaceCopyWithImpl<$Res, _$PlaceImpl>
    implements _$$PlaceImplCopyWith<$Res> {
  __$$PlaceImplCopyWithImpl(
    _$PlaceImpl _value,
    $Res Function(_$PlaceImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Place
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? imageUrl = null,
    Object? category = null,
    Object? eventDate = null,
    Object? attendees = null,
    Object? isTrending = null,
    Object? isNew = null,
    Object? type = null,
    Object? description = freezed,
    Object? location = freezed,
    Object? latitude = freezed,
    Object? longitude = freezed,
    Object? price = freezed,
    Object? currency = freezed,
    Object? imageUrls = null,
    Object? amenities = null,
    Object? isVerified = null,
    Object? isExclusive = null,
    Object? bookingStatus = null,
    Object? capacity = freezed,
    Object? currentBookings = freezed,
    Object? ownerId = freezed,
    Object? ownerName = freezed,
    Object? ownerAvatarUrl = freezed,
    Object? rating = null,
    Object? reviewCount = freezed,
    Object? lastUpdated = freezed,
  }) {
    return _then(
      _$PlaceImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        imageUrl: null == imageUrl
            ? _value.imageUrl
            : imageUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        category: null == category
            ? _value.category
            : category // ignore: cast_nullable_to_non_nullable
                  as PlaceCategory,
        eventDate: null == eventDate
            ? _value.eventDate
            : eventDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        attendees: null == attendees
            ? _value._attendees
            : attendees // ignore: cast_nullable_to_non_nullable
                  as List<PlaceUser>,
        isTrending: null == isTrending
            ? _value.isTrending
            : isTrending // ignore: cast_nullable_to_non_nullable
                  as bool,
        isNew: null == isNew
            ? _value.isNew
            : isNew // ignore: cast_nullable_to_non_nullable
                  as bool,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as PlaceType,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        location: freezed == location
            ? _value.location
            : location // ignore: cast_nullable_to_non_nullable
                  as String?,
        latitude: freezed == latitude
            ? _value.latitude
            : latitude // ignore: cast_nullable_to_non_nullable
                  as double?,
        longitude: freezed == longitude
            ? _value.longitude
            : longitude // ignore: cast_nullable_to_non_nullable
                  as double?,
        price: freezed == price
            ? _value.price
            : price // ignore: cast_nullable_to_non_nullable
                  as double?,
        currency: freezed == currency
            ? _value.currency
            : currency // ignore: cast_nullable_to_non_nullable
                  as String?,
        imageUrls: null == imageUrls
            ? _value._imageUrls
            : imageUrls // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        amenities: null == amenities
            ? _value._amenities
            : amenities // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        isVerified: null == isVerified
            ? _value.isVerified
            : isVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        isExclusive: null == isExclusive
            ? _value.isExclusive
            : isExclusive // ignore: cast_nullable_to_non_nullable
                  as bool,
        bookingStatus: null == bookingStatus
            ? _value.bookingStatus
            : bookingStatus // ignore: cast_nullable_to_non_nullable
                  as BookingStatus,
        capacity: freezed == capacity
            ? _value.capacity
            : capacity // ignore: cast_nullable_to_non_nullable
                  as int?,
        currentBookings: freezed == currentBookings
            ? _value.currentBookings
            : currentBookings // ignore: cast_nullable_to_non_nullable
                  as int?,
        ownerId: freezed == ownerId
            ? _value.ownerId
            : ownerId // ignore: cast_nullable_to_non_nullable
                  as String?,
        ownerName: freezed == ownerName
            ? _value.ownerName
            : ownerName // ignore: cast_nullable_to_non_nullable
                  as String?,
        ownerAvatarUrl: freezed == ownerAvatarUrl
            ? _value.ownerAvatarUrl
            : ownerAvatarUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        rating: null == rating
            ? _value.rating
            : rating // ignore: cast_nullable_to_non_nullable
                  as double,
        reviewCount: freezed == reviewCount
            ? _value.reviewCount
            : reviewCount // ignore: cast_nullable_to_non_nullable
                  as int?,
        lastUpdated: freezed == lastUpdated
            ? _value.lastUpdated
            : lastUpdated // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PlaceImpl implements _Place {
  const _$PlaceImpl({
    required this.id,
    required this.name,
    required this.imageUrl,
    required this.category,
    required this.eventDate,
    final List<PlaceUser> attendees = const [],
    this.isTrending = false,
    this.isNew = false,
    this.type = PlaceType.event,
    this.description,
    this.location,
    this.latitude,
    this.longitude,
    this.price,
    this.currency,
    final List<String> imageUrls = const [],
    final List<String> amenities = const [],
    this.isVerified = false,
    this.isExclusive = false,
    this.bookingStatus = BookingStatus.available,
    this.capacity,
    this.currentBookings,
    this.ownerId,
    this.ownerName,
    this.ownerAvatarUrl,
    this.rating = 0.0,
    this.reviewCount,
    this.lastUpdated,
  }) : _attendees = attendees,
       _imageUrls = imageUrls,
       _amenities = amenities;

  factory _$PlaceImpl.fromJson(Map<String, dynamic> json) =>
      _$$PlaceImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String imageUrl;
  @override
  final PlaceCategory category;
  @override
  final DateTime eventDate;
  final List<PlaceUser> _attendees;
  @override
  @JsonKey()
  List<PlaceUser> get attendees {
    if (_attendees is EqualUnmodifiableListView) return _attendees;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_attendees);
  }

  @override
  @JsonKey()
  final bool isTrending;
  @override
  @JsonKey()
  final bool isNew;
  @override
  @JsonKey()
  final PlaceType type;
  @override
  final String? description;
  @override
  final String? location;
  @override
  final double? latitude;
  @override
  final double? longitude;
  @override
  final double? price;
  @override
  final String? currency;
  final List<String> _imageUrls;
  @override
  @JsonKey()
  List<String> get imageUrls {
    if (_imageUrls is EqualUnmodifiableListView) return _imageUrls;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_imageUrls);
  }

  final List<String> _amenities;
  @override
  @JsonKey()
  List<String> get amenities {
    if (_amenities is EqualUnmodifiableListView) return _amenities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_amenities);
  }

  @override
  @JsonKey()
  final bool isVerified;
  @override
  @JsonKey()
  final bool isExclusive;
  @override
  @JsonKey()
  final BookingStatus bookingStatus;
  @override
  final int? capacity;
  @override
  final int? currentBookings;
  @override
  final String? ownerId;
  @override
  final String? ownerName;
  @override
  final String? ownerAvatarUrl;
  @override
  @JsonKey()
  final double rating;
  @override
  final int? reviewCount;
  @override
  final DateTime? lastUpdated;

  @override
  String toString() {
    return 'Place(id: $id, name: $name, imageUrl: $imageUrl, category: $category, eventDate: $eventDate, attendees: $attendees, isTrending: $isTrending, isNew: $isNew, type: $type, description: $description, location: $location, latitude: $latitude, longitude: $longitude, price: $price, currency: $currency, imageUrls: $imageUrls, amenities: $amenities, isVerified: $isVerified, isExclusive: $isExclusive, bookingStatus: $bookingStatus, capacity: $capacity, currentBookings: $currentBookings, ownerId: $ownerId, ownerName: $ownerName, ownerAvatarUrl: $ownerAvatarUrl, rating: $rating, reviewCount: $reviewCount, lastUpdated: $lastUpdated)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlaceImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.eventDate, eventDate) ||
                other.eventDate == eventDate) &&
            const DeepCollectionEquality().equals(
              other._attendees,
              _attendees,
            ) &&
            (identical(other.isTrending, isTrending) ||
                other.isTrending == isTrending) &&
            (identical(other.isNew, isNew) || other.isNew == isNew) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            const DeepCollectionEquality().equals(
              other._imageUrls,
              _imageUrls,
            ) &&
            const DeepCollectionEquality().equals(
              other._amenities,
              _amenities,
            ) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.isExclusive, isExclusive) ||
                other.isExclusive == isExclusive) &&
            (identical(other.bookingStatus, bookingStatus) ||
                other.bookingStatus == bookingStatus) &&
            (identical(other.capacity, capacity) ||
                other.capacity == capacity) &&
            (identical(other.currentBookings, currentBookings) ||
                other.currentBookings == currentBookings) &&
            (identical(other.ownerId, ownerId) || other.ownerId == ownerId) &&
            (identical(other.ownerName, ownerName) ||
                other.ownerName == ownerName) &&
            (identical(other.ownerAvatarUrl, ownerAvatarUrl) ||
                other.ownerAvatarUrl == ownerAvatarUrl) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            (identical(other.reviewCount, reviewCount) ||
                other.reviewCount == reviewCount) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    name,
    imageUrl,
    category,
    eventDate,
    const DeepCollectionEquality().hash(_attendees),
    isTrending,
    isNew,
    type,
    description,
    location,
    latitude,
    longitude,
    price,
    currency,
    const DeepCollectionEquality().hash(_imageUrls),
    const DeepCollectionEquality().hash(_amenities),
    isVerified,
    isExclusive,
    bookingStatus,
    capacity,
    currentBookings,
    ownerId,
    ownerName,
    ownerAvatarUrl,
    rating,
    reviewCount,
    lastUpdated,
  ]);

  /// Create a copy of Place
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PlaceImplCopyWith<_$PlaceImpl> get copyWith =>
      __$$PlaceImplCopyWithImpl<_$PlaceImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PlaceImplToJson(this);
  }
}

abstract class _Place implements Place {
  const factory _Place({
    required final String id,
    required final String name,
    required final String imageUrl,
    required final PlaceCategory category,
    required final DateTime eventDate,
    final List<PlaceUser> attendees,
    final bool isTrending,
    final bool isNew,
    final PlaceType type,
    final String? description,
    final String? location,
    final double? latitude,
    final double? longitude,
    final double? price,
    final String? currency,
    final List<String> imageUrls,
    final List<String> amenities,
    final bool isVerified,
    final bool isExclusive,
    final BookingStatus bookingStatus,
    final int? capacity,
    final int? currentBookings,
    final String? ownerId,
    final String? ownerName,
    final String? ownerAvatarUrl,
    final double rating,
    final int? reviewCount,
    final DateTime? lastUpdated,
  }) = _$PlaceImpl;

  factory _Place.fromJson(Map<String, dynamic> json) = _$PlaceImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get imageUrl;
  @override
  PlaceCategory get category;
  @override
  DateTime get eventDate;
  @override
  List<PlaceUser> get attendees;
  @override
  bool get isTrending;
  @override
  bool get isNew;
  @override
  PlaceType get type;
  @override
  String? get description;
  @override
  String? get location;
  @override
  double? get latitude;
  @override
  double? get longitude;
  @override
  double? get price;
  @override
  String? get currency;
  @override
  List<String> get imageUrls;
  @override
  List<String> get amenities;
  @override
  bool get isVerified;
  @override
  bool get isExclusive;
  @override
  BookingStatus get bookingStatus;
  @override
  int? get capacity;
  @override
  int? get currentBookings;
  @override
  String? get ownerId;
  @override
  String? get ownerName;
  @override
  String? get ownerAvatarUrl;
  @override
  double get rating;
  @override
  int? get reviewCount;
  @override
  DateTime? get lastUpdated;

  /// Create a copy of Place
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PlaceImplCopyWith<_$PlaceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PlaceJoin _$PlaceJoinFromJson(Map<String, dynamic> json) {
  return _PlaceJoin.fromJson(json);
}

/// @nodoc
mixin _$PlaceJoin {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get placeId => throw _privateConstructorUsedError;
  DateTime get joinDate => throw _privateConstructorUsedError;
  DateTime get visitDate => throw _privateConstructorUsedError;
  JoinStatus get status => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;

  /// Serializes this PlaceJoin to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PlaceJoin
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PlaceJoinCopyWith<PlaceJoin> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlaceJoinCopyWith<$Res> {
  factory $PlaceJoinCopyWith(PlaceJoin value, $Res Function(PlaceJoin) then) =
      _$PlaceJoinCopyWithImpl<$Res, PlaceJoin>;
  @useResult
  $Res call({
    String id,
    String userId,
    String placeId,
    DateTime joinDate,
    DateTime visitDate,
    JoinStatus status,
    String? notes,
  });
}

/// @nodoc
class _$PlaceJoinCopyWithImpl<$Res, $Val extends PlaceJoin>
    implements $PlaceJoinCopyWith<$Res> {
  _$PlaceJoinCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PlaceJoin
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? placeId = null,
    Object? joinDate = null,
    Object? visitDate = null,
    Object? status = null,
    Object? notes = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            placeId: null == placeId
                ? _value.placeId
                : placeId // ignore: cast_nullable_to_non_nullable
                      as String,
            joinDate: null == joinDate
                ? _value.joinDate
                : joinDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            visitDate: null == visitDate
                ? _value.visitDate
                : visitDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as JoinStatus,
            notes: freezed == notes
                ? _value.notes
                : notes // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PlaceJoinImplCopyWith<$Res>
    implements $PlaceJoinCopyWith<$Res> {
  factory _$$PlaceJoinImplCopyWith(
    _$PlaceJoinImpl value,
    $Res Function(_$PlaceJoinImpl) then,
  ) = __$$PlaceJoinImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    String placeId,
    DateTime joinDate,
    DateTime visitDate,
    JoinStatus status,
    String? notes,
  });
}

/// @nodoc
class __$$PlaceJoinImplCopyWithImpl<$Res>
    extends _$PlaceJoinCopyWithImpl<$Res, _$PlaceJoinImpl>
    implements _$$PlaceJoinImplCopyWith<$Res> {
  __$$PlaceJoinImplCopyWithImpl(
    _$PlaceJoinImpl _value,
    $Res Function(_$PlaceJoinImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PlaceJoin
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? placeId = null,
    Object? joinDate = null,
    Object? visitDate = null,
    Object? status = null,
    Object? notes = freezed,
  }) {
    return _then(
      _$PlaceJoinImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        placeId: null == placeId
            ? _value.placeId
            : placeId // ignore: cast_nullable_to_non_nullable
                  as String,
        joinDate: null == joinDate
            ? _value.joinDate
            : joinDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        visitDate: null == visitDate
            ? _value.visitDate
            : visitDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as JoinStatus,
        notes: freezed == notes
            ? _value.notes
            : notes // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PlaceJoinImpl implements _PlaceJoin {
  const _$PlaceJoinImpl({
    required this.id,
    required this.userId,
    required this.placeId,
    required this.joinDate,
    required this.visitDate,
    this.status = JoinStatus.joined,
    this.notes,
  });

  factory _$PlaceJoinImpl.fromJson(Map<String, dynamic> json) =>
      _$$PlaceJoinImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String placeId;
  @override
  final DateTime joinDate;
  @override
  final DateTime visitDate;
  @override
  @JsonKey()
  final JoinStatus status;
  @override
  final String? notes;

  @override
  String toString() {
    return 'PlaceJoin(id: $id, userId: $userId, placeId: $placeId, joinDate: $joinDate, visitDate: $visitDate, status: $status, notes: $notes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlaceJoinImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.placeId, placeId) || other.placeId == placeId) &&
            (identical(other.joinDate, joinDate) ||
                other.joinDate == joinDate) &&
            (identical(other.visitDate, visitDate) ||
                other.visitDate == visitDate) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.notes, notes) || other.notes == notes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    userId,
    placeId,
    joinDate,
    visitDate,
    status,
    notes,
  );

  /// Create a copy of PlaceJoin
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PlaceJoinImplCopyWith<_$PlaceJoinImpl> get copyWith =>
      __$$PlaceJoinImplCopyWithImpl<_$PlaceJoinImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PlaceJoinImplToJson(this);
  }
}

abstract class _PlaceJoin implements PlaceJoin {
  const factory _PlaceJoin({
    required final String id,
    required final String userId,
    required final String placeId,
    required final DateTime joinDate,
    required final DateTime visitDate,
    final JoinStatus status,
    final String? notes,
  }) = _$PlaceJoinImpl;

  factory _PlaceJoin.fromJson(Map<String, dynamic> json) =
      _$PlaceJoinImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get placeId;
  @override
  DateTime get joinDate;
  @override
  DateTime get visitDate;
  @override
  JoinStatus get status;
  @override
  String? get notes;

  /// Create a copy of PlaceJoin
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PlaceJoinImplCopyWith<_$PlaceJoinImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserJoinStats _$UserJoinStatsFromJson(Map<String, dynamic> json) {
  return _UserJoinStats.fromJson(json);
}

/// @nodoc
mixin _$UserJoinStats {
  String get userId => throw _privateConstructorUsedError;
  int get missedJoins => throw _privateConstructorUsedError;
  DateTime? get restrictionEndDate => throw _privateConstructorUsedError;

  /// Serializes this UserJoinStats to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserJoinStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserJoinStatsCopyWith<UserJoinStats> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserJoinStatsCopyWith<$Res> {
  factory $UserJoinStatsCopyWith(
    UserJoinStats value,
    $Res Function(UserJoinStats) then,
  ) = _$UserJoinStatsCopyWithImpl<$Res, UserJoinStats>;
  @useResult
  $Res call({String userId, int missedJoins, DateTime? restrictionEndDate});
}

/// @nodoc
class _$UserJoinStatsCopyWithImpl<$Res, $Val extends UserJoinStats>
    implements $UserJoinStatsCopyWith<$Res> {
  _$UserJoinStatsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserJoinStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? missedJoins = null,
    Object? restrictionEndDate = freezed,
  }) {
    return _then(
      _value.copyWith(
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            missedJoins: null == missedJoins
                ? _value.missedJoins
                : missedJoins // ignore: cast_nullable_to_non_nullable
                      as int,
            restrictionEndDate: freezed == restrictionEndDate
                ? _value.restrictionEndDate
                : restrictionEndDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$UserJoinStatsImplCopyWith<$Res>
    implements $UserJoinStatsCopyWith<$Res> {
  factory _$$UserJoinStatsImplCopyWith(
    _$UserJoinStatsImpl value,
    $Res Function(_$UserJoinStatsImpl) then,
  ) = __$$UserJoinStatsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String userId, int missedJoins, DateTime? restrictionEndDate});
}

/// @nodoc
class __$$UserJoinStatsImplCopyWithImpl<$Res>
    extends _$UserJoinStatsCopyWithImpl<$Res, _$UserJoinStatsImpl>
    implements _$$UserJoinStatsImplCopyWith<$Res> {
  __$$UserJoinStatsImplCopyWithImpl(
    _$UserJoinStatsImpl _value,
    $Res Function(_$UserJoinStatsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of UserJoinStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? missedJoins = null,
    Object? restrictionEndDate = freezed,
  }) {
    return _then(
      _$UserJoinStatsImpl(
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        missedJoins: null == missedJoins
            ? _value.missedJoins
            : missedJoins // ignore: cast_nullable_to_non_nullable
                  as int,
        restrictionEndDate: freezed == restrictionEndDate
            ? _value.restrictionEndDate
            : restrictionEndDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$UserJoinStatsImpl implements _UserJoinStats {
  const _$UserJoinStatsImpl({
    required this.userId,
    this.missedJoins = 0,
    this.restrictionEndDate,
  });

  factory _$UserJoinStatsImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserJoinStatsImplFromJson(json);

  @override
  final String userId;
  @override
  @JsonKey()
  final int missedJoins;
  @override
  final DateTime? restrictionEndDate;

  @override
  String toString() {
    return 'UserJoinStats(userId: $userId, missedJoins: $missedJoins, restrictionEndDate: $restrictionEndDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserJoinStatsImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.missedJoins, missedJoins) ||
                other.missedJoins == missedJoins) &&
            (identical(other.restrictionEndDate, restrictionEndDate) ||
                other.restrictionEndDate == restrictionEndDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, userId, missedJoins, restrictionEndDate);

  /// Create a copy of UserJoinStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserJoinStatsImplCopyWith<_$UserJoinStatsImpl> get copyWith =>
      __$$UserJoinStatsImplCopyWithImpl<_$UserJoinStatsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserJoinStatsImplToJson(this);
  }
}

abstract class _UserJoinStats implements UserJoinStats {
  const factory _UserJoinStats({
    required final String userId,
    final int missedJoins,
    final DateTime? restrictionEndDate,
  }) = _$UserJoinStatsImpl;

  factory _UserJoinStats.fromJson(Map<String, dynamic> json) =
      _$UserJoinStatsImpl.fromJson;

  @override
  String get userId;
  @override
  int get missedJoins;
  @override
  DateTime? get restrictionEndDate;

  /// Create a copy of UserJoinStats
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserJoinStatsImplCopyWith<_$UserJoinStatsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Booking _$BookingFromJson(Map<String, dynamic> json) {
  return _Booking.fromJson(json);
}

/// @nodoc
mixin _$Booking {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get placeId => throw _privateConstructorUsedError;
  DateTime get bookingDate => throw _privateConstructorUsedError;
  DateTime get visitDate => throw _privateConstructorUsedError;
  int get guestCount => throw _privateConstructorUsedError;
  BookingStatus get status => throw _privateConstructorUsedError;
  double? get totalPrice => throw _privateConstructorUsedError;
  String? get currency => throw _privateConstructorUsedError;
  String? get specialRequests => throw _privateConstructorUsedError;
  String? get contactPhone => throw _privateConstructorUsedError;
  String? get contactEmail => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this Booking to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Booking
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BookingCopyWith<Booking> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BookingCopyWith<$Res> {
  factory $BookingCopyWith(Booking value, $Res Function(Booking) then) =
      _$BookingCopyWithImpl<$Res, Booking>;
  @useResult
  $Res call({
    String id,
    String userId,
    String placeId,
    DateTime bookingDate,
    DateTime visitDate,
    int guestCount,
    BookingStatus status,
    double? totalPrice,
    String? currency,
    String? specialRequests,
    String? contactPhone,
    String? contactEmail,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class _$BookingCopyWithImpl<$Res, $Val extends Booking>
    implements $BookingCopyWith<$Res> {
  _$BookingCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Booking
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? placeId = null,
    Object? bookingDate = null,
    Object? visitDate = null,
    Object? guestCount = null,
    Object? status = null,
    Object? totalPrice = freezed,
    Object? currency = freezed,
    Object? specialRequests = freezed,
    Object? contactPhone = freezed,
    Object? contactEmail = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            placeId: null == placeId
                ? _value.placeId
                : placeId // ignore: cast_nullable_to_non_nullable
                      as String,
            bookingDate: null == bookingDate
                ? _value.bookingDate
                : bookingDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            visitDate: null == visitDate
                ? _value.visitDate
                : visitDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            guestCount: null == guestCount
                ? _value.guestCount
                : guestCount // ignore: cast_nullable_to_non_nullable
                      as int,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as BookingStatus,
            totalPrice: freezed == totalPrice
                ? _value.totalPrice
                : totalPrice // ignore: cast_nullable_to_non_nullable
                      as double?,
            currency: freezed == currency
                ? _value.currency
                : currency // ignore: cast_nullable_to_non_nullable
                      as String?,
            specialRequests: freezed == specialRequests
                ? _value.specialRequests
                : specialRequests // ignore: cast_nullable_to_non_nullable
                      as String?,
            contactPhone: freezed == contactPhone
                ? _value.contactPhone
                : contactPhone // ignore: cast_nullable_to_non_nullable
                      as String?,
            contactEmail: freezed == contactEmail
                ? _value.contactEmail
                : contactEmail // ignore: cast_nullable_to_non_nullable
                      as String?,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$BookingImplCopyWith<$Res> implements $BookingCopyWith<$Res> {
  factory _$$BookingImplCopyWith(
    _$BookingImpl value,
    $Res Function(_$BookingImpl) then,
  ) = __$$BookingImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    String placeId,
    DateTime bookingDate,
    DateTime visitDate,
    int guestCount,
    BookingStatus status,
    double? totalPrice,
    String? currency,
    String? specialRequests,
    String? contactPhone,
    String? contactEmail,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class __$$BookingImplCopyWithImpl<$Res>
    extends _$BookingCopyWithImpl<$Res, _$BookingImpl>
    implements _$$BookingImplCopyWith<$Res> {
  __$$BookingImplCopyWithImpl(
    _$BookingImpl _value,
    $Res Function(_$BookingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Booking
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? placeId = null,
    Object? bookingDate = null,
    Object? visitDate = null,
    Object? guestCount = null,
    Object? status = null,
    Object? totalPrice = freezed,
    Object? currency = freezed,
    Object? specialRequests = freezed,
    Object? contactPhone = freezed,
    Object? contactEmail = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$BookingImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        placeId: null == placeId
            ? _value.placeId
            : placeId // ignore: cast_nullable_to_non_nullable
                  as String,
        bookingDate: null == bookingDate
            ? _value.bookingDate
            : bookingDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        visitDate: null == visitDate
            ? _value.visitDate
            : visitDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        guestCount: null == guestCount
            ? _value.guestCount
            : guestCount // ignore: cast_nullable_to_non_nullable
                  as int,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as BookingStatus,
        totalPrice: freezed == totalPrice
            ? _value.totalPrice
            : totalPrice // ignore: cast_nullable_to_non_nullable
                  as double?,
        currency: freezed == currency
            ? _value.currency
            : currency // ignore: cast_nullable_to_non_nullable
                  as String?,
        specialRequests: freezed == specialRequests
            ? _value.specialRequests
            : specialRequests // ignore: cast_nullable_to_non_nullable
                  as String?,
        contactPhone: freezed == contactPhone
            ? _value.contactPhone
            : contactPhone // ignore: cast_nullable_to_non_nullable
                  as String?,
        contactEmail: freezed == contactEmail
            ? _value.contactEmail
            : contactEmail // ignore: cast_nullable_to_non_nullable
                  as String?,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$BookingImpl implements _Booking {
  const _$BookingImpl({
    required this.id,
    required this.userId,
    required this.placeId,
    required this.bookingDate,
    required this.visitDate,
    required this.guestCount,
    this.status = BookingStatus.pending,
    this.totalPrice,
    this.currency,
    this.specialRequests,
    this.contactPhone,
    this.contactEmail,
    this.createdAt,
    this.updatedAt,
  });

  factory _$BookingImpl.fromJson(Map<String, dynamic> json) =>
      _$$BookingImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String placeId;
  @override
  final DateTime bookingDate;
  @override
  final DateTime visitDate;
  @override
  final int guestCount;
  @override
  @JsonKey()
  final BookingStatus status;
  @override
  final double? totalPrice;
  @override
  final String? currency;
  @override
  final String? specialRequests;
  @override
  final String? contactPhone;
  @override
  final String? contactEmail;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'Booking(id: $id, userId: $userId, placeId: $placeId, bookingDate: $bookingDate, visitDate: $visitDate, guestCount: $guestCount, status: $status, totalPrice: $totalPrice, currency: $currency, specialRequests: $specialRequests, contactPhone: $contactPhone, contactEmail: $contactEmail, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BookingImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.placeId, placeId) || other.placeId == placeId) &&
            (identical(other.bookingDate, bookingDate) ||
                other.bookingDate == bookingDate) &&
            (identical(other.visitDate, visitDate) ||
                other.visitDate == visitDate) &&
            (identical(other.guestCount, guestCount) ||
                other.guestCount == guestCount) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.totalPrice, totalPrice) ||
                other.totalPrice == totalPrice) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.specialRequests, specialRequests) ||
                other.specialRequests == specialRequests) &&
            (identical(other.contactPhone, contactPhone) ||
                other.contactPhone == contactPhone) &&
            (identical(other.contactEmail, contactEmail) ||
                other.contactEmail == contactEmail) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    userId,
    placeId,
    bookingDate,
    visitDate,
    guestCount,
    status,
    totalPrice,
    currency,
    specialRequests,
    contactPhone,
    contactEmail,
    createdAt,
    updatedAt,
  );

  /// Create a copy of Booking
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BookingImplCopyWith<_$BookingImpl> get copyWith =>
      __$$BookingImplCopyWithImpl<_$BookingImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BookingImplToJson(this);
  }
}

abstract class _Booking implements Booking {
  const factory _Booking({
    required final String id,
    required final String userId,
    required final String placeId,
    required final DateTime bookingDate,
    required final DateTime visitDate,
    required final int guestCount,
    final BookingStatus status,
    final double? totalPrice,
    final String? currency,
    final String? specialRequests,
    final String? contactPhone,
    final String? contactEmail,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$BookingImpl;

  factory _Booking.fromJson(Map<String, dynamic> json) = _$BookingImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get placeId;
  @override
  DateTime get bookingDate;
  @override
  DateTime get visitDate;
  @override
  int get guestCount;
  @override
  BookingStatus get status;
  @override
  double? get totalPrice;
  @override
  String? get currency;
  @override
  String? get specialRequests;
  @override
  String? get contactPhone;
  @override
  String? get contactEmail;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of Booking
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BookingImplCopyWith<_$BookingImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

BookingRequest _$BookingRequestFromJson(Map<String, dynamic> json) {
  return _BookingRequest.fromJson(json);
}

/// @nodoc
mixin _$BookingRequest {
  String get placeId => throw _privateConstructorUsedError;
  DateTime get visitDate => throw _privateConstructorUsedError;
  int get guestCount => throw _privateConstructorUsedError;
  String? get specialRequests => throw _privateConstructorUsedError;
  String? get contactPhone => throw _privateConstructorUsedError;
  String? get contactEmail => throw _privateConstructorUsedError;

  /// Serializes this BookingRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BookingRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BookingRequestCopyWith<BookingRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BookingRequestCopyWith<$Res> {
  factory $BookingRequestCopyWith(
    BookingRequest value,
    $Res Function(BookingRequest) then,
  ) = _$BookingRequestCopyWithImpl<$Res, BookingRequest>;
  @useResult
  $Res call({
    String placeId,
    DateTime visitDate,
    int guestCount,
    String? specialRequests,
    String? contactPhone,
    String? contactEmail,
  });
}

/// @nodoc
class _$BookingRequestCopyWithImpl<$Res, $Val extends BookingRequest>
    implements $BookingRequestCopyWith<$Res> {
  _$BookingRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BookingRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? placeId = null,
    Object? visitDate = null,
    Object? guestCount = null,
    Object? specialRequests = freezed,
    Object? contactPhone = freezed,
    Object? contactEmail = freezed,
  }) {
    return _then(
      _value.copyWith(
            placeId: null == placeId
                ? _value.placeId
                : placeId // ignore: cast_nullable_to_non_nullable
                      as String,
            visitDate: null == visitDate
                ? _value.visitDate
                : visitDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            guestCount: null == guestCount
                ? _value.guestCount
                : guestCount // ignore: cast_nullable_to_non_nullable
                      as int,
            specialRequests: freezed == specialRequests
                ? _value.specialRequests
                : specialRequests // ignore: cast_nullable_to_non_nullable
                      as String?,
            contactPhone: freezed == contactPhone
                ? _value.contactPhone
                : contactPhone // ignore: cast_nullable_to_non_nullable
                      as String?,
            contactEmail: freezed == contactEmail
                ? _value.contactEmail
                : contactEmail // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$BookingRequestImplCopyWith<$Res>
    implements $BookingRequestCopyWith<$Res> {
  factory _$$BookingRequestImplCopyWith(
    _$BookingRequestImpl value,
    $Res Function(_$BookingRequestImpl) then,
  ) = __$$BookingRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String placeId,
    DateTime visitDate,
    int guestCount,
    String? specialRequests,
    String? contactPhone,
    String? contactEmail,
  });
}

/// @nodoc
class __$$BookingRequestImplCopyWithImpl<$Res>
    extends _$BookingRequestCopyWithImpl<$Res, _$BookingRequestImpl>
    implements _$$BookingRequestImplCopyWith<$Res> {
  __$$BookingRequestImplCopyWithImpl(
    _$BookingRequestImpl _value,
    $Res Function(_$BookingRequestImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of BookingRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? placeId = null,
    Object? visitDate = null,
    Object? guestCount = null,
    Object? specialRequests = freezed,
    Object? contactPhone = freezed,
    Object? contactEmail = freezed,
  }) {
    return _then(
      _$BookingRequestImpl(
        placeId: null == placeId
            ? _value.placeId
            : placeId // ignore: cast_nullable_to_non_nullable
                  as String,
        visitDate: null == visitDate
            ? _value.visitDate
            : visitDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        guestCount: null == guestCount
            ? _value.guestCount
            : guestCount // ignore: cast_nullable_to_non_nullable
                  as int,
        specialRequests: freezed == specialRequests
            ? _value.specialRequests
            : specialRequests // ignore: cast_nullable_to_non_nullable
                  as String?,
        contactPhone: freezed == contactPhone
            ? _value.contactPhone
            : contactPhone // ignore: cast_nullable_to_non_nullable
                  as String?,
        contactEmail: freezed == contactEmail
            ? _value.contactEmail
            : contactEmail // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$BookingRequestImpl implements _BookingRequest {
  const _$BookingRequestImpl({
    required this.placeId,
    required this.visitDate,
    required this.guestCount,
    this.specialRequests,
    this.contactPhone,
    this.contactEmail,
  });

  factory _$BookingRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$BookingRequestImplFromJson(json);

  @override
  final String placeId;
  @override
  final DateTime visitDate;
  @override
  final int guestCount;
  @override
  final String? specialRequests;
  @override
  final String? contactPhone;
  @override
  final String? contactEmail;

  @override
  String toString() {
    return 'BookingRequest(placeId: $placeId, visitDate: $visitDate, guestCount: $guestCount, specialRequests: $specialRequests, contactPhone: $contactPhone, contactEmail: $contactEmail)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BookingRequestImpl &&
            (identical(other.placeId, placeId) || other.placeId == placeId) &&
            (identical(other.visitDate, visitDate) ||
                other.visitDate == visitDate) &&
            (identical(other.guestCount, guestCount) ||
                other.guestCount == guestCount) &&
            (identical(other.specialRequests, specialRequests) ||
                other.specialRequests == specialRequests) &&
            (identical(other.contactPhone, contactPhone) ||
                other.contactPhone == contactPhone) &&
            (identical(other.contactEmail, contactEmail) ||
                other.contactEmail == contactEmail));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    placeId,
    visitDate,
    guestCount,
    specialRequests,
    contactPhone,
    contactEmail,
  );

  /// Create a copy of BookingRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BookingRequestImplCopyWith<_$BookingRequestImpl> get copyWith =>
      __$$BookingRequestImplCopyWithImpl<_$BookingRequestImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$BookingRequestImplToJson(this);
  }
}

abstract class _BookingRequest implements BookingRequest {
  const factory _BookingRequest({
    required final String placeId,
    required final DateTime visitDate,
    required final int guestCount,
    final String? specialRequests,
    final String? contactPhone,
    final String? contactEmail,
  }) = _$BookingRequestImpl;

  factory _BookingRequest.fromJson(Map<String, dynamic> json) =
      _$BookingRequestImpl.fromJson;

  @override
  String get placeId;
  @override
  DateTime get visitDate;
  @override
  int get guestCount;
  @override
  String? get specialRequests;
  @override
  String? get contactPhone;
  @override
  String? get contactEmail;

  /// Create a copy of BookingRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BookingRequestImplCopyWith<_$BookingRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PlacesFilter _$PlacesFilterFromJson(Map<String, dynamic> json) {
  return _PlacesFilter.fromJson(json);
}

/// @nodoc
mixin _$PlacesFilter {
  Set<PlaceCategory> get categories => throw _privateConstructorUsedError;
  Set<PlaceType> get types => throw _privateConstructorUsedError;
  DateTime? get startDate => throw _privateConstructorUsedError;
  DateTime? get endDate => throw _privateConstructorUsedError;
  double? get minPrice => throw _privateConstructorUsedError;
  double? get maxPrice => throw _privateConstructorUsedError;
  bool get verifiedOnly => throw _privateConstructorUsedError;
  bool get exclusiveOnly => throw _privateConstructorUsedError;
  bool get trendingOnly => throw _privateConstructorUsedError;
  String? get searchQuery => throw _privateConstructorUsedError;
  String? get location => throw _privateConstructorUsedError;
  double? get latitude => throw _privateConstructorUsedError;
  double? get longitude => throw _privateConstructorUsedError;
  double? get radius => throw _privateConstructorUsedError;
  double get minRating => throw _privateConstructorUsedError;

  /// Serializes this PlacesFilter to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PlacesFilter
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PlacesFilterCopyWith<PlacesFilter> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlacesFilterCopyWith<$Res> {
  factory $PlacesFilterCopyWith(
    PlacesFilter value,
    $Res Function(PlacesFilter) then,
  ) = _$PlacesFilterCopyWithImpl<$Res, PlacesFilter>;
  @useResult
  $Res call({
    Set<PlaceCategory> categories,
    Set<PlaceType> types,
    DateTime? startDate,
    DateTime? endDate,
    double? minPrice,
    double? maxPrice,
    bool verifiedOnly,
    bool exclusiveOnly,
    bool trendingOnly,
    String? searchQuery,
    String? location,
    double? latitude,
    double? longitude,
    double? radius,
    double minRating,
  });
}

/// @nodoc
class _$PlacesFilterCopyWithImpl<$Res, $Val extends PlacesFilter>
    implements $PlacesFilterCopyWith<$Res> {
  _$PlacesFilterCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PlacesFilter
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? categories = null,
    Object? types = null,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? minPrice = freezed,
    Object? maxPrice = freezed,
    Object? verifiedOnly = null,
    Object? exclusiveOnly = null,
    Object? trendingOnly = null,
    Object? searchQuery = freezed,
    Object? location = freezed,
    Object? latitude = freezed,
    Object? longitude = freezed,
    Object? radius = freezed,
    Object? minRating = null,
  }) {
    return _then(
      _value.copyWith(
            categories: null == categories
                ? _value.categories
                : categories // ignore: cast_nullable_to_non_nullable
                      as Set<PlaceCategory>,
            types: null == types
                ? _value.types
                : types // ignore: cast_nullable_to_non_nullable
                      as Set<PlaceType>,
            startDate: freezed == startDate
                ? _value.startDate
                : startDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            endDate: freezed == endDate
                ? _value.endDate
                : endDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            minPrice: freezed == minPrice
                ? _value.minPrice
                : minPrice // ignore: cast_nullable_to_non_nullable
                      as double?,
            maxPrice: freezed == maxPrice
                ? _value.maxPrice
                : maxPrice // ignore: cast_nullable_to_non_nullable
                      as double?,
            verifiedOnly: null == verifiedOnly
                ? _value.verifiedOnly
                : verifiedOnly // ignore: cast_nullable_to_non_nullable
                      as bool,
            exclusiveOnly: null == exclusiveOnly
                ? _value.exclusiveOnly
                : exclusiveOnly // ignore: cast_nullable_to_non_nullable
                      as bool,
            trendingOnly: null == trendingOnly
                ? _value.trendingOnly
                : trendingOnly // ignore: cast_nullable_to_non_nullable
                      as bool,
            searchQuery: freezed == searchQuery
                ? _value.searchQuery
                : searchQuery // ignore: cast_nullable_to_non_nullable
                      as String?,
            location: freezed == location
                ? _value.location
                : location // ignore: cast_nullable_to_non_nullable
                      as String?,
            latitude: freezed == latitude
                ? _value.latitude
                : latitude // ignore: cast_nullable_to_non_nullable
                      as double?,
            longitude: freezed == longitude
                ? _value.longitude
                : longitude // ignore: cast_nullable_to_non_nullable
                      as double?,
            radius: freezed == radius
                ? _value.radius
                : radius // ignore: cast_nullable_to_non_nullable
                      as double?,
            minRating: null == minRating
                ? _value.minRating
                : minRating // ignore: cast_nullable_to_non_nullable
                      as double,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PlacesFilterImplCopyWith<$Res>
    implements $PlacesFilterCopyWith<$Res> {
  factory _$$PlacesFilterImplCopyWith(
    _$PlacesFilterImpl value,
    $Res Function(_$PlacesFilterImpl) then,
  ) = __$$PlacesFilterImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    Set<PlaceCategory> categories,
    Set<PlaceType> types,
    DateTime? startDate,
    DateTime? endDate,
    double? minPrice,
    double? maxPrice,
    bool verifiedOnly,
    bool exclusiveOnly,
    bool trendingOnly,
    String? searchQuery,
    String? location,
    double? latitude,
    double? longitude,
    double? radius,
    double minRating,
  });
}

/// @nodoc
class __$$PlacesFilterImplCopyWithImpl<$Res>
    extends _$PlacesFilterCopyWithImpl<$Res, _$PlacesFilterImpl>
    implements _$$PlacesFilterImplCopyWith<$Res> {
  __$$PlacesFilterImplCopyWithImpl(
    _$PlacesFilterImpl _value,
    $Res Function(_$PlacesFilterImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PlacesFilter
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? categories = null,
    Object? types = null,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? minPrice = freezed,
    Object? maxPrice = freezed,
    Object? verifiedOnly = null,
    Object? exclusiveOnly = null,
    Object? trendingOnly = null,
    Object? searchQuery = freezed,
    Object? location = freezed,
    Object? latitude = freezed,
    Object? longitude = freezed,
    Object? radius = freezed,
    Object? minRating = null,
  }) {
    return _then(
      _$PlacesFilterImpl(
        categories: null == categories
            ? _value._categories
            : categories // ignore: cast_nullable_to_non_nullable
                  as Set<PlaceCategory>,
        types: null == types
            ? _value._types
            : types // ignore: cast_nullable_to_non_nullable
                  as Set<PlaceType>,
        startDate: freezed == startDate
            ? _value.startDate
            : startDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        endDate: freezed == endDate
            ? _value.endDate
            : endDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        minPrice: freezed == minPrice
            ? _value.minPrice
            : minPrice // ignore: cast_nullable_to_non_nullable
                  as double?,
        maxPrice: freezed == maxPrice
            ? _value.maxPrice
            : maxPrice // ignore: cast_nullable_to_non_nullable
                  as double?,
        verifiedOnly: null == verifiedOnly
            ? _value.verifiedOnly
            : verifiedOnly // ignore: cast_nullable_to_non_nullable
                  as bool,
        exclusiveOnly: null == exclusiveOnly
            ? _value.exclusiveOnly
            : exclusiveOnly // ignore: cast_nullable_to_non_nullable
                  as bool,
        trendingOnly: null == trendingOnly
            ? _value.trendingOnly
            : trendingOnly // ignore: cast_nullable_to_non_nullable
                  as bool,
        searchQuery: freezed == searchQuery
            ? _value.searchQuery
            : searchQuery // ignore: cast_nullable_to_non_nullable
                  as String?,
        location: freezed == location
            ? _value.location
            : location // ignore: cast_nullable_to_non_nullable
                  as String?,
        latitude: freezed == latitude
            ? _value.latitude
            : latitude // ignore: cast_nullable_to_non_nullable
                  as double?,
        longitude: freezed == longitude
            ? _value.longitude
            : longitude // ignore: cast_nullable_to_non_nullable
                  as double?,
        radius: freezed == radius
            ? _value.radius
            : radius // ignore: cast_nullable_to_non_nullable
                  as double?,
        minRating: null == minRating
            ? _value.minRating
            : minRating // ignore: cast_nullable_to_non_nullable
                  as double,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PlacesFilterImpl implements _PlacesFilter {
  const _$PlacesFilterImpl({
    final Set<PlaceCategory> categories = const {},
    final Set<PlaceType> types = const {},
    this.startDate,
    this.endDate,
    this.minPrice,
    this.maxPrice,
    this.verifiedOnly = false,
    this.exclusiveOnly = false,
    this.trendingOnly = false,
    this.searchQuery,
    this.location,
    this.latitude,
    this.longitude,
    this.radius,
    this.minRating = 0.0,
  }) : _categories = categories,
       _types = types;

  factory _$PlacesFilterImpl.fromJson(Map<String, dynamic> json) =>
      _$$PlacesFilterImplFromJson(json);

  final Set<PlaceCategory> _categories;
  @override
  @JsonKey()
  Set<PlaceCategory> get categories {
    if (_categories is EqualUnmodifiableSetView) return _categories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_categories);
  }

  final Set<PlaceType> _types;
  @override
  @JsonKey()
  Set<PlaceType> get types {
    if (_types is EqualUnmodifiableSetView) return _types;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_types);
  }

  @override
  final DateTime? startDate;
  @override
  final DateTime? endDate;
  @override
  final double? minPrice;
  @override
  final double? maxPrice;
  @override
  @JsonKey()
  final bool verifiedOnly;
  @override
  @JsonKey()
  final bool exclusiveOnly;
  @override
  @JsonKey()
  final bool trendingOnly;
  @override
  final String? searchQuery;
  @override
  final String? location;
  @override
  final double? latitude;
  @override
  final double? longitude;
  @override
  final double? radius;
  @override
  @JsonKey()
  final double minRating;

  @override
  String toString() {
    return 'PlacesFilter(categories: $categories, types: $types, startDate: $startDate, endDate: $endDate, minPrice: $minPrice, maxPrice: $maxPrice, verifiedOnly: $verifiedOnly, exclusiveOnly: $exclusiveOnly, trendingOnly: $trendingOnly, searchQuery: $searchQuery, location: $location, latitude: $latitude, longitude: $longitude, radius: $radius, minRating: $minRating)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlacesFilterImpl &&
            const DeepCollectionEquality().equals(
              other._categories,
              _categories,
            ) &&
            const DeepCollectionEquality().equals(other._types, _types) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.minPrice, minPrice) ||
                other.minPrice == minPrice) &&
            (identical(other.maxPrice, maxPrice) ||
                other.maxPrice == maxPrice) &&
            (identical(other.verifiedOnly, verifiedOnly) ||
                other.verifiedOnly == verifiedOnly) &&
            (identical(other.exclusiveOnly, exclusiveOnly) ||
                other.exclusiveOnly == exclusiveOnly) &&
            (identical(other.trendingOnly, trendingOnly) ||
                other.trendingOnly == trendingOnly) &&
            (identical(other.searchQuery, searchQuery) ||
                other.searchQuery == searchQuery) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.radius, radius) || other.radius == radius) &&
            (identical(other.minRating, minRating) ||
                other.minRating == minRating));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_categories),
    const DeepCollectionEquality().hash(_types),
    startDate,
    endDate,
    minPrice,
    maxPrice,
    verifiedOnly,
    exclusiveOnly,
    trendingOnly,
    searchQuery,
    location,
    latitude,
    longitude,
    radius,
    minRating,
  );

  /// Create a copy of PlacesFilter
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PlacesFilterImplCopyWith<_$PlacesFilterImpl> get copyWith =>
      __$$PlacesFilterImplCopyWithImpl<_$PlacesFilterImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PlacesFilterImplToJson(this);
  }
}

abstract class _PlacesFilter implements PlacesFilter {
  const factory _PlacesFilter({
    final Set<PlaceCategory> categories,
    final Set<PlaceType> types,
    final DateTime? startDate,
    final DateTime? endDate,
    final double? minPrice,
    final double? maxPrice,
    final bool verifiedOnly,
    final bool exclusiveOnly,
    final bool trendingOnly,
    final String? searchQuery,
    final String? location,
    final double? latitude,
    final double? longitude,
    final double? radius,
    final double minRating,
  }) = _$PlacesFilterImpl;

  factory _PlacesFilter.fromJson(Map<String, dynamic> json) =
      _$PlacesFilterImpl.fromJson;

  @override
  Set<PlaceCategory> get categories;
  @override
  Set<PlaceType> get types;
  @override
  DateTime? get startDate;
  @override
  DateTime? get endDate;
  @override
  double? get minPrice;
  @override
  double? get maxPrice;
  @override
  bool get verifiedOnly;
  @override
  bool get exclusiveOnly;
  @override
  bool get trendingOnly;
  @override
  String? get searchQuery;
  @override
  String? get location;
  @override
  double? get latitude;
  @override
  double? get longitude;
  @override
  double? get radius;
  @override
  double get minRating;

  /// Create a copy of PlacesFilter
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PlacesFilterImplCopyWith<_$PlacesFilterImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
