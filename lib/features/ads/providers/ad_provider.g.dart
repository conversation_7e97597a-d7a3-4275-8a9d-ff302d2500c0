// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ad_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$adsForPlacementHash() => r'cbe21eb2522fdfe861b5d37a92a5126c3819d1fb';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider for ads in specific placement
///
/// Copied from [adsForPlacement].
@ProviderFor(adsForPlacement)
const adsForPlacementProvider = AdsForPlacementFamily();

/// Provider for ads in specific placement
///
/// Copied from [adsForPlacement].
class AdsForPlacementFamily extends Family<AsyncValue<List<AdModel>>> {
  /// Provider for ads in specific placement
  ///
  /// Copied from [adsForPlacement].
  const AdsForPlacementFamily();

  /// Provider for ads in specific placement
  ///
  /// Copied from [adsForPlacement].
  AdsForPlacementProvider call(AdPlacement placement, {int limit = 5}) {
    return AdsForPlacementProvider(placement, limit: limit);
  }

  @override
  AdsForPlacementProvider getProviderOverride(
    covariant AdsForPlacementProvider provider,
  ) {
    return call(provider.placement, limit: provider.limit);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'adsForPlacementProvider';
}

/// Provider for ads in specific placement
///
/// Copied from [adsForPlacement].
class AdsForPlacementProvider extends AutoDisposeFutureProvider<List<AdModel>> {
  /// Provider for ads in specific placement
  ///
  /// Copied from [adsForPlacement].
  AdsForPlacementProvider(AdPlacement placement, {int limit = 5})
    : this._internal(
        (ref) =>
            adsForPlacement(ref as AdsForPlacementRef, placement, limit: limit),
        from: adsForPlacementProvider,
        name: r'adsForPlacementProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$adsForPlacementHash,
        dependencies: AdsForPlacementFamily._dependencies,
        allTransitiveDependencies:
            AdsForPlacementFamily._allTransitiveDependencies,
        placement: placement,
        limit: limit,
      );

  AdsForPlacementProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.placement,
    required this.limit,
  }) : super.internal();

  final AdPlacement placement;
  final int limit;

  @override
  Override overrideWith(
    FutureOr<List<AdModel>> Function(AdsForPlacementRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: AdsForPlacementProvider._internal(
        (ref) => create(ref as AdsForPlacementRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        placement: placement,
        limit: limit,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<AdModel>> createElement() {
    return _AdsForPlacementProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AdsForPlacementProvider &&
        other.placement == placement &&
        other.limit == limit;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, placement.hashCode);
    hash = _SystemHash.combine(hash, limit.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin AdsForPlacementRef on AutoDisposeFutureProviderRef<List<AdModel>> {
  /// The parameter `placement` of this provider.
  AdPlacement get placement;

  /// The parameter `limit` of this provider.
  int get limit;
}

class _AdsForPlacementProviderElement
    extends AutoDisposeFutureProviderElement<List<AdModel>>
    with AdsForPlacementRef {
  _AdsForPlacementProviderElement(super.provider);

  @override
  AdPlacement get placement => (origin as AdsForPlacementProvider).placement;
  @override
  int get limit => (origin as AdsForPlacementProvider).limit;
}

String _$pendingAdsHash() => r'e6de29093df7966d7d2f196856b6c8b7c9a147e7';

/// Provider for pending ads (admin only)
///
/// Copied from [pendingAds].
@ProviderFor(pendingAds)
final pendingAdsProvider = AutoDisposeFutureProvider<List<AdModel>>.internal(
  pendingAds,
  name: r'pendingAdsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$pendingAdsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PendingAdsRef = AutoDisposeFutureProviderRef<List<AdModel>>;
String _$adAnalyticsHash() => r'40b8e90cfada5248aaeb81a81cf2521e5622e5f0';

/// Provider for ad analytics
///
/// Copied from [adAnalytics].
@ProviderFor(adAnalytics)
const adAnalyticsProvider = AdAnalyticsFamily();

/// Provider for ad analytics
///
/// Copied from [adAnalytics].
class AdAnalyticsFamily extends Family<AsyncValue<AdMetrics?>> {
  /// Provider for ad analytics
  ///
  /// Copied from [adAnalytics].
  const AdAnalyticsFamily();

  /// Provider for ad analytics
  ///
  /// Copied from [adAnalytics].
  AdAnalyticsProvider call(String adId) {
    return AdAnalyticsProvider(adId);
  }

  @override
  AdAnalyticsProvider getProviderOverride(
    covariant AdAnalyticsProvider provider,
  ) {
    return call(provider.adId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'adAnalyticsProvider';
}

/// Provider for ad analytics
///
/// Copied from [adAnalytics].
class AdAnalyticsProvider extends AutoDisposeFutureProvider<AdMetrics?> {
  /// Provider for ad analytics
  ///
  /// Copied from [adAnalytics].
  AdAnalyticsProvider(String adId)
    : this._internal(
        (ref) => adAnalytics(ref as AdAnalyticsRef, adId),
        from: adAnalyticsProvider,
        name: r'adAnalyticsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$adAnalyticsHash,
        dependencies: AdAnalyticsFamily._dependencies,
        allTransitiveDependencies: AdAnalyticsFamily._allTransitiveDependencies,
        adId: adId,
      );

  AdAnalyticsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.adId,
  }) : super.internal();

  final String adId;

  @override
  Override overrideWith(
    FutureOr<AdMetrics?> Function(AdAnalyticsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: AdAnalyticsProvider._internal(
        (ref) => create(ref as AdAnalyticsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        adId: adId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<AdMetrics?> createElement() {
    return _AdAnalyticsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AdAnalyticsProvider && other.adId == adId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, adId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin AdAnalyticsRef on AutoDisposeFutureProviderRef<AdMetrics?> {
  /// The parameter `adId` of this provider.
  String get adId;
}

class _AdAnalyticsProviderElement
    extends AutoDisposeFutureProviderElement<AdMetrics?>
    with AdAnalyticsRef {
  _AdAnalyticsProviderElement(super.provider);

  @override
  String get adId => (origin as AdAnalyticsProvider).adId;
}

String _$storyAdsHash() => r'448ef1241ec6af896df2b72302680a25267e7b18';

/// Provider for story ads
///
/// Copied from [storyAds].
@ProviderFor(storyAds)
final storyAdsProvider = AutoDisposeFutureProvider<List<AdModel>>.internal(
  storyAds,
  name: r'storyAdsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$storyAdsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef StoryAdsRef = AutoDisposeFutureProviderRef<List<AdModel>>;
String _$marketplaceAdsHash() => r'cd110b573cf2e7e7c622cb5125feb161655e43e5';

/// Provider for marketplace ads
///
/// Copied from [marketplaceAds].
@ProviderFor(marketplaceAds)
final marketplaceAdsProvider =
    AutoDisposeFutureProvider<List<AdModel>>.internal(
      marketplaceAds,
      name: r'marketplaceAdsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$marketplaceAdsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef MarketplaceAdsRef = AutoDisposeFutureProviderRef<List<AdModel>>;
String _$searchAdsHash() => r'c888460b727b88d36fae76b7f4a2f985ae6add87';

/// Provider for search ads
///
/// Copied from [searchAds].
@ProviderFor(searchAds)
final searchAdsProvider = AutoDisposeFutureProvider<List<AdModel>>.internal(
  searchAds,
  name: r'searchAdsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$searchAdsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SearchAdsRef = AutoDisposeFutureProviderRef<List<AdModel>>;
String _$sponsoredContentHash() => r'd93014e2f39d278022cb13fa899dcaa57f906626';

/// Provider for sponsored content in feed
///
/// Copied from [SponsoredContent].
@ProviderFor(SponsoredContent)
final sponsoredContentProvider =
    AutoDisposeAsyncNotifierProvider<SponsoredContent, List<AdModel>>.internal(
      SponsoredContent.new,
      name: r'sponsoredContentProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$sponsoredContentHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SponsoredContent = AutoDisposeAsyncNotifier<List<AdModel>>;
String _$feedAdsIntegrationHash() =>
    r'074249a1ee42f5318f6e2343dede3c04e716ea9d';

/// Provider for feed ads integration
///
/// Copied from [FeedAdsIntegration].
@ProviderFor(FeedAdsIntegration)
final feedAdsIntegrationProvider =
    AutoDisposeAsyncNotifierProvider<
      FeedAdsIntegration,
      List<AdModel>
    >.internal(
      FeedAdsIntegration.new,
      name: r'feedAdsIntegrationProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$feedAdsIntegrationHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$FeedAdsIntegration = AutoDisposeAsyncNotifier<List<AdModel>>;
String _$adInteractionTrackerHash() =>
    r'5db652670bb18cb157514c2651e5224e611d6ee9';

/// Ad interaction tracking provider
///
/// Copied from [AdInteractionTracker].
@ProviderFor(AdInteractionTracker)
final adInteractionTrackerProvider =
    AutoDisposeNotifierProvider<
      AdInteractionTracker,
      Map<String, int>
    >.internal(
      AdInteractionTracker.new,
      name: r'adInteractionTrackerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$adInteractionTrackerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$AdInteractionTracker = AutoDisposeNotifier<Map<String, int>>;
String _$adConfigurationHash() => r'23d33faf2f9e9ee49d60b8bc6d115faa9ecf4c69';

/// Ad configuration provider
///
/// Copied from [AdConfiguration].
@ProviderFor(AdConfiguration)
final adConfigurationProvider =
    AutoDisposeNotifierProvider<AdConfiguration, Map<String, dynamic>>.internal(
      AdConfiguration.new,
      name: r'adConfigurationProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$adConfigurationHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$AdConfiguration = AutoDisposeNotifier<Map<String, dynamic>>;
String _$adPerformanceHash() => r'3aa248064a1ef273b4c81ac364df26c284ea2cd8';

/// Ad performance provider
///
/// Copied from [AdPerformance].
@ProviderFor(AdPerformance)
final adPerformanceProvider =
    AutoDisposeNotifierProvider<AdPerformance, Map<String, AdMetrics>>.internal(
      AdPerformance.new,
      name: r'adPerformanceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$adPerformanceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$AdPerformance = AutoDisposeNotifier<Map<String, AdMetrics>>;
String _$adTargetingProviderHash() =>
    r'ae74d82bfadeb0db5ae24c86463d1b072dd823a6';

/// Ad targeting provider
///
/// Copied from [AdTargetingProvider].
@ProviderFor(AdTargetingProvider)
final adTargetingProviderProvider =
    AutoDisposeNotifierProvider<AdTargetingProvider, AdTargeting>.internal(
      AdTargetingProvider.new,
      name: r'adTargetingProviderProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$adTargetingProviderHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$AdTargetingProvider = AutoDisposeNotifier<AdTargeting>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
