import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/auth/models/saved_account_model.dart';
import 'package:billionaires_social/features/auth/models/multi_account_session.dart';
import 'package:billionaires_social/features/auth/providers/multi_account_provider.dart';
import 'package:billionaires_social/features/auth/widgets/add_account_dialog.dart';
import 'package:billionaires_social/core/widgets/cached_network_image_widget.dart';

/// Screen for managing multiple accounts
class AccountManagementScreen extends ConsumerStatefulWidget {
  const AccountManagementScreen({super.key});

  @override
  ConsumerState<AccountManagementScreen> createState() => _AccountManagementScreenState();
}

class _AccountManagementScreenState extends ConsumerState<AccountManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Account Management'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Accounts', icon: Icon(Icons.account_circle)),
            Tab(text: 'History', icon: Icon(Icons.history)),
            Tab(text: 'Settings', icon: Icon(Icons.settings)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [
          AccountsTab(),
          HistoryTab(),
          SettingsTab(),
        ],
      ),
    );
  }
}

/// Tab showing all saved accounts
class AccountsTab extends ConsumerWidget {
  const AccountsTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sessionAsync = ref.watch(multiAccountSessionProvider);
    final statisticsAsync = ref.watch(accountStatisticsProvider);

    return sessionAsync.when(
      data: (session) => Column(
        children: [
          // Statistics Card
          statisticsAsync.when(
            data: (stats) => _buildStatisticsCard(context, stats),
            loading: () => const SizedBox.shrink(),
            error: (_, _) => const SizedBox.shrink(),
          ),
          
          // Accounts List
          Expanded(
            child: session.savedAccounts.isEmpty
                ? _buildEmptyState(context, ref)
                : _buildAccountsList(context, ref, session.savedAccounts, session.activeAccount),
          ),
        ],
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildErrorState(context, ref, error.toString()),
    );
  }

  Widget _buildStatisticsCard(BuildContext context, AccountStatistics stats) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Account Statistics',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Total Accounts',
                    stats.totalAccounts.toString(),
                    Icons.account_circle,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Verified',
                    stats.verifiedAccounts.toString(),
                    Icons.verified,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Billionaires',
                    stats.billionaireAccounts.toString(),
                    Icons.star,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Total Switches',
                    stats.totalSwitches.toString(),
                    Icons.swap_horiz,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Success Rate',
                    '${stats.switchSuccessRate.toStringAsFixed(1)}%',
                    Icons.check_circle,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Avg Duration',
                    '${stats.averageSwitchDuration.inMilliseconds}ms',
                    Icons.timer,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, size: 24, color: Theme.of(context).primaryColor),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildAccountsList(
    BuildContext context,
    WidgetRef ref,
    List<SavedAccountModel> accounts,
    SavedAccountModel? activeAccount,
  ) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: accounts.length,
      itemBuilder: (context, index) {
        final account = accounts[index];
        final isActive = activeAccount?.id == account.id;
        
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            contentPadding: const EdgeInsets.all(16),
            leading: Stack(
              children: [
                CircleAvatar(
                  radius: 24,
                  backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  child: account.profilePictureUrl?.isNotEmpty == true
                      ? ClipOval(
                          child: CachedNetworkImageWidget(
                            imageUrl: account.profilePictureUrl!,
                            width: 48,
                            height: 48,
                            fit: BoxFit.cover,
                          ),
                        )
                      : Text(
                          account.displayName.isNotEmpty 
                              ? account.displayName[0].toUpperCase()
                              : account.email[0].toUpperCase(),
                          style: TextStyle(
                            color: Theme.of(context).primaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
                if (isActive)
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      width: 16,
                      height: 16,
                      decoration: BoxDecoration(
                        color: Colors.green,
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 2),
                      ),
                      child: const Icon(
                        Icons.check,
                        size: 10,
                        color: Colors.white,
                      ),
                    ),
                  ),
              ],
            ),
            title: Row(
              children: [
                Expanded(
                  child: Text(
                    account.displayName.isNotEmpty ? account.displayName : account.username,
                    style: TextStyle(
                      fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
                if (account.isVerified)
                  const Icon(Icons.verified, color: Colors.blue, size: 16),
                if (account.isBillionaire)
                  Container(
                    margin: const EdgeInsets.only(left: 4),
                    padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.amber,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Text(
                      'BILLIONAIRE',
                      style: TextStyle(
                        fontSize: 8,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
              ],
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('@${account.username}'),
                Text(account.email),
                if (account.lastUsedAt != null)
                  Text(
                    'Last used: ${_formatDate(account.lastUsedAt!)}',
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.grey[600],
                    ),
                  ),
              ],
            ),
            trailing: PopupMenuButton<String>(
              onSelected: (value) => _handleAccountAction(context, ref, account, value),
              itemBuilder: (context) => [
                if (!isActive)
                  const PopupMenuItem(
                    value: 'switch',
                    child: ListTile(
                      leading: Icon(Icons.swap_horiz),
                      title: Text('Switch to this account'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                const PopupMenuItem(
                  value: 'validate',
                  child: ListTile(
                    leading: Icon(Icons.verified_user),
                    title: Text('Validate account'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'remove',
                  child: ListTile(
                    leading: Icon(Icons.delete, color: Colors.red),
                    title: Text('Remove account'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context, WidgetRef ref) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_circle_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No accounts saved',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'Add an account to get started with multi-account switching',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _showAddAccountDialog(context),
            icon: const Icon(Icons.add),
            label: const Text('Add Account'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, WidgetRef ref, String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Error loading accounts',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => ref.refresh(multiAccountSessionProvider),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _handleAccountAction(
    BuildContext context,
    WidgetRef ref,
    SavedAccountModel account,
    String action,
  ) async {
    switch (action) {
      case 'switch':
        await _switchToAccount(context, ref, account);
        break;
      case 'validate':
        await _validateAccount(context, ref, account);
        break;
      case 'remove':
        await _removeAccount(context, ref, account);
        break;
    }
  }

  Future<void> _switchToAccount(
    BuildContext context,
    WidgetRef ref,
    SavedAccountModel account,
  ) async {
    try {
      final result = await ref
          .read(multiAccountNotifierProvider.notifier)
          .switchToAccount(account.id);

      if (result.success) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Switched to ${account.displayName}'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.errorMessage ?? 'Failed to switch account'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _validateAccount(
    BuildContext context,
    WidgetRef ref,
    SavedAccountModel account,
  ) async {
    try {
      final result = await ref
          .read(multiAccountNotifierProvider.notifier)
          .validateAccount(account.id);

      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text('Account Validation: ${account.displayName}'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      result.isValid ? Icons.check_circle : Icons.error,
                      color: result.isValid ? Colors.green : Colors.red,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      result.isValid ? 'Account is valid' : 'Account has issues',
                      style: TextStyle(
                        color: result.isValid ? Colors.green : Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                if (result.issues.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  const Text('Issues found:'),
                  ...result.issues.map((issue) => Padding(
                    padding: const EdgeInsets.only(left: 16, top: 4),
                    child: Text('• $issue'),
                  )),
                ],
                if (result.lastValidatedAt != null) ...[
                  const SizedBox(height: 16),
                  Text(
                    'Last validated: ${_formatDate(result.lastValidatedAt!)}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Validation error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _removeAccount(
    BuildContext context,
    WidgetRef ref,
    SavedAccountModel account,
  ) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove Account'),
        content: Text(
          'Are you sure you want to remove ${account.displayName} from saved accounts? '
          'This will delete all stored credentials for this account.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Remove'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final success = await ref
            .read(multiAccountNotifierProvider.notifier)
            .removeAccount(account.id);

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                success 
                    ? 'Account removed successfully'
                    : 'Failed to remove account',
              ),
              backgroundColor: success ? Colors.green : Colors.red,
            ),
          );
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error removing account: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _showAddAccountDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const AddAccountDialog(),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}

/// Tab showing account switch history
class HistoryTab extends ConsumerWidget {
  const HistoryTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final historyAsync = ref.watch(accountSwitchHistoryProvider);

    return historyAsync.when(
      data: (history) => history.isEmpty
          ? _buildEmptyHistory(context)
          : _buildHistoryList(context, history),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('Error loading history: $error'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.refresh(accountSwitchHistoryProvider),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyHistory(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No switch history',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'Account switches will appear here',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryList(BuildContext context, List<AccountSwitchHistory> history) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: history.length,
      itemBuilder: (context, index) {
        final entry = history[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: Icon(
              entry.successful ? Icons.check_circle : Icons.error,
              color: entry.successful ? Colors.green : Colors.red,
            ),
            title: Text(
              entry.successful ? 'Account Switch' : 'Failed Switch',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: entry.successful ? Colors.green : Colors.red,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (entry.fromAccountId != null)
                  Text('From: ${entry.fromAccountId}'),
                Text('To: ${entry.toAccountId}'),
                Text('Method: ${entry.switchMethod}'),
                if (entry.switchDuration != null)
                  Text('Duration: ${entry.switchDuration!.inMilliseconds}ms'),
                if (!entry.successful && entry.errorMessage != null)
                  Text(
                    'Error: ${entry.errorMessage}',
                    style: const TextStyle(color: Colors.red),
                  ),
              ],
            ),
            trailing: Text(
              _formatDate(entry.switchedAt),
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ),
        );
      },
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}

/// Tab for account management settings
class SettingsTab extends ConsumerWidget {
  const SettingsTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final configAsync = ref.watch(quickSwitcherConfigProvider);

    return configAsync.when(
      data: (config) => _buildSettingsList(context, ref, config),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Text('Error loading settings: $error'),
      ),
    );
  }

  Widget _buildSettingsList(BuildContext context, WidgetRef ref, QuickSwitcherConfig config) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        Card(
          child: Column(
            children: [
              SwitchListTile(
                title: const Text('Show Account Previews'),
                subtitle: const Text('Show account previews in quick switcher'),
                value: config.showAccountPreviews,
                onChanged: (value) => _updateConfig(
                  ref,
                  config.copyWith(showAccountPreviews: value),
                ),
              ),
              SwitchListTile(
                title: const Text('Require Biometric Authentication'),
                subtitle: const Text('Use biometric auth for account switching'),
                value: config.requireBiometricAuth,
                onChanged: (value) => _updateConfig(
                  ref,
                  config.copyWith(requireBiometricAuth: value),
                ),
              ),
              SwitchListTile(
                title: const Text('Show Account Notifications'),
                subtitle: const Text('Display notifications for account activities'),
                value: config.showAccountNotifications,
                onChanged: (value) => _updateConfig(
                  ref,
                  config.copyWith(showAccountNotifications: value),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        Card(
          child: Column(
            children: [
              ListTile(
                title: const Text('Maximum Quick Accounts'),
                subtitle: Text('Show up to ${config.maxQuickAccounts} accounts in quick switcher'),
                trailing: DropdownButton<int>(
                  value: config.maxQuickAccounts,
                  items: [3, 5, 7, 10].map((value) => DropdownMenuItem(
                    value: value,
                    child: Text(value.toString()),
                  )).toList(),
                  onChanged: (value) => _updateConfig(
                    ref,
                    config.copyWith(maxQuickAccounts: value ?? 5),
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        Card(
          child: Column(
            children: [
              ListTile(
                title: const Text('Clear All Account Data'),
                subtitle: const Text('Remove all saved accounts and credentials'),
                trailing: const Icon(Icons.warning, color: Colors.red),
                onTap: () => _showClearDataDialog(context, ref),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _updateConfig(WidgetRef ref, QuickSwitcherConfig config) {
    ref.read(multiAccountNotifierProvider.notifier).updateQuickSwitcherConfig(config);
  }

  void _showClearDataDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Account Data'),
        content: const Text(
          'This will remove all saved accounts, credentials, and switch history. '
          'This action cannot be undone. Are you sure?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await ref.read(multiAccountNotifierProvider.notifier).clearAllAccounts();
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('All account data cleared'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error clearing data: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Clear All Data'),
          ),
        ],
      ),
    );
  }
}
