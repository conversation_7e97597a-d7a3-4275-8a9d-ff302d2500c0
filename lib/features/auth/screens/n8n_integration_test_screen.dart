import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/enhanced_account_switcher.dart';
import '../widgets/n8n_quick_setup_widget.dart';
import '../providers/multi_account_provider.dart';

/// Test screen to demonstrate n8n integration with account switching
class N8nIntegrationTestScreen extends ConsumerWidget {
  const N8nIntegrationTestScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('n8n Integration Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Quick Setup Guide
            const N8nQuickSetupWidget(),

            // n8n Configuration Section
            const N8nConfigurationWidget(),

            // n8n Health Status
            _buildHealthStatusSection(ref),

            // Enhanced Account Switcher
            const EnhancedAccountSwitcher(),

            // Test Actions
            _buildTestActionsSection(ref, context),
          ],
        ),
      ),
    );
  }

  Widget _buildHealthStatusSection(WidgetRef ref) {
    final healthAsync = ref.watch(n8nHealthStatusProvider);
    final isEnabledAsync = ref.watch(n8nIntegrationEnabledProvider);
    final isValidAsync = ref.watch(n8nConfigurationValidProvider);

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'n8n Integration Status',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Enabled Status
            isEnabledAsync.when(
              data: (isEnabled) => _buildStatusRow(
                'Integration Enabled',
                isEnabled ? 'Yes' : 'No',
                isEnabled ? Colors.green : Colors.grey,
              ),
              loading: () => _buildStatusRow(
                'Integration Enabled',
                'Loading...',
                Colors.grey,
              ),
              error: (_, __) =>
                  _buildStatusRow('Integration Enabled', 'Error', Colors.red),
            ),

            const SizedBox(height: 8),

            // Configuration Valid
            isValidAsync.when(
              data: (isValid) => _buildStatusRow(
                'Configuration Valid',
                isValid ? 'Yes' : 'No',
                isValid ? Colors.green : Colors.orange,
              ),
              loading: () => _buildStatusRow(
                'Configuration Valid',
                'Loading...',
                Colors.grey,
              ),
              error: (_, __) =>
                  _buildStatusRow('Configuration Valid', 'Error', Colors.red),
            ),

            const SizedBox(height: 8),

            // Health Status
            healthAsync.when(
              data: (health) => Column(
                children: [
                  _buildStatusRow(
                    'Health Status',
                    health.status,
                    health.isHealthy ? Colors.green : Colors.red,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    health.message,
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                  if (health.lastChecked != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      'Last checked: ${health.lastChecked!.toLocal().toString().split('.')[0]}',
                      style: const TextStyle(fontSize: 10, color: Colors.grey),
                    ),
                  ],
                ],
              ),
              loading: () =>
                  _buildStatusRow('Health Status', 'Checking...', Colors.grey),
              error: (error, _) =>
                  _buildStatusRow('Health Status', 'Error: $error', Colors.red),
            ),

            const SizedBox(height: 16),

            ElevatedButton(
              onPressed: () {
                ref.invalidate(n8nHealthStatusProvider);
                ref.invalidate(n8nIntegrationEnabledProvider);
                ref.invalidate(n8nConfigurationValidProvider);
              },
              child: const Text('Refresh Status'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, String value, Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withValues(alpha: 0.3)),
          ),
          child: Text(
            value,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTestActionsSection(WidgetRef ref, BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Test n8n Workflows',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            const Text(
              'Use these buttons to test individual n8n workflows:',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),

            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: () => _testUserEngagement(ref, context),
                  icon: const Icon(Icons.person),
                  label: const Text('Test User Engagement'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _testPostScheduling(ref, context),
                  icon: const Icon(Icons.schedule),
                  label: const Text('Test Post Scheduling'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _testNotification(ref, context),
                  icon: const Icon(Icons.notifications),
                  label: const Text('Test Notification'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _testHealthCheck(ref, context),
                  icon: const Icon(Icons.health_and_safety),
                  label: const Text('Test Health Check'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testUserEngagement(WidgetRef ref, BuildContext context) async {
    try {
      final n8nService = ref.read(n8nServiceProvider);
      final response = await n8nService.triggerUserEngagement(
        eventType: 'test_engagement',
        eventData: {
          'testType': 'manual_test',
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      _showTestResult(
        context,
        'User Engagement Test',
        response.isSuccess,
        response.error,
      );
    } catch (e) {
      _showTestResult(context, 'User Engagement Test', false, e.toString());
    }
  }

  Future<void> _testPostScheduling(WidgetRef ref, BuildContext context) async {
    try {
      final n8nService = ref.read(n8nServiceProvider);
      final response = await n8nService.schedulePost(
        content: 'Test post from n8n integration',
        mediaUrls: [],
        scheduledTime: DateTime.now().add(const Duration(minutes: 5)),
        platforms: ['test'],
        metadata: {'testMode': true},
      );

      _showTestResult(
        context,
        'Post Scheduling Test',
        response.isSuccess,
        response.error,
      );
    } catch (e) {
      _showTestResult(context, 'Post Scheduling Test', false, e.toString());
    }
  }

  Future<void> _testNotification(WidgetRef ref, BuildContext context) async {
    try {
      final n8nService = ref.read(n8nServiceProvider);
      final response = await n8nService.sendNotification(
        recipientId: 'test-user',
        title: 'Test Notification',
        body: 'This is a test notification from n8n integration',
        data: {'testMode': true},
      );

      _showTestResult(
        context,
        'Notification Test',
        response.isSuccess,
        response.error,
      );
    } catch (e) {
      _showTestResult(context, 'Notification Test', false, e.toString());
    }
  }

  Future<void> _testHealthCheck(WidgetRef ref, BuildContext context) async {
    try {
      final n8nService = ref.read(n8nServiceProvider);
      final response = await n8nService.triggerUserEngagement(
        eventType: 'health_check',
        eventData: {
          'timestamp': DateTime.now().toIso8601String(),
          'source': 'manual_test',
        },
      );

      _showTestResult(
        context,
        'Health Check Test',
        response.isSuccess,
        response.error,
      );
    } catch (e) {
      _showTestResult(context, 'Health Check Test', false, e.toString());
    }
  }

  void _showTestResult(
    BuildContext context,
    String testName,
    bool success,
    String? error,
  ) {
    // Check if the widget is still mounted before using context
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          success
              ? '$testName: SUCCESS'
              : '$testName: FAILED${error != null ? ' - $error' : ''}',
        ),
        backgroundColor: success ? Colors.green : Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}
