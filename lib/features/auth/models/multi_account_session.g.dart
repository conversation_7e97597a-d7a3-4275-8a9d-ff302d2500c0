// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'multi_account_session.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MultiAccountSessionImpl _$$MultiAccountSessionImplFromJson(
  Map<String, dynamic> json,
) => _$MultiAccountSessionImpl(
  activeAccount: json['activeAccount'] == null
      ? null
      : SavedAccountModel.fromJson(
          json['activeAccount'] as Map<String, dynamic>,
        ),
  savedAccounts:
      (json['savedAccounts'] as List<dynamic>?)
          ?.map((e) => SavedAccountModel.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  currentUserUid: json['currentUserUid'] as String?,
  sessionStartTime: json['sessionStartTime'] == null
      ? null
      : DateTime.parse(json['sessionStartTime'] as String),
  lastActivityTime: json['lastActivityTime'] == null
      ? null
      : DateTime.parse(json['lastActivityTime'] as String),
  isValid: json['isValid'] as bool? ?? false,
  metadata: json['metadata'] == null
      ? null
      : SessionMetadata.fromJson(json['metadata'] as Map<String, dynamic>),
  quickSwitcherConfig: json['quickSwitcherConfig'] == null
      ? null
      : QuickSwitcherConfig.fromJson(
          json['quickSwitcherConfig'] as Map<String, dynamic>,
        ),
  pendingSwitches:
      (json['pendingSwitches'] as List<dynamic>?)
          ?.map((e) => PendingAccountSwitch.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
);

Map<String, dynamic> _$$MultiAccountSessionImplToJson(
  _$MultiAccountSessionImpl instance,
) => <String, dynamic>{
  'activeAccount': instance.activeAccount,
  'savedAccounts': instance.savedAccounts,
  'currentUserUid': instance.currentUserUid,
  'sessionStartTime': instance.sessionStartTime?.toIso8601String(),
  'lastActivityTime': instance.lastActivityTime?.toIso8601String(),
  'isValid': instance.isValid,
  'metadata': instance.metadata,
  'quickSwitcherConfig': instance.quickSwitcherConfig,
  'pendingSwitches': instance.pendingSwitches,
};

_$SessionMetadataImpl _$$SessionMetadataImplFromJson(
  Map<String, dynamic> json,
) => _$SessionMetadataImpl(
  deviceId: json['deviceId'] as String?,
  deviceType: json['deviceType'] as String?,
  appVersion: json['appVersion'] as String?,
  platformVersion: json['platformVersion'] as String?,
  sessionId: json['sessionId'] as String?,
  switchCount: (json['switchCount'] as num?)?.toInt() ?? 0,
  sessionDuration: json['sessionDuration'] == null
      ? null
      : Duration(microseconds: (json['sessionDuration'] as num).toInt()),
  networkStatus: json['networkStatus'] as String?,
  location: json['location'] as String?,
  securityFlags:
      (json['securityFlags'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as bool),
      ) ??
      const {},
);

Map<String, dynamic> _$$SessionMetadataImplToJson(
  _$SessionMetadataImpl instance,
) => <String, dynamic>{
  'deviceId': instance.deviceId,
  'deviceType': instance.deviceType,
  'appVersion': instance.appVersion,
  'platformVersion': instance.platformVersion,
  'sessionId': instance.sessionId,
  'switchCount': instance.switchCount,
  'sessionDuration': instance.sessionDuration?.inMicroseconds,
  'networkStatus': instance.networkStatus,
  'location': instance.location,
  'securityFlags': instance.securityFlags,
};

_$PendingAccountSwitchImpl _$$PendingAccountSwitchImplFromJson(
  Map<String, dynamic> json,
) => _$PendingAccountSwitchImpl(
  id: json['id'] as String,
  targetAccountId: json['targetAccountId'] as String,
  fromAccountId: json['fromAccountId'] as String?,
  initiatedAt: DateTime.parse(json['initiatedAt'] as String),
  expectedCompletionAt: json['expectedCompletionAt'] == null
      ? null
      : DateTime.parse(json['expectedCompletionAt'] as String),
  status:
      $enumDecodeNullable(_$PendingSwitchStatusEnumMap, json['status']) ??
      PendingSwitchStatus.initiated,
  progress: (json['progress'] as num?)?.toInt() ?? 0,
  currentStep: json['currentStep'] as String?,
  errorMessage: json['errorMessage'] as String?,
  requiresUserInteraction: json['requiresUserInteraction'] as bool? ?? false,
  biometricAuthResult: json['biometricAuthResult'] as bool?,
  context: json['context'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$$PendingAccountSwitchImplToJson(
  _$PendingAccountSwitchImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'targetAccountId': instance.targetAccountId,
  'fromAccountId': instance.fromAccountId,
  'initiatedAt': instance.initiatedAt.toIso8601String(),
  'expectedCompletionAt': instance.expectedCompletionAt?.toIso8601String(),
  'status': _$PendingSwitchStatusEnumMap[instance.status]!,
  'progress': instance.progress,
  'currentStep': instance.currentStep,
  'errorMessage': instance.errorMessage,
  'requiresUserInteraction': instance.requiresUserInteraction,
  'biometricAuthResult': instance.biometricAuthResult,
  'context': instance.context,
};

const _$PendingSwitchStatusEnumMap = {
  PendingSwitchStatus.initiated: 'initiated',
  PendingSwitchStatus.authenticating: 'authenticating',
  PendingSwitchStatus.loggingOut: 'loggingOut',
  PendingSwitchStatus.loggingIn: 'loggingIn',
  PendingSwitchStatus.updatingState: 'updatingState',
  PendingSwitchStatus.completing: 'completing',
  PendingSwitchStatus.completed: 'completed',
  PendingSwitchStatus.failed: 'failed',
  PendingSwitchStatus.cancelled: 'cancelled',
};

_$AccountSwitchResultImpl _$$AccountSwitchResultImplFromJson(
  Map<String, dynamic> json,
) => _$AccountSwitchResultImpl(
  success: json['success'] as bool,
  switchedToAccount: json['switchedToAccount'] == null
      ? null
      : SavedAccountModel.fromJson(
          json['switchedToAccount'] as Map<String, dynamic>,
        ),
  errorMessage: json['errorMessage'] as String?,
  errorCode: json['errorCode'] as String?,
  switchDuration: json['switchDuration'] == null
      ? null
      : Duration(microseconds: (json['switchDuration'] as num).toInt()),
  historyEntry: json['historyEntry'] == null
      ? null
      : AccountSwitchHistory.fromJson(
          json['historyEntry'] as Map<String, dynamic>,
        ),
  requiredUserInteraction: json['requiredUserInteraction'] as bool? ?? false,
  usedBiometricAuth: json['usedBiometricAuth'] as bool? ?? false,
  resultData: json['resultData'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$$AccountSwitchResultImplToJson(
  _$AccountSwitchResultImpl instance,
) => <String, dynamic>{
  'success': instance.success,
  'switchedToAccount': instance.switchedToAccount,
  'errorMessage': instance.errorMessage,
  'errorCode': instance.errorCode,
  'switchDuration': instance.switchDuration?.inMicroseconds,
  'historyEntry': instance.historyEntry,
  'requiredUserInteraction': instance.requiredUserInteraction,
  'usedBiometricAuth': instance.usedBiometricAuth,
  'resultData': instance.resultData,
};

_$AccountValidationResultImpl _$$AccountValidationResultImplFromJson(
  Map<String, dynamic> json,
) => _$AccountValidationResultImpl(
  isValid: json['isValid'] as bool,
  issues:
      (json['issues'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  needsCredentialRefresh: json['needsCredentialRefresh'] as bool? ?? false,
  isLocked: json['isLocked'] as bool? ?? false,
  statusMessage: json['statusMessage'] as String?,
  lastValidatedAt: json['lastValidatedAt'] == null
      ? null
      : DateTime.parse(json['lastValidatedAt'] as String),
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$$AccountValidationResultImplToJson(
  _$AccountValidationResultImpl instance,
) => <String, dynamic>{
  'isValid': instance.isValid,
  'issues': instance.issues,
  'needsCredentialRefresh': instance.needsCredentialRefresh,
  'isLocked': instance.isLocked,
  'statusMessage': instance.statusMessage,
  'lastValidatedAt': instance.lastValidatedAt?.toIso8601String(),
  'metadata': instance.metadata,
};

_$AccountSwitchHistoryImpl _$$AccountSwitchHistoryImplFromJson(
  Map<String, dynamic> json,
) => _$AccountSwitchHistoryImpl(
  id: json['id'] as String,
  fromAccountId: json['fromAccountId'] as String?,
  toAccountId: json['toAccountId'] as String,
  switchedAt: DateTime.parse(json['switchedAt'] as String),
  deviceId: json['deviceId'] as String?,
  ipAddress: json['ipAddress'] as String?,
  switchMethod: json['switchMethod'] as String? ?? 'manual',
  successful: json['successful'] as bool? ?? true,
  errorMessage: json['errorMessage'] as String?,
  switchDuration: json['switchDuration'] == null
      ? null
      : Duration(microseconds: (json['switchDuration'] as num).toInt()),
);

Map<String, dynamic> _$$AccountSwitchHistoryImplToJson(
  _$AccountSwitchHistoryImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'fromAccountId': instance.fromAccountId,
  'toAccountId': instance.toAccountId,
  'switchedAt': instance.switchedAt.toIso8601String(),
  'deviceId': instance.deviceId,
  'ipAddress': instance.ipAddress,
  'switchMethod': instance.switchMethod,
  'successful': instance.successful,
  'errorMessage': instance.errorMessage,
  'switchDuration': instance.switchDuration?.inMicroseconds,
};

_$QuickSwitcherConfigImpl _$$QuickSwitcherConfigImplFromJson(
  Map<String, dynamic> json,
) => _$QuickSwitcherConfigImpl(
  maxQuickAccounts: (json['maxQuickAccounts'] as num?)?.toInt() ?? 5,
  showAccountPreviews: json['showAccountPreviews'] as bool? ?? true,
  requireBiometricAuth: json['requireBiometricAuth'] as bool? ?? false,
  autoSwitchTimeout: (json['autoSwitchTimeout'] as num?)?.toInt(),
  pinnedAccountIds:
      (json['pinnedAccountIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  recentAccountIds:
      (json['recentAccountIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  showAccountNotifications: json['showAccountNotifications'] as bool? ?? true,
  lastUpdated: json['lastUpdated'] == null
      ? null
      : DateTime.parse(json['lastUpdated'] as String),
);

Map<String, dynamic> _$$QuickSwitcherConfigImplToJson(
  _$QuickSwitcherConfigImpl instance,
) => <String, dynamic>{
  'maxQuickAccounts': instance.maxQuickAccounts,
  'showAccountPreviews': instance.showAccountPreviews,
  'requireBiometricAuth': instance.requireBiometricAuth,
  'autoSwitchTimeout': instance.autoSwitchTimeout,
  'pinnedAccountIds': instance.pinnedAccountIds,
  'recentAccountIds': instance.recentAccountIds,
  'showAccountNotifications': instance.showAccountNotifications,
  'lastUpdated': instance.lastUpdated?.toIso8601String(),
};
