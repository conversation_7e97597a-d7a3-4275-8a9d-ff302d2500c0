// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'saved_account_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SavedAccountModelImpl _$$SavedAccountModelImplFromJson(
  Map<String, dynamic> json,
) => _$SavedAccountModelImpl(
  id: json['id'] as String,
  firebaseUid: json['firebaseUid'] as String,
  email: json['email'] as String,
  displayName: json['displayName'] as String,
  username: json['username'] as String,
  profilePictureUrl: json['profilePictureUrl'] as String?,
  accountType: json['accountType'] as String,
  isVerified: json['isVerified'] as bool? ?? false,
  isBillionaire: json['isBillionaire'] as bool? ?? false,
  lastUsedAt: json['lastUsedAt'] == null
      ? null
      : DateTime.parse(json['lastUsedAt'] as String),
  savedAt: DateTime.parse(json['savedAt'] as String),
  isActive: json['isActive'] as bool? ?? false,
  metadata: json['metadata'] == null
      ? null
      : AccountMetadata.fromJson(json['metadata'] as Map<String, dynamic>),
  settings: json['settings'] as Map<String, dynamic>?,
  deviceId: json['deviceId'] as String?,
  lastKnownIp: json['lastKnownIp'] as String?,
);

Map<String, dynamic> _$$SavedAccountModelImplToJson(
  _$SavedAccountModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'firebaseUid': instance.firebaseUid,
  'email': instance.email,
  'displayName': instance.displayName,
  'username': instance.username,
  'profilePictureUrl': instance.profilePictureUrl,
  'accountType': instance.accountType,
  'isVerified': instance.isVerified,
  'isBillionaire': instance.isBillionaire,
  'lastUsedAt': instance.lastUsedAt?.toIso8601String(),
  'savedAt': instance.savedAt.toIso8601String(),
  'isActive': instance.isActive,
  'metadata': instance.metadata,
  'settings': instance.settings,
  'deviceId': instance.deviceId,
  'lastKnownIp': instance.lastKnownIp,
};

_$AccountMetadataImpl _$$AccountMetadataImplFromJson(
  Map<String, dynamic> json,
) => _$AccountMetadataImpl(
  followersCount: (json['followersCount'] as num?)?.toInt() ?? 0,
  followingCount: (json['followingCount'] as num?)?.toInt() ?? 0,
  postsCount: (json['postsCount'] as num?)?.toInt() ?? 0,
  storiesCount: (json['storiesCount'] as num?)?.toInt() ?? 0,
  bio: json['bio'] as String?,
  location: json['location'] as String?,
  website: json['website'] as String?,
);

Map<String, dynamic> _$$AccountMetadataImplToJson(
  _$AccountMetadataImpl instance,
) => <String, dynamic>{
  'followersCount': instance.followersCount,
  'followingCount': instance.followingCount,
  'postsCount': instance.postsCount,
  'storiesCount': instance.storiesCount,
  'bio': instance.bio,
  'location': instance.location,
  'website': instance.website,
};

_$AccountCredentialsImpl _$$AccountCredentialsImplFromJson(
  Map<String, dynamic> json,
) => _$AccountCredentialsImpl(
  accountId: json['accountId'] as String,
  encryptedEmail: json['encryptedEmail'] as String,
  encryptedPassword: json['encryptedPassword'] as String?,
  encryptedRefreshToken: json['encryptedRefreshToken'] as String?,
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  rememberPassword: json['rememberPassword'] as bool? ?? false,
  keyId: json['keyId'] as String,
  expiresAt: json['expiresAt'] == null
      ? null
      : DateTime.parse(json['expiresAt'] as String),
);

Map<String, dynamic> _$$AccountCredentialsImplToJson(
  _$AccountCredentialsImpl instance,
) => <String, dynamic>{
  'accountId': instance.accountId,
  'encryptedEmail': instance.encryptedEmail,
  'encryptedPassword': instance.encryptedPassword,
  'encryptedRefreshToken': instance.encryptedRefreshToken,
  'updatedAt': instance.updatedAt.toIso8601String(),
  'rememberPassword': instance.rememberPassword,
  'keyId': instance.keyId,
  'expiresAt': instance.expiresAt?.toIso8601String(),
};
