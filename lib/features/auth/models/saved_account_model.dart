import 'package:freezed_annotation/freezed_annotation.dart';

part 'saved_account_model.freezed.dart';
part 'saved_account_model.g.dart';

/// Model representing a saved user account for multi-account switching
@freezed
class SavedAccountModel with _$SavedAccountModel {
  const factory SavedAccountModel({
    /// Unique identifier for this saved account
    required String id,

    /// Firebase user ID
    required String firebaseUid,

    /// User's email address
    required String email,

    /// Display name
    required String displayName,

    /// Username
    required String username,

    /// Profile picture URL
    String? profilePictureUrl,

    /// Account type (personal, business, celebrity)
    required String accountType,

    /// Whether this account is verified
    @Default(false) bool isVerified,

    /// Whether this account is a billionaire account
    @Default(false) bool isBillionaire,

    /// Last time this account was used
    DateTime? lastUsedAt,

    /// When this account was first saved
    required DateTime savedAt,

    /// Whether this is the currently active account
    @Default(false) bool isActive,

    /// Account metadata (followers, following, posts count)
    AccountMetadata? metadata,

    /// Account settings and preferences
    Map<String, dynamic>? settings,

    /// Device-specific information
    String? deviceId,

    /// Last known IP address (for security)
    String? lastKnownIp,
  }) = _SavedAccountModel;

  factory SavedAccountModel.fromJson(Map<String, dynamic> json) =>
      _$SavedAccountModelFromJson(json);
}

/// Account metadata for display purposes
@freezed
class AccountMetadata with _$AccountMetadata {
  const factory AccountMetadata({
    @Default(0) int followersCount,
    @Default(0) int followingCount,
    @Default(0) int postsCount,
    @Default(0) int storiesCount,
    String? bio,
    String? location,
    String? website,
  }) = _AccountMetadata;

  factory AccountMetadata.fromJson(Map<String, dynamic> json) =>
      _$AccountMetadataFromJson(json);
}

/// Model for storing encrypted account credentials
@freezed
class AccountCredentials with _$AccountCredentials {
  const factory AccountCredentials({
    /// Account ID this credential belongs to
    required String accountId,

    /// Encrypted email
    required String encryptedEmail,

    /// Encrypted password (optional - for remember me functionality)
    String? encryptedPassword,

    /// Firebase refresh token (encrypted)
    String? encryptedRefreshToken,

    /// When credentials were last updated
    required DateTime updatedAt,

    /// Whether to remember password for this account
    @Default(false) bool rememberPassword,

    /// Encryption key identifier
    required String keyId,

    /// Credential expiry (for security)
    DateTime? expiresAt,
  }) = _AccountCredentials;

  factory AccountCredentials.fromJson(Map<String, dynamic> json) =>
      _$AccountCredentialsFromJson(json);
}
