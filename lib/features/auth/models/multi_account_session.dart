import 'package:freezed_annotation/freezed_annotation.dart';
import 'saved_account_model.dart';

part 'multi_account_session.freezed.dart';
part 'multi_account_session.g.dart';

/// Model representing the current multi-account session state
@freezed
class MultiAccountSession with _$MultiAccountSession {
  const factory MultiAccountSession({
    /// Currently active account
    SavedAccountModel? activeAccount,

    /// All saved accounts on this device
    @Default([]) List<SavedAccountModel> savedAccounts,

    /// Current Firebase user UID (instead of User object for JSON compatibility)
    String? currentUserUid,

    /// Session start time
    DateTime? sessionStartTime,

    /// Last activity time
    DateTime? lastActivityTime,

    /// Whether session is valid
    @Default(false) bool isValid,

    /// Session metadata
    SessionMetadata? metadata,

    /// Quick switcher configuration
    QuickSwitcherConfig? quickSwitcherConfig,

    /// Pending account switches
    @Default([]) List<PendingAccountSwitch> pendingSwitches,
  }) = _MultiAccountSession;

  factory MultiAccountSession.fromJson(Map<String, dynamic> json) =>
      _$MultiAccountSessionFromJson(json);
}

/// Session metadata for tracking and analytics
@freezed
class SessionMetadata with _$SessionMetadata {
  const factory SessionMetadata({
    /// Device identifier
    String? deviceId,

    /// Device type (iOS, Android)
    String? deviceType,

    /// App version
    String? appVersion,

    /// Platform version
    String? platformVersion,

    /// Session ID for tracking
    String? sessionId,

    /// Number of account switches in this session
    @Default(0) int switchCount,

    /// Total session duration
    Duration? sessionDuration,

    /// Network connectivity status
    String? networkStatus,

    /// Location (if permitted)
    String? location,

    /// Security flags
    @Default({}) Map<String, bool> securityFlags,
  }) = _SessionMetadata;

  factory SessionMetadata.fromJson(Map<String, dynamic> json) =>
      _$SessionMetadataFromJson(json);
}

/// Model for pending account switches (for async operations)
@freezed
class PendingAccountSwitch with _$PendingAccountSwitch {
  const factory PendingAccountSwitch({
    /// Unique identifier for this switch operation
    required String id,

    /// Target account to switch to
    required String targetAccountId,

    /// Current account being switched from
    String? fromAccountId,

    /// Switch initiation time
    required DateTime initiatedAt,

    /// Expected completion time
    DateTime? expectedCompletionAt,

    /// Switch status
    @Default(PendingSwitchStatus.initiated) PendingSwitchStatus status,

    /// Progress percentage (0-100)
    @Default(0) int progress,

    /// Current step in the switch process
    String? currentStep,

    /// Error information if switch fails
    String? errorMessage,

    /// Whether this switch requires user interaction
    @Default(false) bool requiresUserInteraction,

    /// Biometric authentication result
    bool? biometricAuthResult,

    /// Additional context data
    Map<String, dynamic>? context,
  }) = _PendingAccountSwitch;

  factory PendingAccountSwitch.fromJson(Map<String, dynamic> json) =>
      _$PendingAccountSwitchFromJson(json);
}

/// Status of a pending account switch
enum PendingSwitchStatus {
  initiated,
  authenticating,
  loggingOut,
  loggingIn,
  updatingState,
  completing,
  completed,
  failed,
  cancelled,
}

/// Account switch result
@freezed
class AccountSwitchResult with _$AccountSwitchResult {
  const factory AccountSwitchResult({
    /// Whether the switch was successful
    required bool success,

    /// The account that was switched to (if successful)
    SavedAccountModel? switchedToAccount,

    /// Error message if switch failed
    String? errorMessage,

    /// Error code for programmatic handling
    String? errorCode,

    /// Duration of the switch operation
    Duration? switchDuration,

    /// Switch history entry
    AccountSwitchHistory? historyEntry,

    /// Whether user interaction was required
    @Default(false) bool requiredUserInteraction,

    /// Whether biometric authentication was used
    @Default(false) bool usedBiometricAuth,

    /// Additional result data
    Map<String, dynamic>? resultData,
  }) = _AccountSwitchResult;

  factory AccountSwitchResult.fromJson(Map<String, dynamic> json) =>
      _$AccountSwitchResultFromJson(json);
}

/// Account validation result
@freezed
class AccountValidationResult with _$AccountValidationResult {
  const factory AccountValidationResult({
    /// Whether the account is valid
    required bool isValid,

    /// Validation issues found
    @Default([]) List<String> issues,

    /// Whether credentials need refresh
    @Default(false) bool needsCredentialRefresh,

    /// Whether account is locked or suspended
    @Default(false) bool isLocked,

    /// Account status message
    String? statusMessage,

    /// Last validation time
    DateTime? lastValidatedAt,

    /// Validation metadata
    Map<String, dynamic>? metadata,
  }) = _AccountValidationResult;

  factory AccountValidationResult.fromJson(Map<String, dynamic> json) =>
      _$AccountValidationResultFromJson(json);
}

/// Model for account switching history
@freezed
class AccountSwitchHistory with _$AccountSwitchHistory {
  const factory AccountSwitchHistory({
    /// Unique identifier for this switch event
    required String id,

    /// Account that was switched from
    String? fromAccountId,

    /// Account that was switched to
    required String toAccountId,

    /// When the switch occurred
    required DateTime switchedAt,

    /// Device information
    String? deviceId,

    /// IP address at time of switch
    String? ipAddress,

    /// Switch method (manual, auto, biometric)
    @Default('manual') String switchMethod,

    /// Whether switch was successful
    @Default(true) bool successful,

    /// Error message if switch failed
    String? errorMessage,

    /// Duration of the switch process
    Duration? switchDuration,
  }) = _AccountSwitchHistory;

  factory AccountSwitchHistory.fromJson(Map<String, dynamic> json) =>
      _$AccountSwitchHistoryFromJson(json);
}

/// Model for quick account switcher configuration
@freezed
class QuickSwitcherConfig with _$QuickSwitcherConfig {
  const factory QuickSwitcherConfig({
    /// Maximum number of accounts to show in quick switcher
    @Default(5) int maxQuickAccounts,

    /// Whether to show account previews
    @Default(true) bool showAccountPreviews,

    /// Whether to require biometric auth for switching
    @Default(false) bool requireBiometricAuth,

    /// Auto-switch timeout (in minutes)
    int? autoSwitchTimeout,

    /// Accounts pinned to quick switcher
    @Default([]) List<String> pinnedAccountIds,

    /// Recently used accounts (ordered by recency)
    @Default([]) List<String> recentAccountIds,

    /// Whether to show account notifications
    @Default(true) bool showAccountNotifications,

    /// Last updated timestamp
    DateTime? lastUpdated,
  }) = _QuickSwitcherConfig;

  factory QuickSwitcherConfig.fromJson(Map<String, dynamic> json) =>
      _$QuickSwitcherConfigFromJson(json);
}
