// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'multi_account_session.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

MultiAccountSession _$MultiAccountSessionFromJson(Map<String, dynamic> json) {
  return _MultiAccountSession.fromJson(json);
}

/// @nodoc
mixin _$MultiAccountSession {
  /// Currently active account
  SavedAccountModel? get activeAccount => throw _privateConstructorUsedError;

  /// All saved accounts on this device
  List<SavedAccountModel> get savedAccounts =>
      throw _privateConstructorUsedError;

  /// Current Firebase user UID (instead of User object for JSON compatibility)
  String? get currentUserUid => throw _privateConstructorUsedError;

  /// Session start time
  DateTime? get sessionStartTime => throw _privateConstructorUsedError;

  /// Last activity time
  DateTime? get lastActivityTime => throw _privateConstructorUsedError;

  /// Whether session is valid
  bool get isValid => throw _privateConstructorUsedError;

  /// Session metadata
  SessionMetadata? get metadata => throw _privateConstructorUsedError;

  /// Quick switcher configuration
  QuickSwitcherConfig? get quickSwitcherConfig =>
      throw _privateConstructorUsedError;

  /// Pending account switches
  List<PendingAccountSwitch> get pendingSwitches =>
      throw _privateConstructorUsedError;

  /// Serializes this MultiAccountSession to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MultiAccountSession
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MultiAccountSessionCopyWith<MultiAccountSession> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MultiAccountSessionCopyWith<$Res> {
  factory $MultiAccountSessionCopyWith(
    MultiAccountSession value,
    $Res Function(MultiAccountSession) then,
  ) = _$MultiAccountSessionCopyWithImpl<$Res, MultiAccountSession>;
  @useResult
  $Res call({
    SavedAccountModel? activeAccount,
    List<SavedAccountModel> savedAccounts,
    String? currentUserUid,
    DateTime? sessionStartTime,
    DateTime? lastActivityTime,
    bool isValid,
    SessionMetadata? metadata,
    QuickSwitcherConfig? quickSwitcherConfig,
    List<PendingAccountSwitch> pendingSwitches,
  });

  $SavedAccountModelCopyWith<$Res>? get activeAccount;
  $SessionMetadataCopyWith<$Res>? get metadata;
  $QuickSwitcherConfigCopyWith<$Res>? get quickSwitcherConfig;
}

/// @nodoc
class _$MultiAccountSessionCopyWithImpl<$Res, $Val extends MultiAccountSession>
    implements $MultiAccountSessionCopyWith<$Res> {
  _$MultiAccountSessionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MultiAccountSession
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? activeAccount = freezed,
    Object? savedAccounts = null,
    Object? currentUserUid = freezed,
    Object? sessionStartTime = freezed,
    Object? lastActivityTime = freezed,
    Object? isValid = null,
    Object? metadata = freezed,
    Object? quickSwitcherConfig = freezed,
    Object? pendingSwitches = null,
  }) {
    return _then(
      _value.copyWith(
            activeAccount: freezed == activeAccount
                ? _value.activeAccount
                : activeAccount // ignore: cast_nullable_to_non_nullable
                      as SavedAccountModel?,
            savedAccounts: null == savedAccounts
                ? _value.savedAccounts
                : savedAccounts // ignore: cast_nullable_to_non_nullable
                      as List<SavedAccountModel>,
            currentUserUid: freezed == currentUserUid
                ? _value.currentUserUid
                : currentUserUid // ignore: cast_nullable_to_non_nullable
                      as String?,
            sessionStartTime: freezed == sessionStartTime
                ? _value.sessionStartTime
                : sessionStartTime // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            lastActivityTime: freezed == lastActivityTime
                ? _value.lastActivityTime
                : lastActivityTime // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            isValid: null == isValid
                ? _value.isValid
                : isValid // ignore: cast_nullable_to_non_nullable
                      as bool,
            metadata: freezed == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as SessionMetadata?,
            quickSwitcherConfig: freezed == quickSwitcherConfig
                ? _value.quickSwitcherConfig
                : quickSwitcherConfig // ignore: cast_nullable_to_non_nullable
                      as QuickSwitcherConfig?,
            pendingSwitches: null == pendingSwitches
                ? _value.pendingSwitches
                : pendingSwitches // ignore: cast_nullable_to_non_nullable
                      as List<PendingAccountSwitch>,
          )
          as $Val,
    );
  }

  /// Create a copy of MultiAccountSession
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SavedAccountModelCopyWith<$Res>? get activeAccount {
    if (_value.activeAccount == null) {
      return null;
    }

    return $SavedAccountModelCopyWith<$Res>(_value.activeAccount!, (value) {
      return _then(_value.copyWith(activeAccount: value) as $Val);
    });
  }

  /// Create a copy of MultiAccountSession
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SessionMetadataCopyWith<$Res>? get metadata {
    if (_value.metadata == null) {
      return null;
    }

    return $SessionMetadataCopyWith<$Res>(_value.metadata!, (value) {
      return _then(_value.copyWith(metadata: value) as $Val);
    });
  }

  /// Create a copy of MultiAccountSession
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $QuickSwitcherConfigCopyWith<$Res>? get quickSwitcherConfig {
    if (_value.quickSwitcherConfig == null) {
      return null;
    }

    return $QuickSwitcherConfigCopyWith<$Res>(_value.quickSwitcherConfig!, (
      value,
    ) {
      return _then(_value.copyWith(quickSwitcherConfig: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$MultiAccountSessionImplCopyWith<$Res>
    implements $MultiAccountSessionCopyWith<$Res> {
  factory _$$MultiAccountSessionImplCopyWith(
    _$MultiAccountSessionImpl value,
    $Res Function(_$MultiAccountSessionImpl) then,
  ) = __$$MultiAccountSessionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    SavedAccountModel? activeAccount,
    List<SavedAccountModel> savedAccounts,
    String? currentUserUid,
    DateTime? sessionStartTime,
    DateTime? lastActivityTime,
    bool isValid,
    SessionMetadata? metadata,
    QuickSwitcherConfig? quickSwitcherConfig,
    List<PendingAccountSwitch> pendingSwitches,
  });

  @override
  $SavedAccountModelCopyWith<$Res>? get activeAccount;
  @override
  $SessionMetadataCopyWith<$Res>? get metadata;
  @override
  $QuickSwitcherConfigCopyWith<$Res>? get quickSwitcherConfig;
}

/// @nodoc
class __$$MultiAccountSessionImplCopyWithImpl<$Res>
    extends _$MultiAccountSessionCopyWithImpl<$Res, _$MultiAccountSessionImpl>
    implements _$$MultiAccountSessionImplCopyWith<$Res> {
  __$$MultiAccountSessionImplCopyWithImpl(
    _$MultiAccountSessionImpl _value,
    $Res Function(_$MultiAccountSessionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MultiAccountSession
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? activeAccount = freezed,
    Object? savedAccounts = null,
    Object? currentUserUid = freezed,
    Object? sessionStartTime = freezed,
    Object? lastActivityTime = freezed,
    Object? isValid = null,
    Object? metadata = freezed,
    Object? quickSwitcherConfig = freezed,
    Object? pendingSwitches = null,
  }) {
    return _then(
      _$MultiAccountSessionImpl(
        activeAccount: freezed == activeAccount
            ? _value.activeAccount
            : activeAccount // ignore: cast_nullable_to_non_nullable
                  as SavedAccountModel?,
        savedAccounts: null == savedAccounts
            ? _value._savedAccounts
            : savedAccounts // ignore: cast_nullable_to_non_nullable
                  as List<SavedAccountModel>,
        currentUserUid: freezed == currentUserUid
            ? _value.currentUserUid
            : currentUserUid // ignore: cast_nullable_to_non_nullable
                  as String?,
        sessionStartTime: freezed == sessionStartTime
            ? _value.sessionStartTime
            : sessionStartTime // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        lastActivityTime: freezed == lastActivityTime
            ? _value.lastActivityTime
            : lastActivityTime // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        isValid: null == isValid
            ? _value.isValid
            : isValid // ignore: cast_nullable_to_non_nullable
                  as bool,
        metadata: freezed == metadata
            ? _value.metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as SessionMetadata?,
        quickSwitcherConfig: freezed == quickSwitcherConfig
            ? _value.quickSwitcherConfig
            : quickSwitcherConfig // ignore: cast_nullable_to_non_nullable
                  as QuickSwitcherConfig?,
        pendingSwitches: null == pendingSwitches
            ? _value._pendingSwitches
            : pendingSwitches // ignore: cast_nullable_to_non_nullable
                  as List<PendingAccountSwitch>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$MultiAccountSessionImpl implements _MultiAccountSession {
  const _$MultiAccountSessionImpl({
    this.activeAccount,
    final List<SavedAccountModel> savedAccounts = const [],
    this.currentUserUid,
    this.sessionStartTime,
    this.lastActivityTime,
    this.isValid = false,
    this.metadata,
    this.quickSwitcherConfig,
    final List<PendingAccountSwitch> pendingSwitches = const [],
  }) : _savedAccounts = savedAccounts,
       _pendingSwitches = pendingSwitches;

  factory _$MultiAccountSessionImpl.fromJson(Map<String, dynamic> json) =>
      _$$MultiAccountSessionImplFromJson(json);

  /// Currently active account
  @override
  final SavedAccountModel? activeAccount;

  /// All saved accounts on this device
  final List<SavedAccountModel> _savedAccounts;

  /// All saved accounts on this device
  @override
  @JsonKey()
  List<SavedAccountModel> get savedAccounts {
    if (_savedAccounts is EqualUnmodifiableListView) return _savedAccounts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_savedAccounts);
  }

  /// Current Firebase user UID (instead of User object for JSON compatibility)
  @override
  final String? currentUserUid;

  /// Session start time
  @override
  final DateTime? sessionStartTime;

  /// Last activity time
  @override
  final DateTime? lastActivityTime;

  /// Whether session is valid
  @override
  @JsonKey()
  final bool isValid;

  /// Session metadata
  @override
  final SessionMetadata? metadata;

  /// Quick switcher configuration
  @override
  final QuickSwitcherConfig? quickSwitcherConfig;

  /// Pending account switches
  final List<PendingAccountSwitch> _pendingSwitches;

  /// Pending account switches
  @override
  @JsonKey()
  List<PendingAccountSwitch> get pendingSwitches {
    if (_pendingSwitches is EqualUnmodifiableListView) return _pendingSwitches;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_pendingSwitches);
  }

  @override
  String toString() {
    return 'MultiAccountSession(activeAccount: $activeAccount, savedAccounts: $savedAccounts, currentUserUid: $currentUserUid, sessionStartTime: $sessionStartTime, lastActivityTime: $lastActivityTime, isValid: $isValid, metadata: $metadata, quickSwitcherConfig: $quickSwitcherConfig, pendingSwitches: $pendingSwitches)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MultiAccountSessionImpl &&
            (identical(other.activeAccount, activeAccount) ||
                other.activeAccount == activeAccount) &&
            const DeepCollectionEquality().equals(
              other._savedAccounts,
              _savedAccounts,
            ) &&
            (identical(other.currentUserUid, currentUserUid) ||
                other.currentUserUid == currentUserUid) &&
            (identical(other.sessionStartTime, sessionStartTime) ||
                other.sessionStartTime == sessionStartTime) &&
            (identical(other.lastActivityTime, lastActivityTime) ||
                other.lastActivityTime == lastActivityTime) &&
            (identical(other.isValid, isValid) || other.isValid == isValid) &&
            (identical(other.metadata, metadata) ||
                other.metadata == metadata) &&
            (identical(other.quickSwitcherConfig, quickSwitcherConfig) ||
                other.quickSwitcherConfig == quickSwitcherConfig) &&
            const DeepCollectionEquality().equals(
              other._pendingSwitches,
              _pendingSwitches,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    activeAccount,
    const DeepCollectionEquality().hash(_savedAccounts),
    currentUserUid,
    sessionStartTime,
    lastActivityTime,
    isValid,
    metadata,
    quickSwitcherConfig,
    const DeepCollectionEquality().hash(_pendingSwitches),
  );

  /// Create a copy of MultiAccountSession
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MultiAccountSessionImplCopyWith<_$MultiAccountSessionImpl> get copyWith =>
      __$$MultiAccountSessionImplCopyWithImpl<_$MultiAccountSessionImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$MultiAccountSessionImplToJson(this);
  }
}

abstract class _MultiAccountSession implements MultiAccountSession {
  const factory _MultiAccountSession({
    final SavedAccountModel? activeAccount,
    final List<SavedAccountModel> savedAccounts,
    final String? currentUserUid,
    final DateTime? sessionStartTime,
    final DateTime? lastActivityTime,
    final bool isValid,
    final SessionMetadata? metadata,
    final QuickSwitcherConfig? quickSwitcherConfig,
    final List<PendingAccountSwitch> pendingSwitches,
  }) = _$MultiAccountSessionImpl;

  factory _MultiAccountSession.fromJson(Map<String, dynamic> json) =
      _$MultiAccountSessionImpl.fromJson;

  /// Currently active account
  @override
  SavedAccountModel? get activeAccount;

  /// All saved accounts on this device
  @override
  List<SavedAccountModel> get savedAccounts;

  /// Current Firebase user UID (instead of User object for JSON compatibility)
  @override
  String? get currentUserUid;

  /// Session start time
  @override
  DateTime? get sessionStartTime;

  /// Last activity time
  @override
  DateTime? get lastActivityTime;

  /// Whether session is valid
  @override
  bool get isValid;

  /// Session metadata
  @override
  SessionMetadata? get metadata;

  /// Quick switcher configuration
  @override
  QuickSwitcherConfig? get quickSwitcherConfig;

  /// Pending account switches
  @override
  List<PendingAccountSwitch> get pendingSwitches;

  /// Create a copy of MultiAccountSession
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MultiAccountSessionImplCopyWith<_$MultiAccountSessionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SessionMetadata _$SessionMetadataFromJson(Map<String, dynamic> json) {
  return _SessionMetadata.fromJson(json);
}

/// @nodoc
mixin _$SessionMetadata {
  /// Device identifier
  String? get deviceId => throw _privateConstructorUsedError;

  /// Device type (iOS, Android)
  String? get deviceType => throw _privateConstructorUsedError;

  /// App version
  String? get appVersion => throw _privateConstructorUsedError;

  /// Platform version
  String? get platformVersion => throw _privateConstructorUsedError;

  /// Session ID for tracking
  String? get sessionId => throw _privateConstructorUsedError;

  /// Number of account switches in this session
  int get switchCount => throw _privateConstructorUsedError;

  /// Total session duration
  Duration? get sessionDuration => throw _privateConstructorUsedError;

  /// Network connectivity status
  String? get networkStatus => throw _privateConstructorUsedError;

  /// Location (if permitted)
  String? get location => throw _privateConstructorUsedError;

  /// Security flags
  Map<String, bool> get securityFlags => throw _privateConstructorUsedError;

  /// Serializes this SessionMetadata to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SessionMetadata
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SessionMetadataCopyWith<SessionMetadata> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SessionMetadataCopyWith<$Res> {
  factory $SessionMetadataCopyWith(
    SessionMetadata value,
    $Res Function(SessionMetadata) then,
  ) = _$SessionMetadataCopyWithImpl<$Res, SessionMetadata>;
  @useResult
  $Res call({
    String? deviceId,
    String? deviceType,
    String? appVersion,
    String? platformVersion,
    String? sessionId,
    int switchCount,
    Duration? sessionDuration,
    String? networkStatus,
    String? location,
    Map<String, bool> securityFlags,
  });
}

/// @nodoc
class _$SessionMetadataCopyWithImpl<$Res, $Val extends SessionMetadata>
    implements $SessionMetadataCopyWith<$Res> {
  _$SessionMetadataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SessionMetadata
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deviceId = freezed,
    Object? deviceType = freezed,
    Object? appVersion = freezed,
    Object? platformVersion = freezed,
    Object? sessionId = freezed,
    Object? switchCount = null,
    Object? sessionDuration = freezed,
    Object? networkStatus = freezed,
    Object? location = freezed,
    Object? securityFlags = null,
  }) {
    return _then(
      _value.copyWith(
            deviceId: freezed == deviceId
                ? _value.deviceId
                : deviceId // ignore: cast_nullable_to_non_nullable
                      as String?,
            deviceType: freezed == deviceType
                ? _value.deviceType
                : deviceType // ignore: cast_nullable_to_non_nullable
                      as String?,
            appVersion: freezed == appVersion
                ? _value.appVersion
                : appVersion // ignore: cast_nullable_to_non_nullable
                      as String?,
            platformVersion: freezed == platformVersion
                ? _value.platformVersion
                : platformVersion // ignore: cast_nullable_to_non_nullable
                      as String?,
            sessionId: freezed == sessionId
                ? _value.sessionId
                : sessionId // ignore: cast_nullable_to_non_nullable
                      as String?,
            switchCount: null == switchCount
                ? _value.switchCount
                : switchCount // ignore: cast_nullable_to_non_nullable
                      as int,
            sessionDuration: freezed == sessionDuration
                ? _value.sessionDuration
                : sessionDuration // ignore: cast_nullable_to_non_nullable
                      as Duration?,
            networkStatus: freezed == networkStatus
                ? _value.networkStatus
                : networkStatus // ignore: cast_nullable_to_non_nullable
                      as String?,
            location: freezed == location
                ? _value.location
                : location // ignore: cast_nullable_to_non_nullable
                      as String?,
            securityFlags: null == securityFlags
                ? _value.securityFlags
                : securityFlags // ignore: cast_nullable_to_non_nullable
                      as Map<String, bool>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$SessionMetadataImplCopyWith<$Res>
    implements $SessionMetadataCopyWith<$Res> {
  factory _$$SessionMetadataImplCopyWith(
    _$SessionMetadataImpl value,
    $Res Function(_$SessionMetadataImpl) then,
  ) = __$$SessionMetadataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String? deviceId,
    String? deviceType,
    String? appVersion,
    String? platformVersion,
    String? sessionId,
    int switchCount,
    Duration? sessionDuration,
    String? networkStatus,
    String? location,
    Map<String, bool> securityFlags,
  });
}

/// @nodoc
class __$$SessionMetadataImplCopyWithImpl<$Res>
    extends _$SessionMetadataCopyWithImpl<$Res, _$SessionMetadataImpl>
    implements _$$SessionMetadataImplCopyWith<$Res> {
  __$$SessionMetadataImplCopyWithImpl(
    _$SessionMetadataImpl _value,
    $Res Function(_$SessionMetadataImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of SessionMetadata
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deviceId = freezed,
    Object? deviceType = freezed,
    Object? appVersion = freezed,
    Object? platformVersion = freezed,
    Object? sessionId = freezed,
    Object? switchCount = null,
    Object? sessionDuration = freezed,
    Object? networkStatus = freezed,
    Object? location = freezed,
    Object? securityFlags = null,
  }) {
    return _then(
      _$SessionMetadataImpl(
        deviceId: freezed == deviceId
            ? _value.deviceId
            : deviceId // ignore: cast_nullable_to_non_nullable
                  as String?,
        deviceType: freezed == deviceType
            ? _value.deviceType
            : deviceType // ignore: cast_nullable_to_non_nullable
                  as String?,
        appVersion: freezed == appVersion
            ? _value.appVersion
            : appVersion // ignore: cast_nullable_to_non_nullable
                  as String?,
        platformVersion: freezed == platformVersion
            ? _value.platformVersion
            : platformVersion // ignore: cast_nullable_to_non_nullable
                  as String?,
        sessionId: freezed == sessionId
            ? _value.sessionId
            : sessionId // ignore: cast_nullable_to_non_nullable
                  as String?,
        switchCount: null == switchCount
            ? _value.switchCount
            : switchCount // ignore: cast_nullable_to_non_nullable
                  as int,
        sessionDuration: freezed == sessionDuration
            ? _value.sessionDuration
            : sessionDuration // ignore: cast_nullable_to_non_nullable
                  as Duration?,
        networkStatus: freezed == networkStatus
            ? _value.networkStatus
            : networkStatus // ignore: cast_nullable_to_non_nullable
                  as String?,
        location: freezed == location
            ? _value.location
            : location // ignore: cast_nullable_to_non_nullable
                  as String?,
        securityFlags: null == securityFlags
            ? _value._securityFlags
            : securityFlags // ignore: cast_nullable_to_non_nullable
                  as Map<String, bool>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$SessionMetadataImpl implements _SessionMetadata {
  const _$SessionMetadataImpl({
    this.deviceId,
    this.deviceType,
    this.appVersion,
    this.platformVersion,
    this.sessionId,
    this.switchCount = 0,
    this.sessionDuration,
    this.networkStatus,
    this.location,
    final Map<String, bool> securityFlags = const {},
  }) : _securityFlags = securityFlags;

  factory _$SessionMetadataImpl.fromJson(Map<String, dynamic> json) =>
      _$$SessionMetadataImplFromJson(json);

  /// Device identifier
  @override
  final String? deviceId;

  /// Device type (iOS, Android)
  @override
  final String? deviceType;

  /// App version
  @override
  final String? appVersion;

  /// Platform version
  @override
  final String? platformVersion;

  /// Session ID for tracking
  @override
  final String? sessionId;

  /// Number of account switches in this session
  @override
  @JsonKey()
  final int switchCount;

  /// Total session duration
  @override
  final Duration? sessionDuration;

  /// Network connectivity status
  @override
  final String? networkStatus;

  /// Location (if permitted)
  @override
  final String? location;

  /// Security flags
  final Map<String, bool> _securityFlags;

  /// Security flags
  @override
  @JsonKey()
  Map<String, bool> get securityFlags {
    if (_securityFlags is EqualUnmodifiableMapView) return _securityFlags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_securityFlags);
  }

  @override
  String toString() {
    return 'SessionMetadata(deviceId: $deviceId, deviceType: $deviceType, appVersion: $appVersion, platformVersion: $platformVersion, sessionId: $sessionId, switchCount: $switchCount, sessionDuration: $sessionDuration, networkStatus: $networkStatus, location: $location, securityFlags: $securityFlags)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SessionMetadataImpl &&
            (identical(other.deviceId, deviceId) ||
                other.deviceId == deviceId) &&
            (identical(other.deviceType, deviceType) ||
                other.deviceType == deviceType) &&
            (identical(other.appVersion, appVersion) ||
                other.appVersion == appVersion) &&
            (identical(other.platformVersion, platformVersion) ||
                other.platformVersion == platformVersion) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.switchCount, switchCount) ||
                other.switchCount == switchCount) &&
            (identical(other.sessionDuration, sessionDuration) ||
                other.sessionDuration == sessionDuration) &&
            (identical(other.networkStatus, networkStatus) ||
                other.networkStatus == networkStatus) &&
            (identical(other.location, location) ||
                other.location == location) &&
            const DeepCollectionEquality().equals(
              other._securityFlags,
              _securityFlags,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    deviceId,
    deviceType,
    appVersion,
    platformVersion,
    sessionId,
    switchCount,
    sessionDuration,
    networkStatus,
    location,
    const DeepCollectionEquality().hash(_securityFlags),
  );

  /// Create a copy of SessionMetadata
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SessionMetadataImplCopyWith<_$SessionMetadataImpl> get copyWith =>
      __$$SessionMetadataImplCopyWithImpl<_$SessionMetadataImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$SessionMetadataImplToJson(this);
  }
}

abstract class _SessionMetadata implements SessionMetadata {
  const factory _SessionMetadata({
    final String? deviceId,
    final String? deviceType,
    final String? appVersion,
    final String? platformVersion,
    final String? sessionId,
    final int switchCount,
    final Duration? sessionDuration,
    final String? networkStatus,
    final String? location,
    final Map<String, bool> securityFlags,
  }) = _$SessionMetadataImpl;

  factory _SessionMetadata.fromJson(Map<String, dynamic> json) =
      _$SessionMetadataImpl.fromJson;

  /// Device identifier
  @override
  String? get deviceId;

  /// Device type (iOS, Android)
  @override
  String? get deviceType;

  /// App version
  @override
  String? get appVersion;

  /// Platform version
  @override
  String? get platformVersion;

  /// Session ID for tracking
  @override
  String? get sessionId;

  /// Number of account switches in this session
  @override
  int get switchCount;

  /// Total session duration
  @override
  Duration? get sessionDuration;

  /// Network connectivity status
  @override
  String? get networkStatus;

  /// Location (if permitted)
  @override
  String? get location;

  /// Security flags
  @override
  Map<String, bool> get securityFlags;

  /// Create a copy of SessionMetadata
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SessionMetadataImplCopyWith<_$SessionMetadataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PendingAccountSwitch _$PendingAccountSwitchFromJson(Map<String, dynamic> json) {
  return _PendingAccountSwitch.fromJson(json);
}

/// @nodoc
mixin _$PendingAccountSwitch {
  /// Unique identifier for this switch operation
  String get id => throw _privateConstructorUsedError;

  /// Target account to switch to
  String get targetAccountId => throw _privateConstructorUsedError;

  /// Current account being switched from
  String? get fromAccountId => throw _privateConstructorUsedError;

  /// Switch initiation time
  DateTime get initiatedAt => throw _privateConstructorUsedError;

  /// Expected completion time
  DateTime? get expectedCompletionAt => throw _privateConstructorUsedError;

  /// Switch status
  PendingSwitchStatus get status => throw _privateConstructorUsedError;

  /// Progress percentage (0-100)
  int get progress => throw _privateConstructorUsedError;

  /// Current step in the switch process
  String? get currentStep => throw _privateConstructorUsedError;

  /// Error information if switch fails
  String? get errorMessage => throw _privateConstructorUsedError;

  /// Whether this switch requires user interaction
  bool get requiresUserInteraction => throw _privateConstructorUsedError;

  /// Biometric authentication result
  bool? get biometricAuthResult => throw _privateConstructorUsedError;

  /// Additional context data
  Map<String, dynamic>? get context => throw _privateConstructorUsedError;

  /// Serializes this PendingAccountSwitch to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PendingAccountSwitch
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PendingAccountSwitchCopyWith<PendingAccountSwitch> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PendingAccountSwitchCopyWith<$Res> {
  factory $PendingAccountSwitchCopyWith(
    PendingAccountSwitch value,
    $Res Function(PendingAccountSwitch) then,
  ) = _$PendingAccountSwitchCopyWithImpl<$Res, PendingAccountSwitch>;
  @useResult
  $Res call({
    String id,
    String targetAccountId,
    String? fromAccountId,
    DateTime initiatedAt,
    DateTime? expectedCompletionAt,
    PendingSwitchStatus status,
    int progress,
    String? currentStep,
    String? errorMessage,
    bool requiresUserInteraction,
    bool? biometricAuthResult,
    Map<String, dynamic>? context,
  });
}

/// @nodoc
class _$PendingAccountSwitchCopyWithImpl<
  $Res,
  $Val extends PendingAccountSwitch
>
    implements $PendingAccountSwitchCopyWith<$Res> {
  _$PendingAccountSwitchCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PendingAccountSwitch
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? targetAccountId = null,
    Object? fromAccountId = freezed,
    Object? initiatedAt = null,
    Object? expectedCompletionAt = freezed,
    Object? status = null,
    Object? progress = null,
    Object? currentStep = freezed,
    Object? errorMessage = freezed,
    Object? requiresUserInteraction = null,
    Object? biometricAuthResult = freezed,
    Object? context = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            targetAccountId: null == targetAccountId
                ? _value.targetAccountId
                : targetAccountId // ignore: cast_nullable_to_non_nullable
                      as String,
            fromAccountId: freezed == fromAccountId
                ? _value.fromAccountId
                : fromAccountId // ignore: cast_nullable_to_non_nullable
                      as String?,
            initiatedAt: null == initiatedAt
                ? _value.initiatedAt
                : initiatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            expectedCompletionAt: freezed == expectedCompletionAt
                ? _value.expectedCompletionAt
                : expectedCompletionAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as PendingSwitchStatus,
            progress: null == progress
                ? _value.progress
                : progress // ignore: cast_nullable_to_non_nullable
                      as int,
            currentStep: freezed == currentStep
                ? _value.currentStep
                : currentStep // ignore: cast_nullable_to_non_nullable
                      as String?,
            errorMessage: freezed == errorMessage
                ? _value.errorMessage
                : errorMessage // ignore: cast_nullable_to_non_nullable
                      as String?,
            requiresUserInteraction: null == requiresUserInteraction
                ? _value.requiresUserInteraction
                : requiresUserInteraction // ignore: cast_nullable_to_non_nullable
                      as bool,
            biometricAuthResult: freezed == biometricAuthResult
                ? _value.biometricAuthResult
                : biometricAuthResult // ignore: cast_nullable_to_non_nullable
                      as bool?,
            context: freezed == context
                ? _value.context
                : context // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PendingAccountSwitchImplCopyWith<$Res>
    implements $PendingAccountSwitchCopyWith<$Res> {
  factory _$$PendingAccountSwitchImplCopyWith(
    _$PendingAccountSwitchImpl value,
    $Res Function(_$PendingAccountSwitchImpl) then,
  ) = __$$PendingAccountSwitchImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String targetAccountId,
    String? fromAccountId,
    DateTime initiatedAt,
    DateTime? expectedCompletionAt,
    PendingSwitchStatus status,
    int progress,
    String? currentStep,
    String? errorMessage,
    bool requiresUserInteraction,
    bool? biometricAuthResult,
    Map<String, dynamic>? context,
  });
}

/// @nodoc
class __$$PendingAccountSwitchImplCopyWithImpl<$Res>
    extends _$PendingAccountSwitchCopyWithImpl<$Res, _$PendingAccountSwitchImpl>
    implements _$$PendingAccountSwitchImplCopyWith<$Res> {
  __$$PendingAccountSwitchImplCopyWithImpl(
    _$PendingAccountSwitchImpl _value,
    $Res Function(_$PendingAccountSwitchImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PendingAccountSwitch
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? targetAccountId = null,
    Object? fromAccountId = freezed,
    Object? initiatedAt = null,
    Object? expectedCompletionAt = freezed,
    Object? status = null,
    Object? progress = null,
    Object? currentStep = freezed,
    Object? errorMessage = freezed,
    Object? requiresUserInteraction = null,
    Object? biometricAuthResult = freezed,
    Object? context = freezed,
  }) {
    return _then(
      _$PendingAccountSwitchImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        targetAccountId: null == targetAccountId
            ? _value.targetAccountId
            : targetAccountId // ignore: cast_nullable_to_non_nullable
                  as String,
        fromAccountId: freezed == fromAccountId
            ? _value.fromAccountId
            : fromAccountId // ignore: cast_nullable_to_non_nullable
                  as String?,
        initiatedAt: null == initiatedAt
            ? _value.initiatedAt
            : initiatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        expectedCompletionAt: freezed == expectedCompletionAt
            ? _value.expectedCompletionAt
            : expectedCompletionAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as PendingSwitchStatus,
        progress: null == progress
            ? _value.progress
            : progress // ignore: cast_nullable_to_non_nullable
                  as int,
        currentStep: freezed == currentStep
            ? _value.currentStep
            : currentStep // ignore: cast_nullable_to_non_nullable
                  as String?,
        errorMessage: freezed == errorMessage
            ? _value.errorMessage
            : errorMessage // ignore: cast_nullable_to_non_nullable
                  as String?,
        requiresUserInteraction: null == requiresUserInteraction
            ? _value.requiresUserInteraction
            : requiresUserInteraction // ignore: cast_nullable_to_non_nullable
                  as bool,
        biometricAuthResult: freezed == biometricAuthResult
            ? _value.biometricAuthResult
            : biometricAuthResult // ignore: cast_nullable_to_non_nullable
                  as bool?,
        context: freezed == context
            ? _value._context
            : context // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PendingAccountSwitchImpl implements _PendingAccountSwitch {
  const _$PendingAccountSwitchImpl({
    required this.id,
    required this.targetAccountId,
    this.fromAccountId,
    required this.initiatedAt,
    this.expectedCompletionAt,
    this.status = PendingSwitchStatus.initiated,
    this.progress = 0,
    this.currentStep,
    this.errorMessage,
    this.requiresUserInteraction = false,
    this.biometricAuthResult,
    final Map<String, dynamic>? context,
  }) : _context = context;

  factory _$PendingAccountSwitchImpl.fromJson(Map<String, dynamic> json) =>
      _$$PendingAccountSwitchImplFromJson(json);

  /// Unique identifier for this switch operation
  @override
  final String id;

  /// Target account to switch to
  @override
  final String targetAccountId;

  /// Current account being switched from
  @override
  final String? fromAccountId;

  /// Switch initiation time
  @override
  final DateTime initiatedAt;

  /// Expected completion time
  @override
  final DateTime? expectedCompletionAt;

  /// Switch status
  @override
  @JsonKey()
  final PendingSwitchStatus status;

  /// Progress percentage (0-100)
  @override
  @JsonKey()
  final int progress;

  /// Current step in the switch process
  @override
  final String? currentStep;

  /// Error information if switch fails
  @override
  final String? errorMessage;

  /// Whether this switch requires user interaction
  @override
  @JsonKey()
  final bool requiresUserInteraction;

  /// Biometric authentication result
  @override
  final bool? biometricAuthResult;

  /// Additional context data
  final Map<String, dynamic>? _context;

  /// Additional context data
  @override
  Map<String, dynamic>? get context {
    final value = _context;
    if (value == null) return null;
    if (_context is EqualUnmodifiableMapView) return _context;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'PendingAccountSwitch(id: $id, targetAccountId: $targetAccountId, fromAccountId: $fromAccountId, initiatedAt: $initiatedAt, expectedCompletionAt: $expectedCompletionAt, status: $status, progress: $progress, currentStep: $currentStep, errorMessage: $errorMessage, requiresUserInteraction: $requiresUserInteraction, biometricAuthResult: $biometricAuthResult, context: $context)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PendingAccountSwitchImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.targetAccountId, targetAccountId) ||
                other.targetAccountId == targetAccountId) &&
            (identical(other.fromAccountId, fromAccountId) ||
                other.fromAccountId == fromAccountId) &&
            (identical(other.initiatedAt, initiatedAt) ||
                other.initiatedAt == initiatedAt) &&
            (identical(other.expectedCompletionAt, expectedCompletionAt) ||
                other.expectedCompletionAt == expectedCompletionAt) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.progress, progress) ||
                other.progress == progress) &&
            (identical(other.currentStep, currentStep) ||
                other.currentStep == currentStep) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(
                  other.requiresUserInteraction,
                  requiresUserInteraction,
                ) ||
                other.requiresUserInteraction == requiresUserInteraction) &&
            (identical(other.biometricAuthResult, biometricAuthResult) ||
                other.biometricAuthResult == biometricAuthResult) &&
            const DeepCollectionEquality().equals(other._context, _context));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    targetAccountId,
    fromAccountId,
    initiatedAt,
    expectedCompletionAt,
    status,
    progress,
    currentStep,
    errorMessage,
    requiresUserInteraction,
    biometricAuthResult,
    const DeepCollectionEquality().hash(_context),
  );

  /// Create a copy of PendingAccountSwitch
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PendingAccountSwitchImplCopyWith<_$PendingAccountSwitchImpl>
  get copyWith =>
      __$$PendingAccountSwitchImplCopyWithImpl<_$PendingAccountSwitchImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$PendingAccountSwitchImplToJson(this);
  }
}

abstract class _PendingAccountSwitch implements PendingAccountSwitch {
  const factory _PendingAccountSwitch({
    required final String id,
    required final String targetAccountId,
    final String? fromAccountId,
    required final DateTime initiatedAt,
    final DateTime? expectedCompletionAt,
    final PendingSwitchStatus status,
    final int progress,
    final String? currentStep,
    final String? errorMessage,
    final bool requiresUserInteraction,
    final bool? biometricAuthResult,
    final Map<String, dynamic>? context,
  }) = _$PendingAccountSwitchImpl;

  factory _PendingAccountSwitch.fromJson(Map<String, dynamic> json) =
      _$PendingAccountSwitchImpl.fromJson;

  /// Unique identifier for this switch operation
  @override
  String get id;

  /// Target account to switch to
  @override
  String get targetAccountId;

  /// Current account being switched from
  @override
  String? get fromAccountId;

  /// Switch initiation time
  @override
  DateTime get initiatedAt;

  /// Expected completion time
  @override
  DateTime? get expectedCompletionAt;

  /// Switch status
  @override
  PendingSwitchStatus get status;

  /// Progress percentage (0-100)
  @override
  int get progress;

  /// Current step in the switch process
  @override
  String? get currentStep;

  /// Error information if switch fails
  @override
  String? get errorMessage;

  /// Whether this switch requires user interaction
  @override
  bool get requiresUserInteraction;

  /// Biometric authentication result
  @override
  bool? get biometricAuthResult;

  /// Additional context data
  @override
  Map<String, dynamic>? get context;

  /// Create a copy of PendingAccountSwitch
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PendingAccountSwitchImplCopyWith<_$PendingAccountSwitchImpl>
  get copyWith => throw _privateConstructorUsedError;
}

AccountSwitchResult _$AccountSwitchResultFromJson(Map<String, dynamic> json) {
  return _AccountSwitchResult.fromJson(json);
}

/// @nodoc
mixin _$AccountSwitchResult {
  /// Whether the switch was successful
  bool get success => throw _privateConstructorUsedError;

  /// The account that was switched to (if successful)
  SavedAccountModel? get switchedToAccount =>
      throw _privateConstructorUsedError;

  /// Error message if switch failed
  String? get errorMessage => throw _privateConstructorUsedError;

  /// Error code for programmatic handling
  String? get errorCode => throw _privateConstructorUsedError;

  /// Duration of the switch operation
  Duration? get switchDuration => throw _privateConstructorUsedError;

  /// Switch history entry
  AccountSwitchHistory? get historyEntry => throw _privateConstructorUsedError;

  /// Whether user interaction was required
  bool get requiredUserInteraction => throw _privateConstructorUsedError;

  /// Whether biometric authentication was used
  bool get usedBiometricAuth => throw _privateConstructorUsedError;

  /// Additional result data
  Map<String, dynamic>? get resultData => throw _privateConstructorUsedError;

  /// Serializes this AccountSwitchResult to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AccountSwitchResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AccountSwitchResultCopyWith<AccountSwitchResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountSwitchResultCopyWith<$Res> {
  factory $AccountSwitchResultCopyWith(
    AccountSwitchResult value,
    $Res Function(AccountSwitchResult) then,
  ) = _$AccountSwitchResultCopyWithImpl<$Res, AccountSwitchResult>;
  @useResult
  $Res call({
    bool success,
    SavedAccountModel? switchedToAccount,
    String? errorMessage,
    String? errorCode,
    Duration? switchDuration,
    AccountSwitchHistory? historyEntry,
    bool requiredUserInteraction,
    bool usedBiometricAuth,
    Map<String, dynamic>? resultData,
  });

  $SavedAccountModelCopyWith<$Res>? get switchedToAccount;
  $AccountSwitchHistoryCopyWith<$Res>? get historyEntry;
}

/// @nodoc
class _$AccountSwitchResultCopyWithImpl<$Res, $Val extends AccountSwitchResult>
    implements $AccountSwitchResultCopyWith<$Res> {
  _$AccountSwitchResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AccountSwitchResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? switchedToAccount = freezed,
    Object? errorMessage = freezed,
    Object? errorCode = freezed,
    Object? switchDuration = freezed,
    Object? historyEntry = freezed,
    Object? requiredUserInteraction = null,
    Object? usedBiometricAuth = null,
    Object? resultData = freezed,
  }) {
    return _then(
      _value.copyWith(
            success: null == success
                ? _value.success
                : success // ignore: cast_nullable_to_non_nullable
                      as bool,
            switchedToAccount: freezed == switchedToAccount
                ? _value.switchedToAccount
                : switchedToAccount // ignore: cast_nullable_to_non_nullable
                      as SavedAccountModel?,
            errorMessage: freezed == errorMessage
                ? _value.errorMessage
                : errorMessage // ignore: cast_nullable_to_non_nullable
                      as String?,
            errorCode: freezed == errorCode
                ? _value.errorCode
                : errorCode // ignore: cast_nullable_to_non_nullable
                      as String?,
            switchDuration: freezed == switchDuration
                ? _value.switchDuration
                : switchDuration // ignore: cast_nullable_to_non_nullable
                      as Duration?,
            historyEntry: freezed == historyEntry
                ? _value.historyEntry
                : historyEntry // ignore: cast_nullable_to_non_nullable
                      as AccountSwitchHistory?,
            requiredUserInteraction: null == requiredUserInteraction
                ? _value.requiredUserInteraction
                : requiredUserInteraction // ignore: cast_nullable_to_non_nullable
                      as bool,
            usedBiometricAuth: null == usedBiometricAuth
                ? _value.usedBiometricAuth
                : usedBiometricAuth // ignore: cast_nullable_to_non_nullable
                      as bool,
            resultData: freezed == resultData
                ? _value.resultData
                : resultData // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
          )
          as $Val,
    );
  }

  /// Create a copy of AccountSwitchResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SavedAccountModelCopyWith<$Res>? get switchedToAccount {
    if (_value.switchedToAccount == null) {
      return null;
    }

    return $SavedAccountModelCopyWith<$Res>(_value.switchedToAccount!, (value) {
      return _then(_value.copyWith(switchedToAccount: value) as $Val);
    });
  }

  /// Create a copy of AccountSwitchResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AccountSwitchHistoryCopyWith<$Res>? get historyEntry {
    if (_value.historyEntry == null) {
      return null;
    }

    return $AccountSwitchHistoryCopyWith<$Res>(_value.historyEntry!, (value) {
      return _then(_value.copyWith(historyEntry: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AccountSwitchResultImplCopyWith<$Res>
    implements $AccountSwitchResultCopyWith<$Res> {
  factory _$$AccountSwitchResultImplCopyWith(
    _$AccountSwitchResultImpl value,
    $Res Function(_$AccountSwitchResultImpl) then,
  ) = __$$AccountSwitchResultImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    bool success,
    SavedAccountModel? switchedToAccount,
    String? errorMessage,
    String? errorCode,
    Duration? switchDuration,
    AccountSwitchHistory? historyEntry,
    bool requiredUserInteraction,
    bool usedBiometricAuth,
    Map<String, dynamic>? resultData,
  });

  @override
  $SavedAccountModelCopyWith<$Res>? get switchedToAccount;
  @override
  $AccountSwitchHistoryCopyWith<$Res>? get historyEntry;
}

/// @nodoc
class __$$AccountSwitchResultImplCopyWithImpl<$Res>
    extends _$AccountSwitchResultCopyWithImpl<$Res, _$AccountSwitchResultImpl>
    implements _$$AccountSwitchResultImplCopyWith<$Res> {
  __$$AccountSwitchResultImplCopyWithImpl(
    _$AccountSwitchResultImpl _value,
    $Res Function(_$AccountSwitchResultImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AccountSwitchResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? switchedToAccount = freezed,
    Object? errorMessage = freezed,
    Object? errorCode = freezed,
    Object? switchDuration = freezed,
    Object? historyEntry = freezed,
    Object? requiredUserInteraction = null,
    Object? usedBiometricAuth = null,
    Object? resultData = freezed,
  }) {
    return _then(
      _$AccountSwitchResultImpl(
        success: null == success
            ? _value.success
            : success // ignore: cast_nullable_to_non_nullable
                  as bool,
        switchedToAccount: freezed == switchedToAccount
            ? _value.switchedToAccount
            : switchedToAccount // ignore: cast_nullable_to_non_nullable
                  as SavedAccountModel?,
        errorMessage: freezed == errorMessage
            ? _value.errorMessage
            : errorMessage // ignore: cast_nullable_to_non_nullable
                  as String?,
        errorCode: freezed == errorCode
            ? _value.errorCode
            : errorCode // ignore: cast_nullable_to_non_nullable
                  as String?,
        switchDuration: freezed == switchDuration
            ? _value.switchDuration
            : switchDuration // ignore: cast_nullable_to_non_nullable
                  as Duration?,
        historyEntry: freezed == historyEntry
            ? _value.historyEntry
            : historyEntry // ignore: cast_nullable_to_non_nullable
                  as AccountSwitchHistory?,
        requiredUserInteraction: null == requiredUserInteraction
            ? _value.requiredUserInteraction
            : requiredUserInteraction // ignore: cast_nullable_to_non_nullable
                  as bool,
        usedBiometricAuth: null == usedBiometricAuth
            ? _value.usedBiometricAuth
            : usedBiometricAuth // ignore: cast_nullable_to_non_nullable
                  as bool,
        resultData: freezed == resultData
            ? _value._resultData
            : resultData // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$AccountSwitchResultImpl implements _AccountSwitchResult {
  const _$AccountSwitchResultImpl({
    required this.success,
    this.switchedToAccount,
    this.errorMessage,
    this.errorCode,
    this.switchDuration,
    this.historyEntry,
    this.requiredUserInteraction = false,
    this.usedBiometricAuth = false,
    final Map<String, dynamic>? resultData,
  }) : _resultData = resultData;

  factory _$AccountSwitchResultImpl.fromJson(Map<String, dynamic> json) =>
      _$$AccountSwitchResultImplFromJson(json);

  /// Whether the switch was successful
  @override
  final bool success;

  /// The account that was switched to (if successful)
  @override
  final SavedAccountModel? switchedToAccount;

  /// Error message if switch failed
  @override
  final String? errorMessage;

  /// Error code for programmatic handling
  @override
  final String? errorCode;

  /// Duration of the switch operation
  @override
  final Duration? switchDuration;

  /// Switch history entry
  @override
  final AccountSwitchHistory? historyEntry;

  /// Whether user interaction was required
  @override
  @JsonKey()
  final bool requiredUserInteraction;

  /// Whether biometric authentication was used
  @override
  @JsonKey()
  final bool usedBiometricAuth;

  /// Additional result data
  final Map<String, dynamic>? _resultData;

  /// Additional result data
  @override
  Map<String, dynamic>? get resultData {
    final value = _resultData;
    if (value == null) return null;
    if (_resultData is EqualUnmodifiableMapView) return _resultData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'AccountSwitchResult(success: $success, switchedToAccount: $switchedToAccount, errorMessage: $errorMessage, errorCode: $errorCode, switchDuration: $switchDuration, historyEntry: $historyEntry, requiredUserInteraction: $requiredUserInteraction, usedBiometricAuth: $usedBiometricAuth, resultData: $resultData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountSwitchResultImpl &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.switchedToAccount, switchedToAccount) ||
                other.switchedToAccount == switchedToAccount) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.errorCode, errorCode) ||
                other.errorCode == errorCode) &&
            (identical(other.switchDuration, switchDuration) ||
                other.switchDuration == switchDuration) &&
            (identical(other.historyEntry, historyEntry) ||
                other.historyEntry == historyEntry) &&
            (identical(
                  other.requiredUserInteraction,
                  requiredUserInteraction,
                ) ||
                other.requiredUserInteraction == requiredUserInteraction) &&
            (identical(other.usedBiometricAuth, usedBiometricAuth) ||
                other.usedBiometricAuth == usedBiometricAuth) &&
            const DeepCollectionEquality().equals(
              other._resultData,
              _resultData,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    success,
    switchedToAccount,
    errorMessage,
    errorCode,
    switchDuration,
    historyEntry,
    requiredUserInteraction,
    usedBiometricAuth,
    const DeepCollectionEquality().hash(_resultData),
  );

  /// Create a copy of AccountSwitchResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountSwitchResultImplCopyWith<_$AccountSwitchResultImpl> get copyWith =>
      __$$AccountSwitchResultImplCopyWithImpl<_$AccountSwitchResultImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$AccountSwitchResultImplToJson(this);
  }
}

abstract class _AccountSwitchResult implements AccountSwitchResult {
  const factory _AccountSwitchResult({
    required final bool success,
    final SavedAccountModel? switchedToAccount,
    final String? errorMessage,
    final String? errorCode,
    final Duration? switchDuration,
    final AccountSwitchHistory? historyEntry,
    final bool requiredUserInteraction,
    final bool usedBiometricAuth,
    final Map<String, dynamic>? resultData,
  }) = _$AccountSwitchResultImpl;

  factory _AccountSwitchResult.fromJson(Map<String, dynamic> json) =
      _$AccountSwitchResultImpl.fromJson;

  /// Whether the switch was successful
  @override
  bool get success;

  /// The account that was switched to (if successful)
  @override
  SavedAccountModel? get switchedToAccount;

  /// Error message if switch failed
  @override
  String? get errorMessage;

  /// Error code for programmatic handling
  @override
  String? get errorCode;

  /// Duration of the switch operation
  @override
  Duration? get switchDuration;

  /// Switch history entry
  @override
  AccountSwitchHistory? get historyEntry;

  /// Whether user interaction was required
  @override
  bool get requiredUserInteraction;

  /// Whether biometric authentication was used
  @override
  bool get usedBiometricAuth;

  /// Additional result data
  @override
  Map<String, dynamic>? get resultData;

  /// Create a copy of AccountSwitchResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountSwitchResultImplCopyWith<_$AccountSwitchResultImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AccountValidationResult _$AccountValidationResultFromJson(
  Map<String, dynamic> json,
) {
  return _AccountValidationResult.fromJson(json);
}

/// @nodoc
mixin _$AccountValidationResult {
  /// Whether the account is valid
  bool get isValid => throw _privateConstructorUsedError;

  /// Validation issues found
  List<String> get issues => throw _privateConstructorUsedError;

  /// Whether credentials need refresh
  bool get needsCredentialRefresh => throw _privateConstructorUsedError;

  /// Whether account is locked or suspended
  bool get isLocked => throw _privateConstructorUsedError;

  /// Account status message
  String? get statusMessage => throw _privateConstructorUsedError;

  /// Last validation time
  DateTime? get lastValidatedAt => throw _privateConstructorUsedError;

  /// Validation metadata
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this AccountValidationResult to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AccountValidationResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AccountValidationResultCopyWith<AccountValidationResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountValidationResultCopyWith<$Res> {
  factory $AccountValidationResultCopyWith(
    AccountValidationResult value,
    $Res Function(AccountValidationResult) then,
  ) = _$AccountValidationResultCopyWithImpl<$Res, AccountValidationResult>;
  @useResult
  $Res call({
    bool isValid,
    List<String> issues,
    bool needsCredentialRefresh,
    bool isLocked,
    String? statusMessage,
    DateTime? lastValidatedAt,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class _$AccountValidationResultCopyWithImpl<
  $Res,
  $Val extends AccountValidationResult
>
    implements $AccountValidationResultCopyWith<$Res> {
  _$AccountValidationResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AccountValidationResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isValid = null,
    Object? issues = null,
    Object? needsCredentialRefresh = null,
    Object? isLocked = null,
    Object? statusMessage = freezed,
    Object? lastValidatedAt = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _value.copyWith(
            isValid: null == isValid
                ? _value.isValid
                : isValid // ignore: cast_nullable_to_non_nullable
                      as bool,
            issues: null == issues
                ? _value.issues
                : issues // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            needsCredentialRefresh: null == needsCredentialRefresh
                ? _value.needsCredentialRefresh
                : needsCredentialRefresh // ignore: cast_nullable_to_non_nullable
                      as bool,
            isLocked: null == isLocked
                ? _value.isLocked
                : isLocked // ignore: cast_nullable_to_non_nullable
                      as bool,
            statusMessage: freezed == statusMessage
                ? _value.statusMessage
                : statusMessage // ignore: cast_nullable_to_non_nullable
                      as String?,
            lastValidatedAt: freezed == lastValidatedAt
                ? _value.lastValidatedAt
                : lastValidatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            metadata: freezed == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$AccountValidationResultImplCopyWith<$Res>
    implements $AccountValidationResultCopyWith<$Res> {
  factory _$$AccountValidationResultImplCopyWith(
    _$AccountValidationResultImpl value,
    $Res Function(_$AccountValidationResultImpl) then,
  ) = __$$AccountValidationResultImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    bool isValid,
    List<String> issues,
    bool needsCredentialRefresh,
    bool isLocked,
    String? statusMessage,
    DateTime? lastValidatedAt,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class __$$AccountValidationResultImplCopyWithImpl<$Res>
    extends
        _$AccountValidationResultCopyWithImpl<
          $Res,
          _$AccountValidationResultImpl
        >
    implements _$$AccountValidationResultImplCopyWith<$Res> {
  __$$AccountValidationResultImplCopyWithImpl(
    _$AccountValidationResultImpl _value,
    $Res Function(_$AccountValidationResultImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AccountValidationResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isValid = null,
    Object? issues = null,
    Object? needsCredentialRefresh = null,
    Object? isLocked = null,
    Object? statusMessage = freezed,
    Object? lastValidatedAt = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _$AccountValidationResultImpl(
        isValid: null == isValid
            ? _value.isValid
            : isValid // ignore: cast_nullable_to_non_nullable
                  as bool,
        issues: null == issues
            ? _value._issues
            : issues // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        needsCredentialRefresh: null == needsCredentialRefresh
            ? _value.needsCredentialRefresh
            : needsCredentialRefresh // ignore: cast_nullable_to_non_nullable
                  as bool,
        isLocked: null == isLocked
            ? _value.isLocked
            : isLocked // ignore: cast_nullable_to_non_nullable
                  as bool,
        statusMessage: freezed == statusMessage
            ? _value.statusMessage
            : statusMessage // ignore: cast_nullable_to_non_nullable
                  as String?,
        lastValidatedAt: freezed == lastValidatedAt
            ? _value.lastValidatedAt
            : lastValidatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        metadata: freezed == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$AccountValidationResultImpl implements _AccountValidationResult {
  const _$AccountValidationResultImpl({
    required this.isValid,
    final List<String> issues = const [],
    this.needsCredentialRefresh = false,
    this.isLocked = false,
    this.statusMessage,
    this.lastValidatedAt,
    final Map<String, dynamic>? metadata,
  }) : _issues = issues,
       _metadata = metadata;

  factory _$AccountValidationResultImpl.fromJson(Map<String, dynamic> json) =>
      _$$AccountValidationResultImplFromJson(json);

  /// Whether the account is valid
  @override
  final bool isValid;

  /// Validation issues found
  final List<String> _issues;

  /// Validation issues found
  @override
  @JsonKey()
  List<String> get issues {
    if (_issues is EqualUnmodifiableListView) return _issues;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_issues);
  }

  /// Whether credentials need refresh
  @override
  @JsonKey()
  final bool needsCredentialRefresh;

  /// Whether account is locked or suspended
  @override
  @JsonKey()
  final bool isLocked;

  /// Account status message
  @override
  final String? statusMessage;

  /// Last validation time
  @override
  final DateTime? lastValidatedAt;

  /// Validation metadata
  final Map<String, dynamic>? _metadata;

  /// Validation metadata
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'AccountValidationResult(isValid: $isValid, issues: $issues, needsCredentialRefresh: $needsCredentialRefresh, isLocked: $isLocked, statusMessage: $statusMessage, lastValidatedAt: $lastValidatedAt, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountValidationResultImpl &&
            (identical(other.isValid, isValid) || other.isValid == isValid) &&
            const DeepCollectionEquality().equals(other._issues, _issues) &&
            (identical(other.needsCredentialRefresh, needsCredentialRefresh) ||
                other.needsCredentialRefresh == needsCredentialRefresh) &&
            (identical(other.isLocked, isLocked) ||
                other.isLocked == isLocked) &&
            (identical(other.statusMessage, statusMessage) ||
                other.statusMessage == statusMessage) &&
            (identical(other.lastValidatedAt, lastValidatedAt) ||
                other.lastValidatedAt == lastValidatedAt) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    isValid,
    const DeepCollectionEquality().hash(_issues),
    needsCredentialRefresh,
    isLocked,
    statusMessage,
    lastValidatedAt,
    const DeepCollectionEquality().hash(_metadata),
  );

  /// Create a copy of AccountValidationResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountValidationResultImplCopyWith<_$AccountValidationResultImpl>
  get copyWith =>
      __$$AccountValidationResultImplCopyWithImpl<
        _$AccountValidationResultImpl
      >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AccountValidationResultImplToJson(this);
  }
}

abstract class _AccountValidationResult implements AccountValidationResult {
  const factory _AccountValidationResult({
    required final bool isValid,
    final List<String> issues,
    final bool needsCredentialRefresh,
    final bool isLocked,
    final String? statusMessage,
    final DateTime? lastValidatedAt,
    final Map<String, dynamic>? metadata,
  }) = _$AccountValidationResultImpl;

  factory _AccountValidationResult.fromJson(Map<String, dynamic> json) =
      _$AccountValidationResultImpl.fromJson;

  /// Whether the account is valid
  @override
  bool get isValid;

  /// Validation issues found
  @override
  List<String> get issues;

  /// Whether credentials need refresh
  @override
  bool get needsCredentialRefresh;

  /// Whether account is locked or suspended
  @override
  bool get isLocked;

  /// Account status message
  @override
  String? get statusMessage;

  /// Last validation time
  @override
  DateTime? get lastValidatedAt;

  /// Validation metadata
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of AccountValidationResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountValidationResultImplCopyWith<_$AccountValidationResultImpl>
  get copyWith => throw _privateConstructorUsedError;
}

AccountSwitchHistory _$AccountSwitchHistoryFromJson(Map<String, dynamic> json) {
  return _AccountSwitchHistory.fromJson(json);
}

/// @nodoc
mixin _$AccountSwitchHistory {
  /// Unique identifier for this switch event
  String get id => throw _privateConstructorUsedError;

  /// Account that was switched from
  String? get fromAccountId => throw _privateConstructorUsedError;

  /// Account that was switched to
  String get toAccountId => throw _privateConstructorUsedError;

  /// When the switch occurred
  DateTime get switchedAt => throw _privateConstructorUsedError;

  /// Device information
  String? get deviceId => throw _privateConstructorUsedError;

  /// IP address at time of switch
  String? get ipAddress => throw _privateConstructorUsedError;

  /// Switch method (manual, auto, biometric)
  String get switchMethod => throw _privateConstructorUsedError;

  /// Whether switch was successful
  bool get successful => throw _privateConstructorUsedError;

  /// Error message if switch failed
  String? get errorMessage => throw _privateConstructorUsedError;

  /// Duration of the switch process
  Duration? get switchDuration => throw _privateConstructorUsedError;

  /// Serializes this AccountSwitchHistory to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AccountSwitchHistory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AccountSwitchHistoryCopyWith<AccountSwitchHistory> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountSwitchHistoryCopyWith<$Res> {
  factory $AccountSwitchHistoryCopyWith(
    AccountSwitchHistory value,
    $Res Function(AccountSwitchHistory) then,
  ) = _$AccountSwitchHistoryCopyWithImpl<$Res, AccountSwitchHistory>;
  @useResult
  $Res call({
    String id,
    String? fromAccountId,
    String toAccountId,
    DateTime switchedAt,
    String? deviceId,
    String? ipAddress,
    String switchMethod,
    bool successful,
    String? errorMessage,
    Duration? switchDuration,
  });
}

/// @nodoc
class _$AccountSwitchHistoryCopyWithImpl<
  $Res,
  $Val extends AccountSwitchHistory
>
    implements $AccountSwitchHistoryCopyWith<$Res> {
  _$AccountSwitchHistoryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AccountSwitchHistory
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? fromAccountId = freezed,
    Object? toAccountId = null,
    Object? switchedAt = null,
    Object? deviceId = freezed,
    Object? ipAddress = freezed,
    Object? switchMethod = null,
    Object? successful = null,
    Object? errorMessage = freezed,
    Object? switchDuration = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            fromAccountId: freezed == fromAccountId
                ? _value.fromAccountId
                : fromAccountId // ignore: cast_nullable_to_non_nullable
                      as String?,
            toAccountId: null == toAccountId
                ? _value.toAccountId
                : toAccountId // ignore: cast_nullable_to_non_nullable
                      as String,
            switchedAt: null == switchedAt
                ? _value.switchedAt
                : switchedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            deviceId: freezed == deviceId
                ? _value.deviceId
                : deviceId // ignore: cast_nullable_to_non_nullable
                      as String?,
            ipAddress: freezed == ipAddress
                ? _value.ipAddress
                : ipAddress // ignore: cast_nullable_to_non_nullable
                      as String?,
            switchMethod: null == switchMethod
                ? _value.switchMethod
                : switchMethod // ignore: cast_nullable_to_non_nullable
                      as String,
            successful: null == successful
                ? _value.successful
                : successful // ignore: cast_nullable_to_non_nullable
                      as bool,
            errorMessage: freezed == errorMessage
                ? _value.errorMessage
                : errorMessage // ignore: cast_nullable_to_non_nullable
                      as String?,
            switchDuration: freezed == switchDuration
                ? _value.switchDuration
                : switchDuration // ignore: cast_nullable_to_non_nullable
                      as Duration?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$AccountSwitchHistoryImplCopyWith<$Res>
    implements $AccountSwitchHistoryCopyWith<$Res> {
  factory _$$AccountSwitchHistoryImplCopyWith(
    _$AccountSwitchHistoryImpl value,
    $Res Function(_$AccountSwitchHistoryImpl) then,
  ) = __$$AccountSwitchHistoryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String? fromAccountId,
    String toAccountId,
    DateTime switchedAt,
    String? deviceId,
    String? ipAddress,
    String switchMethod,
    bool successful,
    String? errorMessage,
    Duration? switchDuration,
  });
}

/// @nodoc
class __$$AccountSwitchHistoryImplCopyWithImpl<$Res>
    extends _$AccountSwitchHistoryCopyWithImpl<$Res, _$AccountSwitchHistoryImpl>
    implements _$$AccountSwitchHistoryImplCopyWith<$Res> {
  __$$AccountSwitchHistoryImplCopyWithImpl(
    _$AccountSwitchHistoryImpl _value,
    $Res Function(_$AccountSwitchHistoryImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AccountSwitchHistory
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? fromAccountId = freezed,
    Object? toAccountId = null,
    Object? switchedAt = null,
    Object? deviceId = freezed,
    Object? ipAddress = freezed,
    Object? switchMethod = null,
    Object? successful = null,
    Object? errorMessage = freezed,
    Object? switchDuration = freezed,
  }) {
    return _then(
      _$AccountSwitchHistoryImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        fromAccountId: freezed == fromAccountId
            ? _value.fromAccountId
            : fromAccountId // ignore: cast_nullable_to_non_nullable
                  as String?,
        toAccountId: null == toAccountId
            ? _value.toAccountId
            : toAccountId // ignore: cast_nullable_to_non_nullable
                  as String,
        switchedAt: null == switchedAt
            ? _value.switchedAt
            : switchedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        deviceId: freezed == deviceId
            ? _value.deviceId
            : deviceId // ignore: cast_nullable_to_non_nullable
                  as String?,
        ipAddress: freezed == ipAddress
            ? _value.ipAddress
            : ipAddress // ignore: cast_nullable_to_non_nullable
                  as String?,
        switchMethod: null == switchMethod
            ? _value.switchMethod
            : switchMethod // ignore: cast_nullable_to_non_nullable
                  as String,
        successful: null == successful
            ? _value.successful
            : successful // ignore: cast_nullable_to_non_nullable
                  as bool,
        errorMessage: freezed == errorMessage
            ? _value.errorMessage
            : errorMessage // ignore: cast_nullable_to_non_nullable
                  as String?,
        switchDuration: freezed == switchDuration
            ? _value.switchDuration
            : switchDuration // ignore: cast_nullable_to_non_nullable
                  as Duration?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$AccountSwitchHistoryImpl implements _AccountSwitchHistory {
  const _$AccountSwitchHistoryImpl({
    required this.id,
    this.fromAccountId,
    required this.toAccountId,
    required this.switchedAt,
    this.deviceId,
    this.ipAddress,
    this.switchMethod = 'manual',
    this.successful = true,
    this.errorMessage,
    this.switchDuration,
  });

  factory _$AccountSwitchHistoryImpl.fromJson(Map<String, dynamic> json) =>
      _$$AccountSwitchHistoryImplFromJson(json);

  /// Unique identifier for this switch event
  @override
  final String id;

  /// Account that was switched from
  @override
  final String? fromAccountId;

  /// Account that was switched to
  @override
  final String toAccountId;

  /// When the switch occurred
  @override
  final DateTime switchedAt;

  /// Device information
  @override
  final String? deviceId;

  /// IP address at time of switch
  @override
  final String? ipAddress;

  /// Switch method (manual, auto, biometric)
  @override
  @JsonKey()
  final String switchMethod;

  /// Whether switch was successful
  @override
  @JsonKey()
  final bool successful;

  /// Error message if switch failed
  @override
  final String? errorMessage;

  /// Duration of the switch process
  @override
  final Duration? switchDuration;

  @override
  String toString() {
    return 'AccountSwitchHistory(id: $id, fromAccountId: $fromAccountId, toAccountId: $toAccountId, switchedAt: $switchedAt, deviceId: $deviceId, ipAddress: $ipAddress, switchMethod: $switchMethod, successful: $successful, errorMessage: $errorMessage, switchDuration: $switchDuration)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountSwitchHistoryImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.fromAccountId, fromAccountId) ||
                other.fromAccountId == fromAccountId) &&
            (identical(other.toAccountId, toAccountId) ||
                other.toAccountId == toAccountId) &&
            (identical(other.switchedAt, switchedAt) ||
                other.switchedAt == switchedAt) &&
            (identical(other.deviceId, deviceId) ||
                other.deviceId == deviceId) &&
            (identical(other.ipAddress, ipAddress) ||
                other.ipAddress == ipAddress) &&
            (identical(other.switchMethod, switchMethod) ||
                other.switchMethod == switchMethod) &&
            (identical(other.successful, successful) ||
                other.successful == successful) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.switchDuration, switchDuration) ||
                other.switchDuration == switchDuration));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    fromAccountId,
    toAccountId,
    switchedAt,
    deviceId,
    ipAddress,
    switchMethod,
    successful,
    errorMessage,
    switchDuration,
  );

  /// Create a copy of AccountSwitchHistory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountSwitchHistoryImplCopyWith<_$AccountSwitchHistoryImpl>
  get copyWith =>
      __$$AccountSwitchHistoryImplCopyWithImpl<_$AccountSwitchHistoryImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$AccountSwitchHistoryImplToJson(this);
  }
}

abstract class _AccountSwitchHistory implements AccountSwitchHistory {
  const factory _AccountSwitchHistory({
    required final String id,
    final String? fromAccountId,
    required final String toAccountId,
    required final DateTime switchedAt,
    final String? deviceId,
    final String? ipAddress,
    final String switchMethod,
    final bool successful,
    final String? errorMessage,
    final Duration? switchDuration,
  }) = _$AccountSwitchHistoryImpl;

  factory _AccountSwitchHistory.fromJson(Map<String, dynamic> json) =
      _$AccountSwitchHistoryImpl.fromJson;

  /// Unique identifier for this switch event
  @override
  String get id;

  /// Account that was switched from
  @override
  String? get fromAccountId;

  /// Account that was switched to
  @override
  String get toAccountId;

  /// When the switch occurred
  @override
  DateTime get switchedAt;

  /// Device information
  @override
  String? get deviceId;

  /// IP address at time of switch
  @override
  String? get ipAddress;

  /// Switch method (manual, auto, biometric)
  @override
  String get switchMethod;

  /// Whether switch was successful
  @override
  bool get successful;

  /// Error message if switch failed
  @override
  String? get errorMessage;

  /// Duration of the switch process
  @override
  Duration? get switchDuration;

  /// Create a copy of AccountSwitchHistory
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountSwitchHistoryImplCopyWith<_$AccountSwitchHistoryImpl>
  get copyWith => throw _privateConstructorUsedError;
}

QuickSwitcherConfig _$QuickSwitcherConfigFromJson(Map<String, dynamic> json) {
  return _QuickSwitcherConfig.fromJson(json);
}

/// @nodoc
mixin _$QuickSwitcherConfig {
  /// Maximum number of accounts to show in quick switcher
  int get maxQuickAccounts => throw _privateConstructorUsedError;

  /// Whether to show account previews
  bool get showAccountPreviews => throw _privateConstructorUsedError;

  /// Whether to require biometric auth for switching
  bool get requireBiometricAuth => throw _privateConstructorUsedError;

  /// Auto-switch timeout (in minutes)
  int? get autoSwitchTimeout => throw _privateConstructorUsedError;

  /// Accounts pinned to quick switcher
  List<String> get pinnedAccountIds => throw _privateConstructorUsedError;

  /// Recently used accounts (ordered by recency)
  List<String> get recentAccountIds => throw _privateConstructorUsedError;

  /// Whether to show account notifications
  bool get showAccountNotifications => throw _privateConstructorUsedError;

  /// Last updated timestamp
  DateTime? get lastUpdated => throw _privateConstructorUsedError;

  /// Serializes this QuickSwitcherConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of QuickSwitcherConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $QuickSwitcherConfigCopyWith<QuickSwitcherConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QuickSwitcherConfigCopyWith<$Res> {
  factory $QuickSwitcherConfigCopyWith(
    QuickSwitcherConfig value,
    $Res Function(QuickSwitcherConfig) then,
  ) = _$QuickSwitcherConfigCopyWithImpl<$Res, QuickSwitcherConfig>;
  @useResult
  $Res call({
    int maxQuickAccounts,
    bool showAccountPreviews,
    bool requireBiometricAuth,
    int? autoSwitchTimeout,
    List<String> pinnedAccountIds,
    List<String> recentAccountIds,
    bool showAccountNotifications,
    DateTime? lastUpdated,
  });
}

/// @nodoc
class _$QuickSwitcherConfigCopyWithImpl<$Res, $Val extends QuickSwitcherConfig>
    implements $QuickSwitcherConfigCopyWith<$Res> {
  _$QuickSwitcherConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of QuickSwitcherConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? maxQuickAccounts = null,
    Object? showAccountPreviews = null,
    Object? requireBiometricAuth = null,
    Object? autoSwitchTimeout = freezed,
    Object? pinnedAccountIds = null,
    Object? recentAccountIds = null,
    Object? showAccountNotifications = null,
    Object? lastUpdated = freezed,
  }) {
    return _then(
      _value.copyWith(
            maxQuickAccounts: null == maxQuickAccounts
                ? _value.maxQuickAccounts
                : maxQuickAccounts // ignore: cast_nullable_to_non_nullable
                      as int,
            showAccountPreviews: null == showAccountPreviews
                ? _value.showAccountPreviews
                : showAccountPreviews // ignore: cast_nullable_to_non_nullable
                      as bool,
            requireBiometricAuth: null == requireBiometricAuth
                ? _value.requireBiometricAuth
                : requireBiometricAuth // ignore: cast_nullable_to_non_nullable
                      as bool,
            autoSwitchTimeout: freezed == autoSwitchTimeout
                ? _value.autoSwitchTimeout
                : autoSwitchTimeout // ignore: cast_nullable_to_non_nullable
                      as int?,
            pinnedAccountIds: null == pinnedAccountIds
                ? _value.pinnedAccountIds
                : pinnedAccountIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            recentAccountIds: null == recentAccountIds
                ? _value.recentAccountIds
                : recentAccountIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            showAccountNotifications: null == showAccountNotifications
                ? _value.showAccountNotifications
                : showAccountNotifications // ignore: cast_nullable_to_non_nullable
                      as bool,
            lastUpdated: freezed == lastUpdated
                ? _value.lastUpdated
                : lastUpdated // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$QuickSwitcherConfigImplCopyWith<$Res>
    implements $QuickSwitcherConfigCopyWith<$Res> {
  factory _$$QuickSwitcherConfigImplCopyWith(
    _$QuickSwitcherConfigImpl value,
    $Res Function(_$QuickSwitcherConfigImpl) then,
  ) = __$$QuickSwitcherConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int maxQuickAccounts,
    bool showAccountPreviews,
    bool requireBiometricAuth,
    int? autoSwitchTimeout,
    List<String> pinnedAccountIds,
    List<String> recentAccountIds,
    bool showAccountNotifications,
    DateTime? lastUpdated,
  });
}

/// @nodoc
class __$$QuickSwitcherConfigImplCopyWithImpl<$Res>
    extends _$QuickSwitcherConfigCopyWithImpl<$Res, _$QuickSwitcherConfigImpl>
    implements _$$QuickSwitcherConfigImplCopyWith<$Res> {
  __$$QuickSwitcherConfigImplCopyWithImpl(
    _$QuickSwitcherConfigImpl _value,
    $Res Function(_$QuickSwitcherConfigImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of QuickSwitcherConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? maxQuickAccounts = null,
    Object? showAccountPreviews = null,
    Object? requireBiometricAuth = null,
    Object? autoSwitchTimeout = freezed,
    Object? pinnedAccountIds = null,
    Object? recentAccountIds = null,
    Object? showAccountNotifications = null,
    Object? lastUpdated = freezed,
  }) {
    return _then(
      _$QuickSwitcherConfigImpl(
        maxQuickAccounts: null == maxQuickAccounts
            ? _value.maxQuickAccounts
            : maxQuickAccounts // ignore: cast_nullable_to_non_nullable
                  as int,
        showAccountPreviews: null == showAccountPreviews
            ? _value.showAccountPreviews
            : showAccountPreviews // ignore: cast_nullable_to_non_nullable
                  as bool,
        requireBiometricAuth: null == requireBiometricAuth
            ? _value.requireBiometricAuth
            : requireBiometricAuth // ignore: cast_nullable_to_non_nullable
                  as bool,
        autoSwitchTimeout: freezed == autoSwitchTimeout
            ? _value.autoSwitchTimeout
            : autoSwitchTimeout // ignore: cast_nullable_to_non_nullable
                  as int?,
        pinnedAccountIds: null == pinnedAccountIds
            ? _value._pinnedAccountIds
            : pinnedAccountIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        recentAccountIds: null == recentAccountIds
            ? _value._recentAccountIds
            : recentAccountIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        showAccountNotifications: null == showAccountNotifications
            ? _value.showAccountNotifications
            : showAccountNotifications // ignore: cast_nullable_to_non_nullable
                  as bool,
        lastUpdated: freezed == lastUpdated
            ? _value.lastUpdated
            : lastUpdated // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$QuickSwitcherConfigImpl implements _QuickSwitcherConfig {
  const _$QuickSwitcherConfigImpl({
    this.maxQuickAccounts = 5,
    this.showAccountPreviews = true,
    this.requireBiometricAuth = false,
    this.autoSwitchTimeout,
    final List<String> pinnedAccountIds = const [],
    final List<String> recentAccountIds = const [],
    this.showAccountNotifications = true,
    this.lastUpdated,
  }) : _pinnedAccountIds = pinnedAccountIds,
       _recentAccountIds = recentAccountIds;

  factory _$QuickSwitcherConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$QuickSwitcherConfigImplFromJson(json);

  /// Maximum number of accounts to show in quick switcher
  @override
  @JsonKey()
  final int maxQuickAccounts;

  /// Whether to show account previews
  @override
  @JsonKey()
  final bool showAccountPreviews;

  /// Whether to require biometric auth for switching
  @override
  @JsonKey()
  final bool requireBiometricAuth;

  /// Auto-switch timeout (in minutes)
  @override
  final int? autoSwitchTimeout;

  /// Accounts pinned to quick switcher
  final List<String> _pinnedAccountIds;

  /// Accounts pinned to quick switcher
  @override
  @JsonKey()
  List<String> get pinnedAccountIds {
    if (_pinnedAccountIds is EqualUnmodifiableListView)
      return _pinnedAccountIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_pinnedAccountIds);
  }

  /// Recently used accounts (ordered by recency)
  final List<String> _recentAccountIds;

  /// Recently used accounts (ordered by recency)
  @override
  @JsonKey()
  List<String> get recentAccountIds {
    if (_recentAccountIds is EqualUnmodifiableListView)
      return _recentAccountIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_recentAccountIds);
  }

  /// Whether to show account notifications
  @override
  @JsonKey()
  final bool showAccountNotifications;

  /// Last updated timestamp
  @override
  final DateTime? lastUpdated;

  @override
  String toString() {
    return 'QuickSwitcherConfig(maxQuickAccounts: $maxQuickAccounts, showAccountPreviews: $showAccountPreviews, requireBiometricAuth: $requireBiometricAuth, autoSwitchTimeout: $autoSwitchTimeout, pinnedAccountIds: $pinnedAccountIds, recentAccountIds: $recentAccountIds, showAccountNotifications: $showAccountNotifications, lastUpdated: $lastUpdated)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QuickSwitcherConfigImpl &&
            (identical(other.maxQuickAccounts, maxQuickAccounts) ||
                other.maxQuickAccounts == maxQuickAccounts) &&
            (identical(other.showAccountPreviews, showAccountPreviews) ||
                other.showAccountPreviews == showAccountPreviews) &&
            (identical(other.requireBiometricAuth, requireBiometricAuth) ||
                other.requireBiometricAuth == requireBiometricAuth) &&
            (identical(other.autoSwitchTimeout, autoSwitchTimeout) ||
                other.autoSwitchTimeout == autoSwitchTimeout) &&
            const DeepCollectionEquality().equals(
              other._pinnedAccountIds,
              _pinnedAccountIds,
            ) &&
            const DeepCollectionEquality().equals(
              other._recentAccountIds,
              _recentAccountIds,
            ) &&
            (identical(
                  other.showAccountNotifications,
                  showAccountNotifications,
                ) ||
                other.showAccountNotifications == showAccountNotifications) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    maxQuickAccounts,
    showAccountPreviews,
    requireBiometricAuth,
    autoSwitchTimeout,
    const DeepCollectionEquality().hash(_pinnedAccountIds),
    const DeepCollectionEquality().hash(_recentAccountIds),
    showAccountNotifications,
    lastUpdated,
  );

  /// Create a copy of QuickSwitcherConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$QuickSwitcherConfigImplCopyWith<_$QuickSwitcherConfigImpl> get copyWith =>
      __$$QuickSwitcherConfigImplCopyWithImpl<_$QuickSwitcherConfigImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$QuickSwitcherConfigImplToJson(this);
  }
}

abstract class _QuickSwitcherConfig implements QuickSwitcherConfig {
  const factory _QuickSwitcherConfig({
    final int maxQuickAccounts,
    final bool showAccountPreviews,
    final bool requireBiometricAuth,
    final int? autoSwitchTimeout,
    final List<String> pinnedAccountIds,
    final List<String> recentAccountIds,
    final bool showAccountNotifications,
    final DateTime? lastUpdated,
  }) = _$QuickSwitcherConfigImpl;

  factory _QuickSwitcherConfig.fromJson(Map<String, dynamic> json) =
      _$QuickSwitcherConfigImpl.fromJson;

  /// Maximum number of accounts to show in quick switcher
  @override
  int get maxQuickAccounts;

  /// Whether to show account previews
  @override
  bool get showAccountPreviews;

  /// Whether to require biometric auth for switching
  @override
  bool get requireBiometricAuth;

  /// Auto-switch timeout (in minutes)
  @override
  int? get autoSwitchTimeout;

  /// Accounts pinned to quick switcher
  @override
  List<String> get pinnedAccountIds;

  /// Recently used accounts (ordered by recency)
  @override
  List<String> get recentAccountIds;

  /// Whether to show account notifications
  @override
  bool get showAccountNotifications;

  /// Last updated timestamp
  @override
  DateTime? get lastUpdated;

  /// Create a copy of QuickSwitcherConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$QuickSwitcherConfigImplCopyWith<_$QuickSwitcherConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
