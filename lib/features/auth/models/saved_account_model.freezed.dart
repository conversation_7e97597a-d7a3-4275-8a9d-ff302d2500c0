// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'saved_account_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

SavedAccountModel _$SavedAccountModelFromJson(Map<String, dynamic> json) {
  return _SavedAccountModel.fromJson(json);
}

/// @nodoc
mixin _$SavedAccountModel {
  /// Unique identifier for this saved account
  String get id => throw _privateConstructorUsedError;

  /// Firebase user ID
  String get firebaseUid => throw _privateConstructorUsedError;

  /// User's email address
  String get email => throw _privateConstructorUsedError;

  /// Display name
  String get displayName => throw _privateConstructorUsedError;

  /// Username
  String get username => throw _privateConstructorUsedError;

  /// Profile picture URL
  String? get profilePictureUrl => throw _privateConstructorUsedError;

  /// Account type (personal, business, celebrity)
  String get accountType => throw _privateConstructorUsedError;

  /// Whether this account is verified
  bool get isVerified => throw _privateConstructorUsedError;

  /// Whether this account is a billionaire account
  bool get isBillionaire => throw _privateConstructorUsedError;

  /// Last time this account was used
  DateTime? get lastUsedAt => throw _privateConstructorUsedError;

  /// When this account was first saved
  DateTime get savedAt => throw _privateConstructorUsedError;

  /// Whether this is the currently active account
  bool get isActive => throw _privateConstructorUsedError;

  /// Account metadata (followers, following, posts count)
  AccountMetadata? get metadata => throw _privateConstructorUsedError;

  /// Account settings and preferences
  Map<String, dynamic>? get settings => throw _privateConstructorUsedError;

  /// Device-specific information
  String? get deviceId => throw _privateConstructorUsedError;

  /// Last known IP address (for security)
  String? get lastKnownIp => throw _privateConstructorUsedError;

  /// Serializes this SavedAccountModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SavedAccountModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SavedAccountModelCopyWith<SavedAccountModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SavedAccountModelCopyWith<$Res> {
  factory $SavedAccountModelCopyWith(
    SavedAccountModel value,
    $Res Function(SavedAccountModel) then,
  ) = _$SavedAccountModelCopyWithImpl<$Res, SavedAccountModel>;
  @useResult
  $Res call({
    String id,
    String firebaseUid,
    String email,
    String displayName,
    String username,
    String? profilePictureUrl,
    String accountType,
    bool isVerified,
    bool isBillionaire,
    DateTime? lastUsedAt,
    DateTime savedAt,
    bool isActive,
    AccountMetadata? metadata,
    Map<String, dynamic>? settings,
    String? deviceId,
    String? lastKnownIp,
  });

  $AccountMetadataCopyWith<$Res>? get metadata;
}

/// @nodoc
class _$SavedAccountModelCopyWithImpl<$Res, $Val extends SavedAccountModel>
    implements $SavedAccountModelCopyWith<$Res> {
  _$SavedAccountModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SavedAccountModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? firebaseUid = null,
    Object? email = null,
    Object? displayName = null,
    Object? username = null,
    Object? profilePictureUrl = freezed,
    Object? accountType = null,
    Object? isVerified = null,
    Object? isBillionaire = null,
    Object? lastUsedAt = freezed,
    Object? savedAt = null,
    Object? isActive = null,
    Object? metadata = freezed,
    Object? settings = freezed,
    Object? deviceId = freezed,
    Object? lastKnownIp = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            firebaseUid: null == firebaseUid
                ? _value.firebaseUid
                : firebaseUid // ignore: cast_nullable_to_non_nullable
                      as String,
            email: null == email
                ? _value.email
                : email // ignore: cast_nullable_to_non_nullable
                      as String,
            displayName: null == displayName
                ? _value.displayName
                : displayName // ignore: cast_nullable_to_non_nullable
                      as String,
            username: null == username
                ? _value.username
                : username // ignore: cast_nullable_to_non_nullable
                      as String,
            profilePictureUrl: freezed == profilePictureUrl
                ? _value.profilePictureUrl
                : profilePictureUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            accountType: null == accountType
                ? _value.accountType
                : accountType // ignore: cast_nullable_to_non_nullable
                      as String,
            isVerified: null == isVerified
                ? _value.isVerified
                : isVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            isBillionaire: null == isBillionaire
                ? _value.isBillionaire
                : isBillionaire // ignore: cast_nullable_to_non_nullable
                      as bool,
            lastUsedAt: freezed == lastUsedAt
                ? _value.lastUsedAt
                : lastUsedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            savedAt: null == savedAt
                ? _value.savedAt
                : savedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isActive: null == isActive
                ? _value.isActive
                : isActive // ignore: cast_nullable_to_non_nullable
                      as bool,
            metadata: freezed == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as AccountMetadata?,
            settings: freezed == settings
                ? _value.settings
                : settings // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
            deviceId: freezed == deviceId
                ? _value.deviceId
                : deviceId // ignore: cast_nullable_to_non_nullable
                      as String?,
            lastKnownIp: freezed == lastKnownIp
                ? _value.lastKnownIp
                : lastKnownIp // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }

  /// Create a copy of SavedAccountModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AccountMetadataCopyWith<$Res>? get metadata {
    if (_value.metadata == null) {
      return null;
    }

    return $AccountMetadataCopyWith<$Res>(_value.metadata!, (value) {
      return _then(_value.copyWith(metadata: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SavedAccountModelImplCopyWith<$Res>
    implements $SavedAccountModelCopyWith<$Res> {
  factory _$$SavedAccountModelImplCopyWith(
    _$SavedAccountModelImpl value,
    $Res Function(_$SavedAccountModelImpl) then,
  ) = __$$SavedAccountModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String firebaseUid,
    String email,
    String displayName,
    String username,
    String? profilePictureUrl,
    String accountType,
    bool isVerified,
    bool isBillionaire,
    DateTime? lastUsedAt,
    DateTime savedAt,
    bool isActive,
    AccountMetadata? metadata,
    Map<String, dynamic>? settings,
    String? deviceId,
    String? lastKnownIp,
  });

  @override
  $AccountMetadataCopyWith<$Res>? get metadata;
}

/// @nodoc
class __$$SavedAccountModelImplCopyWithImpl<$Res>
    extends _$SavedAccountModelCopyWithImpl<$Res, _$SavedAccountModelImpl>
    implements _$$SavedAccountModelImplCopyWith<$Res> {
  __$$SavedAccountModelImplCopyWithImpl(
    _$SavedAccountModelImpl _value,
    $Res Function(_$SavedAccountModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of SavedAccountModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? firebaseUid = null,
    Object? email = null,
    Object? displayName = null,
    Object? username = null,
    Object? profilePictureUrl = freezed,
    Object? accountType = null,
    Object? isVerified = null,
    Object? isBillionaire = null,
    Object? lastUsedAt = freezed,
    Object? savedAt = null,
    Object? isActive = null,
    Object? metadata = freezed,
    Object? settings = freezed,
    Object? deviceId = freezed,
    Object? lastKnownIp = freezed,
  }) {
    return _then(
      _$SavedAccountModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        firebaseUid: null == firebaseUid
            ? _value.firebaseUid
            : firebaseUid // ignore: cast_nullable_to_non_nullable
                  as String,
        email: null == email
            ? _value.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String,
        displayName: null == displayName
            ? _value.displayName
            : displayName // ignore: cast_nullable_to_non_nullable
                  as String,
        username: null == username
            ? _value.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        profilePictureUrl: freezed == profilePictureUrl
            ? _value.profilePictureUrl
            : profilePictureUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        accountType: null == accountType
            ? _value.accountType
            : accountType // ignore: cast_nullable_to_non_nullable
                  as String,
        isVerified: null == isVerified
            ? _value.isVerified
            : isVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        isBillionaire: null == isBillionaire
            ? _value.isBillionaire
            : isBillionaire // ignore: cast_nullable_to_non_nullable
                  as bool,
        lastUsedAt: freezed == lastUsedAt
            ? _value.lastUsedAt
            : lastUsedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        savedAt: null == savedAt
            ? _value.savedAt
            : savedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isActive: null == isActive
            ? _value.isActive
            : isActive // ignore: cast_nullable_to_non_nullable
                  as bool,
        metadata: freezed == metadata
            ? _value.metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as AccountMetadata?,
        settings: freezed == settings
            ? _value._settings
            : settings // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
        deviceId: freezed == deviceId
            ? _value.deviceId
            : deviceId // ignore: cast_nullable_to_non_nullable
                  as String?,
        lastKnownIp: freezed == lastKnownIp
            ? _value.lastKnownIp
            : lastKnownIp // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$SavedAccountModelImpl implements _SavedAccountModel {
  const _$SavedAccountModelImpl({
    required this.id,
    required this.firebaseUid,
    required this.email,
    required this.displayName,
    required this.username,
    this.profilePictureUrl,
    required this.accountType,
    this.isVerified = false,
    this.isBillionaire = false,
    this.lastUsedAt,
    required this.savedAt,
    this.isActive = false,
    this.metadata,
    final Map<String, dynamic>? settings,
    this.deviceId,
    this.lastKnownIp,
  }) : _settings = settings;

  factory _$SavedAccountModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SavedAccountModelImplFromJson(json);

  /// Unique identifier for this saved account
  @override
  final String id;

  /// Firebase user ID
  @override
  final String firebaseUid;

  /// User's email address
  @override
  final String email;

  /// Display name
  @override
  final String displayName;

  /// Username
  @override
  final String username;

  /// Profile picture URL
  @override
  final String? profilePictureUrl;

  /// Account type (personal, business, celebrity)
  @override
  final String accountType;

  /// Whether this account is verified
  @override
  @JsonKey()
  final bool isVerified;

  /// Whether this account is a billionaire account
  @override
  @JsonKey()
  final bool isBillionaire;

  /// Last time this account was used
  @override
  final DateTime? lastUsedAt;

  /// When this account was first saved
  @override
  final DateTime savedAt;

  /// Whether this is the currently active account
  @override
  @JsonKey()
  final bool isActive;

  /// Account metadata (followers, following, posts count)
  @override
  final AccountMetadata? metadata;

  /// Account settings and preferences
  final Map<String, dynamic>? _settings;

  /// Account settings and preferences
  @override
  Map<String, dynamic>? get settings {
    final value = _settings;
    if (value == null) return null;
    if (_settings is EqualUnmodifiableMapView) return _settings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  /// Device-specific information
  @override
  final String? deviceId;

  /// Last known IP address (for security)
  @override
  final String? lastKnownIp;

  @override
  String toString() {
    return 'SavedAccountModel(id: $id, firebaseUid: $firebaseUid, email: $email, displayName: $displayName, username: $username, profilePictureUrl: $profilePictureUrl, accountType: $accountType, isVerified: $isVerified, isBillionaire: $isBillionaire, lastUsedAt: $lastUsedAt, savedAt: $savedAt, isActive: $isActive, metadata: $metadata, settings: $settings, deviceId: $deviceId, lastKnownIp: $lastKnownIp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SavedAccountModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.firebaseUid, firebaseUid) ||
                other.firebaseUid == firebaseUid) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.profilePictureUrl, profilePictureUrl) ||
                other.profilePictureUrl == profilePictureUrl) &&
            (identical(other.accountType, accountType) ||
                other.accountType == accountType) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.isBillionaire, isBillionaire) ||
                other.isBillionaire == isBillionaire) &&
            (identical(other.lastUsedAt, lastUsedAt) ||
                other.lastUsedAt == lastUsedAt) &&
            (identical(other.savedAt, savedAt) || other.savedAt == savedAt) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.metadata, metadata) ||
                other.metadata == metadata) &&
            const DeepCollectionEquality().equals(other._settings, _settings) &&
            (identical(other.deviceId, deviceId) ||
                other.deviceId == deviceId) &&
            (identical(other.lastKnownIp, lastKnownIp) ||
                other.lastKnownIp == lastKnownIp));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    firebaseUid,
    email,
    displayName,
    username,
    profilePictureUrl,
    accountType,
    isVerified,
    isBillionaire,
    lastUsedAt,
    savedAt,
    isActive,
    metadata,
    const DeepCollectionEquality().hash(_settings),
    deviceId,
    lastKnownIp,
  );

  /// Create a copy of SavedAccountModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SavedAccountModelImplCopyWith<_$SavedAccountModelImpl> get copyWith =>
      __$$SavedAccountModelImplCopyWithImpl<_$SavedAccountModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$SavedAccountModelImplToJson(this);
  }
}

abstract class _SavedAccountModel implements SavedAccountModel {
  const factory _SavedAccountModel({
    required final String id,
    required final String firebaseUid,
    required final String email,
    required final String displayName,
    required final String username,
    final String? profilePictureUrl,
    required final String accountType,
    final bool isVerified,
    final bool isBillionaire,
    final DateTime? lastUsedAt,
    required final DateTime savedAt,
    final bool isActive,
    final AccountMetadata? metadata,
    final Map<String, dynamic>? settings,
    final String? deviceId,
    final String? lastKnownIp,
  }) = _$SavedAccountModelImpl;

  factory _SavedAccountModel.fromJson(Map<String, dynamic> json) =
      _$SavedAccountModelImpl.fromJson;

  /// Unique identifier for this saved account
  @override
  String get id;

  /// Firebase user ID
  @override
  String get firebaseUid;

  /// User's email address
  @override
  String get email;

  /// Display name
  @override
  String get displayName;

  /// Username
  @override
  String get username;

  /// Profile picture URL
  @override
  String? get profilePictureUrl;

  /// Account type (personal, business, celebrity)
  @override
  String get accountType;

  /// Whether this account is verified
  @override
  bool get isVerified;

  /// Whether this account is a billionaire account
  @override
  bool get isBillionaire;

  /// Last time this account was used
  @override
  DateTime? get lastUsedAt;

  /// When this account was first saved
  @override
  DateTime get savedAt;

  /// Whether this is the currently active account
  @override
  bool get isActive;

  /// Account metadata (followers, following, posts count)
  @override
  AccountMetadata? get metadata;

  /// Account settings and preferences
  @override
  Map<String, dynamic>? get settings;

  /// Device-specific information
  @override
  String? get deviceId;

  /// Last known IP address (for security)
  @override
  String? get lastKnownIp;

  /// Create a copy of SavedAccountModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SavedAccountModelImplCopyWith<_$SavedAccountModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AccountMetadata _$AccountMetadataFromJson(Map<String, dynamic> json) {
  return _AccountMetadata.fromJson(json);
}

/// @nodoc
mixin _$AccountMetadata {
  int get followersCount => throw _privateConstructorUsedError;
  int get followingCount => throw _privateConstructorUsedError;
  int get postsCount => throw _privateConstructorUsedError;
  int get storiesCount => throw _privateConstructorUsedError;
  String? get bio => throw _privateConstructorUsedError;
  String? get location => throw _privateConstructorUsedError;
  String? get website => throw _privateConstructorUsedError;

  /// Serializes this AccountMetadata to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AccountMetadata
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AccountMetadataCopyWith<AccountMetadata> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountMetadataCopyWith<$Res> {
  factory $AccountMetadataCopyWith(
    AccountMetadata value,
    $Res Function(AccountMetadata) then,
  ) = _$AccountMetadataCopyWithImpl<$Res, AccountMetadata>;
  @useResult
  $Res call({
    int followersCount,
    int followingCount,
    int postsCount,
    int storiesCount,
    String? bio,
    String? location,
    String? website,
  });
}

/// @nodoc
class _$AccountMetadataCopyWithImpl<$Res, $Val extends AccountMetadata>
    implements $AccountMetadataCopyWith<$Res> {
  _$AccountMetadataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AccountMetadata
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? followersCount = null,
    Object? followingCount = null,
    Object? postsCount = null,
    Object? storiesCount = null,
    Object? bio = freezed,
    Object? location = freezed,
    Object? website = freezed,
  }) {
    return _then(
      _value.copyWith(
            followersCount: null == followersCount
                ? _value.followersCount
                : followersCount // ignore: cast_nullable_to_non_nullable
                      as int,
            followingCount: null == followingCount
                ? _value.followingCount
                : followingCount // ignore: cast_nullable_to_non_nullable
                      as int,
            postsCount: null == postsCount
                ? _value.postsCount
                : postsCount // ignore: cast_nullable_to_non_nullable
                      as int,
            storiesCount: null == storiesCount
                ? _value.storiesCount
                : storiesCount // ignore: cast_nullable_to_non_nullable
                      as int,
            bio: freezed == bio
                ? _value.bio
                : bio // ignore: cast_nullable_to_non_nullable
                      as String?,
            location: freezed == location
                ? _value.location
                : location // ignore: cast_nullable_to_non_nullable
                      as String?,
            website: freezed == website
                ? _value.website
                : website // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$AccountMetadataImplCopyWith<$Res>
    implements $AccountMetadataCopyWith<$Res> {
  factory _$$AccountMetadataImplCopyWith(
    _$AccountMetadataImpl value,
    $Res Function(_$AccountMetadataImpl) then,
  ) = __$$AccountMetadataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int followersCount,
    int followingCount,
    int postsCount,
    int storiesCount,
    String? bio,
    String? location,
    String? website,
  });
}

/// @nodoc
class __$$AccountMetadataImplCopyWithImpl<$Res>
    extends _$AccountMetadataCopyWithImpl<$Res, _$AccountMetadataImpl>
    implements _$$AccountMetadataImplCopyWith<$Res> {
  __$$AccountMetadataImplCopyWithImpl(
    _$AccountMetadataImpl _value,
    $Res Function(_$AccountMetadataImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AccountMetadata
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? followersCount = null,
    Object? followingCount = null,
    Object? postsCount = null,
    Object? storiesCount = null,
    Object? bio = freezed,
    Object? location = freezed,
    Object? website = freezed,
  }) {
    return _then(
      _$AccountMetadataImpl(
        followersCount: null == followersCount
            ? _value.followersCount
            : followersCount // ignore: cast_nullable_to_non_nullable
                  as int,
        followingCount: null == followingCount
            ? _value.followingCount
            : followingCount // ignore: cast_nullable_to_non_nullable
                  as int,
        postsCount: null == postsCount
            ? _value.postsCount
            : postsCount // ignore: cast_nullable_to_non_nullable
                  as int,
        storiesCount: null == storiesCount
            ? _value.storiesCount
            : storiesCount // ignore: cast_nullable_to_non_nullable
                  as int,
        bio: freezed == bio
            ? _value.bio
            : bio // ignore: cast_nullable_to_non_nullable
                  as String?,
        location: freezed == location
            ? _value.location
            : location // ignore: cast_nullable_to_non_nullable
                  as String?,
        website: freezed == website
            ? _value.website
            : website // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$AccountMetadataImpl implements _AccountMetadata {
  const _$AccountMetadataImpl({
    this.followersCount = 0,
    this.followingCount = 0,
    this.postsCount = 0,
    this.storiesCount = 0,
    this.bio,
    this.location,
    this.website,
  });

  factory _$AccountMetadataImpl.fromJson(Map<String, dynamic> json) =>
      _$$AccountMetadataImplFromJson(json);

  @override
  @JsonKey()
  final int followersCount;
  @override
  @JsonKey()
  final int followingCount;
  @override
  @JsonKey()
  final int postsCount;
  @override
  @JsonKey()
  final int storiesCount;
  @override
  final String? bio;
  @override
  final String? location;
  @override
  final String? website;

  @override
  String toString() {
    return 'AccountMetadata(followersCount: $followersCount, followingCount: $followingCount, postsCount: $postsCount, storiesCount: $storiesCount, bio: $bio, location: $location, website: $website)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountMetadataImpl &&
            (identical(other.followersCount, followersCount) ||
                other.followersCount == followersCount) &&
            (identical(other.followingCount, followingCount) ||
                other.followingCount == followingCount) &&
            (identical(other.postsCount, postsCount) ||
                other.postsCount == postsCount) &&
            (identical(other.storiesCount, storiesCount) ||
                other.storiesCount == storiesCount) &&
            (identical(other.bio, bio) || other.bio == bio) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.website, website) || other.website == website));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    followersCount,
    followingCount,
    postsCount,
    storiesCount,
    bio,
    location,
    website,
  );

  /// Create a copy of AccountMetadata
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountMetadataImplCopyWith<_$AccountMetadataImpl> get copyWith =>
      __$$AccountMetadataImplCopyWithImpl<_$AccountMetadataImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$AccountMetadataImplToJson(this);
  }
}

abstract class _AccountMetadata implements AccountMetadata {
  const factory _AccountMetadata({
    final int followersCount,
    final int followingCount,
    final int postsCount,
    final int storiesCount,
    final String? bio,
    final String? location,
    final String? website,
  }) = _$AccountMetadataImpl;

  factory _AccountMetadata.fromJson(Map<String, dynamic> json) =
      _$AccountMetadataImpl.fromJson;

  @override
  int get followersCount;
  @override
  int get followingCount;
  @override
  int get postsCount;
  @override
  int get storiesCount;
  @override
  String? get bio;
  @override
  String? get location;
  @override
  String? get website;

  /// Create a copy of AccountMetadata
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountMetadataImplCopyWith<_$AccountMetadataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AccountCredentials _$AccountCredentialsFromJson(Map<String, dynamic> json) {
  return _AccountCredentials.fromJson(json);
}

/// @nodoc
mixin _$AccountCredentials {
  /// Account ID this credential belongs to
  String get accountId => throw _privateConstructorUsedError;

  /// Encrypted email
  String get encryptedEmail => throw _privateConstructorUsedError;

  /// Encrypted password (optional - for remember me functionality)
  String? get encryptedPassword => throw _privateConstructorUsedError;

  /// Firebase refresh token (encrypted)
  String? get encryptedRefreshToken => throw _privateConstructorUsedError;

  /// When credentials were last updated
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// Whether to remember password for this account
  bool get rememberPassword => throw _privateConstructorUsedError;

  /// Encryption key identifier
  String get keyId => throw _privateConstructorUsedError;

  /// Credential expiry (for security)
  DateTime? get expiresAt => throw _privateConstructorUsedError;

  /// Serializes this AccountCredentials to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AccountCredentials
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AccountCredentialsCopyWith<AccountCredentials> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountCredentialsCopyWith<$Res> {
  factory $AccountCredentialsCopyWith(
    AccountCredentials value,
    $Res Function(AccountCredentials) then,
  ) = _$AccountCredentialsCopyWithImpl<$Res, AccountCredentials>;
  @useResult
  $Res call({
    String accountId,
    String encryptedEmail,
    String? encryptedPassword,
    String? encryptedRefreshToken,
    DateTime updatedAt,
    bool rememberPassword,
    String keyId,
    DateTime? expiresAt,
  });
}

/// @nodoc
class _$AccountCredentialsCopyWithImpl<$Res, $Val extends AccountCredentials>
    implements $AccountCredentialsCopyWith<$Res> {
  _$AccountCredentialsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AccountCredentials
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accountId = null,
    Object? encryptedEmail = null,
    Object? encryptedPassword = freezed,
    Object? encryptedRefreshToken = freezed,
    Object? updatedAt = null,
    Object? rememberPassword = null,
    Object? keyId = null,
    Object? expiresAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            accountId: null == accountId
                ? _value.accountId
                : accountId // ignore: cast_nullable_to_non_nullable
                      as String,
            encryptedEmail: null == encryptedEmail
                ? _value.encryptedEmail
                : encryptedEmail // ignore: cast_nullable_to_non_nullable
                      as String,
            encryptedPassword: freezed == encryptedPassword
                ? _value.encryptedPassword
                : encryptedPassword // ignore: cast_nullable_to_non_nullable
                      as String?,
            encryptedRefreshToken: freezed == encryptedRefreshToken
                ? _value.encryptedRefreshToken
                : encryptedRefreshToken // ignore: cast_nullable_to_non_nullable
                      as String?,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            rememberPassword: null == rememberPassword
                ? _value.rememberPassword
                : rememberPassword // ignore: cast_nullable_to_non_nullable
                      as bool,
            keyId: null == keyId
                ? _value.keyId
                : keyId // ignore: cast_nullable_to_non_nullable
                      as String,
            expiresAt: freezed == expiresAt
                ? _value.expiresAt
                : expiresAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$AccountCredentialsImplCopyWith<$Res>
    implements $AccountCredentialsCopyWith<$Res> {
  factory _$$AccountCredentialsImplCopyWith(
    _$AccountCredentialsImpl value,
    $Res Function(_$AccountCredentialsImpl) then,
  ) = __$$AccountCredentialsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String accountId,
    String encryptedEmail,
    String? encryptedPassword,
    String? encryptedRefreshToken,
    DateTime updatedAt,
    bool rememberPassword,
    String keyId,
    DateTime? expiresAt,
  });
}

/// @nodoc
class __$$AccountCredentialsImplCopyWithImpl<$Res>
    extends _$AccountCredentialsCopyWithImpl<$Res, _$AccountCredentialsImpl>
    implements _$$AccountCredentialsImplCopyWith<$Res> {
  __$$AccountCredentialsImplCopyWithImpl(
    _$AccountCredentialsImpl _value,
    $Res Function(_$AccountCredentialsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AccountCredentials
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accountId = null,
    Object? encryptedEmail = null,
    Object? encryptedPassword = freezed,
    Object? encryptedRefreshToken = freezed,
    Object? updatedAt = null,
    Object? rememberPassword = null,
    Object? keyId = null,
    Object? expiresAt = freezed,
  }) {
    return _then(
      _$AccountCredentialsImpl(
        accountId: null == accountId
            ? _value.accountId
            : accountId // ignore: cast_nullable_to_non_nullable
                  as String,
        encryptedEmail: null == encryptedEmail
            ? _value.encryptedEmail
            : encryptedEmail // ignore: cast_nullable_to_non_nullable
                  as String,
        encryptedPassword: freezed == encryptedPassword
            ? _value.encryptedPassword
            : encryptedPassword // ignore: cast_nullable_to_non_nullable
                  as String?,
        encryptedRefreshToken: freezed == encryptedRefreshToken
            ? _value.encryptedRefreshToken
            : encryptedRefreshToken // ignore: cast_nullable_to_non_nullable
                  as String?,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        rememberPassword: null == rememberPassword
            ? _value.rememberPassword
            : rememberPassword // ignore: cast_nullable_to_non_nullable
                  as bool,
        keyId: null == keyId
            ? _value.keyId
            : keyId // ignore: cast_nullable_to_non_nullable
                  as String,
        expiresAt: freezed == expiresAt
            ? _value.expiresAt
            : expiresAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$AccountCredentialsImpl implements _AccountCredentials {
  const _$AccountCredentialsImpl({
    required this.accountId,
    required this.encryptedEmail,
    this.encryptedPassword,
    this.encryptedRefreshToken,
    required this.updatedAt,
    this.rememberPassword = false,
    required this.keyId,
    this.expiresAt,
  });

  factory _$AccountCredentialsImpl.fromJson(Map<String, dynamic> json) =>
      _$$AccountCredentialsImplFromJson(json);

  /// Account ID this credential belongs to
  @override
  final String accountId;

  /// Encrypted email
  @override
  final String encryptedEmail;

  /// Encrypted password (optional - for remember me functionality)
  @override
  final String? encryptedPassword;

  /// Firebase refresh token (encrypted)
  @override
  final String? encryptedRefreshToken;

  /// When credentials were last updated
  @override
  final DateTime updatedAt;

  /// Whether to remember password for this account
  @override
  @JsonKey()
  final bool rememberPassword;

  /// Encryption key identifier
  @override
  final String keyId;

  /// Credential expiry (for security)
  @override
  final DateTime? expiresAt;

  @override
  String toString() {
    return 'AccountCredentials(accountId: $accountId, encryptedEmail: $encryptedEmail, encryptedPassword: $encryptedPassword, encryptedRefreshToken: $encryptedRefreshToken, updatedAt: $updatedAt, rememberPassword: $rememberPassword, keyId: $keyId, expiresAt: $expiresAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountCredentialsImpl &&
            (identical(other.accountId, accountId) ||
                other.accountId == accountId) &&
            (identical(other.encryptedEmail, encryptedEmail) ||
                other.encryptedEmail == encryptedEmail) &&
            (identical(other.encryptedPassword, encryptedPassword) ||
                other.encryptedPassword == encryptedPassword) &&
            (identical(other.encryptedRefreshToken, encryptedRefreshToken) ||
                other.encryptedRefreshToken == encryptedRefreshToken) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.rememberPassword, rememberPassword) ||
                other.rememberPassword == rememberPassword) &&
            (identical(other.keyId, keyId) || other.keyId == keyId) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    accountId,
    encryptedEmail,
    encryptedPassword,
    encryptedRefreshToken,
    updatedAt,
    rememberPassword,
    keyId,
    expiresAt,
  );

  /// Create a copy of AccountCredentials
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountCredentialsImplCopyWith<_$AccountCredentialsImpl> get copyWith =>
      __$$AccountCredentialsImplCopyWithImpl<_$AccountCredentialsImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$AccountCredentialsImplToJson(this);
  }
}

abstract class _AccountCredentials implements AccountCredentials {
  const factory _AccountCredentials({
    required final String accountId,
    required final String encryptedEmail,
    final String? encryptedPassword,
    final String? encryptedRefreshToken,
    required final DateTime updatedAt,
    final bool rememberPassword,
    required final String keyId,
    final DateTime? expiresAt,
  }) = _$AccountCredentialsImpl;

  factory _AccountCredentials.fromJson(Map<String, dynamic> json) =
      _$AccountCredentialsImpl.fromJson;

  /// Account ID this credential belongs to
  @override
  String get accountId;

  /// Encrypted email
  @override
  String get encryptedEmail;

  /// Encrypted password (optional - for remember me functionality)
  @override
  String? get encryptedPassword;

  /// Firebase refresh token (encrypted)
  @override
  String? get encryptedRefreshToken;

  /// When credentials were last updated
  @override
  DateTime get updatedAt;

  /// Whether to remember password for this account
  @override
  bool get rememberPassword;

  /// Encryption key identifier
  @override
  String get keyId;

  /// Credential expiry (for security)
  @override
  DateTime? get expiresAt;

  /// Create a copy of AccountCredentials
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountCredentialsImplCopyWith<_$AccountCredentialsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
