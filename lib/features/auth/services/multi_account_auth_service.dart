import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:billionaires_social/features/auth/models/saved_account_model.dart';
import 'package:billionaires_social/features/auth/models/multi_account_session.dart';
import 'package:billionaires_social/features/auth/services/multi_account_storage_service.dart';
import 'package:billionaires_social/core/services/firebase_service.dart';
import 'package:billionaires_social/core/services/security_service.dart';

import 'package:billionaires_social/core/security/biometric_auth_service.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/services/n8n_firebase_integration.dart';
import 'package:billionaires_social/services/n8n_service.dart' as n8n;

/// Multi-account authentication service that handles switching between different Firebase users
/// Manages secure authentication, session management, and account switching
class MultiAccountAuthService {
  static final MultiAccountAuthService _instance =
      MultiAccountAuthService._internal();
  factory MultiAccountAuthService() => _instance;
  MultiAccountAuthService._internal();

  // Services
  final MultiAccountStorageService _storageService =
      MultiAccountStorageService();
  final FirebaseService _firebaseService = getIt<FirebaseService>();
  final SecurityService _securityService = getIt<SecurityService>();
  final BiometricAuthService _biometricService = BiometricAuthService();
  final n8n.N8nService _n8nService = n8n.N8nService();
  final N8nFirebaseIntegration _n8nIntegration = N8nFirebaseIntegration(
    n8nService: n8n.N8nService(),
  );

  // Current session state
  MultiAccountSession? _currentSession;
  StreamController<MultiAccountSession>? _sessionController;

  /// Initialize the multi-account authentication service
  Future<void> initialize() async {
    try {
      await _storageService.initialize();
      await _biometricService.initialize();

      // Initialize session controller
      _sessionController = StreamController<MultiAccountSession>.broadcast();

      // Load existing session if available
      await _loadExistingSession();

      debugPrint('✅ MultiAccountAuth: Initialized successfully');
    } catch (e) {
      debugPrint('❌ MultiAccountAuth: Initialization failed: $e');
      rethrow;
    }
  }

  /// Stream of session changes
  Stream<MultiAccountSession> get sessionStream {
    return _sessionController?.stream ?? const Stream.empty();
  }

  /// Get current session
  MultiAccountSession? get currentSession => _currentSession;

  /// Add a new account to the multi-account system
  Future<AccountSwitchResult> addAccount({
    required String email,
    required String password,
    bool rememberPassword = false,
    bool setBiometricAuth = false,
  }) async {
    final stopwatch = Stopwatch()..start();

    try {
      debugPrint('🔄 MultiAccountAuth: Adding new account: $email');

      // Validate input
      if (email.isEmpty || password.isEmpty) {
        throw Exception('Email and password are required');
      }

      // Check if account already exists
      final existingAccounts = await _storageService.getSavedAccounts();
      final existingAccount = existingAccounts
          .where((a) => a.email == email)
          .firstOrNull;

      if (existingAccount != null) {
        throw Exception('Account already exists in saved accounts');
      }

      // Attempt to sign in with the new account
      await _firebaseService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      final user = _firebaseService.currentUser;
      if (user == null) {
        throw Exception('Authentication failed - no user returned');
      }

      // Get user profile from Firestore
      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .get();

      if (!userDoc.exists) {
        throw Exception('User profile not found');
      }

      final userData = userDoc.data()!;

      // Create saved account model
      final savedAccount = SavedAccountModel(
        id: user.uid,
        firebaseUid: user.uid,
        email: email,
        displayName: userData['name'] ?? user.displayName ?? '',
        username: userData['username'] ?? email.split('@')[0],
        profilePictureUrl: userData['profilePictureUrl'],
        accountType: userData['accountType'] ?? 'personal',
        isVerified: userData['isVerified'] ?? false,
        isBillionaire: userData['isBillionaire'] ?? false,
        savedAt: DateTime.now(),
        lastUsedAt: DateTime.now(),
        isActive: true,
        metadata: AccountMetadata(
          followersCount: userData['followerCount'] ?? 0,
          followingCount: userData['followingCount'] ?? 0,
          postsCount: userData['postCount'] ?? 0,
          bio: userData['bio'],
          location: userData['location'],
          website: userData['website'],
        ),
      );

      // Save account to storage
      await _storageService.saveAccount(savedAccount);

      // Save credentials if requested
      if (rememberPassword) {
        final credentials = AccountCredentials(
          accountId: user.uid,
          encryptedEmail: await _encryptData(email),
          encryptedPassword: await _encryptData(password),
          updatedAt: DateTime.now(),
          rememberPassword: true,
          keyId: '', // Will be set by storage service
        );

        await _storageService.saveAccountCredentials(credentials);
      }

      // Set as active account
      await _storageService.setActiveAccount(user.uid);

      // Update session
      await _updateCurrentSession();

      // Create switch history entry
      final historyEntry = AccountSwitchHistory(
        id: _generateId(),
        toAccountId: user.uid,
        switchedAt: DateTime.now(),
        switchMethod: 'manual_add',
        successful: true,
        switchDuration: stopwatch.elapsed,
      );

      await _storageService.saveAccountSwitchHistory(historyEntry);

      stopwatch.stop();

      debugPrint('✅ MultiAccountAuth: Successfully added account: $email');

      return AccountSwitchResult(
        success: true,
        switchedToAccount: savedAccount,
        switchDuration: stopwatch.elapsed,
        historyEntry: historyEntry,
      );
    } catch (e) {
      stopwatch.stop();

      debugPrint('❌ MultiAccountAuth: Failed to add account: $e');

      return AccountSwitchResult(
        success: false,
        errorMessage: e.toString(),
        errorCode: 'ADD_ACCOUNT_FAILED',
        switchDuration: stopwatch.elapsed,
      );
    }
  }

  /// Switch to an existing saved account
  Future<AccountSwitchResult> switchToAccount(
    String accountId, {
    bool requireBiometric = false,
  }) async {
    final stopwatch = Stopwatch()..start();

    try {
      debugPrint('🔄 MultiAccountAuth: Switching to account: $accountId');

      // Get target account
      final savedAccounts = await _storageService.getSavedAccounts();
      final targetAccount = savedAccounts
          .where((a) => a.id == accountId)
          .firstOrNull;

      if (targetAccount == null) {
        throw Exception('Target account not found');
      }

      // Check if already active
      final currentActiveId = await _storageService.getActiveAccountId();
      if (currentActiveId == accountId) {
        debugPrint('ℹ️ MultiAccountAuth: Account already active');
        return AccountSwitchResult(
          success: true,
          switchedToAccount: targetAccount,
          switchDuration: stopwatch.elapsed,
        );
      }

      // Perform biometric authentication if required
      if (requireBiometric) {
        final biometricResult = await _biometricService.authenticate(
          reason: 'Authenticate to switch to ${targetAccount.displayName}',
        );

        if (!biometricResult.success) {
          throw Exception(
            'Biometric authentication failed: ${biometricResult.errorMessage}',
          );
        }
      }

      // Get stored credentials
      final credentials = await _storageService.getAccountCredentials(
        accountId,
      );

      if (credentials == null || !credentials.rememberPassword) {
        throw Exception('No stored credentials found for account');
      }

      // Decrypt credentials
      final email = await _decryptData(credentials.encryptedEmail);
      final password = credentials.encryptedPassword != null
          ? await _decryptData(credentials.encryptedPassword!)
          : null;

      if (password == null) {
        throw Exception('No stored password found for account');
      }

      // Perform comprehensive logout from current session
      // Note: We'll need a WidgetRef for this, so we'll create a simplified version
      await _performSimpleLogout();

      // Sign in with target account
      await _firebaseService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Verify the user matches expected account
      final user = _firebaseService.currentUser;
      if (user == null || user.uid != accountId) {
        throw Exception('Authentication failed or user mismatch');
      }

      // Set as active account
      await _storageService.setActiveAccount(accountId);

      // Update session
      await _updateCurrentSession();

      // Create switch history entry
      final historyEntry = AccountSwitchHistory(
        id: _generateId(),
        fromAccountId: currentActiveId,
        toAccountId: accountId,
        switchedAt: DateTime.now(),
        switchMethod: requireBiometric ? 'biometric' : 'manual',
        successful: true,
        switchDuration: stopwatch.elapsed,
      );

      await _storageService.saveAccountSwitchHistory(historyEntry);

      stopwatch.stop();

      debugPrint(
        '✅ MultiAccountAuth: Successfully switched to account: ${targetAccount.email}',
      );

      // Trigger n8n workflows for account switch
      try {
        await _triggerN8nAccountSwitch(
          fromAccountId: currentActiveId,
          toAccountId: accountId,
          targetAccount: targetAccount,
          switchMethod: requireBiometric ? 'biometric' : 'manual',
        );
      } catch (n8nError) {
        debugPrint('⚠️ MultiAccountAuth: n8n workflow failed: $n8nError');
        // Don't fail the account switch if n8n fails
      }

      return AccountSwitchResult(
        success: true,
        switchedToAccount: targetAccount,
        switchDuration: stopwatch.elapsed,
        historyEntry: historyEntry,
        usedBiometricAuth: requireBiometric,
      );
    } catch (e) {
      stopwatch.stop();

      debugPrint('❌ MultiAccountAuth: Failed to switch account: $e');

      // Create failed history entry
      final historyEntry = AccountSwitchHistory(
        id: _generateId(),
        toAccountId: accountId,
        switchedAt: DateTime.now(),
        switchMethod: requireBiometric ? 'biometric' : 'manual',
        successful: false,
        errorMessage: e.toString(),
        switchDuration: stopwatch.elapsed,
      );

      await _storageService.saveAccountSwitchHistory(historyEntry);

      return AccountSwitchResult(
        success: false,
        errorMessage: e.toString(),
        errorCode: 'SWITCH_ACCOUNT_FAILED',
        switchDuration: stopwatch.elapsed,
        historyEntry: historyEntry,
      );
    }
  }

  /// Remove an account from saved accounts
  Future<bool> removeAccount(String accountId) async {
    try {
      debugPrint('🔄 MultiAccountAuth: Removing account: $accountId');

      // Check if it's the currently active account
      final activeAccountId = await _storageService.getActiveAccountId();
      final isActiveAccount = activeAccountId == accountId;

      // Remove from storage
      await _storageService.removeAccount(accountId);

      // If it was the active account, we need to handle the session
      if (isActiveAccount) {
        // Sign out from Firebase
        await _firebaseService.signOut();

        // Clear current session
        _currentSession = null;
        _sessionController?.add(const MultiAccountSession());

        // Check if there are other accounts to switch to
        final remainingAccounts = await _storageService.getSavedAccounts();
        if (remainingAccounts.isNotEmpty) {
          // Set the most recently used account as active
          final mostRecent = remainingAccounts.reduce(
            (a, b) =>
                (a.lastUsedAt?.isAfter(b.lastUsedAt ?? DateTime(0)) ?? false)
                ? a
                : b,
          );

          // Switch to the most recent account
          await switchToAccount(mostRecent.id);
        }
      }

      debugPrint(
        '✅ MultiAccountAuth: Successfully removed account: $accountId',
      );
      return true;
    } catch (e) {
      debugPrint('❌ MultiAccountAuth: Failed to remove account: $e');
      return false;
    }
  }

  /// Get all saved accounts
  Future<List<SavedAccountModel>> getSavedAccounts() async {
    return await _storageService.getSavedAccounts();
  }

  /// Get account switch history
  Future<List<AccountSwitchHistory>> getSwitchHistory() async {
    return await _storageService.getAccountSwitchHistory();
  }

  /// Validate an account's credentials and status
  Future<AccountValidationResult> validateAccount(String accountId) async {
    try {
      final savedAccounts = await _storageService.getSavedAccounts();
      final account = savedAccounts.where((a) => a.id == accountId).firstOrNull;

      if (account == null) {
        return const AccountValidationResult(
          isValid: false,
          issues: ['Account not found'],
        );
      }

      final credentials = await _storageService.getAccountCredentials(
        accountId,
      );

      if (credentials == null) {
        return const AccountValidationResult(
          isValid: false,
          issues: ['No credentials found'],
          needsCredentialRefresh: true,
        );
      }

      // Check if credentials are expired
      if (credentials.expiresAt != null &&
          DateTime.now().isAfter(credentials.expiresAt!)) {
        return const AccountValidationResult(
          isValid: false,
          issues: ['Credentials expired'],
          needsCredentialRefresh: true,
        );
      }

      return AccountValidationResult(
        isValid: true,
        lastValidatedAt: DateTime.now(),
      );
    } catch (e) {
      return AccountValidationResult(
        isValid: false,
        issues: ['Validation error: $e'],
      );
    }
  }

  /// Clear all account data and sign out
  Future<void> clearAllAccounts() async {
    try {
      debugPrint('🔄 MultiAccountAuth: Clearing all accounts');

      // Sign out from Firebase
      await _firebaseService.signOut();

      // Clear all storage
      await _storageService.clearAllAccountData();

      // Clear session
      _currentSession = null;
      _sessionController?.add(const MultiAccountSession());

      debugPrint('✅ MultiAccountAuth: Cleared all accounts');
    } catch (e) {
      debugPrint('❌ MultiAccountAuth: Failed to clear accounts: $e');
      rethrow;
    }
  }

  /// Dispose resources
  void dispose() {
    _sessionController?.close();
    _sessionController = null;
  }

  // Private helper methods

  /// Load existing session on startup
  Future<void> _loadExistingSession() async {
    try {
      final activeAccount = await _storageService.getActiveAccount();
      final savedAccounts = await _storageService.getSavedAccounts();
      final quickSwitcherConfig = await _storageService
          .getQuickSwitcherConfig();

      _currentSession = MultiAccountSession(
        activeAccount: activeAccount,
        savedAccounts: savedAccounts,
        currentUserUid: _firebaseService.currentUser?.uid,
        sessionStartTime: DateTime.now(),
        lastActivityTime: DateTime.now(),
        isValid: activeAccount != null && _firebaseService.currentUser != null,
        quickSwitcherConfig: quickSwitcherConfig,
      );

      _sessionController?.add(_currentSession!);
    } catch (e) {
      debugPrint('❌ MultiAccountAuth: Failed to load existing session: $e');
    }
  }

  /// Update current session state
  Future<void> _updateCurrentSession() async {
    try {
      final activeAccount = await _storageService.getActiveAccount();
      final savedAccounts = await _storageService.getSavedAccounts();
      final quickSwitcherConfig = await _storageService
          .getQuickSwitcherConfig();

      _currentSession =
          _currentSession?.copyWith(
            activeAccount: activeAccount,
            savedAccounts: savedAccounts,
            currentUserUid: _firebaseService.currentUser?.uid,
            lastActivityTime: DateTime.now(),
            isValid:
                activeAccount != null && _firebaseService.currentUser != null,
            quickSwitcherConfig: quickSwitcherConfig,
          ) ??
          MultiAccountSession(
            activeAccount: activeAccount,
            savedAccounts: savedAccounts,
            currentUserUid: _firebaseService.currentUser?.uid,
            sessionStartTime: DateTime.now(),
            lastActivityTime: DateTime.now(),
            isValid:
                activeAccount != null && _firebaseService.currentUser != null,
            quickSwitcherConfig: quickSwitcherConfig,
          );

      _sessionController?.add(_currentSession!);
    } catch (e) {
      debugPrint('❌ MultiAccountAuth: Failed to update session: $e');
    }
  }

  /// Perform simple logout without WidgetRef
  Future<void> _performSimpleLogout() async {
    try {
      await _firebaseService.signOut();
      // Clear any cached data that doesn't require WidgetRef
      await _securityService.clearAllSecureTokens();
    } catch (e) {
      debugPrint('⚠️ MultiAccountAuth: Simple logout failed: $e');
    }
  }

  /// Encrypt sensitive data
  Future<String> _encryptData(String data) async {
    // Use security service for encryption
    return await _securityService.encryptSensitiveData(data);
  }

  /// Decrypt sensitive data
  Future<String> _decryptData(String encryptedData) async {
    // Use security service for decryption
    return await _securityService.decryptSensitiveData(encryptedData);
  }

  /// Generate unique ID
  String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  /// Trigger n8n workflows for account switch
  Future<void> _triggerN8nAccountSwitch({
    String? fromAccountId,
    required String toAccountId,
    required SavedAccountModel targetAccount,
    required String switchMethod,
  }) async {
    try {
      // Initialize n8n service if needed
      await _n8nService.initialize();

      // Check if n8n is enabled
      if (!await _n8nService.isEnabled) {
        debugPrint('ℹ️ MultiAccountAuth: n8n integration is disabled');
        return;
      }

      debugPrint(
        '🔄 MultiAccountAuth: Triggering n8n account switch workflows',
      );

      // Trigger account switch workflow
      final switchContext = {
        'switchMethod': switchMethod,
        'deviceInfo': 'mobile_app',
        'timestamp': DateTime.now().toIso8601String(),
        'accountType': targetAccount.accountType,
        'isVerified': targetAccount.isVerified,
        'isBillionaire': targetAccount.isBillionaire,
      };

      final switchResult = await _n8nService.triggerAccountSwitch(
        fromAccountId: fromAccountId ?? '',
        toAccountId: toAccountId,
        switchContext: switchContext,
      );

      if (switchResult.isSuccess) {
        debugPrint(
          '✅ MultiAccountAuth: n8n account switch workflow triggered successfully',
        );
      } else {
        debugPrint(
          '❌ MultiAccountAuth: n8n account switch workflow failed: ${switchResult.error}',
        );
      }

      // Trigger user engagement workflow
      final engagementResult = await _n8nService.triggerUserEngagement(
        eventType: 'account_switched',
        eventData: {
          'fromAccountId': fromAccountId,
          'toAccountId': toAccountId,
          'switchMethod': switchMethod,
          'accountType': targetAccount.accountType,
          'userDisplayName': targetAccount.displayName,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      if (engagementResult.isSuccess) {
        debugPrint(
          '✅ MultiAccountAuth: n8n user engagement workflow triggered successfully',
        );
      } else {
        debugPrint(
          '❌ MultiAccountAuth: n8n user engagement workflow failed: ${engagementResult.error}',
        );
      }

      // Create Firestore trigger for additional workflows
      await _n8nIntegration.createTrigger(
        triggerType: 'account_switch_complete',
        payload: {
          'fromAccountId': fromAccountId,
          'toAccountId': toAccountId,
          'targetAccount': {
            'id': targetAccount.id,
            'displayName': targetAccount.displayName,
            'email': targetAccount.email,
            'accountType': targetAccount.accountType,
            'isVerified': targetAccount.isVerified,
            'isBillionaire': targetAccount.isBillionaire,
          },
          'switchMethod': switchMethod,
          'completedAt': DateTime.now().toIso8601String(),
        },
      );

      debugPrint('✅ MultiAccountAuth: n8n integration completed successfully');
    } catch (e) {
      debugPrint('❌ MultiAccountAuth: n8n integration error: $e');
      // Don't rethrow - we don't want n8n failures to break account switching
    }
  }
}
