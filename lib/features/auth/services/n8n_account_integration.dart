import 'dart:developer' as developer;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/saved_account_model.dart';
import '../models/multi_account_session.dart';
import '../../../services/n8n_firebase_integration.dart';
import '../../../services/n8n_service.dart';

/// Integration service for n8n workflows with account management
class N8nAccountIntegration {
  final N8nFirebaseIntegration _firebaseIntegration;
  final N8nService _n8nService;

  N8nAccountIntegration({
    required N8nFirebaseIntegration firebaseIntegration,
    required N8nService n8nService,
  }) : _firebaseIntegration = firebaseIntegration,
       _n8nService = n8nService;

  /// Trigger account switch workflow in n8n
  Future<void> triggerAccountSwitchWorkflow({
    required String fromAccountId,
    required String toAccountId,
    required Map<String, dynamic> switchContext,
  }) async {
    try {
      // Create trigger in Firestore for n8n to process
      await _firebaseIntegration.createTrigger(
        triggerType: 'account_switch',
        payload: {
          'fromAccountId': fromAccountId,
          'toAccountId': toAccountId,
          'context': switchContext,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      // Also trigger directly via n8n API for immediate processing
      await _n8nService.triggerAccountSwitch(
        fromAccountId: fromAccountId,
        toAccountId: toAccountId,
        switchContext: switchContext,
      );
    } catch (e) {
      developer.log(
        'Error triggering account switch workflow: $e',
        name: 'N8nAccountIntegration',
      );
      rethrow;
    }
  }

  /// Trigger user engagement workflow when account is switched
  Future<void> triggerUserEngagementWorkflow({
    required String userId,
    required String eventType,
    required Map<String, dynamic> eventData,
  }) async {
    try {
      await _n8nService.triggerUserEngagement(
        eventType: eventType,
        eventData: {'userId': userId, ...eventData},
      );
    } catch (e) {
      developer.log(
        'Error triggering user engagement workflow: $e',
        name: 'N8nAccountIntegration',
      );
    }
  }

  /// Schedule automated posts for newly switched account
  Future<void> scheduleAutomatedPosts({
    required SavedAccountModel account,
    required List<Map<String, dynamic>> postTemplates,
  }) async {
    try {
      for (final template in postTemplates) {
        final scheduledTime = DateTime.now().add(
          Duration(minutes: template['delayMinutes'] ?? 0),
        );

        await _n8nService.schedulePost(
          content: template['content'] ?? '',
          mediaUrls: (template['mediaUrls'] as List?)?.cast<String>() ?? [],
          scheduledTime: scheduledTime,
          platforms: (template['platforms'] as List?)?.cast<String>() ?? [],
          metadata: {
            'accountId': account.id,
            'accountType': account.accountType,
            'templateId': template['id'],
          },
        );
      }
    } catch (e) {
      developer.log(
        'Error scheduling automated posts: $e',
        name: 'N8nAccountIntegration',
      );
    }
  }

  /// Send notifications about account switch
  Future<void> sendAccountSwitchNotifications({
    required SavedAccountModel fromAccount,
    required SavedAccountModel toAccount,
    required bool success,
    String? errorMessage,
  }) async {
    try {
      final title = success
          ? 'Account Switch Successful'
          : 'Account Switch Failed';

      final body = success
          ? 'Successfully switched from ${fromAccount.displayName} to ${toAccount.displayName}'
          : 'Failed to switch accounts: ${errorMessage ?? 'Unknown error'}';

      await _n8nService.sendNotification(
        recipientId: toAccount.firebaseUid,
        title: title,
        body: body,
        data: {
          'type': 'account_switch',
          'fromAccountId': fromAccount.id,
          'toAccountId': toAccount.id,
          'success': success,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      developer.log(
        'Error sending account switch notifications: $e',
        name: 'N8nAccountIntegration',
      );
    }
  }

  /// Sync account preferences with n8n workflows
  Future<void> syncAccountPreferences({
    required SavedAccountModel account,
  }) async {
    try {
      await _firebaseIntegration.updateWorkflowPreferences(
        preferences: {
          'activeAccountId': account.id,
          'accountType': account.accountType,
          'isVerified': account.isVerified,
          'isBillionaire': account.isBillionaire,
          'autoPostEnabled': account.settings?['autoPostEnabled'] ?? false,
          'notificationsEnabled':
              account.settings?['notificationsEnabled'] ?? true,
          'lastSyncTime': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      developer.log(
        'Error syncing account preferences: $e',
        name: 'N8nAccountIntegration',
      );
    }
  }

  /// Handle account validation through n8n
  Future<AccountValidationResult> validateAccountThroughN8n({
    required SavedAccountModel account,
  }) async {
    try {
      // Trigger validation workflow
      final response = await _n8nService.triggerUserEngagement(
        eventType: 'account_validation',
        eventData: {
          'accountId': account.id,
          'firebaseUid': account.firebaseUid,
          'email': account.email,
          'accountType': account.accountType,
          'lastUsedAt': account.lastUsedAt?.toIso8601String(),
        },
      );

      if (response.isSuccess && response.data != null) {
        final result = response.data!;
        return AccountValidationResult(
          isValid: result.analytics?['isValid'] ?? false,
          issues: (result.analytics?['issues'] as List?)?.cast<String>() ?? [],
          needsCredentialRefresh:
              result.analytics?['needsCredentialRefresh'] ?? false,
          isLocked: result.analytics?['isLocked'] ?? false,
          statusMessage: result.analytics?['statusMessage'],
          lastValidatedAt: DateTime.now(),
          metadata: result.analytics,
        );
      } else {
        return AccountValidationResult(
          isValid: false,
          issues: ['Validation workflow failed'],
          statusMessage: response.error ?? 'Unknown validation error',
          lastValidatedAt: DateTime.now(),
        );
      }
    } catch (e) {
      return AccountValidationResult(
        isValid: false,
        issues: ['Validation error: $e'],
        statusMessage: 'Failed to validate account through n8n',
        lastValidatedAt: DateTime.now(),
      );
    }
  }

  /// Get account analytics from n8n workflows
  Future<Map<String, dynamic>?> getAccountAnalytics({
    required String accountId,
  }) async {
    try {
      final response = await _n8nService.triggerUserEngagement(
        eventType: 'get_account_analytics',
        eventData: {
          'accountId': accountId,
          'requestedAt': DateTime.now().toIso8601String(),
        },
      );

      if (response.isSuccess && response.data != null) {
        return response.data!.analytics;
      }
      return null;
    } catch (e) {
      developer.log(
        'Error getting account analytics: $e',
        name: 'N8nAccountIntegration',
      );
      return null;
    }
  }

  /// Setup automated workflows for new account
  Future<void> setupAutomatedWorkflows({
    required SavedAccountModel account,
  }) async {
    try {
      // Create trigger for setting up automated workflows
      await _firebaseIntegration.createTrigger(
        triggerType: 'setup_automation',
        payload: {
          'accountId': account.id,
          'accountType': account.accountType,
          'isVerified': account.isVerified,
          'isBillionaire': account.isBillionaire,
          'preferences': account.settings ?? {},
        },
      );
    } catch (e) {
      developer.log(
        'Error setting up automated workflows: $e',
        name: 'N8nAccountIntegration',
      );
    }
  }

  /// Clean up workflows when account is removed
  Future<void> cleanupAccountWorkflows({required String accountId}) async {
    try {
      await _firebaseIntegration.createTrigger(
        triggerType: 'cleanup_workflows',
        payload: {
          'accountId': accountId,
          'cleanupType': 'account_removal',
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      developer.log(
        'Error cleaning up account workflows: $e',
        name: 'N8nAccountIntegration',
      );
    }
  }
}

/// Riverpod provider for n8n account integration
final n8nAccountIntegrationProvider = Provider<N8nAccountIntegration>((ref) {
  final firebaseIntegration = ref.watch(n8nFirebaseIntegrationProvider);
  final n8nService = ref.watch(n8nServiceProvider);

  return N8nAccountIntegration(
    firebaseIntegration: firebaseIntegration,
    n8nService: n8nService,
  );
});

// Note: accountAnalyticsProvider is defined in multi_account_provider.dart to avoid conflicts

/// Provider for account validation
final accountValidationProvider =
    FutureProvider.family<AccountValidationResult, SavedAccountModel>((
      ref,
      account,
    ) {
      final integration = ref.watch(n8nAccountIntegrationProvider);
      return integration.validateAccountThroughN8n(account: account);
    });
