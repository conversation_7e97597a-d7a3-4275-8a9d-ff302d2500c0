import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/auth/models/multi_account_session.dart';
import 'package:billionaires_social/features/auth/services/multi_account_auth_service.dart';
import 'package:billionaires_social/features/auth/services/multi_account_storage_service.dart';
import 'package:billionaires_social/features/auth/providers/multi_account_provider.dart';

/// Simplified service that handles account switching
class AccountSwitchingService {
  static final AccountSwitchingService _instance =
      AccountSwitchingService._internal();
  factory AccountSwitchingService() => _instance;
  AccountSwitchingService._internal();

  // Services
  final MultiAccountAuthService _authService = MultiAccountAuthService();
  final MultiAccountStorageService _storageService =
      MultiAccountStorageService();

  // State management
  final StreamController<AccountSwitchProgress> _progressController =
      StreamController<AccountSwitchProgress>.broadcast();
  final Map<String, AccountSwitchState> _switchStates = {};

  /// Stream of account switch progress
  Stream<AccountSwitchProgress> get switchProgressStream =>
      _progressController.stream;

  /// Initialize the switching service
  Future<void> initialize() async {
    try {
      await _authService.initialize();
      await _storageService.initialize();
      debugPrint('✅ AccountSwitching: Service initialized');
    } catch (e) {
      debugPrint('❌ AccountSwitching: Initialization failed: $e');
      rethrow;
    }
  }

  /// Perform account switch
  Future<AccountSwitchResult> performSwitch({
    required String targetAccountId,
    required WidgetRef ref,
    bool requireBiometric = false,
  }) async {
    final switchId = _generateSwitchId();
    final stopwatch = Stopwatch()..start();

    try {
      debugPrint(
        '🔄 AccountSwitching: Starting switch to $targetAccountId',
      );

      // Step 1: Validate target account
      _emitProgress(
        switchId,
        AccountSwitchStep.validating,
        'Validating target account...',
      );
      
      final savedAccounts = await _authService.getSavedAccounts();
      final targetAccount = savedAccounts
          .where((a) => a.id == targetAccountId)
          .firstOrNull;

      if (targetAccount == null) {
        throw Exception('Target account not found');
      }

      // Step 2: Perform logout
      _emitProgress(
        switchId,
        AccountSwitchStep.loggingOut,
        'Logging out from current account...',
      );
      await _performLogout(ref);

      // Step 3: Switch to target account
      _emitProgress(
        switchId,
        AccountSwitchStep.authenticating,
        'Authenticating with target account...',
      );
      
      final authResult = await _authService.switchToAccount(
        targetAccountId,
        requireBiometric: requireBiometric,
      );

      if (!authResult.success) {
        throw Exception(
          'Authentication failed: ${authResult.errorMessage ?? 'Unknown error'}',
        );
      }

      // Step 4: Update app state
      _emitProgress(
        switchId,
        AccountSwitchStep.updatingState,
        'Updating application state...',
      );
      await _updateAppState(ref, targetAccountId);

      // Step 5: Finalize
      _emitProgress(
        switchId,
        AccountSwitchStep.finalizing,
        'Finalizing switch...',
      );
      await _finalizeSwitch(switchId, targetAccountId);

      stopwatch.stop();

      _emitProgress(
        switchId,
        AccountSwitchStep.completed,
        'Account switch completed successfully',
      );

      debugPrint(
        '✅ AccountSwitching: Switch completed in ${stopwatch.elapsed}',
      );

      return AccountSwitchResult(
        success: true,
        switchedToAccount: targetAccount,
        switchDuration: stopwatch.elapsed,
      );
    } catch (e) {
      stopwatch.stop();

      debugPrint('❌ AccountSwitching: Switch failed: $e');

      _emitProgress(switchId, AccountSwitchStep.failed, 'Switch failed: $e');

      // Cleanup failed switch state
      _switchStates.remove(switchId);

      return AccountSwitchResult(
        success: false,
        errorMessage: e.toString(),
        errorCode: 'SWITCH_FAILED',
        switchDuration: stopwatch.elapsed,
      );
    }
  }

  /// Perform logout from current account
  Future<void> _performLogout(WidgetRef ref) async {
    try {
      // Clear providers
      ref.invalidate(multiAccountSessionProvider);
      
      debugPrint('🧹 AccountSwitching: Logout completed');
    } catch (e) {
      debugPrint('⚠️ AccountSwitching: Logout had issues: $e');
    }
  }

  /// Update application state after account switch
  Future<void> _updateAppState(WidgetRef ref, String targetAccountId) async {
    try {
      // Refresh main providers
      ref.invalidate(multiAccountSessionProvider);
      ref.invalidate(multiAccountNotifierProvider);
      
      debugPrint(
        '🔄 AccountSwitching: App state updated for account $targetAccountId',
      );
    } catch (e) {
      debugPrint('⚠️ AccountSwitching: Failed to update some app state: $e');
    }
  }

  /// Finalize the account switch
  Future<void> _finalizeSwitch(String switchId, String targetAccountId) async {
    try {
      // Update last used time if the method exists
      try {
        await _storageService.updateLastUsedTime(targetAccountId);
      } catch (e) {
        // Method might not exist, that's okay
        debugPrint('Note: updateLastUsedTime method not available');
      }

      // Clean up switch state
      _switchStates.remove(switchId);

      debugPrint(
        '✅ AccountSwitching: Switch finalized for account $targetAccountId',
      );
    } catch (e) {
      debugPrint('⚠️ AccountSwitching: Finalization had issues: $e');
    }
  }

  void _emitProgress(String switchId, AccountSwitchStep step, String message) {
    _progressController.add(
      AccountSwitchProgress(
        switchId: switchId,
        step: step,
        message: message,
        timestamp: DateTime.now(),
      ),
    );
  }

  String _generateSwitchId() {
    return 'switch_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// Dispose resources
  void dispose() {
    _progressController.close();
    _switchStates.clear();
  }
}

// Data classes for switch management

class AccountSwitchState {
  final String switchId;
  final String targetAccountId;
  final DateTime startTime;
  final bool preserveDrafts;
  final bool preserveTemporaryData;

  AccountSwitchState({
    String? switchId,
    required this.targetAccountId,
    DateTime? startTime,
    this.preserveDrafts = true,
    this.preserveTemporaryData = true,
  }) : switchId = switchId ?? 'switch_${DateTime.now().millisecondsSinceEpoch}',
       startTime = startTime ?? DateTime.now();
}

class AccountSwitchProgress {
  final String switchId;
  final AccountSwitchStep step;
  final String message;
  final DateTime timestamp;

  AccountSwitchProgress({
    required this.switchId,
    required this.step,
    required this.message,
    required this.timestamp,
  });
}

enum AccountSwitchStep {
  validating,
  preservingData,
  loggingOut,
  authenticating,
  restoringData,
  updatingState,
  finalizing,
  completed,
  failed,
}
