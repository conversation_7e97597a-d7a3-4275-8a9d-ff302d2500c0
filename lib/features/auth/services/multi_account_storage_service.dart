import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:billionaires_social/features/auth/models/saved_account_model.dart';
import 'package:billionaires_social/features/auth/models/multi_account_session.dart';

/// Secure storage service for managing multiple user accounts
/// Handles encryption, secure storage, and account metadata management
class MultiAccountStorageService {
  static final MultiAccountStorageService _instance =
      MultiAccountStorageService._internal();
  factory MultiAccountStorageService() => _instance;
  MultiAccountStorageService._internal();

  // Secure storage instance with enhanced security options
  static const _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      sharedPreferencesName: 'billionaires_social_accounts',
      preferencesKeyPrefix: 'bsa_',
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
      accountName: 'billionaires_social_accounts',
      synchronizable: false,
    ),
  );

  // Storage keys
  static const String _savedAccountsKey = 'saved_accounts';
  static const String _activeAccountKey = 'active_account_id';
  static const String _credentialsPrefix = 'credentials_';
  static const String _encryptionKeyPrefix = 'enc_key_';
  static const String _switchHistoryKey = 'switch_history';
  static const String _quickSwitcherConfigKey = 'quick_switcher_config';
  static const String _masterKeyId = 'master_key_v1';

  // Note: Security service integration can be added when needed

  /// Initialize the storage service and ensure encryption keys exist
  Future<void> initialize() async {
    try {
      await _ensureMasterKeyExists();
      await _validateStorageIntegrity();
      debugPrint('✅ MultiAccountStorage: Initialized successfully');
    } catch (e) {
      debugPrint('❌ MultiAccountStorage: Initialization failed: $e');
      rethrow;
    }
  }

  /// Save a new account securely
  Future<void> saveAccount(SavedAccountModel account) async {
    try {
      // Get current saved accounts
      final savedAccounts = await getSavedAccounts();
      
      // Check if account already exists
      final existingIndex = savedAccounts.indexWhere((a) => a.id == account.id);
      
      if (existingIndex >= 0) {
        // Update existing account
        savedAccounts[existingIndex] = account;
      } else {
        // Add new account
        savedAccounts.add(account);
      }

      // Save updated accounts list
      await _saveSavedAccountsList(savedAccounts);
      
      debugPrint('✅ MultiAccountStorage: Saved account: ${account.email}');
    } catch (e) {
      debugPrint('❌ MultiAccountStorage: Error saving account: $e');
      rethrow;
    }
  }

  /// Save account credentials securely with encryption
  Future<void> saveAccountCredentials(AccountCredentials credentials) async {
    try {
      // Generate unique encryption key for this credential
      final encryptionKey = await _generateEncryptionKey();
      final keyId = _generateKeyId();
      
      // Store encryption key
      await _secureStorage.write(
        key: '$_encryptionKeyPrefix$keyId',
        value: encryptionKey,
      );

      // Encrypt sensitive data
      final encryptedCredentials = credentials.copyWith(
        keyId: keyId,
        updatedAt: DateTime.now(),
      );

      // Store encrypted credentials
      await _secureStorage.write(
        key: '$_credentialsPrefix${credentials.accountId}',
        value: json.encode(encryptedCredentials.toJson()),
      );

      debugPrint('✅ MultiAccountStorage: Saved credentials for account: ${credentials.accountId}');
    } catch (e) {
      debugPrint('❌ MultiAccountStorage: Error saving credentials: $e');
      rethrow;
    }
  }

  /// Get all saved accounts
  Future<List<SavedAccountModel>> getSavedAccounts() async {
    try {
      final accountsJson = await _secureStorage.read(key: _savedAccountsKey);
      if (accountsJson == null) return [];

      final List<dynamic> accountsList = json.decode(accountsJson);
      return accountsList
          .map((json) => SavedAccountModel.fromJson(json))
          .toList();
    } catch (e) {
      debugPrint('❌ MultiAccountStorage: Error getting saved accounts: $e');
      return [];
    }
  }

  /// Get account credentials securely
  Future<AccountCredentials?> getAccountCredentials(String accountId) async {
    try {
      final credentialsJson = await _secureStorage.read(
        key: '$_credentialsPrefix$accountId',
      );
      
      if (credentialsJson == null) return null;

      final credentialsData = json.decode(credentialsJson);
      return AccountCredentials.fromJson(credentialsData);
    } catch (e) {
      debugPrint('❌ MultiAccountStorage: Error getting credentials: $e');
      return null;
    }
  }

  /// Set active account
  Future<void> setActiveAccount(String accountId) async {
    try {
      await _secureStorage.write(key: _activeAccountKey, value: accountId);
      
      // Update account's last used time
      final savedAccounts = await getSavedAccounts();
      final accountIndex = savedAccounts.indexWhere((a) => a.id == accountId);
      
      if (accountIndex >= 0) {
        savedAccounts[accountIndex] = savedAccounts[accountIndex].copyWith(
          lastUsedAt: DateTime.now(),
          isActive: true,
        );
        
        // Set all other accounts as inactive
        for (int i = 0; i < savedAccounts.length; i++) {
          if (i != accountIndex) {
            savedAccounts[i] = savedAccounts[i].copyWith(isActive: false);
          }
        }
        
        await _saveSavedAccountsList(savedAccounts);
      }

      debugPrint('✅ MultiAccountStorage: Set active account: $accountId');
    } catch (e) {
      debugPrint('❌ MultiAccountStorage: Error setting active account: $e');
      rethrow;
    }
  }

  /// Get active account ID
  Future<String?> getActiveAccountId() async {
    try {
      return await _secureStorage.read(key: _activeAccountKey);
    } catch (e) {
      debugPrint('❌ MultiAccountStorage: Error getting active account: $e');
      return null;
    }
  }

  /// Get active account
  Future<SavedAccountModel?> getActiveAccount() async {
    try {
      final activeAccountId = await getActiveAccountId();
      if (activeAccountId == null) return null;

      final savedAccounts = await getSavedAccounts();
      return savedAccounts.where((a) => a.id == activeAccountId).firstOrNull;
    } catch (e) {
      debugPrint('❌ MultiAccountStorage: Error getting active account: $e');
      return null;
    }
  }

  /// Remove account and its credentials
  Future<void> removeAccount(String accountId) async {
    try {
      // Remove from saved accounts list
      final savedAccounts = await getSavedAccounts();
      savedAccounts.removeWhere((a) => a.id == accountId);
      await _saveSavedAccountsList(savedAccounts);

      // Remove credentials
      await _secureStorage.delete(key: '$_credentialsPrefix$accountId');

      // Get and remove encryption key
      final credentials = await getAccountCredentials(accountId);
      if (credentials != null) {
        await _secureStorage.delete(key: '$_encryptionKeyPrefix${credentials.keyId}');
      }

      // Clear active account if it was the removed one
      final activeAccountId = await getActiveAccountId();
      if (activeAccountId == accountId) {
        await _secureStorage.delete(key: _activeAccountKey);
      }

      debugPrint('✅ MultiAccountStorage: Removed account: $accountId');
    } catch (e) {
      debugPrint('❌ MultiAccountStorage: Error removing account: $e');
      rethrow;
    }
  }

  /// Save account switch history
  Future<void> saveAccountSwitchHistory(AccountSwitchHistory history) async {
    try {
      final historyListJson = await _secureStorage.read(key: _switchHistoryKey) ?? '[]';
      final List<dynamic> historyList = json.decode(historyListJson);
      
      // Add new history entry
      historyList.insert(0, history.toJson());
      
      // Keep only last 100 entries
      if (historyList.length > 100) {
        historyList.removeRange(100, historyList.length);
      }
      
      await _secureStorage.write(
        key: _switchHistoryKey,
        value: json.encode(historyList),
      );

      debugPrint('✅ MultiAccountStorage: Saved switch history');
    } catch (e) {
      debugPrint('❌ MultiAccountStorage: Error saving switch history: $e');
    }
  }

  /// Get account switch history
  Future<List<AccountSwitchHistory>> getAccountSwitchHistory() async {
    try {
      final historyJson = await _secureStorage.read(key: _switchHistoryKey);
      if (historyJson == null) return [];

      final List<dynamic> historyList = json.decode(historyJson);
      return historyList
          .map((json) => AccountSwitchHistory.fromJson(json))
          .toList();
    } catch (e) {
      debugPrint('❌ MultiAccountStorage: Error getting switch history: $e');
      return [];
    }
  }

  /// Save quick switcher configuration
  Future<void> saveQuickSwitcherConfig(QuickSwitcherConfig config) async {
    try {
      await _secureStorage.write(
        key: _quickSwitcherConfigKey,
        value: json.encode(config.toJson()),
      );
      debugPrint('✅ MultiAccountStorage: Saved quick switcher config');
    } catch (e) {
      debugPrint('❌ MultiAccountStorage: Error saving quick switcher config: $e');
    }
  }

  /// Get quick switcher configuration
  Future<QuickSwitcherConfig> getQuickSwitcherConfig() async {
    try {
      final configJson = await _secureStorage.read(key: _quickSwitcherConfigKey);
      if (configJson == null) {
        return const QuickSwitcherConfig(); // Return default config
      }

      return QuickSwitcherConfig.fromJson(json.decode(configJson));
    } catch (e) {
      debugPrint('❌ MultiAccountStorage: Error getting quick switcher config: $e');
      return const QuickSwitcherConfig(); // Return default config
    }
  }

  /// Clear all account data (for logout or reset)
  Future<void> clearAllAccountData() async {
    try {
      await _secureStorage.deleteAll();
      
      // Also clear SharedPreferences data
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => 
        key.startsWith('account_') || 
        key.startsWith('multi_account_') ||
        key.startsWith('bsa_')
      ).toList();
      
      for (final key in keys) {
        await prefs.remove(key);
      }

      debugPrint('✅ MultiAccountStorage: Cleared all account data');
    } catch (e) {
      debugPrint('❌ MultiAccountStorage: Error clearing account data: $e');
      rethrow;
    }
  }

  // Private helper methods

  /// Save the saved accounts list to secure storage
  Future<void> _saveSavedAccountsList(List<SavedAccountModel> accounts) async {
    final accountsJson = json.encode(accounts.map((a) => a.toJson()).toList());
    await _secureStorage.write(key: _savedAccountsKey, value: accountsJson);
  }

  /// Ensure master encryption key exists
  Future<void> _ensureMasterKeyExists() async {
    final existingKey = await _secureStorage.read(key: _masterKeyId);
    if (existingKey == null) {
      final masterKey = await _generateEncryptionKey();
      await _secureStorage.write(key: _masterKeyId, value: masterKey);
      debugPrint('✅ MultiAccountStorage: Generated new master key');
    }
  }

  /// Generate a secure encryption key
  Future<String> _generateEncryptionKey() async {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64Encode(bytes);
  }

  /// Generate a unique key identifier
  String _generateKeyId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(999999);
    return '${timestamp}_$random';
  }

  /// Get account by ID
  Future<SavedAccountModel?> getAccountById(String accountId) async {
    try {
      final savedAccounts = await getSavedAccounts();
      return savedAccounts.where((a) => a.id == accountId).firstOrNull;
    } catch (e) {
      debugPrint('❌ MultiAccountStorage: Error getting account by ID: $e');
      return null;
    }
  }

  /// Update account's last used time
  Future<void> updateLastUsedTime(String accountId) async {
    try {
      final savedAccounts = await getSavedAccounts();
      final accountIndex = savedAccounts.indexWhere((a) => a.id == accountId);
      
      if (accountIndex >= 0) {
        savedAccounts[accountIndex] = savedAccounts[accountIndex].copyWith(
          lastUsedAt: DateTime.now(),
        );
        await _saveSavedAccountsList(savedAccounts);
        debugPrint('✅ MultiAccountStorage: Updated last used time for: $accountId');
      }
    } catch (e) {
      debugPrint('❌ MultiAccountStorage: Error updating last used time: $e');
    }
  }

  /// Get account by ID (alias for getAccountById)
  Future<SavedAccountModel?> getAccount(String accountId) async {
    return getAccountById(accountId);
  }

  /// Update account last used (alias for updateLastUsedTime)
  Future<void> updateAccountLastUsed(String accountId) async {
    return updateLastUsedTime(accountId);
  }

  /// Validate storage integrity
  Future<void> _validateStorageIntegrity() async {
    try {
      // Check if we can read/write to secure storage
      const testKey = 'integrity_test';
      const testValue = 'test_value';
      
      await _secureStorage.write(key: testKey, value: testValue);
      final readValue = await _secureStorage.read(key: testKey);
      await _secureStorage.delete(key: testKey);
      
      if (readValue != testValue) {
        throw Exception('Storage integrity check failed');
      }
      
      debugPrint('✅ MultiAccountStorage: Storage integrity validated');
    } catch (e) {
      debugPrint('❌ MultiAccountStorage: Storage integrity validation failed: $e');
      rethrow;
    }
  }
}
