import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/auth/models/multi_account_session.dart';
import 'package:billionaires_social/features/auth/services/multi_account_auth_service.dart';
import 'package:billionaires_social/features/auth/services/multi_account_storage_service.dart';

/// Service that handles the seamless switching logic between accounts
/// Manages state preservation, cleanup, and restoration during account switches
class AccountSwitchingService {
  static final AccountSwitchingService _instance =
      AccountSwitchingService._internal();
  factory AccountSwitchingService() => _instance;
  AccountSwitchingService._internal();

  // Services
  final MultiAccountAuthService _authService = MultiAccountAuthService();
  final MultiAccountStorageService _storageService =
      MultiAccountStorageService();

  // State management
  final StreamController<AccountSwitchProgress> _progressController =
      StreamController<AccountSwitchProgress>.broadcast();
  final Map<String, AccountSwitchState> _switchStates = {};

  /// Stream of account switch progress
  Stream<AccountSwitchProgress> get switchProgressStream =>
      _progressController.stream;

  /// Initialize the switching service
  Future<void> initialize() async {
    try {
      await _authService.initialize();
      await _storageService.initialize();
      debugPrint('✅ AccountSwitching: Service initialized');
    } catch (e) {
      debugPrint('❌ AccountSwitching: Initialization failed: $e');
      rethrow;
    }
  }

  /// Perform seamless account switch with state preservation
  Future<AccountSwitchResult> performSeamlessSwitch({
    required String targetAccountId,
    required WidgetRef ref,
    bool preserveDrafts = true,
    bool preserveTemporaryData = true,
    bool requireBiometric = false,
  }) async {
    final switchId = _generateSwitchId();
    final stopwatch = Stopwatch()..start();

    try {
      debugPrint(
        '🔄 AccountSwitching: Starting seamless switch to $targetAccountId',
      );

      // Step 1: Get target account info
      _emitProgress(
        switchId,
        AccountSwitchStep.validating,
        'Validating target account...',
      );
      final savedAccounts = await _authService.getSavedAccounts();
      final targetAccount = savedAccounts
          .where((a) => a.id == targetAccountId)
          .firstOrNull;

      if (targetAccount == null) {
        throw Exception('Target account not found');
      }

      // Step 2: Preserve current session data
      _emitProgress(
        switchId,
        AccountSwitchStep.preservingData,
        'Preserving current session data...',
      );
      final preservedData = await _preserveCurrentSessionData(ref, AccountSwitchState(
        switchId: switchId,
        targetAccountId: targetAccountId,
        startTime: DateTime.now(),
        preserveDrafts: preserveDrafts,
        preserveTemporaryData: preserveTemporaryData,
      ));

      // Step 3: Perform comprehensive logout
      _emitProgress(
        switchId,
        AccountSwitchStep.loggingOut,
        'Logging out from current account...',
      );
      await _performComprehensiveLogout(ref);

      // Step 4: Switch to target account
      _emitProgress(
        switchId,
        AccountSwitchStep.authenticating,
        'Authenticating with target account...',
      );
      final authResult = await _authService.switchToAccount(
        targetAccountId,
        requireBiometric: requireBiometric,
      );

      if (!authResult.success) {
        // Attempt to restore previous session if switch failed
        await _attemptSessionRestore(ref, preservedData);
        throw Exception(
          'Authentication failed: ${authResult.errorMessage ?? 'Unknown error'}',
        );
      }

      // Step 5: Restore preserved data
      _emitProgress(
        switchId,
        AccountSwitchStep.restoringData,
        'Restoring session data...',
      );
      await _restoreSessionData(ref, preservedData, targetAccountId);

      // Step 6: Update app state
      _emitProgress(
        switchId,
        AccountSwitchStep.updatingState,
        'Updating application state...',
      );
      await _updateAppState(ref, targetAccountId);

      // Step 7: Cleanup and finalize
      _emitProgress(
        switchId,
        AccountSwitchStep.finalizing,
        'Finalizing switch...',
      );
      await _finalizeSwitch(switchId, targetAccountId);

      stopwatch.stop();

      _emitProgress(
        switchId,
        AccountSwitchStep.completed,
        'Account switch completed successfully',
      );

      debugPrint(
        '✅ AccountSwitching: Seamless switch completed in ${stopwatch.elapsed}',
      );

      return AccountSwitchResult(
        success: true,
        switchedToAccount: await _storageService.getAccountById(targetAccountId),
        switchDuration: stopwatch.elapsed,
        // preservedDataSize: preservedData.estimatedSize,
      );
    } catch (e) {
      stopwatch.stop();

      debugPrint('❌ AccountSwitching: Seamless switch failed: $e');

      _emitProgress(switchId, AccountSwitchStep.failed, 'Switch failed: $e');

      // Cleanup failed switch state
      _switchStates.remove(switchId);

      return AccountSwitchResult(
        success: false,
        errorMessage: e.toString(),
        errorCode: 'SEAMLESS_SWITCH_FAILED',
        switchDuration: stopwatch.elapsed,
      );
    }
  }

  /// Preserve current session data before switching
  Future<PreservedSessionData> _preserveCurrentSessionData(
    WidgetRef ref,
    AccountSwitchState switchState,
  ) async {
    final preservedData = PreservedSessionData();

    try {
      // Preserve drafts if requested
      if (switchState.preserveDrafts) {
        preservedData.drafts = await _preserveDrafts(ref);
      }

      // Preserve temporary data if requested
      if (switchState.preserveTemporaryData) {
        preservedData.temporaryData = await _preserveTemporaryData(ref);
      }

      // Preserve navigation state
      preservedData.navigationState = await _preserveNavigationState(ref);

      // Preserve user preferences
      preservedData.userPreferences = await _preserveUserPreferences(ref);

      // Preserve cached data
      preservedData.cachedData = await _preserveCachedData(ref);

      debugPrint(
        '📦 AccountSwitching: Preserved ${preservedData.estimatedSize} bytes of session data',
      );

      return preservedData;
    } catch (e) {
      debugPrint(
        '⚠️ AccountSwitching: Failed to preserve some session data: $e',
      );
      return preservedData; // Return partial data
    }
  }

  /// Perform comprehensive logout from current account
  Future<void> _performComprehensiveLogout(WidgetRef ref) async {
    try {
      // Clear Riverpod providers
      await _clearRiverpodProviders(ref);

      // TODO: Clear Firebase Auth session when service is available
      // await _firebaseService.signOut();

      // TODO: Clear secure tokens when service is available
      // await _securityService.clearAllSecureTokens();

      // TODO: Clear session management when service is available
      // await _sessionService.performComprehensiveLogout(ref);

      debugPrint('🧹 AccountSwitching: Comprehensive logout completed');
    } catch (e) {
      debugPrint('⚠️ AccountSwitching: Logout had issues: $e');
      // Continue with switch even if logout has issues
    }
  }

  /// Restore session data after successful switch
  Future<void> _restoreSessionData(
    WidgetRef ref,
    PreservedSessionData preservedData,
    String targetAccountId,
  ) async {
    try {
      // Restore drafts
      if (preservedData.drafts.isNotEmpty) {
        await _restoreDrafts(ref, preservedData.drafts, targetAccountId);
      }

      // Restore temporary data
      if (preservedData.temporaryData.isNotEmpty) {
        await _restoreTemporaryData(
          ref,
          preservedData.temporaryData,
          targetAccountId,
        );
      }

      // Restore navigation state
      if (preservedData.navigationState.isNotEmpty) {
        await _restoreNavigationState(ref, preservedData.navigationState);
      }

      // Restore user preferences
      if (preservedData.userPreferences.isNotEmpty) {
        await _restoreUserPreferences(
          ref,
          preservedData.userPreferences,
          targetAccountId,
        );
      }

      // Restore cached data
      if (preservedData.cachedData.isNotEmpty) {
        await _restoreCachedData(
          ref,
          preservedData.cachedData,
          targetAccountId,
        );
      }

      debugPrint(
        '📦 AccountSwitching: Restored session data for account $targetAccountId',
      );
    } catch (e) {
      debugPrint(
        '⚠️ AccountSwitching: Failed to restore some session data: $e',
      );
      // Continue even if restoration has issues
    }
  }

  /// Update application state after account switch
  Future<void> _updateAppState(WidgetRef ref, String targetAccountId) async {
    try {
      // TODO: Refresh providers when they are available
      // ref.invalidate(authProvider);
      // ref.invalidate(userProfileProvider);
      // ref.invalidate(feedProvider);
      // ref.invalidate(storyProvider);
      // ref.invalidate(notificationProvider);

      // Update analytics context
      await _updateAnalyticsContext(targetAccountId);

      // Update push notification tokens
      await _updatePushNotificationTokens(targetAccountId);

      debugPrint(
        '🔄 AccountSwitching: App state updated for account $targetAccountId',
      );
    } catch (e) {
      debugPrint('⚠️ AccountSwitching: Failed to update some app state: $e');
    }
  }

  /// Finalize the account switch
  Future<void> _finalizeSwitch(String switchId, String targetAccountId) async {
    try {
      // Update last used time
      await _storageService.updateAccountLastUsed(targetAccountId);

      // Clean up switch state
      _switchStates.remove(switchId);

      // Trigger post-switch hooks
      await _triggerPostSwitchHooks(targetAccountId);

      debugPrint(
        '✅ AccountSwitching: Switch finalized for account $targetAccountId',
      );
    } catch (e) {
      debugPrint('⚠️ AccountSwitching: Finalization had issues: $e');
    }
  }

  // Helper methods for data preservation and restoration

  Future<Map<String, dynamic>> _preserveDrafts(WidgetRef ref) async {
    // Preserve story drafts, post drafts, message drafts, etc.
    final drafts = <String, dynamic>{};

    try {
      // TODO: Implement draft preservation when draft providers are available
      // For now, return empty drafts to avoid provider errors
      debugPrint('📝 AccountSwitching: Draft preservation placeholder');
    } catch (e) {
      debugPrint('⚠️ AccountSwitching: Error preserving drafts: $e');
    }

    return drafts;
  }

  Future<Map<String, dynamic>> _preserveTemporaryData(WidgetRef ref) async {
    final tempData = <String, dynamic>{};

    try {
      // TODO: Preserve data when providers are available
      // tempData['scroll_positions'] = ref.read(scrollPositionProvider);
      // tempData['form_data'] = ref.read(formDataProvider);
      // tempData['search_history'] = ref.read(searchHistoryProvider);
    } catch (e) {
      debugPrint('⚠️ AccountSwitching: Error preserving temporary data: $e');
    }

    return tempData;
  }

  Future<Map<String, dynamic>> _preserveNavigationState(WidgetRef ref) async {
    final navState = <String, dynamic>{};

    try {
      // TODO: Preserve navigation state when providers are available
      // navState['current_route'] = ref.read(currentRouteProvider);
      // navState['tab_index'] = ref.read(tabIndexProvider);
      // navState['navigation_history'] = ref.read(navigationHistoryProvider);
    } catch (e) {
      debugPrint('⚠️ AccountSwitching: Error preserving navigation state: $e');
    }

    return navState;
  }

  Future<Map<String, dynamic>> _preserveUserPreferences(WidgetRef ref) async {
    final preferences = <String, dynamic>{};

    try {
      // TODO: Preserve preferences when providers are available
      // preferences['theme'] = ref.read(themeProvider);
      // preferences['notifications'] = ref.read(notificationSettingsProvider);
      // preferences['privacy'] = ref.read(privacySettingsProvider);
    } catch (e) {
      debugPrint('⚠️ AccountSwitching: Error preserving user preferences: $e');
    }

    return preferences;
  }

  Future<Map<String, dynamic>> _preserveCachedData(WidgetRef ref) async {
    final cachedData = <String, dynamic>{};

    try {
      // TODO: Preserve cached data when providers are available
      // final feedCache = ref.read(feedCacheProvider);
      // if (feedCache.isNotEmpty) {
      //   cachedData['feed_cache'] = feedCache;
      // }
      // final profileCache = ref.read(profileCacheProvider);
      // if (profileCache.isNotEmpty) {
      //   cachedData['profile_cache'] = profileCache;
      // }
    } catch (e) {
      debugPrint('⚠️ AccountSwitching: Error preserving cached data: $e');
    }

    return cachedData;
  }

  Future<void> _restoreDrafts(
    WidgetRef ref,
    Map<String, dynamic> drafts,
    String targetAccountId,
  ) async {
    try {
      // TODO: Restore drafts when providers and models are available
      // if (drafts.containsKey('story_drafts')) {
      //   final storyDrafts = (drafts['story_drafts'] as List)
      //       .map((d) => StoryDraft.fromJson(d))
      //       .toList();
      //   ref.read(storyDraftsProvider.notifier).restoreDrafts(storyDrafts);
      // }
      // if (drafts.containsKey('post_drafts')) {
      //   final postDrafts = (drafts['post_drafts'] as List)
      //       .map((d) => PostDraft.fromJson(d))
      //       .toList();
      //   ref.read(postDraftsProvider.notifier).restoreDrafts(postDrafts);
      // }
      // if (drafts.containsKey('message_drafts')) {
      //   ref
      //       .read(messageDraftsProvider.notifier)
      //       .restoreDrafts(drafts['message_drafts']);
      // }
    } catch (e) {
      debugPrint('⚠️ AccountSwitching: Error restoring drafts: $e');
    }
  }

  Future<void> _restoreTemporaryData(
    WidgetRef ref,
    Map<String, dynamic> tempData,
    String targetAccountId,
  ) async {
    try {
      // TODO: Restore temporary data when providers are available
      // if (tempData.containsKey('scroll_positions')) {
      //   ref
      //       .read(scrollPositionProvider.notifier)
      //       .restorePositions(tempData['scroll_positions']);
      // }
      // if (tempData.containsKey('form_data')) {
      //   ref.read(formDataProvider.notifier).restoreData(tempData['form_data']);
      // }
      // if (tempData.containsKey('search_history')) {
      //   ref
      //       .read(searchHistoryProvider.notifier)
      //       .restoreHistory(tempData['search_history']);
      // }
    } catch (e) {
      debugPrint('⚠️ AccountSwitching: Error restoring temporary data: $e');
    }
  }

  Future<void> _restoreNavigationState(
    WidgetRef ref,
    Map<String, dynamic> navState,
  ) async {
    try {
      // TODO: Restore navigation state when providers are available
      // if (navState.containsKey('tab_index')) {
      //   ref.read(tabIndexProvider.notifier).state = navState['tab_index'];
      // }

      // Note: Current route and navigation history are typically managed
      // by the router and may not need explicit restoration
    } catch (e) {
      debugPrint('⚠️ AccountSwitching: Error restoring navigation state: $e');
    }
  }

  Future<void> _restoreUserPreferences(
    WidgetRef ref,
    Map<String, dynamic> preferences,
    String targetAccountId,
  ) async {
    try {
      // User preferences are typically account-specific and should be
      // loaded from the new account's settings rather than restored
      // from the previous account

      // Load account-specific preferences
      await _loadAccountSpecificPreferences(ref, targetAccountId);
    } catch (e) {
      debugPrint('⚠️ AccountSwitching: Error restoring user preferences: $e');
    }
  }

  Future<void> _restoreCachedData(
    WidgetRef ref,
    Map<String, dynamic> cachedData,
    String targetAccountId,
  ) async {
    try {
      // Cached data should typically be cleared and reloaded for the new account
      // rather than restored from the previous account

      // TODO: Clear cache when providers are available
      // ref.invalidate(feedCacheProvider);
      // ref.invalidate(profileCacheProvider);

      // The cache will be repopulated as the user interacts with the app
    } catch (e) {
      debugPrint('⚠️ AccountSwitching: Error handling cached data: $e');
    }
  }

  // Additional helper methods

  Future<void> _clearRiverpodProviders(WidgetRef ref) async {
    // TODO: Clear providers when they are available
    // ref.invalidate(authProvider);
    // ref.invalidate(userProfileProvider);
    // ref.invalidate(feedProvider);
    // ref.invalidate(storyProvider);
    // ref.invalidate(notificationProvider);
    // ref.invalidate(chatProvider);
  }

  Future<void> _attemptSessionRestore(
    WidgetRef ref,
    PreservedSessionData preservedData,
  ) async {
    try {
      debugPrint(
        '🔄 AccountSwitching: Attempting to restore previous session...',
      );
      // Attempt to restore the previous session if switch failed
      // This is a best-effort operation
    } catch (e) {
      debugPrint('⚠️ AccountSwitching: Session restore failed: $e');
    }
  }

  Future<void> _updateAnalyticsContext(String targetAccountId) async {
    try {
      // TODO: Update analytics when service is available
      // await _firebaseService.setAnalyticsUserId(targetAccountId);
    } catch (e) {
      debugPrint('⚠️ AccountSwitching: Analytics update failed: $e');
    }
  }

  Future<void> _updatePushNotificationTokens(String targetAccountId) async {
    try {
      // TODO: Update FCM token when service is available
      // await _firebaseService.updateFCMToken(targetAccountId);
    } catch (e) {
      debugPrint('⚠️ AccountSwitching: Push token update failed: $e');
    }
  }

  Future<void> _loadAccountSpecificPreferences(
    WidgetRef ref,
    String targetAccountId,
  ) async {
    try {
      // TODO: Load preferences when provider is available
      // ref.invalidate(userPreferencesProvider);
    } catch (e) {
      debugPrint('⚠️ AccountSwitching: Preference loading failed: $e');
    }
  }

  Future<void> _triggerPostSwitchHooks(String targetAccountId) async {
    try {
      // Trigger any post-switch hooks or callbacks
      // This could include updating widgets, refreshing data, etc.
    } catch (e) {
      debugPrint('⚠️ AccountSwitching: Post-switch hooks failed: $e');
    }
  }

  void _emitProgress(String switchId, AccountSwitchStep step, String message) {
    _progressController.add(
      AccountSwitchProgress(
        switchId: switchId,
        step: step,
        message: message,
        timestamp: DateTime.now(),
      ),
    );
  }

  String _generateSwitchId() {
    return 'switch_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// Dispose resources
  void dispose() {
    _progressController.close();
    _switchStates.clear();
  }
}

// Data classes for switch management

class AccountSwitchState {
  final String switchId;
  final String targetAccountId;
  final DateTime startTime;
  final bool preserveDrafts;
  final bool preserveTemporaryData;

  AccountSwitchState({
    required this.switchId,
    required this.targetAccountId,
    required this.startTime,
    required this.preserveDrafts,
    required this.preserveTemporaryData,
  });
}

class PreservedSessionData {
  Map<String, dynamic> drafts = {};
  Map<String, dynamic> temporaryData = {};
  Map<String, dynamic> navigationState = {};
  Map<String, dynamic> userPreferences = {};
  Map<String, dynamic> cachedData = {};

  int get estimatedSize {
    // Rough estimation of data size in bytes
    final jsonString = {
      'drafts': drafts,
      'temporaryData': temporaryData,
      'navigationState': navigationState,
      'userPreferences': userPreferences,
      'cachedData': cachedData,
    }.toString();
    return jsonString.length;
  }
}

class AccountSwitchProgress {
  final String switchId;
  final AccountSwitchStep step;
  final String message;
  final DateTime timestamp;

  AccountSwitchProgress({
    required this.switchId,
    required this.step,
    required this.message,
    required this.timestamp,
  });
}

enum AccountSwitchStep {
  validating,
  preservingData,
  loggingOut,
  authenticating,
  restoringData,
  updatingState,
  finalizing,
  completed,
  failed,
}
