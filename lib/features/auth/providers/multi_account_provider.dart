import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:billionaires_social/features/auth/models/saved_account_model.dart';
import 'package:billionaires_social/features/auth/models/multi_account_session.dart';
import 'package:billionaires_social/features/auth/services/multi_account_auth_service.dart';
import 'package:billionaires_social/features/auth/services/multi_account_storage_service.dart';
import 'package:billionaires_social/services/n8n_service.dart' as n8n;
import 'package:billionaires_social/services/n8n_firebase_integration.dart';
import 'package:billionaires_social/core/config/n8n_config.dart';

// Import providers that need to be invalidated during account switching
// Note: Only import providers that actually exist in the codebase

part 'multi_account_provider.g.dart';

/// Provider for the multi-account authentication service
@riverpod
MultiAccountAuthService multiAccountAuthService(Ref ref) {
  final service = MultiAccountAuthService();

  // Initialize the service
  service.initialize().catchError((error) {
    // Handle initialization error
    debugPrint('⚠️ MultiAccount: Failed to initialize auth service: $error');
  });

  return service;
}

/// Provider for the multi-account storage service
@riverpod
MultiAccountStorageService multiAccountStorageService(Ref ref) {
  final service = MultiAccountStorageService();

  // Initialize the service
  service.initialize().catchError((error) {
    // Handle initialization error
    debugPrint('⚠️ MultiAccount: Failed to initialize storage service: $error');
  });

  return service;
}

/// Provider for the current multi-account session
@riverpod
Stream<MultiAccountSession> multiAccountSession(Ref ref) {
  final authService = ref.watch(multiAccountAuthServiceProvider);

  // Return the session stream from the auth service
  return authService.sessionStream;
}

/// Provider for saved accounts list
@riverpod
Future<List<SavedAccountModel>> savedAccounts(Ref ref) async {
  final authService = ref.watch(multiAccountAuthServiceProvider);
  return await authService.getSavedAccounts();
}

/// Provider for the currently active account
@riverpod
Future<SavedAccountModel?> activeAccount(Ref ref) async {
  final storageService = ref.watch(multiAccountStorageServiceProvider);
  return await storageService.getActiveAccount();
}

/// Provider for account switch history
@riverpod
Future<List<AccountSwitchHistory>> accountSwitchHistory(Ref ref) async {
  final authService = ref.watch(multiAccountAuthServiceProvider);
  return await authService.getSwitchHistory();
}

/// Provider for quick switcher configuration
@riverpod
Future<QuickSwitcherConfig> quickSwitcherConfig(Ref ref) async {
  final storageService = ref.watch(multiAccountStorageServiceProvider);
  return await storageService.getQuickSwitcherConfig();
}

/// Notifier for managing multi-account operations
@riverpod
class MultiAccountNotifier extends _$MultiAccountNotifier {
  @override
  Future<MultiAccountSession> build() async {
    final authService = ref.watch(multiAccountAuthServiceProvider);

    // Return current session or empty session if none exists
    return authService.currentSession ?? const MultiAccountSession();
  }

  /// Add a new account
  Future<AccountSwitchResult> addAccount({
    required String email,
    required String password,
    bool rememberPassword = false,
    bool setBiometricAuth = false,
  }) async {
    final authService = ref.read(multiAccountAuthServiceProvider);

    final result = await authService.addAccount(
      email: email,
      password: password,
      rememberPassword: rememberPassword,
      setBiometricAuth: setBiometricAuth,
    );

    if (result.success) {
      // Refresh the session and related providers
      ref.invalidateSelf();
      ref.invalidate(savedAccountsProvider);
      ref.invalidate(activeAccountProvider);
    }

    return result;
  }

  /// Switch to an existing account with seamless state management
  Future<AccountSwitchResult> switchToAccount(
    String accountId, {
    bool requireBiometric = false,
  }) async {
    try {
      // Step 1: Preserve current application state
      await _preserveCurrentState();

      // Step 2: Perform the account switch
      final authService = ref.read(multiAccountAuthServiceProvider);
      final result = await authService.switchToAccount(
        accountId,
        requireBiometric: requireBiometric,
      );

      if (result.success) {
        // Step 3: Update providers and refresh state
        await _updateProvidersAfterSwitch(accountId);

        // Step 4: Restore appropriate state for new account
        await _restoreStateForAccount(accountId);

        // Refresh the session and related providers
        ref.invalidateSelf();
        ref.invalidate(savedAccountsProvider);
        ref.invalidate(activeAccountProvider);
        ref.invalidate(accountSwitchHistoryProvider);
      }

      return result;
    } catch (e) {
      return AccountSwitchResult(
        success: false,
        errorMessage: e.toString(),
        errorCode: 'SEAMLESS_SWITCH_FAILED',
      );
    }
  }

  /// Preserve current application state before switching
  Future<void> _preserveCurrentState() async {
    try {
      // Preserve drafts, scroll positions, form data, etc.
      // This is a placeholder for future implementation
      debugPrint('📦 MultiAccount: Preserving current state');
    } catch (e) {
      debugPrint('⚠️ MultiAccount: Failed to preserve state: $e');
    }
  }

  /// Update providers after successful account switch
  Future<void> _updateProvidersAfterSwitch(String accountId) async {
    try {
      // Invalidate multi-account related providers
      ref.invalidate(multiAccountSessionProvider);
      ref.invalidate(savedAccountsProvider);
      ref.invalidate(activeAccountProvider);
      ref.invalidate(accountSwitchHistoryProvider);
      ref.invalidate(quickSwitcherConfigProvider);
      ref.invalidate(accountValidationStatusProvider);
      ref.invalidate(recentlyUsedAccountsProvider);
      ref.invalidate(accountStatisticsProvider);

      // TODO: Add other provider invalidations when they are properly imported
      // This is where we would invalidate user-specific providers like:
      // - User profile providers
      // - Feed providers
      // - Story providers
      // - Notification providers
      // - Settings providers
      // These will be added once the proper imports are established

      debugPrint('🔄 MultiAccount: Updated providers for account $accountId');
    } catch (e) {
      debugPrint('⚠️ MultiAccount: Failed to update providers: $e');
    }
  }

  /// Restore state for the newly switched account
  Future<void> _restoreStateForAccount(String accountId) async {
    try {
      // Load account-specific preferences, cached data, etc.
      // This is a placeholder for future implementation
      debugPrint('📦 MultiAccount: Restoring state for account $accountId');
    } catch (e) {
      debugPrint('⚠️ MultiAccount: Failed to restore state: $e');
    }
  }

  /// Remove an account
  Future<bool> removeAccount(String accountId) async {
    final authService = ref.read(multiAccountAuthServiceProvider);

    final success = await authService.removeAccount(accountId);

    if (success) {
      // Refresh the session and related providers
      ref.invalidateSelf();
      ref.invalidate(savedAccountsProvider);
      ref.invalidate(activeAccountProvider);
    }

    return success;
  }

  /// Validate an account
  Future<AccountValidationResult> validateAccount(String accountId) async {
    final authService = ref.read(multiAccountAuthServiceProvider);
    return await authService.validateAccount(accountId);
  }

  /// Clear all accounts
  Future<void> clearAllAccounts() async {
    final authService = ref.read(multiAccountAuthServiceProvider);
    await authService.clearAllAccounts();

    // Refresh all providers
    ref.invalidateSelf();
    ref.invalidate(savedAccountsProvider);
    ref.invalidate(activeAccountProvider);
    ref.invalidate(accountSwitchHistoryProvider);
  }

  /// Update quick switcher configuration
  Future<void> updateQuickSwitcherConfig(QuickSwitcherConfig config) async {
    final storageService = ref.read(multiAccountStorageServiceProvider);
    await storageService.saveQuickSwitcherConfig(config);

    // Refresh the config provider
    ref.invalidate(quickSwitcherConfigProvider);
  }

  /// Refresh session data
  Future<void> refreshSession() async {
    ref.invalidateSelf();
    ref.invalidate(savedAccountsProvider);
    ref.invalidate(activeAccountProvider);
    ref.invalidate(accountSwitchHistoryProvider);
    ref.invalidate(quickSwitcherConfigProvider);
  }
}

/// Provider for account validation status
@riverpod
Future<Map<String, AccountValidationResult>> accountValidationStatus(
  Ref ref,
) async {
  final savedAccounts = await ref.watch(savedAccountsProvider.future);
  final authService = ref.watch(multiAccountAuthServiceProvider);

  final validationResults = <String, AccountValidationResult>{};

  for (final account in savedAccounts) {
    try {
      final result = await authService.validateAccount(account.id);
      validationResults[account.id] = result;
    } catch (e) {
      validationResults[account.id] = AccountValidationResult(
        isValid: false,
        issues: ['Validation failed: $e'],
      );
    }
  }

  return validationResults;
}

/// Provider for recently used accounts (for quick switcher)
@riverpod
Future<List<SavedAccountModel>> recentlyUsedAccounts(Ref ref) async {
  final savedAccounts = await ref.watch(savedAccountsProvider.future);

  // Sort by last used time, most recent first
  final sortedAccounts = List<SavedAccountModel>.from(savedAccounts);
  sortedAccounts.sort((a, b) {
    final aTime = a.lastUsedAt ?? DateTime(0);
    final bTime = b.lastUsedAt ?? DateTime(0);
    return bTime.compareTo(aTime);
  });

  // Return top 5 most recently used accounts
  return sortedAccounts.take(5).toList();
}

/// Provider for account statistics
@riverpod
Future<AccountStatistics> accountStatistics(Ref ref) async {
  final savedAccounts = await ref.watch(savedAccountsProvider.future);
  final switchHistory = await ref.watch(accountSwitchHistoryProvider.future);

  final totalAccounts = savedAccounts.length;
  final verifiedAccounts = savedAccounts.where((a) => a.isVerified).length;
  final billionaireAccounts = savedAccounts
      .where((a) => a.isBillionaire)
      .length;
  final totalSwitches = switchHistory.length;
  final successfulSwitches = switchHistory.where((h) => h.successful).length;

  final lastSwitchTime = switchHistory.isNotEmpty
      ? switchHistory.first.switchedAt
      : null;

  final averageSwitchDuration = switchHistory.isNotEmpty
      ? Duration(
          milliseconds:
              switchHistory
                  .where((h) => h.switchDuration != null)
                  .map((h) => h.switchDuration!.inMilliseconds)
                  .fold(0, (a, b) => a + b) ~/
              switchHistory.where((h) => h.switchDuration != null).length,
        )
      : Duration.zero;

  return AccountStatistics(
    totalAccounts: totalAccounts,
    verifiedAccounts: verifiedAccounts,
    billionaireAccounts: billionaireAccounts,
    totalSwitches: totalSwitches,
    successfulSwitches: successfulSwitches,
    lastSwitchTime: lastSwitchTime,
    averageSwitchDuration: averageSwitchDuration,
  );
}

/// Data class for account statistics
class AccountStatistics {
  final int totalAccounts;
  final int verifiedAccounts;
  final int billionaireAccounts;
  final int totalSwitches;
  final int successfulSwitches;
  final DateTime? lastSwitchTime;
  final Duration averageSwitchDuration;

  const AccountStatistics({
    required this.totalAccounts,
    required this.verifiedAccounts,
    required this.billionaireAccounts,
    required this.totalSwitches,
    required this.successfulSwitches,
    this.lastSwitchTime,
    required this.averageSwitchDuration,
  });

  double get switchSuccessRate {
    if (totalSwitches == 0) return 0.0;
    return (successfulSwitches / totalSwitches) * 100;
  }
}

// ============================================================================
// n8n Integration Providers
// ============================================================================

/// Provider for n8n service
@riverpod
n8n.N8nService n8nService(Ref ref) {
  final service = n8n.N8nService();

  // Initialize the service
  service.initialize().catchError((error) {
    debugPrint('⚠️ n8n: Failed to initialize service: $error');
  });

  return service;
}

/// Provider for n8n Firebase integration
@riverpod
N8nFirebaseIntegration n8nFirebaseIntegration(Ref ref) {
  final n8nService = ref.watch(n8nServiceProvider);
  return N8nFirebaseIntegration(n8nService: n8nService);
}

/// Provider for n8n configuration status
@riverpod
Future<bool> n8nIntegrationEnabled(Ref ref) async {
  return await N8nConfig.isEnabled();
}

/// Provider for n8n configuration validation
@riverpod
Future<bool> n8nConfigurationValid(Ref ref) async {
  return await N8nConfig.isConfigurationValid();
}

/// Provider for account analytics from n8n
@riverpod
Future<Map<String, dynamic>?> accountAnalytics(
  Ref ref,
  String accountId,
) async {
  final n8nService = ref.watch(n8nServiceProvider);

  try {
    if (!await n8nService.isEnabled) {
      return null;
    }

    final response = await n8nService.triggerUserEngagement(
      eventType: 'get_account_analytics',
      eventData: {
        'accountId': accountId,
        'requestedAt': DateTime.now().toIso8601String(),
      },
    );

    if (response.isSuccess && response.data != null) {
      return response.data!.analytics;
    }
    return null;
  } catch (e) {
    debugPrint('⚠️ n8n: Failed to get account analytics: $e');
    return null;
  }
}

/// Provider for n8n workflow status
@riverpod
class N8nWorkflowStatus extends _$N8nWorkflowStatus {
  @override
  Map<String, String> build() {
    return {};
  }

  void updateStatus(String workflowType, String status) {
    state = {...state, workflowType: status};
  }

  void clearStatus(String workflowType) {
    final newState = Map<String, String>.from(state);
    newState.remove(workflowType);
    state = newState;
  }

  void clearAllStatuses() {
    state = {};
  }
}

/// Provider for monitoring n8n integration health
@riverpod
Future<N8nHealthStatus> n8nHealthStatus(Ref ref) async {
  try {
    final isEnabled = await N8nConfig.isEnabled();
    final isConfigValid = await N8nConfig.isConfigurationValid();

    if (!isEnabled) {
      return N8nHealthStatus(
        isHealthy: false,
        status: 'disabled',
        message: 'n8n integration is disabled',
      );
    }

    if (!isConfigValid) {
      return N8nHealthStatus(
        isHealthy: false,
        status: 'misconfigured',
        message: 'n8n configuration is invalid or incomplete',
      );
    }

    // Try to make a simple test request
    final n8nService = ref.watch(n8nServiceProvider);
    final testResponse = await n8nService.triggerUserEngagement(
      eventType: 'health_check',
      eventData: {'timestamp': DateTime.now().toIso8601String()},
    );

    if (testResponse.isSuccess) {
      return N8nHealthStatus(
        isHealthy: true,
        status: 'healthy',
        message: 'n8n integration is working properly',
        lastChecked: DateTime.now(),
      );
    } else {
      return N8nHealthStatus(
        isHealthy: false,
        status: 'connection_error',
        message: 'Failed to connect to n8n: ${testResponse.error}',
        lastChecked: DateTime.now(),
      );
    }
  } catch (e) {
    return N8nHealthStatus(
      isHealthy: false,
      status: 'error',
      message: 'Health check failed: $e',
      lastChecked: DateTime.now(),
    );
  }
}

/// Model for n8n health status
class N8nHealthStatus {
  final bool isHealthy;
  final String status;
  final String message;
  final DateTime? lastChecked;

  const N8nHealthStatus({
    required this.isHealthy,
    required this.status,
    required this.message,
    this.lastChecked,
  });

  @override
  String toString() {
    return 'N8nHealthStatus(isHealthy: $isHealthy, status: $status, message: $message)';
  }
}
