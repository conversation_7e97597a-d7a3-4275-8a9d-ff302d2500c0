// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'multi_account_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$multiAccountAuthServiceHash() =>
    r'9a3d5e0ae3efe84c4d14b5563ea2b15cc0de5085';

/// Provider for the multi-account authentication service
///
/// Copied from [multiAccountAuthService].
@ProviderFor(multiAccountAuthService)
final multiAccountAuthServiceProvider =
    AutoDisposeProvider<MultiAccountAuthService>.internal(
      multiAccountAuthService,
      name: r'multiAccountAuthServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$multiAccountAuthServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef MultiAccountAuthServiceRef =
    AutoDisposeProviderRef<MultiAccountAuthService>;
String _$multiAccountStorageServiceHash() =>
    r'7b24c7e467208a0c44d53e388e5fc574106cd05a';

/// Provider for the multi-account storage service
///
/// Copied from [multiAccountStorageService].
@ProviderFor(multiAccountStorageService)
final multiAccountStorageServiceProvider =
    AutoDisposeProvider<MultiAccountStorageService>.internal(
      multiAccountStorageService,
      name: r'multiAccountStorageServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$multiAccountStorageServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef MultiAccountStorageServiceRef =
    AutoDisposeProviderRef<MultiAccountStorageService>;
String _$multiAccountSessionHash() =>
    r'94097439d42a5ad26159072a55e7dbfea4f9b649';

/// Provider for the current multi-account session
///
/// Copied from [multiAccountSession].
@ProviderFor(multiAccountSession)
final multiAccountSessionProvider =
    AutoDisposeStreamProvider<MultiAccountSession>.internal(
      multiAccountSession,
      name: r'multiAccountSessionProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$multiAccountSessionHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef MultiAccountSessionRef =
    AutoDisposeStreamProviderRef<MultiAccountSession>;
String _$savedAccountsHash() => r'd172870db8ac64ec4e5d8ecaf57703da9539c058';

/// Provider for saved accounts list
///
/// Copied from [savedAccounts].
@ProviderFor(savedAccounts)
final savedAccountsProvider =
    AutoDisposeFutureProvider<List<SavedAccountModel>>.internal(
      savedAccounts,
      name: r'savedAccountsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$savedAccountsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SavedAccountsRef =
    AutoDisposeFutureProviderRef<List<SavedAccountModel>>;
String _$activeAccountHash() => r'5d74988f6c96da9a4c3252b4e40e80a276776960';

/// Provider for the currently active account
///
/// Copied from [activeAccount].
@ProviderFor(activeAccount)
final activeAccountProvider =
    AutoDisposeFutureProvider<SavedAccountModel?>.internal(
      activeAccount,
      name: r'activeAccountProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$activeAccountHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ActiveAccountRef = AutoDisposeFutureProviderRef<SavedAccountModel?>;
String _$accountSwitchHistoryHash() =>
    r'c37f366f3139189befda8e0a688638bba532b3db';

/// Provider for account switch history
///
/// Copied from [accountSwitchHistory].
@ProviderFor(accountSwitchHistory)
final accountSwitchHistoryProvider =
    AutoDisposeFutureProvider<List<AccountSwitchHistory>>.internal(
      accountSwitchHistory,
      name: r'accountSwitchHistoryProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$accountSwitchHistoryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AccountSwitchHistoryRef =
    AutoDisposeFutureProviderRef<List<AccountSwitchHistory>>;
String _$quickSwitcherConfigHash() =>
    r'd150b3685d2916f97cc28cd2b2c700f5b6e73f23';

/// Provider for quick switcher configuration
///
/// Copied from [quickSwitcherConfig].
@ProviderFor(quickSwitcherConfig)
final quickSwitcherConfigProvider =
    AutoDisposeFutureProvider<QuickSwitcherConfig>.internal(
      quickSwitcherConfig,
      name: r'quickSwitcherConfigProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$quickSwitcherConfigHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef QuickSwitcherConfigRef =
    AutoDisposeFutureProviderRef<QuickSwitcherConfig>;
String _$accountValidationStatusHash() =>
    r'64eb35f629428dc5031cbb4197e9bdef258829e9';

/// Provider for account validation status
///
/// Copied from [accountValidationStatus].
@ProviderFor(accountValidationStatus)
final accountValidationStatusProvider =
    AutoDisposeFutureProvider<Map<String, AccountValidationResult>>.internal(
      accountValidationStatus,
      name: r'accountValidationStatusProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$accountValidationStatusHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AccountValidationStatusRef =
    AutoDisposeFutureProviderRef<Map<String, AccountValidationResult>>;
String _$recentlyUsedAccountsHash() =>
    r'0dab0ec61a06b5640301df37758a81053cadd6a9';

/// Provider for recently used accounts (for quick switcher)
///
/// Copied from [recentlyUsedAccounts].
@ProviderFor(recentlyUsedAccounts)
final recentlyUsedAccountsProvider =
    AutoDisposeFutureProvider<List<SavedAccountModel>>.internal(
      recentlyUsedAccounts,
      name: r'recentlyUsedAccountsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$recentlyUsedAccountsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef RecentlyUsedAccountsRef =
    AutoDisposeFutureProviderRef<List<SavedAccountModel>>;
String _$accountStatisticsHash() => r'7ac0da939f2ad08f4375b062194cff57270cab07';

/// Provider for account statistics
///
/// Copied from [accountStatistics].
@ProviderFor(accountStatistics)
final accountStatisticsProvider =
    AutoDisposeFutureProvider<AccountStatistics>.internal(
      accountStatistics,
      name: r'accountStatisticsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$accountStatisticsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AccountStatisticsRef = AutoDisposeFutureProviderRef<AccountStatistics>;
String _$n8nServiceHash() => r'5395d96a323eba18bd3a2f6393a96613599454ca';

/// Provider for n8n service
///
/// Copied from [n8nService].
@ProviderFor(n8nService)
final n8nServiceProvider = AutoDisposeProvider<n8n.N8nService>.internal(
  n8nService,
  name: r'n8nServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$n8nServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef N8nServiceRef = AutoDisposeProviderRef<n8n.N8nService>;
String _$n8nFirebaseIntegrationHash() =>
    r'2cb4835e2f89efee3f9c9ced5c66ebefcead52ef';

/// Provider for n8n Firebase integration
///
/// Copied from [n8nFirebaseIntegration].
@ProviderFor(n8nFirebaseIntegration)
final n8nFirebaseIntegrationProvider =
    AutoDisposeProvider<N8nFirebaseIntegration>.internal(
      n8nFirebaseIntegration,
      name: r'n8nFirebaseIntegrationProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$n8nFirebaseIntegrationHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef N8nFirebaseIntegrationRef =
    AutoDisposeProviderRef<N8nFirebaseIntegration>;
String _$n8nIntegrationEnabledHash() =>
    r'e909860af36954320a409af1f6e07c3bf4e50dd5';

/// Provider for n8n configuration status
///
/// Copied from [n8nIntegrationEnabled].
@ProviderFor(n8nIntegrationEnabled)
final n8nIntegrationEnabledProvider = AutoDisposeFutureProvider<bool>.internal(
  n8nIntegrationEnabled,
  name: r'n8nIntegrationEnabledProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$n8nIntegrationEnabledHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef N8nIntegrationEnabledRef = AutoDisposeFutureProviderRef<bool>;
String _$n8nConfigurationValidHash() =>
    r'3e29cfc297d632074033e138f6e9bafec9cb39e4';

/// Provider for n8n configuration validation
///
/// Copied from [n8nConfigurationValid].
@ProviderFor(n8nConfigurationValid)
final n8nConfigurationValidProvider = AutoDisposeFutureProvider<bool>.internal(
  n8nConfigurationValid,
  name: r'n8nConfigurationValidProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$n8nConfigurationValidHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef N8nConfigurationValidRef = AutoDisposeFutureProviderRef<bool>;
String _$accountAnalyticsHash() => r'b056f5332d87395eac623c52dcc0084c616e522a';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider for account analytics from n8n
///
/// Copied from [accountAnalytics].
@ProviderFor(accountAnalytics)
const accountAnalyticsProvider = AccountAnalyticsFamily();

/// Provider for account analytics from n8n
///
/// Copied from [accountAnalytics].
class AccountAnalyticsFamily extends Family<AsyncValue<Map<String, dynamic>?>> {
  /// Provider for account analytics from n8n
  ///
  /// Copied from [accountAnalytics].
  const AccountAnalyticsFamily();

  /// Provider for account analytics from n8n
  ///
  /// Copied from [accountAnalytics].
  AccountAnalyticsProvider call(String accountId) {
    return AccountAnalyticsProvider(accountId);
  }

  @override
  AccountAnalyticsProvider getProviderOverride(
    covariant AccountAnalyticsProvider provider,
  ) {
    return call(provider.accountId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'accountAnalyticsProvider';
}

/// Provider for account analytics from n8n
///
/// Copied from [accountAnalytics].
class AccountAnalyticsProvider
    extends AutoDisposeFutureProvider<Map<String, dynamic>?> {
  /// Provider for account analytics from n8n
  ///
  /// Copied from [accountAnalytics].
  AccountAnalyticsProvider(String accountId)
    : this._internal(
        (ref) => accountAnalytics(ref as AccountAnalyticsRef, accountId),
        from: accountAnalyticsProvider,
        name: r'accountAnalyticsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$accountAnalyticsHash,
        dependencies: AccountAnalyticsFamily._dependencies,
        allTransitiveDependencies:
            AccountAnalyticsFamily._allTransitiveDependencies,
        accountId: accountId,
      );

  AccountAnalyticsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.accountId,
  }) : super.internal();

  final String accountId;

  @override
  Override overrideWith(
    FutureOr<Map<String, dynamic>?> Function(AccountAnalyticsRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: AccountAnalyticsProvider._internal(
        (ref) => create(ref as AccountAnalyticsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        accountId: accountId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Map<String, dynamic>?> createElement() {
    return _AccountAnalyticsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AccountAnalyticsProvider && other.accountId == accountId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, accountId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin AccountAnalyticsRef
    on AutoDisposeFutureProviderRef<Map<String, dynamic>?> {
  /// The parameter `accountId` of this provider.
  String get accountId;
}

class _AccountAnalyticsProviderElement
    extends AutoDisposeFutureProviderElement<Map<String, dynamic>?>
    with AccountAnalyticsRef {
  _AccountAnalyticsProviderElement(super.provider);

  @override
  String get accountId => (origin as AccountAnalyticsProvider).accountId;
}

String _$n8nHealthStatusHash() => r'5e1f16ea72aec76fac53875c59098963c03cebb4';

/// Provider for monitoring n8n integration health
///
/// Copied from [n8nHealthStatus].
@ProviderFor(n8nHealthStatus)
final n8nHealthStatusProvider =
    AutoDisposeFutureProvider<N8nHealthStatus>.internal(
      n8nHealthStatus,
      name: r'n8nHealthStatusProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$n8nHealthStatusHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef N8nHealthStatusRef = AutoDisposeFutureProviderRef<N8nHealthStatus>;
String _$multiAccountNotifierHash() =>
    r'00143cc17ed728a87b935a7aab04d032a82c7a30';

/// Notifier for managing multi-account operations
///
/// Copied from [MultiAccountNotifier].
@ProviderFor(MultiAccountNotifier)
final multiAccountNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      MultiAccountNotifier,
      MultiAccountSession
    >.internal(
      MultiAccountNotifier.new,
      name: r'multiAccountNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$multiAccountNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$MultiAccountNotifier = AutoDisposeAsyncNotifier<MultiAccountSession>;
String _$n8nWorkflowStatusHash() => r'8964d8913f4b2636e1b6839564901d11d5c7f57f';

/// Provider for n8n workflow status
///
/// Copied from [N8nWorkflowStatus].
@ProviderFor(N8nWorkflowStatus)
final n8nWorkflowStatusProvider =
    AutoDisposeNotifierProvider<
      N8nWorkflowStatus,
      Map<String, String>
    >.internal(
      N8nWorkflowStatus.new,
      name: r'n8nWorkflowStatusProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$n8nWorkflowStatusHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$N8nWorkflowStatus = AutoDisposeNotifier<Map<String, String>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
