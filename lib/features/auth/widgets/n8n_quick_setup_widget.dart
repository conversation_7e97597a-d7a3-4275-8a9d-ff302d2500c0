import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/config/n8n_config.dart';
import '../providers/multi_account_provider.dart';

/// Quick setup widget for n8n integration with guided configuration
class N8nQuickSetupWidget extends ConsumerStatefulWidget {
  const N8nQuickSetupWidget({super.key});

  @override
  ConsumerState<N8nQuickSetupWidget> createState() =>
      _N8nQuickSetupWidgetState();
}

class _N8nQuickSetupWidgetState extends ConsumerState<N8nQuickSetupWidget> {
  final _formKey = GlobalKey<FormState>();
  final _apiKeyController = TextEditingController();
  final _baseUrlController = TextEditingController();
  final _webhookSecretController = TextEditingController();

  bool _isLoading = false;
  int _currentStep = 0;
  bool _setupComplete = false;

  @override
  void initState() {
    super.initState();
    _baseUrlController.text = 'https://your-instance.app.n8n.cloud';
    _checkExistingConfiguration();
  }

  Future<void> _checkExistingConfiguration() async {
    try {
      final config = await N8nConfig.getConfiguration();
      if (config.isValid) {
        setState(() {
          _setupComplete = true;
          _currentStep = 3;
          _apiKeyController.text = config.apiKey ?? '';
          _baseUrlController.text = config.baseUrl;
          _webhookSecretController.text = config.webhookSecret ?? '';
        });
      }
    } catch (e) {
      // Configuration doesn't exist yet
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.settings, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'n8n Quick Setup',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                if (_setupComplete)
                  const Icon(Icons.check_circle, color: Colors.green),
              ],
            ),
            const SizedBox(height: 16),

            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else
              _buildSetupStepper(),
          ],
        ),
      ),
    );
  }

  Widget _buildSetupStepper() {
    return Theme(
      data: Theme.of(context).copyWith(
        colorScheme: Theme.of(
          context,
        ).colorScheme.copyWith(primary: Colors.blue),
      ),
      child: Stepper(
        currentStep: _currentStep,
        onStepTapped: (step) {
          if (step <= _currentStep || _setupComplete) {
            setState(() => _currentStep = step);
          }
        },
        controlsBuilder: (context, details) {
          return Row(
            children: [
              if (details.stepIndex < 3)
                ElevatedButton(
                  onPressed: details.onStepContinue,
                  child: Text(
                    details.stepIndex == 2 ? 'Complete Setup' : 'Next',
                  ),
                ),
              const SizedBox(width: 8),
              if (details.stepIndex > 0)
                TextButton(
                  onPressed: details.onStepCancel,
                  child: const Text('Back'),
                ),
            ],
          );
        },
        onStepContinue: () {
          if (_currentStep < 3) {
            if (_currentStep == 2) {
              _completeSetup();
            } else {
              setState(() => _currentStep++);
            }
          }
        },
        onStepCancel: () {
          if (_currentStep > 0) {
            setState(() => _currentStep--);
          }
        },
        steps: [
          Step(
            title: const Text('Create n8n Cloud Account'),
            content: _buildStep1(),
            isActive: _currentStep >= 0,
            state: _currentStep > 0 ? StepState.complete : StepState.indexed,
          ),
          Step(
            title: const Text('Import Workflows'),
            content: _buildStep2(),
            isActive: _currentStep >= 1,
            state: _currentStep > 1
                ? StepState.complete
                : _currentStep == 1
                ? StepState.indexed
                : StepState.disabled,
          ),
          Step(
            title: const Text('Configure Credentials'),
            content: _buildStep3(),
            isActive: _currentStep >= 2,
            state: _currentStep > 2
                ? StepState.complete
                : _currentStep == 2
                ? StepState.indexed
                : StepState.disabled,
          ),
          Step(
            title: const Text('Test Integration'),
            content: _buildStep4(),
            isActive: _currentStep >= 3,
            state: _setupComplete
                ? StepState.complete
                : _currentStep == 3
                ? StepState.indexed
                : StepState.disabled,
          ),
        ],
      ),
    );
  }

  Widget _buildStep1() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '1. Go to n8n.cloud and create a free account\n'
          '2. Create a new workspace named "billionaires-social"\n'
          '3. Get your API key from Settings → API Keys',
          style: TextStyle(fontSize: 14),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            ElevatedButton.icon(
              onPressed: () => _launchUrl('https://n8n.cloud'),
              icon: const Icon(Icons.open_in_new),
              label: const Text('Open n8n.cloud'),
            ),
            const SizedBox(width: 8),
            ElevatedButton.icon(
              onPressed: () => _copyToClipboard('billionaires-social'),
              icon: const Icon(Icons.copy),
              label: const Text('Copy Workspace Name'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStep2() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Import the workflow templates into your n8n workspace:\n\n'
          '1. In n8n, create a new workflow\n'
          '2. Click "..." → "Import from file"\n'
          '3. Import each of these files:',
          style: TextStyle(fontSize: 14),
        ),
        const SizedBox(height: 12),
        _buildWorkflowFileList(),
        const SizedBox(height: 16),
        const Text(
          '4. Activate each workflow by toggling the switch\n'
          '5. Copy the webhook URLs from each workflow',
          style: TextStyle(fontSize: 14),
        ),
      ],
    );
  }

  Widget _buildWorkflowFileList() {
    final workflows = [
      'account_switch_workflow.json',
      'user_engagement_workflow.json',
      'post_scheduling_workflow.json',
      'notification_workflow.json',
    ];

    return Column(
      children: workflows
          .map(
            (workflow) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 2),
              child: Row(
                children: [
                  const Icon(Icons.file_present, size: 16, color: Colors.blue),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'docs/n8n_workflows/$workflow',
                      style: const TextStyle(
                        fontSize: 12,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () =>
                        _copyToClipboard('docs/n8n_workflows/$workflow'),
                    icon: const Icon(Icons.copy, size: 16),
                    tooltip: 'Copy path',
                  ),
                ],
              ),
            ),
          )
          .toList(),
    );
  }

  Widget _buildStep3() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Enter your n8n credentials:',
            style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 16),

          TextFormField(
            controller: _baseUrlController,
            decoration: const InputDecoration(
              labelText: 'n8n Base URL',
              hintText: 'https://your-instance.app.n8n.cloud',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.link),
            ),
            validator: (value) {
              if (value?.isEmpty ?? true) return 'Base URL is required';
              if (!value!.startsWith('https://')) return 'Must use HTTPS';
              return null;
            },
          ),

          const SizedBox(height: 16),

          TextFormField(
            controller: _apiKeyController,
            decoration: const InputDecoration(
              labelText: 'API Key',
              hintText: 'Your n8n API key',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.key),
            ),
            obscureText: true,
            validator: (value) {
              if (value?.isEmpty ?? true) return 'API key is required';
              if (value!.length < 10) return 'API key seems too short';
              return null;
            },
          ),

          const SizedBox(height: 16),

          TextFormField(
            controller: _webhookSecretController,
            decoration: InputDecoration(
              labelText: 'Webhook Secret',
              hintText: 'Generate a secure random string',
              border: const OutlineInputBorder(),
              prefixIcon: const Icon(Icons.security),
              suffixIcon: IconButton(
                onPressed: _generateWebhookSecret,
                icon: const Icon(Icons.refresh),
                tooltip: 'Generate random secret',
              ),
            ),
            obscureText: true,
            validator: (value) {
              if (value?.isEmpty ?? true) return 'Webhook secret is required';
              if (value!.length < 32)
                return 'Secret should be at least 32 characters';
              return null;
            },
          ),

          const SizedBox(height: 16),

          ElevatedButton.icon(
            onPressed: _generateWebhookSecret,
            icon: const Icon(Icons.auto_fix_high),
            label: const Text('Generate Secure Secret'),
          ),
        ],
      ),
    );
  }

  Widget _buildStep4() {
    final healthAsync = ref.watch(n8nHealthStatusProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Integration Status:',
          style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 16),

        healthAsync.when(
          data: (health) => _buildHealthStatus(health),
          loading: () => const Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Testing connection...'),
            ],
          ),
          error: (error, _) => _buildErrorStatus(error.toString()),
        ),

        const SizedBox(height: 16),

        Row(
          children: [
            ElevatedButton.icon(
              onPressed: () {
                ref.invalidate(n8nHealthStatusProvider);
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Test Again'),
            ),
            const SizedBox(width: 8),
            if (_setupComplete)
              ElevatedButton.icon(
                onPressed: () => Navigator.of(context).pushNamed('/n8n-test'),
                icon: const Icon(Icons.play_arrow),
                label: const Text('Open Test Screen'),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildHealthStatus(N8nHealthStatus health) {
    final color = health.isHealthy ? Colors.green : Colors.red;
    final icon = health.isHealthy ? Icons.check_circle : Icons.error;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  health.isHealthy
                      ? 'Integration Working!'
                      : 'Integration Failed',
                  style: TextStyle(fontWeight: FontWeight.bold, color: color),
                ),
                Text(health.message, style: const TextStyle(fontSize: 12)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorStatus(String error) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          const Icon(Icons.error, color: Colors.red),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Connection Error',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
                Text(error, style: const TextStyle(fontSize: 12)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _generateWebhookSecret() {
    // Generate a secure random string
    const chars = 'abcdef0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    final secret = List.generate(
      64,
      (index) => chars[(random + index) % chars.length],
    ).join();

    setState(() {
      _webhookSecretController.text = secret;
    });

    _copyToClipboard(secret);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Secure secret generated and copied to clipboard'),
        backgroundColor: Colors.green,
      ),
    );
  }

  Future<void> _completeSetup() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      await N8nConfig.initialize(
        apiKey: _apiKeyController.text.trim(),
        baseUrl: _baseUrlController.text.trim(),
        webhookSecret: _webhookSecretController.text.trim(),
        enabled: true,
      );

      setState(() {
        _setupComplete = true;
        _currentStep = 3;
      });

      // Refresh providers
      ref.invalidate(n8nIntegrationEnabledProvider);
      ref.invalidate(n8nConfigurationValidProvider);
      ref.invalidate(n8nHealthStatusProvider);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('n8n integration configured successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Setup failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
  }

  void _launchUrl(String url) {
    // In a real app, you'd use url_launcher package
    _copyToClipboard(url);
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('URL copied to clipboard: $url')));
  }

  @override
  void dispose() {
    _apiKeyController.dispose();
    _baseUrlController.dispose();
    _webhookSecretController.dispose();
    super.dispose();
  }
}
