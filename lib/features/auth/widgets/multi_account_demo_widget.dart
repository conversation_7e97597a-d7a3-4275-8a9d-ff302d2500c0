import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/auth/providers/multi_account_provider.dart';
import 'package:billionaires_social/features/auth/widgets/account_switcher_widget.dart';

/// Demo widget to showcase the multi-account switching functionality
class MultiAccountDemoWidget extends ConsumerWidget {
  const MultiAccountDemoWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final savedAccountsAsync = ref.watch(savedAccountsProvider);
    final activeAccountAsync = ref.watch(activeAccountProvider);
    final sessionAsync = ref.watch(multiAccountSessionProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Multi-Account Demo'),
        actions: [
          IconButton(
            icon: const Icon(Icons.switch_account),
            onPressed: () => _showAccountSwitcher(context),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Current Session Info
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Current Session',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    sessionAsync.when(
                      data: (session) => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('Session ID: ${session.metadata?.sessionId ?? 'None'}'),
                          Text('Active Account: ${session.activeAccount?.displayName ?? 'None'}'),
                          Text('Status: ${session.isValid ? 'Active' : 'Inactive'}'),
                          Text('Last Activity: ${session.lastActivityTime?.toString() ?? 'Never'}'),
                        ],
                      ),
                      loading: () => const CircularProgressIndicator(),
                      error: (error, stack) => Text('Error: $error'),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Active Account Info
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Active Account',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    activeAccountAsync.when(
                      data: (account) => account != null
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Display Name: ${account.displayName}'),
                                Text('Email: ${account.email}'),
                                Text('Username: ${account.username}'),
                                Text('Account Type: ${account.accountType}'),
                                Text('Last Used: ${account.lastUsedAt?.toString() ?? 'Never'}'),
                                if (account.isVerified)
                                  const Chip(
                                    label: Text('Verified'),
                                    backgroundColor: Colors.green,
                                  ),
                                if (account.isBillionaire)
                                  const Chip(
                                    label: Text('Billionaire'),
                                    backgroundColor: Colors.amber,
                                  ),
                              ],
                            )
                          : const Text('No active account'),
                      loading: () => const CircularProgressIndicator(),
                      error: (error, stack) => Text('Error: $error'),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Saved Accounts List
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Saved Accounts',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: savedAccountsAsync.when(
                          data: (accounts) => accounts.isEmpty
                              ? const Center(
                                  child: Text('No saved accounts'),
                                )
                              : ListView.builder(
                                  itemCount: accounts.length,
                                  itemBuilder: (context, index) {
                                    final account = accounts[index];
                                    return ListTile(
                                      leading: CircleAvatar(
                                        backgroundImage: account.profilePictureUrl != null
                                            ? NetworkImage(account.profilePictureUrl!)
                                            : null,
                                        child: account.profilePictureUrl == null
                                            ? Text(account.displayName[0].toUpperCase())
                                            : null,
                                      ),
                                      title: Text(account.displayName),
                                      subtitle: Text(account.email),
                                      trailing: account.isActive
                                          ? const Icon(
                                              Icons.check_circle,
                                              color: Colors.green,
                                            )
                                          : IconButton(
                                              icon: const Icon(Icons.switch_account),
                                              onPressed: () => _switchToAccount(ref, account.id),
                                            ),
                                    );
                                  },
                                ),
                          loading: () => const Center(child: CircularProgressIndicator()),
                          error: (error, stack) => Center(child: Text('Error: $error')),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddAccountDialog(context, ref),
        child: const Icon(Icons.add),
      ),
    );
  }

  void _showAccountSwitcher(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => const AccountSwitcherWidget(),
    );
  }

  Future<void> _switchToAccount(WidgetRef ref, String accountId) async {
    try {
      final provider = ref.read(multiAccountNotifierProvider.notifier);
      final result = await provider.switchToAccount(accountId);
      
      if (!result.success && ref.context.mounted) {
        // Show error message
        ScaffoldMessenger.of(ref.context).showSnackBar(
          SnackBar(
            content: Text('Failed to switch account: ${result.errorMessage}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (ref.context.mounted) {
        ScaffoldMessenger.of(ref.context).showSnackBar(
          SnackBar(
            content: Text('Error switching account: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showAddAccountDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Account'),
        content: const Text(
          'This demo shows the multi-account system structure. '
          'In a real implementation, this would open the login screen '
          'to add a new account.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
