import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/saved_account_model.dart';
import '../models/multi_account_session.dart';
import '../providers/multi_account_provider.dart';
import '../../../core/config/n8n_config.dart';

/// Enhanced account switcher with n8n workflow integration
class EnhancedAccountSwitcher extends ConsumerStatefulWidget {
  const EnhancedAccountSwitcher({super.key});

  @override
  ConsumerState<EnhancedAccountSwitcher> createState() =>
      _EnhancedAccountSwitcherState();
}

class _EnhancedAccountSwitcherState
    extends ConsumerState<EnhancedAccountSwitcher> {
  bool _isProcessingSwitch = false;
  String? _currentWorkflowStatus;

  @override
  Widget build(BuildContext context) {
    final sessionAsync = ref.watch(multiAccountSessionProvider);
    final n8nHealthAsync = ref.watch(n8nHealthStatusProvider);
    final workflowStatus = ref.watch(n8nWorkflowStatusProvider);

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(n8nHealthAsync),
            const SizedBox(height: 16),

            sessionAsync.when(
              data: (session) => _buildAccountList(session),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => _buildErrorWidget(error),
            ),

            if (_currentWorkflowStatus != null) ...[
              const SizedBox(height: 16),
              _buildWorkflowStatus(_currentWorkflowStatus!),
            ],

            if (workflowStatus.isNotEmpty) ...[
              const SizedBox(height: 16),
              _buildActiveWorkflows(workflowStatus),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(AsyncValue<N8nHealthStatus> n8nHealthAsync) {
    return Row(
      children: [
        const Icon(Icons.swap_horiz, color: Colors.blue),
        const SizedBox(width: 8),
        const Text(
          'Account Switcher',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const Spacer(),
        if (_isProcessingSwitch)
          const SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
        const SizedBox(width: 8),
        n8nHealthAsync.when(
          data: (health) => _buildHealthIndicator(health),
          loading: () => const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(strokeWidth: 1),
          ),
          error: (_, __) =>
              const Icon(Icons.error_outline, size: 16, color: Colors.red),
        ),
      ],
    );
  }

  Widget _buildHealthIndicator(N8nHealthStatus health) {
    Color color;
    IconData icon;

    switch (health.status) {
      case 'healthy':
        color = Colors.green;
        icon = Icons.check_circle;
        break;
      case 'disabled':
        color = Colors.grey;
        icon = Icons.power_off;
        break;
      case 'misconfigured':
        color = Colors.orange;
        icon = Icons.warning;
        break;
      default:
        color = Colors.red;
        icon = Icons.error;
    }

    return Tooltip(
      message: health.message,
      child: Icon(icon, size: 16, color: color),
    );
  }

  Widget _buildAccountList(MultiAccountSession session) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Current Account
        if (session.activeAccount != null) ...[
          const Text(
            'Current Account',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 8),
          _buildAccountTile(
            session.activeAccount!,
            isActive: true,
            onTap: null,
          ),
          const SizedBox(height: 16),
        ],

        // Available Accounts
        if (session.savedAccounts.isNotEmpty) ...[
          const Text(
            'Switch to Account',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 8),

          ...session.savedAccounts
              .where((account) => account.id != session.activeAccount?.id)
              .map(
                (account) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: _buildAccountTile(
                    account,
                    isActive: false,
                    onTap: _isProcessingSwitch
                        ? null
                        : () => _switchAccount(account),
                  ),
                ),
              ),
        ],

        // Analytics Section
        if (session.activeAccount != null) ...[
          const SizedBox(height: 16),
          _buildAccountAnalytics(session.activeAccount!),
        ],
      ],
    );
  }

  Widget _buildAccountTile(
    SavedAccountModel account, {
    required bool isActive,
    VoidCallback? onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: isActive ? Colors.blue : Colors.grey.withValues(alpha: 0.3),
          width: isActive ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        leading: CircleAvatar(
          backgroundImage: account.profilePictureUrl != null
              ? NetworkImage(account.profilePictureUrl!)
              : null,
          child: account.profilePictureUrl == null
              ? Text(account.displayName.substring(0, 1).toUpperCase())
              : null,
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                account.displayName,
                style: TextStyle(
                  fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
            if (account.isVerified)
              const Icon(Icons.verified, color: Colors.blue, size: 16),
            if (account.isBillionaire)
              const Icon(Icons.diamond, color: Colors.amber, size: 16),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(account.email),
            Text(
              account.accountType.toUpperCase(),
              style: TextStyle(
                fontSize: 12,
                color: _getAccountTypeColor(account.accountType),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        trailing: isActive
            ? const Icon(Icons.check_circle, color: Colors.green)
            : const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }

  Widget _buildAccountAnalytics(SavedAccountModel account) {
    final analyticsAsync = ref.watch(accountAnalyticsProvider(account.id));

    return analyticsAsync.when(
      data: (analytics) {
        if (analytics == null) return const SizedBox.shrink();

        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Account Analytics (via n8n)',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildAnalyticItem(
                    'Posts Today',
                    '${analytics['postsToday'] ?? 0}',
                    Icons.post_add,
                  ),
                  _buildAnalyticItem(
                    'Engagement',
                    '${analytics['engagementRate'] ?? 0}%',
                    Icons.favorite,
                  ),
                  _buildAnalyticItem(
                    'Followers',
                    '${analytics['followersCount'] ?? 0}',
                    Icons.people,
                  ),
                ],
              ),
            ],
          ),
        );
      },
      loading: () => const SizedBox(
        height: 40,
        child: Center(child: CircularProgressIndicator()),
      ),
      error: (error, stack) => const SizedBox.shrink(),
    );
  }

  Widget _buildAnalyticItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, size: 20, color: Colors.blue),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
        ),
        Text(label, style: const TextStyle(fontSize: 12, color: Colors.grey)),
      ],
    );
  }

  Widget _buildWorkflowStatus(String status) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(
            width: 12,
            height: 12,
            child: CircularProgressIndicator(strokeWidth: 1),
          ),
          const SizedBox(width: 8),
          Text(
            status,
            style: const TextStyle(fontSize: 12, color: Colors.blue),
          ),
        ],
      ),
    );
  }

  Widget _buildActiveWorkflows(Map<String, String> workflows) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Active n8n Workflows',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.blue,
            ),
          ),
          const SizedBox(height: 8),
          ...workflows.entries.map(
            (entry) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                children: [
                  Icon(
                    Icons.play_circle_outline,
                    size: 16,
                    color: Colors.blue.withValues(alpha: 0.7),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${entry.key}: ${entry.value}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue.withValues(alpha: 0.8),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(Object error) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          const Icon(Icons.error_outline, color: Colors.red),
          const SizedBox(height: 8),
          Text(
            'Error loading accounts: $error',
            style: const TextStyle(color: Colors.red),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Color _getAccountTypeColor(String accountType) {
    switch (accountType.toLowerCase()) {
      case 'business':
        return Colors.orange;
      case 'celebrity':
        return Colors.purple;
      case 'billionaire':
        return Colors.amber;
      default:
        return Colors.blue;
    }
  }

  Future<void> _switchAccount(SavedAccountModel targetAccount) async {
    setState(() {
      _isProcessingSwitch = true;
      _currentWorkflowStatus = 'Initiating account switch...';
    });

    try {
      // Update workflow status
      ref
          .read(n8nWorkflowStatusProvider.notifier)
          .updateStatus(
            'account_switch',
            'Processing switch to ${targetAccount.displayName}',
          );

      setState(() {
        _currentWorkflowStatus = 'Switching account...';
      });

      // Perform the account switch
      final authService = ref.read(multiAccountNotifierProvider.notifier);
      final result = await authService.switchToAccount(targetAccount.id);

      if (result.success) {
        setState(() {
          _currentWorkflowStatus = 'Account switch completed!';
        });

        // Clear workflow status after success
        ref
            .read(n8nWorkflowStatusProvider.notifier)
            .clearStatus('account_switch');

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Successfully switched to ${targetAccount.displayName}',
              ),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        throw Exception(result.errorMessage ?? 'Unknown error');
      }

      // Clear status after delay
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          setState(() {
            _currentWorkflowStatus = null;
          });
        }
      });
    } catch (e) {
      setState(() {
        _currentWorkflowStatus = 'Switch failed: ${e.toString()}';
      });

      // Update workflow status with error
      ref
          .read(n8nWorkflowStatusProvider.notifier)
          .updateStatus('account_switch', 'Failed: ${e.toString()}');

      // Clear error status after delay
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          setState(() {
            _currentWorkflowStatus = null;
          });
          ref
              .read(n8nWorkflowStatusProvider.notifier)
              .clearStatus('account_switch');
        }
      });

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to switch account: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isProcessingSwitch = false;
      });
    }
  }
}

/// Simple n8n configuration widget for testing
class N8nConfigurationWidget extends ConsumerStatefulWidget {
  const N8nConfigurationWidget({super.key});

  @override
  ConsumerState<N8nConfigurationWidget> createState() =>
      _N8nConfigurationWidgetState();
}

class _N8nConfigurationWidgetState
    extends ConsumerState<N8nConfigurationWidget> {
  final _apiKeyController = TextEditingController();
  final _baseUrlController = TextEditingController();
  final _webhookSecretController = TextEditingController();
  bool _isEnabled = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadConfiguration();
  }

  Future<void> _loadConfiguration() async {
    setState(() => _isLoading = true);

    try {
      final config = await N8nConfig.getConfiguration();
      setState(() {
        _apiKeyController.text = config.apiKey ?? '';
        _baseUrlController.text = config.baseUrl;
        _webhookSecretController.text = config.webhookSecret ?? '';
        _isEnabled = config.enabled;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load configuration: $e')),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _saveConfiguration() async {
    setState(() => _isLoading = true);

    try {
      await N8nConfig.initialize(
        apiKey: _apiKeyController.text.trim(),
        baseUrl: _baseUrlController.text.trim(),
        webhookSecret: _webhookSecretController.text.trim(),
        enabled: _isEnabled,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Configuration saved successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }

      // Refresh providers
      ref.invalidate(n8nIntegrationEnabledProvider);
      ref.invalidate(n8nConfigurationValidProvider);
      ref.invalidate(n8nHealthStatusProvider);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to save configuration: $e')),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'n8n Configuration',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else ...[
              SwitchListTile(
                title: const Text('Enable n8n Integration'),
                subtitle: const Text('Enable automated workflows'),
                value: _isEnabled,
                onChanged: (value) => setState(() => _isEnabled = value),
              ),

              const SizedBox(height: 16),

              TextField(
                controller: _baseUrlController,
                decoration: const InputDecoration(
                  labelText: 'n8n Base URL',
                  hintText: 'https://your-n8n-instance.app.n8n.cloud',
                  border: OutlineInputBorder(),
                ),
              ),

              const SizedBox(height: 16),

              TextField(
                controller: _apiKeyController,
                decoration: const InputDecoration(
                  labelText: 'API Key',
                  hintText: 'Your n8n API key',
                  border: OutlineInputBorder(),
                ),
                obscureText: true,
              ),

              const SizedBox(height: 16),

              TextField(
                controller: _webhookSecretController,
                decoration: const InputDecoration(
                  labelText: 'Webhook Secret',
                  hintText: 'Your webhook secret for signature verification',
                  border: OutlineInputBorder(),
                ),
                obscureText: true,
              ),

              const SizedBox(height: 24),

              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _saveConfiguration,
                      child: const Text('Save Configuration'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: _loadConfiguration,
                    child: const Text('Reload'),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _apiKeyController.dispose();
    _baseUrlController.dispose();
    _webhookSecretController.dispose();
    super.dispose();
  }
}
