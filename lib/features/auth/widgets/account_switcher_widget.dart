import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/auth/models/saved_account_model.dart';
import 'package:billionaires_social/features/auth/models/multi_account_session.dart';
import 'package:billionaires_social/features/auth/providers/multi_account_provider.dart';
import 'package:billionaires_social/core/widgets/cached_network_image_widget.dart';

/// Widget that displays the account switcher interface
class AccountSwitcherWidget extends ConsumerStatefulWidget {
  final VoidCallback? onAccountSwitched;
  final bool showAddAccount;
  final bool requireBiometric;

  const AccountSwitcherWidget({
    super.key,
    this.onAccountSwitched,
    this.showAddAccount = true,
    this.requireBiometric = false,
  });

  @override
  ConsumerState<AccountSwitcherWidget> createState() => _AccountSwitcherWidgetState();
}

class _AccountSwitcherWidgetState extends ConsumerState<AccountSwitcherWidget> {
  bool _isLoading = false;
  String? _errorMessage;

  @override
  Widget build(BuildContext context) {
    final sessionAsync = ref.watch(multiAccountSessionProvider);

    return sessionAsync.when(
      data: (session) => _buildAccountSwitcher(session),
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
      error: (error, stack) => _buildErrorWidget(error.toString()),
    );
  }

  Widget _buildAccountSwitcher(MultiAccountSession session) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          _buildHeader(),
          
          // Account List
          if (session.savedAccounts.isNotEmpty)
            _buildAccountList(session.savedAccounts, session.activeAccount),
          
          // Add Account Button
          if (widget.showAddAccount)
            _buildAddAccountButton(),
          
          // Error Message
          if (_errorMessage != null)
            _buildErrorMessage(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.account_circle,
            color: Theme.of(context).primaryColor,
            size: 24,
          ),
          const SizedBox(width: 12),
          Text(
            'Switch Account',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close),
            iconSize: 20,
          ),
        ],
      ),
    );
  }

  Widget _buildAccountList(List<SavedAccountModel> accounts, SavedAccountModel? activeAccount) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: accounts.length,
      separatorBuilder: (context, index) => const Divider(height: 1),
      itemBuilder: (context, index) {
        final account = accounts[index];
        final isActive = activeAccount?.id == account.id;
        
        return _buildAccountTile(account, isActive);
      },
    );
  }

  Widget _buildAccountTile(SavedAccountModel account, bool isActive) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      leading: Stack(
        children: [
          CircleAvatar(
            radius: 24,
            backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
            child: account.profilePictureUrl?.isNotEmpty == true
                ? ClipOval(
                    child: CachedNetworkImageWidget(
                      imageUrl: account.profilePictureUrl!,
                      width: 48,
                      height: 48,
                      fit: BoxFit.cover,
                    ),
                  )
                : Text(
                    account.displayName.isNotEmpty 
                        ? account.displayName[0].toUpperCase()
                        : account.email[0].toUpperCase(),
                    style: TextStyle(
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
          if (isActive)
            Positioned(
              bottom: 0,
              right: 0,
              child: Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 2),
                ),
                child: const Icon(
                  Icons.check,
                  size: 10,
                  color: Colors.white,
                ),
              ),
            ),
          if (account.isVerified)
            Positioned(
              top: 0,
              right: 0,
              child: Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  color: Colors.blue,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 2),
                ),
                child: const Icon(
                  Icons.verified,
                  size: 10,
                  color: Colors.white,
                ),
              ),
            ),
        ],
      ),
      title: Row(
        children: [
          Expanded(
            child: Text(
              account.displayName.isNotEmpty ? account.displayName : account.username,
              style: TextStyle(
                fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                color: isActive ? Theme.of(context).primaryColor : null,
              ),
            ),
          ),
          if (account.isBillionaire)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.amber,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                'BILLIONAIRE',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
        ],
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '@${account.username}',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
            ),
          ),
          if (account.metadata != null)
            Text(
              '${account.metadata!.followersCount} followers • ${account.metadata!.postsCount} posts',
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 11,
              ),
            ),
          if (account.lastUsedAt != null)
            Text(
              'Last used: ${_formatLastUsed(account.lastUsedAt!)}',
              style: TextStyle(
                color: Colors.grey[400],
                fontSize: 10,
              ),
            ),
        ],
      ),
      trailing: isActive
          ? Icon(
              Icons.radio_button_checked,
              color: Theme.of(context).primaryColor,
            )
          : const Icon(Icons.radio_button_unchecked),
      onTap: isActive ? null : () => _switchToAccount(account),
      enabled: !_isLoading,
    );
  }

  Widget _buildAddAccountButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SizedBox(
        width: double.infinity,
        child: OutlinedButton.icon(
          onPressed: _isLoading ? null : _showAddAccountDialog,
          icon: const Icon(Icons.add),
          label: const Text('Add Another Account'),
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildErrorMessage() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          const Icon(Icons.error, color: Colors.red, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _errorMessage!,
              style: const TextStyle(color: Colors.red, fontSize: 12),
            ),
          ),
          IconButton(
            onPressed: () => setState(() => _errorMessage = null),
            icon: const Icon(Icons.close, size: 16),
            color: Colors.red,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(String error) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.error, color: Colors.red, size: 48),
          const SizedBox(height: 16),
          Text(
            'Failed to load accounts',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => ref.refresh(multiAccountSessionProvider),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Future<void> _switchToAccount(SavedAccountModel account) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final result = await ref
          .read(multiAccountAuthServiceProvider)
          .switchToAccount(account.id, requireBiometric: widget.requireBiometric);

      if (result.success) {
        // Refresh the session
        final _ = await ref.refresh(multiAccountSessionProvider.future);
        
        // Call callback
        widget.onAccountSwitched?.call();
        
        // Close the switcher
        if (mounted) {
          Navigator.of(context).pop();
        }
      } else {
        setState(() {
          _errorMessage = result.errorMessage ?? 'Failed to switch account';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error switching account: $e';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showAddAccountDialog() {
    showDialog(
      context: context,
      builder: (context) => const AddAccountDialog(),
    );
  }

  String _formatLastUsed(DateTime lastUsed) {
    final now = DateTime.now();
    final difference = now.difference(lastUsed);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${lastUsed.day}/${lastUsed.month}/${lastUsed.year}';
    }
  }
}

/// Dialog for adding a new account
class AddAccountDialog extends ConsumerStatefulWidget {
  const AddAccountDialog({super.key});

  @override
  ConsumerState<AddAccountDialog> createState() => _AddAccountDialogState();
}

class _AddAccountDialogState extends ConsumerState<AddAccountDialog> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _rememberPassword = true;
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Account'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'Email',
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'Please enter your email';
                }
                if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value!)) {
                  return 'Please enter a valid email';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _passwordController,
              decoration: const InputDecoration(
                labelText: 'Password',
                prefixIcon: Icon(Icons.lock),
              ),
              obscureText: true,
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'Please enter your password';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            CheckboxListTile(
              title: const Text('Remember password'),
              subtitle: const Text('Store password securely for quick switching'),
              value: _rememberPassword,
              onChanged: (value) => setState(() => _rememberPassword = value ?? true),
              controlAffinity: ListTileControlAffinity.leading,
            ),
            if (_errorMessage != null) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _errorMessage!,
                  style: const TextStyle(color: Colors.red),
                ),
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _addAccount,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Add Account'),
        ),
      ],
    );
  }

  Future<void> _addAccount() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final result = await ref
          .read(multiAccountAuthServiceProvider)
          .addAccount(
            email: _emailController.text.trim(),
            password: _passwordController.text,
            rememberPassword: _rememberPassword,
          );

      if (result.success) {
        // Refresh the session
        final _ = await ref.refresh(multiAccountSessionProvider.future);
        
        if (mounted) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Account added successfully: ${result.switchedToAccount?.email}'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        setState(() {
          _errorMessage = result.errorMessage ?? 'Failed to add account';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error adding account: $e';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
