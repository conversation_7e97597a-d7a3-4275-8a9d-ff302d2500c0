import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/auth/models/saved_account_model.dart';
import 'package:billionaires_social/features/auth/providers/multi_account_provider.dart';
import 'package:billionaires_social/features/auth/widgets/account_switcher_widget.dart';
import 'package:billionaires_social/core/widgets/cached_network_image_widget.dart';

/// Quick account switcher that can be embedded in app bars or drawers
class QuickAccountSwitcher extends ConsumerWidget {
  final bool showAccountName;
  final bool showDropdownIcon;
  final VoidCallback? onAccountSwitched;

  const QuickAccountSwitcher({
    super.key,
    this.showAccountName = true,
    this.showDropdownIcon = true,
    this.onAccountSwitched,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sessionAsync = ref.watch(multiAccountSessionProvider);

    return sessionAsync.when(
      data: (session) {
        final activeAccount = session.activeAccount;
        if (activeAccount == null) {
          return _buildSignInPrompt(context);
        }
        
        return _buildAccountButton(context, ref, activeAccount, session.savedAccounts);
      },
      loading: () => _buildLoadingState(),
      error: (error, stack) => _buildErrorState(context, ref),
    );
  }

  Widget _buildAccountButton(
    BuildContext context,
    WidgetRef ref,
    SavedAccountModel activeAccount,
    List<SavedAccountModel> savedAccounts,
  ) {
    return InkWell(
      onTap: () => _showAccountSwitcher(context, ref),
      borderRadius: BorderRadius.circular(24),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(24),
          border: Border.all(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Profile Picture
            Stack(
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  child: activeAccount.profilePictureUrl?.isNotEmpty == true
                      ? ClipOval(
                          child: CachedNetworkImageWidget(
                            imageUrl: activeAccount.profilePictureUrl!,
                            width: 32,
                            height: 32,
                            fit: BoxFit.cover,
                          ),
                        )
                      : Text(
                          activeAccount.displayName.isNotEmpty 
                              ? activeAccount.displayName[0].toUpperCase()
                              : activeAccount.email[0].toUpperCase(),
                          style: TextStyle(
                            color: Theme.of(context).primaryColor,
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                ),
                if (activeAccount.isVerified)
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: Colors.blue,
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 1),
                      ),
                      child: const Icon(
                        Icons.verified,
                        size: 8,
                        color: Colors.white,
                      ),
                    ),
                  ),
              ],
            ),
            
            if (showAccountName) ...[
              const SizedBox(width: 8),
              // Account Name
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      activeAccount.displayName.isNotEmpty 
                          ? activeAccount.displayName 
                          : activeAccount.username,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (savedAccounts.length > 1)
                      Text(
                        '${savedAccounts.length} accounts',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                          fontSize: 11,
                        ),
                      ),
                  ],
                ),
              ),
            ],
            
            if (showDropdownIcon) ...[
              const SizedBox(width: 4),
              Icon(
                Icons.keyboard_arrow_down,
                size: 16,
                color: Colors.grey[600],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSignInPrompt(BuildContext context) {
    return InkWell(
      onTap: () {
        // Navigate to sign in screen
        Navigator.of(context).pushNamed('/auth/signin');
      },
      borderRadius: BorderRadius.circular(24),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(24),
          border: Border.all(
            color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.account_circle,
              size: 20,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(width: 8),
            Text(
              'Sign In',
              style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
          if (showAccountName) ...[
            const SizedBox(width: 8),
            const Text('Loading...'),
          ],
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, WidgetRef ref) {
    return InkWell(
      onTap: () => ref.refresh(multiAccountSessionProvider),
      borderRadius: BorderRadius.circular(24),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.red.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(24),
          border: Border.all(
            color: Colors.red.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.error,
              size: 16,
              color: Colors.red,
            ),
            if (showAccountName) ...[
              const SizedBox(width: 8),
              const Text(
                'Error',
                style: TextStyle(color: Colors.red),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showAccountSwitcher(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: AccountSwitcherWidget(
          onAccountSwitched: onAccountSwitched,
        ),
      ),
    );
  }
}

/// Compact version of the quick account switcher for tight spaces
class CompactAccountSwitcher extends ConsumerWidget {
  final VoidCallback? onAccountSwitched;

  const CompactAccountSwitcher({
    super.key,
    this.onAccountSwitched,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sessionAsync = ref.watch(multiAccountSessionProvider);

    return sessionAsync.when(
      data: (session) {
        final activeAccount = session.activeAccount;
        if (activeAccount == null) {
          return IconButton(
            onPressed: () => Navigator.of(context).pushNamed('/auth/signin'),
            icon: const Icon(Icons.account_circle),
            tooltip: 'Sign In',
          );
        }
        
        return _buildCompactButton(context, ref, activeAccount);
      },
      loading: () => const SizedBox(
        width: 24,
        height: 24,
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
      error: (error, stack) => IconButton(
        onPressed: () => ref.refresh(multiAccountSessionProvider),
        icon: const Icon(Icons.error, color: Colors.red),
        tooltip: 'Retry',
      ),
    );
  }

  Widget _buildCompactButton(
    BuildContext context,
    WidgetRef ref,
    SavedAccountModel activeAccount,
  ) {
    return GestureDetector(
      onTap: () => _showAccountSwitcher(context, ref),
      child: Stack(
        children: [
          CircleAvatar(
            radius: 18,
            backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
            child: activeAccount.profilePictureUrl?.isNotEmpty == true
                ? ClipOval(
                    child: CachedNetworkImageWidget(
                      imageUrl: activeAccount.profilePictureUrl!,
                      width: 36,
                      height: 36,
                      fit: BoxFit.cover,
                    ),
                  )
                : Text(
                    activeAccount.displayName.isNotEmpty 
                        ? activeAccount.displayName[0].toUpperCase()
                        : activeAccount.email[0].toUpperCase(),
                    style: TextStyle(
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
          if (activeAccount.isVerified)
            Positioned(
              bottom: 0,
              right: 0,
              child: Container(
                width: 14,
                height: 14,
                decoration: BoxDecoration(
                  color: Colors.blue,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 2),
                ),
                child: const Icon(
                  Icons.verified,
                  size: 8,
                  color: Colors.white,
                ),
              ),
            ),
          // Indicator for multiple accounts
          Positioned(
            top: 0,
            right: 0,
            child: Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 1),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showAccountSwitcher(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: AccountSwitcherWidget(
          onAccountSwitched: onAccountSwitched,
        ),
      ),
    );
  }
}

/// Account switcher for app drawer
class DrawerAccountSwitcher extends ConsumerWidget {
  final VoidCallback? onAccountSwitched;

  const DrawerAccountSwitcher({
    super.key,
    this.onAccountSwitched,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sessionAsync = ref.watch(multiAccountSessionProvider);

    return sessionAsync.when(
      data: (session) {
        final activeAccount = session.activeAccount;
        if (activeAccount == null) {
          return _buildSignInTile(context);
        }
        
        return _buildAccountTile(context, ref, activeAccount, session.savedAccounts);
      },
      loading: () => const ListTile(
        leading: CircularProgressIndicator(),
        title: Text('Loading account...'),
      ),
      error: (error, stack) => ListTile(
        leading: const Icon(Icons.error, color: Colors.red),
        title: const Text('Error loading account'),
        subtitle: Text(error.toString()),
        onTap: () => ref.refresh(multiAccountSessionProvider),
      ),
    );
  }

  Widget _buildAccountTile(
    BuildContext context,
    WidgetRef ref,
    SavedAccountModel activeAccount,
    List<SavedAccountModel> savedAccounts,
  ) {
    return ListTile(
      leading: Stack(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
            child: activeAccount.profilePictureUrl?.isNotEmpty == true
                ? ClipOval(
                    child: CachedNetworkImageWidget(
                      imageUrl: activeAccount.profilePictureUrl!,
                      width: 40,
                      height: 40,
                      fit: BoxFit.cover,
                    ),
                  )
                : Text(
                    activeAccount.displayName.isNotEmpty 
                        ? activeAccount.displayName[0].toUpperCase()
                        : activeAccount.email[0].toUpperCase(),
                    style: TextStyle(
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
          if (activeAccount.isVerified)
            Positioned(
              bottom: 0,
              right: 0,
              child: Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  color: Colors.blue,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 2),
                ),
                child: const Icon(
                  Icons.verified,
                  size: 10,
                  color: Colors.white,
                ),
              ),
            ),
        ],
      ),
      title: Text(
        activeAccount.displayName.isNotEmpty 
            ? activeAccount.displayName 
            : activeAccount.username,
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('@${activeAccount.username}'),
          if (savedAccounts.length > 1)
            Text(
              '${savedAccounts.length} accounts available',
              style: TextStyle(
                fontSize: 11,
                color: Colors.grey[600],
              ),
            ),
        ],
      ),
      trailing: savedAccounts.length > 1 
          ? const Icon(Icons.keyboard_arrow_down)
          : null,
      onTap: savedAccounts.length > 1 
          ? () => _showAccountSwitcher(context, ref)
          : null,
    );
  }

  Widget _buildSignInTile(BuildContext context) {
    return ListTile(
      leading: Icon(
        Icons.account_circle,
        color: Theme.of(context).primaryColor,
      ),
      title: Text(
        'Sign In',
        style: TextStyle(
          color: Theme.of(context).primaryColor,
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: const Text('Access your account'),
      onTap: () => Navigator.of(context).pushNamed('/auth/signin'),
    );
  }

  void _showAccountSwitcher(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: AccountSwitcherWidget(
          onAccountSwitched: onAccountSwitched,
        ),
      ),
    );
  }
}
