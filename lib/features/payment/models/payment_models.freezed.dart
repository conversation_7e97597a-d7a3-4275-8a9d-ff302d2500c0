// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

PaymentMethod _$PaymentMethodFromJson(Map<String, dynamic> json) {
  return _PaymentMethod.fromJson(json);
}

/// @nodoc
mixin _$PaymentMethod {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  PaymentMethodType get type => throw _privateConstructorUsedError;
  String get lastFourDigits => throw _privateConstructorUsedError;
  String get cardBrand => throw _privateConstructorUsedError;
  DateTime get expiryDate => throw _privateConstructorUsedError;
  bool get isDefault => throw _privateConstructorUsedError;
  bool get isEnabled => throw _privateConstructorUsedError;
  String? get cardholderName => throw _privateConstructorUsedError;
  String? get billingAddress => throw _privateConstructorUsedError;
  String? get paymentMethodId =>
      throw _privateConstructorUsedError; // External payment provider ID
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime? get lastUsedAt => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this PaymentMethod to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PaymentMethod
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PaymentMethodCopyWith<PaymentMethod> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentMethodCopyWith<$Res> {
  factory $PaymentMethodCopyWith(
    PaymentMethod value,
    $Res Function(PaymentMethod) then,
  ) = _$PaymentMethodCopyWithImpl<$Res, PaymentMethod>;
  @useResult
  $Res call({
    String id,
    String userId,
    PaymentMethodType type,
    String lastFourDigits,
    String cardBrand,
    DateTime expiryDate,
    bool isDefault,
    bool isEnabled,
    String? cardholderName,
    String? billingAddress,
    String? paymentMethodId,
    DateTime createdAt,
    DateTime? lastUsedAt,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class _$PaymentMethodCopyWithImpl<$Res, $Val extends PaymentMethod>
    implements $PaymentMethodCopyWith<$Res> {
  _$PaymentMethodCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentMethod
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? type = null,
    Object? lastFourDigits = null,
    Object? cardBrand = null,
    Object? expiryDate = null,
    Object? isDefault = null,
    Object? isEnabled = null,
    Object? cardholderName = freezed,
    Object? billingAddress = freezed,
    Object? paymentMethodId = freezed,
    Object? createdAt = null,
    Object? lastUsedAt = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as PaymentMethodType,
            lastFourDigits: null == lastFourDigits
                ? _value.lastFourDigits
                : lastFourDigits // ignore: cast_nullable_to_non_nullable
                      as String,
            cardBrand: null == cardBrand
                ? _value.cardBrand
                : cardBrand // ignore: cast_nullable_to_non_nullable
                      as String,
            expiryDate: null == expiryDate
                ? _value.expiryDate
                : expiryDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isDefault: null == isDefault
                ? _value.isDefault
                : isDefault // ignore: cast_nullable_to_non_nullable
                      as bool,
            isEnabled: null == isEnabled
                ? _value.isEnabled
                : isEnabled // ignore: cast_nullable_to_non_nullable
                      as bool,
            cardholderName: freezed == cardholderName
                ? _value.cardholderName
                : cardholderName // ignore: cast_nullable_to_non_nullable
                      as String?,
            billingAddress: freezed == billingAddress
                ? _value.billingAddress
                : billingAddress // ignore: cast_nullable_to_non_nullable
                      as String?,
            paymentMethodId: freezed == paymentMethodId
                ? _value.paymentMethodId
                : paymentMethodId // ignore: cast_nullable_to_non_nullable
                      as String?,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            lastUsedAt: freezed == lastUsedAt
                ? _value.lastUsedAt
                : lastUsedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            metadata: freezed == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PaymentMethodImplCopyWith<$Res>
    implements $PaymentMethodCopyWith<$Res> {
  factory _$$PaymentMethodImplCopyWith(
    _$PaymentMethodImpl value,
    $Res Function(_$PaymentMethodImpl) then,
  ) = __$$PaymentMethodImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    PaymentMethodType type,
    String lastFourDigits,
    String cardBrand,
    DateTime expiryDate,
    bool isDefault,
    bool isEnabled,
    String? cardholderName,
    String? billingAddress,
    String? paymentMethodId,
    DateTime createdAt,
    DateTime? lastUsedAt,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class __$$PaymentMethodImplCopyWithImpl<$Res>
    extends _$PaymentMethodCopyWithImpl<$Res, _$PaymentMethodImpl>
    implements _$$PaymentMethodImplCopyWith<$Res> {
  __$$PaymentMethodImplCopyWithImpl(
    _$PaymentMethodImpl _value,
    $Res Function(_$PaymentMethodImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PaymentMethod
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? type = null,
    Object? lastFourDigits = null,
    Object? cardBrand = null,
    Object? expiryDate = null,
    Object? isDefault = null,
    Object? isEnabled = null,
    Object? cardholderName = freezed,
    Object? billingAddress = freezed,
    Object? paymentMethodId = freezed,
    Object? createdAt = null,
    Object? lastUsedAt = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _$PaymentMethodImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as PaymentMethodType,
        lastFourDigits: null == lastFourDigits
            ? _value.lastFourDigits
            : lastFourDigits // ignore: cast_nullable_to_non_nullable
                  as String,
        cardBrand: null == cardBrand
            ? _value.cardBrand
            : cardBrand // ignore: cast_nullable_to_non_nullable
                  as String,
        expiryDate: null == expiryDate
            ? _value.expiryDate
            : expiryDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isDefault: null == isDefault
            ? _value.isDefault
            : isDefault // ignore: cast_nullable_to_non_nullable
                  as bool,
        isEnabled: null == isEnabled
            ? _value.isEnabled
            : isEnabled // ignore: cast_nullable_to_non_nullable
                  as bool,
        cardholderName: freezed == cardholderName
            ? _value.cardholderName
            : cardholderName // ignore: cast_nullable_to_non_nullable
                  as String?,
        billingAddress: freezed == billingAddress
            ? _value.billingAddress
            : billingAddress // ignore: cast_nullable_to_non_nullable
                  as String?,
        paymentMethodId: freezed == paymentMethodId
            ? _value.paymentMethodId
            : paymentMethodId // ignore: cast_nullable_to_non_nullable
                  as String?,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        lastUsedAt: freezed == lastUsedAt
            ? _value.lastUsedAt
            : lastUsedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        metadata: freezed == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PaymentMethodImpl implements _PaymentMethod {
  const _$PaymentMethodImpl({
    required this.id,
    required this.userId,
    required this.type,
    required this.lastFourDigits,
    required this.cardBrand,
    required this.expiryDate,
    required this.isDefault,
    required this.isEnabled,
    this.cardholderName,
    this.billingAddress,
    this.paymentMethodId,
    required this.createdAt,
    this.lastUsedAt,
    final Map<String, dynamic>? metadata,
  }) : _metadata = metadata;

  factory _$PaymentMethodImpl.fromJson(Map<String, dynamic> json) =>
      _$$PaymentMethodImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final PaymentMethodType type;
  @override
  final String lastFourDigits;
  @override
  final String cardBrand;
  @override
  final DateTime expiryDate;
  @override
  final bool isDefault;
  @override
  final bool isEnabled;
  @override
  final String? cardholderName;
  @override
  final String? billingAddress;
  @override
  final String? paymentMethodId;
  // External payment provider ID
  @override
  final DateTime createdAt;
  @override
  final DateTime? lastUsedAt;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'PaymentMethod(id: $id, userId: $userId, type: $type, lastFourDigits: $lastFourDigits, cardBrand: $cardBrand, expiryDate: $expiryDate, isDefault: $isDefault, isEnabled: $isEnabled, cardholderName: $cardholderName, billingAddress: $billingAddress, paymentMethodId: $paymentMethodId, createdAt: $createdAt, lastUsedAt: $lastUsedAt, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentMethodImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.lastFourDigits, lastFourDigits) ||
                other.lastFourDigits == lastFourDigits) &&
            (identical(other.cardBrand, cardBrand) ||
                other.cardBrand == cardBrand) &&
            (identical(other.expiryDate, expiryDate) ||
                other.expiryDate == expiryDate) &&
            (identical(other.isDefault, isDefault) ||
                other.isDefault == isDefault) &&
            (identical(other.isEnabled, isEnabled) ||
                other.isEnabled == isEnabled) &&
            (identical(other.cardholderName, cardholderName) ||
                other.cardholderName == cardholderName) &&
            (identical(other.billingAddress, billingAddress) ||
                other.billingAddress == billingAddress) &&
            (identical(other.paymentMethodId, paymentMethodId) ||
                other.paymentMethodId == paymentMethodId) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.lastUsedAt, lastUsedAt) ||
                other.lastUsedAt == lastUsedAt) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    userId,
    type,
    lastFourDigits,
    cardBrand,
    expiryDate,
    isDefault,
    isEnabled,
    cardholderName,
    billingAddress,
    paymentMethodId,
    createdAt,
    lastUsedAt,
    const DeepCollectionEquality().hash(_metadata),
  );

  /// Create a copy of PaymentMethod
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentMethodImplCopyWith<_$PaymentMethodImpl> get copyWith =>
      __$$PaymentMethodImplCopyWithImpl<_$PaymentMethodImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PaymentMethodImplToJson(this);
  }
}

abstract class _PaymentMethod implements PaymentMethod {
  const factory _PaymentMethod({
    required final String id,
    required final String userId,
    required final PaymentMethodType type,
    required final String lastFourDigits,
    required final String cardBrand,
    required final DateTime expiryDate,
    required final bool isDefault,
    required final bool isEnabled,
    final String? cardholderName,
    final String? billingAddress,
    final String? paymentMethodId,
    required final DateTime createdAt,
    final DateTime? lastUsedAt,
    final Map<String, dynamic>? metadata,
  }) = _$PaymentMethodImpl;

  factory _PaymentMethod.fromJson(Map<String, dynamic> json) =
      _$PaymentMethodImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  PaymentMethodType get type;
  @override
  String get lastFourDigits;
  @override
  String get cardBrand;
  @override
  DateTime get expiryDate;
  @override
  bool get isDefault;
  @override
  bool get isEnabled;
  @override
  String? get cardholderName;
  @override
  String? get billingAddress;
  @override
  String? get paymentMethodId; // External payment provider ID
  @override
  DateTime get createdAt;
  @override
  DateTime? get lastUsedAt;
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of PaymentMethod
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentMethodImplCopyWith<_$PaymentMethodImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PaymentTransaction _$PaymentTransactionFromJson(Map<String, dynamic> json) {
  return _PaymentTransaction.fromJson(json);
}

/// @nodoc
mixin _$PaymentTransaction {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get merchantId => throw _privateConstructorUsedError;
  double get amount => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  PaymentStatus get status => throw _privateConstructorUsedError;
  PaymentType get type => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime? get processedAt => throw _privateConstructorUsedError;
  DateTime? get failedAt => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get receiptUrl => throw _privateConstructorUsedError;
  String? get transactionId =>
      throw _privateConstructorUsedError; // External payment provider transaction ID
  String? get failureReason => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;
  List<String>? get tags => throw _privateConstructorUsedError;
  String? get invoiceId => throw _privateConstructorUsedError;
  String? get subscriptionId => throw _privateConstructorUsedError;

  /// Serializes this PaymentTransaction to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PaymentTransaction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PaymentTransactionCopyWith<PaymentTransaction> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentTransactionCopyWith<$Res> {
  factory $PaymentTransactionCopyWith(
    PaymentTransaction value,
    $Res Function(PaymentTransaction) then,
  ) = _$PaymentTransactionCopyWithImpl<$Res, PaymentTransaction>;
  @useResult
  $Res call({
    String id,
    String userId,
    String merchantId,
    double amount,
    String currency,
    PaymentStatus status,
    PaymentType type,
    DateTime createdAt,
    DateTime? processedAt,
    DateTime? failedAt,
    String? description,
    String? receiptUrl,
    String? transactionId,
    String? failureReason,
    Map<String, dynamic>? metadata,
    List<String>? tags,
    String? invoiceId,
    String? subscriptionId,
  });
}

/// @nodoc
class _$PaymentTransactionCopyWithImpl<$Res, $Val extends PaymentTransaction>
    implements $PaymentTransactionCopyWith<$Res> {
  _$PaymentTransactionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentTransaction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? merchantId = null,
    Object? amount = null,
    Object? currency = null,
    Object? status = null,
    Object? type = null,
    Object? createdAt = null,
    Object? processedAt = freezed,
    Object? failedAt = freezed,
    Object? description = freezed,
    Object? receiptUrl = freezed,
    Object? transactionId = freezed,
    Object? failureReason = freezed,
    Object? metadata = freezed,
    Object? tags = freezed,
    Object? invoiceId = freezed,
    Object? subscriptionId = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            merchantId: null == merchantId
                ? _value.merchantId
                : merchantId // ignore: cast_nullable_to_non_nullable
                      as String,
            amount: null == amount
                ? _value.amount
                : amount // ignore: cast_nullable_to_non_nullable
                      as double,
            currency: null == currency
                ? _value.currency
                : currency // ignore: cast_nullable_to_non_nullable
                      as String,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as PaymentStatus,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as PaymentType,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            processedAt: freezed == processedAt
                ? _value.processedAt
                : processedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            failedAt: freezed == failedAt
                ? _value.failedAt
                : failedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            receiptUrl: freezed == receiptUrl
                ? _value.receiptUrl
                : receiptUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            transactionId: freezed == transactionId
                ? _value.transactionId
                : transactionId // ignore: cast_nullable_to_non_nullable
                      as String?,
            failureReason: freezed == failureReason
                ? _value.failureReason
                : failureReason // ignore: cast_nullable_to_non_nullable
                      as String?,
            metadata: freezed == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
            tags: freezed == tags
                ? _value.tags
                : tags // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            invoiceId: freezed == invoiceId
                ? _value.invoiceId
                : invoiceId // ignore: cast_nullable_to_non_nullable
                      as String?,
            subscriptionId: freezed == subscriptionId
                ? _value.subscriptionId
                : subscriptionId // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PaymentTransactionImplCopyWith<$Res>
    implements $PaymentTransactionCopyWith<$Res> {
  factory _$$PaymentTransactionImplCopyWith(
    _$PaymentTransactionImpl value,
    $Res Function(_$PaymentTransactionImpl) then,
  ) = __$$PaymentTransactionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    String merchantId,
    double amount,
    String currency,
    PaymentStatus status,
    PaymentType type,
    DateTime createdAt,
    DateTime? processedAt,
    DateTime? failedAt,
    String? description,
    String? receiptUrl,
    String? transactionId,
    String? failureReason,
    Map<String, dynamic>? metadata,
    List<String>? tags,
    String? invoiceId,
    String? subscriptionId,
  });
}

/// @nodoc
class __$$PaymentTransactionImplCopyWithImpl<$Res>
    extends _$PaymentTransactionCopyWithImpl<$Res, _$PaymentTransactionImpl>
    implements _$$PaymentTransactionImplCopyWith<$Res> {
  __$$PaymentTransactionImplCopyWithImpl(
    _$PaymentTransactionImpl _value,
    $Res Function(_$PaymentTransactionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PaymentTransaction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? merchantId = null,
    Object? amount = null,
    Object? currency = null,
    Object? status = null,
    Object? type = null,
    Object? createdAt = null,
    Object? processedAt = freezed,
    Object? failedAt = freezed,
    Object? description = freezed,
    Object? receiptUrl = freezed,
    Object? transactionId = freezed,
    Object? failureReason = freezed,
    Object? metadata = freezed,
    Object? tags = freezed,
    Object? invoiceId = freezed,
    Object? subscriptionId = freezed,
  }) {
    return _then(
      _$PaymentTransactionImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        merchantId: null == merchantId
            ? _value.merchantId
            : merchantId // ignore: cast_nullable_to_non_nullable
                  as String,
        amount: null == amount
            ? _value.amount
            : amount // ignore: cast_nullable_to_non_nullable
                  as double,
        currency: null == currency
            ? _value.currency
            : currency // ignore: cast_nullable_to_non_nullable
                  as String,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as PaymentStatus,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as PaymentType,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        processedAt: freezed == processedAt
            ? _value.processedAt
            : processedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        failedAt: freezed == failedAt
            ? _value.failedAt
            : failedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        receiptUrl: freezed == receiptUrl
            ? _value.receiptUrl
            : receiptUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        transactionId: freezed == transactionId
            ? _value.transactionId
            : transactionId // ignore: cast_nullable_to_non_nullable
                  as String?,
        failureReason: freezed == failureReason
            ? _value.failureReason
            : failureReason // ignore: cast_nullable_to_non_nullable
                  as String?,
        metadata: freezed == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
        tags: freezed == tags
            ? _value._tags
            : tags // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        invoiceId: freezed == invoiceId
            ? _value.invoiceId
            : invoiceId // ignore: cast_nullable_to_non_nullable
                  as String?,
        subscriptionId: freezed == subscriptionId
            ? _value.subscriptionId
            : subscriptionId // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PaymentTransactionImpl implements _PaymentTransaction {
  const _$PaymentTransactionImpl({
    required this.id,
    required this.userId,
    required this.merchantId,
    required this.amount,
    required this.currency,
    required this.status,
    required this.type,
    required this.createdAt,
    this.processedAt,
    this.failedAt,
    this.description,
    this.receiptUrl,
    this.transactionId,
    this.failureReason,
    final Map<String, dynamic>? metadata,
    final List<String>? tags,
    this.invoiceId,
    this.subscriptionId,
  }) : _metadata = metadata,
       _tags = tags;

  factory _$PaymentTransactionImpl.fromJson(Map<String, dynamic> json) =>
      _$$PaymentTransactionImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String merchantId;
  @override
  final double amount;
  @override
  final String currency;
  @override
  final PaymentStatus status;
  @override
  final PaymentType type;
  @override
  final DateTime createdAt;
  @override
  final DateTime? processedAt;
  @override
  final DateTime? failedAt;
  @override
  final String? description;
  @override
  final String? receiptUrl;
  @override
  final String? transactionId;
  // External payment provider transaction ID
  @override
  final String? failureReason;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  final List<String>? _tags;
  @override
  List<String>? get tags {
    final value = _tags;
    if (value == null) return null;
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? invoiceId;
  @override
  final String? subscriptionId;

  @override
  String toString() {
    return 'PaymentTransaction(id: $id, userId: $userId, merchantId: $merchantId, amount: $amount, currency: $currency, status: $status, type: $type, createdAt: $createdAt, processedAt: $processedAt, failedAt: $failedAt, description: $description, receiptUrl: $receiptUrl, transactionId: $transactionId, failureReason: $failureReason, metadata: $metadata, tags: $tags, invoiceId: $invoiceId, subscriptionId: $subscriptionId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentTransactionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.merchantId, merchantId) ||
                other.merchantId == merchantId) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.processedAt, processedAt) ||
                other.processedAt == processedAt) &&
            (identical(other.failedAt, failedAt) ||
                other.failedAt == failedAt) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.receiptUrl, receiptUrl) ||
                other.receiptUrl == receiptUrl) &&
            (identical(other.transactionId, transactionId) ||
                other.transactionId == transactionId) &&
            (identical(other.failureReason, failureReason) ||
                other.failureReason == failureReason) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.invoiceId, invoiceId) ||
                other.invoiceId == invoiceId) &&
            (identical(other.subscriptionId, subscriptionId) ||
                other.subscriptionId == subscriptionId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    userId,
    merchantId,
    amount,
    currency,
    status,
    type,
    createdAt,
    processedAt,
    failedAt,
    description,
    receiptUrl,
    transactionId,
    failureReason,
    const DeepCollectionEquality().hash(_metadata),
    const DeepCollectionEquality().hash(_tags),
    invoiceId,
    subscriptionId,
  );

  /// Create a copy of PaymentTransaction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentTransactionImplCopyWith<_$PaymentTransactionImpl> get copyWith =>
      __$$PaymentTransactionImplCopyWithImpl<_$PaymentTransactionImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$PaymentTransactionImplToJson(this);
  }
}

abstract class _PaymentTransaction implements PaymentTransaction {
  const factory _PaymentTransaction({
    required final String id,
    required final String userId,
    required final String merchantId,
    required final double amount,
    required final String currency,
    required final PaymentStatus status,
    required final PaymentType type,
    required final DateTime createdAt,
    final DateTime? processedAt,
    final DateTime? failedAt,
    final String? description,
    final String? receiptUrl,
    final String? transactionId,
    final String? failureReason,
    final Map<String, dynamic>? metadata,
    final List<String>? tags,
    final String? invoiceId,
    final String? subscriptionId,
  }) = _$PaymentTransactionImpl;

  factory _PaymentTransaction.fromJson(Map<String, dynamic> json) =
      _$PaymentTransactionImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get merchantId;
  @override
  double get amount;
  @override
  String get currency;
  @override
  PaymentStatus get status;
  @override
  PaymentType get type;
  @override
  DateTime get createdAt;
  @override
  DateTime? get processedAt;
  @override
  DateTime? get failedAt;
  @override
  String? get description;
  @override
  String? get receiptUrl;
  @override
  String? get transactionId; // External payment provider transaction ID
  @override
  String? get failureReason;
  @override
  Map<String, dynamic>? get metadata;
  @override
  List<String>? get tags;
  @override
  String? get invoiceId;
  @override
  String? get subscriptionId;

  /// Create a copy of PaymentTransaction
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentTransactionImplCopyWith<_$PaymentTransactionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Subscription _$SubscriptionFromJson(Map<String, dynamic> json) {
  return _Subscription.fromJson(json);
}

/// @nodoc
mixin _$Subscription {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get planId => throw _privateConstructorUsedError;
  SubscriptionStatus get status => throw _privateConstructorUsedError;
  DateTime get startDate => throw _privateConstructorUsedError;
  DateTime get endDate => throw _privateConstructorUsedError;
  double get amount => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  BillingCycle get billingCycle => throw _privateConstructorUsedError;
  bool get autoRenew => throw _privateConstructorUsedError;
  DateTime? get nextBillingDate => throw _privateConstructorUsedError;
  DateTime? get cancelledAt => throw _privateConstructorUsedError;
  String? get cancellationReason => throw _privateConstructorUsedError;
  String? get externalSubscriptionId => throw _privateConstructorUsedError;
  Map<String, dynamic>? get features => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this Subscription to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Subscription
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SubscriptionCopyWith<Subscription> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SubscriptionCopyWith<$Res> {
  factory $SubscriptionCopyWith(
    Subscription value,
    $Res Function(Subscription) then,
  ) = _$SubscriptionCopyWithImpl<$Res, Subscription>;
  @useResult
  $Res call({
    String id,
    String userId,
    String planId,
    SubscriptionStatus status,
    DateTime startDate,
    DateTime endDate,
    double amount,
    String currency,
    BillingCycle billingCycle,
    bool autoRenew,
    DateTime? nextBillingDate,
    DateTime? cancelledAt,
    String? cancellationReason,
    String? externalSubscriptionId,
    Map<String, dynamic>? features,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class _$SubscriptionCopyWithImpl<$Res, $Val extends Subscription>
    implements $SubscriptionCopyWith<$Res> {
  _$SubscriptionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Subscription
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? planId = null,
    Object? status = null,
    Object? startDate = null,
    Object? endDate = null,
    Object? amount = null,
    Object? currency = null,
    Object? billingCycle = null,
    Object? autoRenew = null,
    Object? nextBillingDate = freezed,
    Object? cancelledAt = freezed,
    Object? cancellationReason = freezed,
    Object? externalSubscriptionId = freezed,
    Object? features = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            planId: null == planId
                ? _value.planId
                : planId // ignore: cast_nullable_to_non_nullable
                      as String,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as SubscriptionStatus,
            startDate: null == startDate
                ? _value.startDate
                : startDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            endDate: null == endDate
                ? _value.endDate
                : endDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            amount: null == amount
                ? _value.amount
                : amount // ignore: cast_nullable_to_non_nullable
                      as double,
            currency: null == currency
                ? _value.currency
                : currency // ignore: cast_nullable_to_non_nullable
                      as String,
            billingCycle: null == billingCycle
                ? _value.billingCycle
                : billingCycle // ignore: cast_nullable_to_non_nullable
                      as BillingCycle,
            autoRenew: null == autoRenew
                ? _value.autoRenew
                : autoRenew // ignore: cast_nullable_to_non_nullable
                      as bool,
            nextBillingDate: freezed == nextBillingDate
                ? _value.nextBillingDate
                : nextBillingDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            cancelledAt: freezed == cancelledAt
                ? _value.cancelledAt
                : cancelledAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            cancellationReason: freezed == cancellationReason
                ? _value.cancellationReason
                : cancellationReason // ignore: cast_nullable_to_non_nullable
                      as String?,
            externalSubscriptionId: freezed == externalSubscriptionId
                ? _value.externalSubscriptionId
                : externalSubscriptionId // ignore: cast_nullable_to_non_nullable
                      as String?,
            features: freezed == features
                ? _value.features
                : features // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
            metadata: freezed == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$SubscriptionImplCopyWith<$Res>
    implements $SubscriptionCopyWith<$Res> {
  factory _$$SubscriptionImplCopyWith(
    _$SubscriptionImpl value,
    $Res Function(_$SubscriptionImpl) then,
  ) = __$$SubscriptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    String planId,
    SubscriptionStatus status,
    DateTime startDate,
    DateTime endDate,
    double amount,
    String currency,
    BillingCycle billingCycle,
    bool autoRenew,
    DateTime? nextBillingDate,
    DateTime? cancelledAt,
    String? cancellationReason,
    String? externalSubscriptionId,
    Map<String, dynamic>? features,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class __$$SubscriptionImplCopyWithImpl<$Res>
    extends _$SubscriptionCopyWithImpl<$Res, _$SubscriptionImpl>
    implements _$$SubscriptionImplCopyWith<$Res> {
  __$$SubscriptionImplCopyWithImpl(
    _$SubscriptionImpl _value,
    $Res Function(_$SubscriptionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Subscription
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? planId = null,
    Object? status = null,
    Object? startDate = null,
    Object? endDate = null,
    Object? amount = null,
    Object? currency = null,
    Object? billingCycle = null,
    Object? autoRenew = null,
    Object? nextBillingDate = freezed,
    Object? cancelledAt = freezed,
    Object? cancellationReason = freezed,
    Object? externalSubscriptionId = freezed,
    Object? features = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _$SubscriptionImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        planId: null == planId
            ? _value.planId
            : planId // ignore: cast_nullable_to_non_nullable
                  as String,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as SubscriptionStatus,
        startDate: null == startDate
            ? _value.startDate
            : startDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        endDate: null == endDate
            ? _value.endDate
            : endDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        amount: null == amount
            ? _value.amount
            : amount // ignore: cast_nullable_to_non_nullable
                  as double,
        currency: null == currency
            ? _value.currency
            : currency // ignore: cast_nullable_to_non_nullable
                  as String,
        billingCycle: null == billingCycle
            ? _value.billingCycle
            : billingCycle // ignore: cast_nullable_to_non_nullable
                  as BillingCycle,
        autoRenew: null == autoRenew
            ? _value.autoRenew
            : autoRenew // ignore: cast_nullable_to_non_nullable
                  as bool,
        nextBillingDate: freezed == nextBillingDate
            ? _value.nextBillingDate
            : nextBillingDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        cancelledAt: freezed == cancelledAt
            ? _value.cancelledAt
            : cancelledAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        cancellationReason: freezed == cancellationReason
            ? _value.cancellationReason
            : cancellationReason // ignore: cast_nullable_to_non_nullable
                  as String?,
        externalSubscriptionId: freezed == externalSubscriptionId
            ? _value.externalSubscriptionId
            : externalSubscriptionId // ignore: cast_nullable_to_non_nullable
                  as String?,
        features: freezed == features
            ? _value._features
            : features // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
        metadata: freezed == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$SubscriptionImpl implements _Subscription {
  const _$SubscriptionImpl({
    required this.id,
    required this.userId,
    required this.planId,
    required this.status,
    required this.startDate,
    required this.endDate,
    required this.amount,
    required this.currency,
    required this.billingCycle,
    required this.autoRenew,
    this.nextBillingDate,
    this.cancelledAt,
    this.cancellationReason,
    this.externalSubscriptionId,
    final Map<String, dynamic>? features,
    final Map<String, dynamic>? metadata,
  }) : _features = features,
       _metadata = metadata;

  factory _$SubscriptionImpl.fromJson(Map<String, dynamic> json) =>
      _$$SubscriptionImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String planId;
  @override
  final SubscriptionStatus status;
  @override
  final DateTime startDate;
  @override
  final DateTime endDate;
  @override
  final double amount;
  @override
  final String currency;
  @override
  final BillingCycle billingCycle;
  @override
  final bool autoRenew;
  @override
  final DateTime? nextBillingDate;
  @override
  final DateTime? cancelledAt;
  @override
  final String? cancellationReason;
  @override
  final String? externalSubscriptionId;
  final Map<String, dynamic>? _features;
  @override
  Map<String, dynamic>? get features {
    final value = _features;
    if (value == null) return null;
    if (_features is EqualUnmodifiableMapView) return _features;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'Subscription(id: $id, userId: $userId, planId: $planId, status: $status, startDate: $startDate, endDate: $endDate, amount: $amount, currency: $currency, billingCycle: $billingCycle, autoRenew: $autoRenew, nextBillingDate: $nextBillingDate, cancelledAt: $cancelledAt, cancellationReason: $cancellationReason, externalSubscriptionId: $externalSubscriptionId, features: $features, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SubscriptionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.planId, planId) || other.planId == planId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.billingCycle, billingCycle) ||
                other.billingCycle == billingCycle) &&
            (identical(other.autoRenew, autoRenew) ||
                other.autoRenew == autoRenew) &&
            (identical(other.nextBillingDate, nextBillingDate) ||
                other.nextBillingDate == nextBillingDate) &&
            (identical(other.cancelledAt, cancelledAt) ||
                other.cancelledAt == cancelledAt) &&
            (identical(other.cancellationReason, cancellationReason) ||
                other.cancellationReason == cancellationReason) &&
            (identical(other.externalSubscriptionId, externalSubscriptionId) ||
                other.externalSubscriptionId == externalSubscriptionId) &&
            const DeepCollectionEquality().equals(other._features, _features) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    userId,
    planId,
    status,
    startDate,
    endDate,
    amount,
    currency,
    billingCycle,
    autoRenew,
    nextBillingDate,
    cancelledAt,
    cancellationReason,
    externalSubscriptionId,
    const DeepCollectionEquality().hash(_features),
    const DeepCollectionEquality().hash(_metadata),
  );

  /// Create a copy of Subscription
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SubscriptionImplCopyWith<_$SubscriptionImpl> get copyWith =>
      __$$SubscriptionImplCopyWithImpl<_$SubscriptionImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SubscriptionImplToJson(this);
  }
}

abstract class _Subscription implements Subscription {
  const factory _Subscription({
    required final String id,
    required final String userId,
    required final String planId,
    required final SubscriptionStatus status,
    required final DateTime startDate,
    required final DateTime endDate,
    required final double amount,
    required final String currency,
    required final BillingCycle billingCycle,
    required final bool autoRenew,
    final DateTime? nextBillingDate,
    final DateTime? cancelledAt,
    final String? cancellationReason,
    final String? externalSubscriptionId,
    final Map<String, dynamic>? features,
    final Map<String, dynamic>? metadata,
  }) = _$SubscriptionImpl;

  factory _Subscription.fromJson(Map<String, dynamic> json) =
      _$SubscriptionImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get planId;
  @override
  SubscriptionStatus get status;
  @override
  DateTime get startDate;
  @override
  DateTime get endDate;
  @override
  double get amount;
  @override
  String get currency;
  @override
  BillingCycle get billingCycle;
  @override
  bool get autoRenew;
  @override
  DateTime? get nextBillingDate;
  @override
  DateTime? get cancelledAt;
  @override
  String? get cancellationReason;
  @override
  String? get externalSubscriptionId;
  @override
  Map<String, dynamic>? get features;
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of Subscription
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SubscriptionImplCopyWith<_$SubscriptionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PaymentPlan _$PaymentPlanFromJson(Map<String, dynamic> json) {
  return _PaymentPlan.fromJson(json);
}

/// @nodoc
mixin _$PaymentPlan {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  double get price => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  BillingCycle get billingCycle => throw _privateConstructorUsedError;
  List<String> get features => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  bool get isPopular => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;
  DateTime? get validFrom => throw _privateConstructorUsedError;
  DateTime? get validUntil => throw _privateConstructorUsedError;

  /// Serializes this PaymentPlan to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PaymentPlan
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PaymentPlanCopyWith<PaymentPlan> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentPlanCopyWith<$Res> {
  factory $PaymentPlanCopyWith(
    PaymentPlan value,
    $Res Function(PaymentPlan) then,
  ) = _$PaymentPlanCopyWithImpl<$Res, PaymentPlan>;
  @useResult
  $Res call({
    String id,
    String name,
    String description,
    double price,
    String currency,
    BillingCycle billingCycle,
    List<String> features,
    bool isActive,
    bool isPopular,
    String? imageUrl,
    Map<String, dynamic>? metadata,
    DateTime? validFrom,
    DateTime? validUntil,
  });
}

/// @nodoc
class _$PaymentPlanCopyWithImpl<$Res, $Val extends PaymentPlan>
    implements $PaymentPlanCopyWith<$Res> {
  _$PaymentPlanCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentPlan
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? price = null,
    Object? currency = null,
    Object? billingCycle = null,
    Object? features = null,
    Object? isActive = null,
    Object? isPopular = null,
    Object? imageUrl = freezed,
    Object? metadata = freezed,
    Object? validFrom = freezed,
    Object? validUntil = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            price: null == price
                ? _value.price
                : price // ignore: cast_nullable_to_non_nullable
                      as double,
            currency: null == currency
                ? _value.currency
                : currency // ignore: cast_nullable_to_non_nullable
                      as String,
            billingCycle: null == billingCycle
                ? _value.billingCycle
                : billingCycle // ignore: cast_nullable_to_non_nullable
                      as BillingCycle,
            features: null == features
                ? _value.features
                : features // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            isActive: null == isActive
                ? _value.isActive
                : isActive // ignore: cast_nullable_to_non_nullable
                      as bool,
            isPopular: null == isPopular
                ? _value.isPopular
                : isPopular // ignore: cast_nullable_to_non_nullable
                      as bool,
            imageUrl: freezed == imageUrl
                ? _value.imageUrl
                : imageUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            metadata: freezed == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
            validFrom: freezed == validFrom
                ? _value.validFrom
                : validFrom // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            validUntil: freezed == validUntil
                ? _value.validUntil
                : validUntil // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PaymentPlanImplCopyWith<$Res>
    implements $PaymentPlanCopyWith<$Res> {
  factory _$$PaymentPlanImplCopyWith(
    _$PaymentPlanImpl value,
    $Res Function(_$PaymentPlanImpl) then,
  ) = __$$PaymentPlanImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String description,
    double price,
    String currency,
    BillingCycle billingCycle,
    List<String> features,
    bool isActive,
    bool isPopular,
    String? imageUrl,
    Map<String, dynamic>? metadata,
    DateTime? validFrom,
    DateTime? validUntil,
  });
}

/// @nodoc
class __$$PaymentPlanImplCopyWithImpl<$Res>
    extends _$PaymentPlanCopyWithImpl<$Res, _$PaymentPlanImpl>
    implements _$$PaymentPlanImplCopyWith<$Res> {
  __$$PaymentPlanImplCopyWithImpl(
    _$PaymentPlanImpl _value,
    $Res Function(_$PaymentPlanImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PaymentPlan
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? price = null,
    Object? currency = null,
    Object? billingCycle = null,
    Object? features = null,
    Object? isActive = null,
    Object? isPopular = null,
    Object? imageUrl = freezed,
    Object? metadata = freezed,
    Object? validFrom = freezed,
    Object? validUntil = freezed,
  }) {
    return _then(
      _$PaymentPlanImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        price: null == price
            ? _value.price
            : price // ignore: cast_nullable_to_non_nullable
                  as double,
        currency: null == currency
            ? _value.currency
            : currency // ignore: cast_nullable_to_non_nullable
                  as String,
        billingCycle: null == billingCycle
            ? _value.billingCycle
            : billingCycle // ignore: cast_nullable_to_non_nullable
                  as BillingCycle,
        features: null == features
            ? _value._features
            : features // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        isActive: null == isActive
            ? _value.isActive
            : isActive // ignore: cast_nullable_to_non_nullable
                  as bool,
        isPopular: null == isPopular
            ? _value.isPopular
            : isPopular // ignore: cast_nullable_to_non_nullable
                  as bool,
        imageUrl: freezed == imageUrl
            ? _value.imageUrl
            : imageUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        metadata: freezed == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
        validFrom: freezed == validFrom
            ? _value.validFrom
            : validFrom // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        validUntil: freezed == validUntil
            ? _value.validUntil
            : validUntil // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PaymentPlanImpl implements _PaymentPlan {
  const _$PaymentPlanImpl({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.currency,
    required this.billingCycle,
    required final List<String> features,
    required this.isActive,
    required this.isPopular,
    this.imageUrl,
    final Map<String, dynamic>? metadata,
    this.validFrom,
    this.validUntil,
  }) : _features = features,
       _metadata = metadata;

  factory _$PaymentPlanImpl.fromJson(Map<String, dynamic> json) =>
      _$$PaymentPlanImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String description;
  @override
  final double price;
  @override
  final String currency;
  @override
  final BillingCycle billingCycle;
  final List<String> _features;
  @override
  List<String> get features {
    if (_features is EqualUnmodifiableListView) return _features;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_features);
  }

  @override
  final bool isActive;
  @override
  final bool isPopular;
  @override
  final String? imageUrl;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final DateTime? validFrom;
  @override
  final DateTime? validUntil;

  @override
  String toString() {
    return 'PaymentPlan(id: $id, name: $name, description: $description, price: $price, currency: $currency, billingCycle: $billingCycle, features: $features, isActive: $isActive, isPopular: $isPopular, imageUrl: $imageUrl, metadata: $metadata, validFrom: $validFrom, validUntil: $validUntil)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentPlanImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.billingCycle, billingCycle) ||
                other.billingCycle == billingCycle) &&
            const DeepCollectionEquality().equals(other._features, _features) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.isPopular, isPopular) ||
                other.isPopular == isPopular) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.validFrom, validFrom) ||
                other.validFrom == validFrom) &&
            (identical(other.validUntil, validUntil) ||
                other.validUntil == validUntil));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    price,
    currency,
    billingCycle,
    const DeepCollectionEquality().hash(_features),
    isActive,
    isPopular,
    imageUrl,
    const DeepCollectionEquality().hash(_metadata),
    validFrom,
    validUntil,
  );

  /// Create a copy of PaymentPlan
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentPlanImplCopyWith<_$PaymentPlanImpl> get copyWith =>
      __$$PaymentPlanImplCopyWithImpl<_$PaymentPlanImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PaymentPlanImplToJson(this);
  }
}

abstract class _PaymentPlan implements PaymentPlan {
  const factory _PaymentPlan({
    required final String id,
    required final String name,
    required final String description,
    required final double price,
    required final String currency,
    required final BillingCycle billingCycle,
    required final List<String> features,
    required final bool isActive,
    required final bool isPopular,
    final String? imageUrl,
    final Map<String, dynamic>? metadata,
    final DateTime? validFrom,
    final DateTime? validUntil,
  }) = _$PaymentPlanImpl;

  factory _PaymentPlan.fromJson(Map<String, dynamic> json) =
      _$PaymentPlanImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get description;
  @override
  double get price;
  @override
  String get currency;
  @override
  BillingCycle get billingCycle;
  @override
  List<String> get features;
  @override
  bool get isActive;
  @override
  bool get isPopular;
  @override
  String? get imageUrl;
  @override
  Map<String, dynamic>? get metadata;
  @override
  DateTime? get validFrom;
  @override
  DateTime? get validUntil;

  /// Create a copy of PaymentPlan
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentPlanImplCopyWith<_$PaymentPlanImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Invoice _$InvoiceFromJson(Map<String, dynamic> json) {
  return _Invoice.fromJson(json);
}

/// @nodoc
mixin _$Invoice {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get subscriptionId => throw _privateConstructorUsedError;
  double get amount => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  InvoiceStatus get status => throw _privateConstructorUsedError;
  DateTime get dueDate => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime? get paidAt => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get invoiceUrl => throw _privateConstructorUsedError;
  String? get receiptUrl => throw _privateConstructorUsedError;
  Map<String, dynamic>? get items => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this Invoice to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Invoice
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $InvoiceCopyWith<Invoice> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InvoiceCopyWith<$Res> {
  factory $InvoiceCopyWith(Invoice value, $Res Function(Invoice) then) =
      _$InvoiceCopyWithImpl<$Res, Invoice>;
  @useResult
  $Res call({
    String id,
    String userId,
    String subscriptionId,
    double amount,
    String currency,
    InvoiceStatus status,
    DateTime dueDate,
    DateTime createdAt,
    DateTime? paidAt,
    String? description,
    String? invoiceUrl,
    String? receiptUrl,
    Map<String, dynamic>? items,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class _$InvoiceCopyWithImpl<$Res, $Val extends Invoice>
    implements $InvoiceCopyWith<$Res> {
  _$InvoiceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Invoice
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? subscriptionId = null,
    Object? amount = null,
    Object? currency = null,
    Object? status = null,
    Object? dueDate = null,
    Object? createdAt = null,
    Object? paidAt = freezed,
    Object? description = freezed,
    Object? invoiceUrl = freezed,
    Object? receiptUrl = freezed,
    Object? items = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            subscriptionId: null == subscriptionId
                ? _value.subscriptionId
                : subscriptionId // ignore: cast_nullable_to_non_nullable
                      as String,
            amount: null == amount
                ? _value.amount
                : amount // ignore: cast_nullable_to_non_nullable
                      as double,
            currency: null == currency
                ? _value.currency
                : currency // ignore: cast_nullable_to_non_nullable
                      as String,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as InvoiceStatus,
            dueDate: null == dueDate
                ? _value.dueDate
                : dueDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            paidAt: freezed == paidAt
                ? _value.paidAt
                : paidAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            invoiceUrl: freezed == invoiceUrl
                ? _value.invoiceUrl
                : invoiceUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            receiptUrl: freezed == receiptUrl
                ? _value.receiptUrl
                : receiptUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            items: freezed == items
                ? _value.items
                : items // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
            metadata: freezed == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$InvoiceImplCopyWith<$Res> implements $InvoiceCopyWith<$Res> {
  factory _$$InvoiceImplCopyWith(
    _$InvoiceImpl value,
    $Res Function(_$InvoiceImpl) then,
  ) = __$$InvoiceImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    String subscriptionId,
    double amount,
    String currency,
    InvoiceStatus status,
    DateTime dueDate,
    DateTime createdAt,
    DateTime? paidAt,
    String? description,
    String? invoiceUrl,
    String? receiptUrl,
    Map<String, dynamic>? items,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class __$$InvoiceImplCopyWithImpl<$Res>
    extends _$InvoiceCopyWithImpl<$Res, _$InvoiceImpl>
    implements _$$InvoiceImplCopyWith<$Res> {
  __$$InvoiceImplCopyWithImpl(
    _$InvoiceImpl _value,
    $Res Function(_$InvoiceImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Invoice
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? subscriptionId = null,
    Object? amount = null,
    Object? currency = null,
    Object? status = null,
    Object? dueDate = null,
    Object? createdAt = null,
    Object? paidAt = freezed,
    Object? description = freezed,
    Object? invoiceUrl = freezed,
    Object? receiptUrl = freezed,
    Object? items = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _$InvoiceImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        subscriptionId: null == subscriptionId
            ? _value.subscriptionId
            : subscriptionId // ignore: cast_nullable_to_non_nullable
                  as String,
        amount: null == amount
            ? _value.amount
            : amount // ignore: cast_nullable_to_non_nullable
                  as double,
        currency: null == currency
            ? _value.currency
            : currency // ignore: cast_nullable_to_non_nullable
                  as String,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as InvoiceStatus,
        dueDate: null == dueDate
            ? _value.dueDate
            : dueDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        paidAt: freezed == paidAt
            ? _value.paidAt
            : paidAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        invoiceUrl: freezed == invoiceUrl
            ? _value.invoiceUrl
            : invoiceUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        receiptUrl: freezed == receiptUrl
            ? _value.receiptUrl
            : receiptUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        items: freezed == items
            ? _value._items
            : items // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
        metadata: freezed == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$InvoiceImpl implements _Invoice {
  const _$InvoiceImpl({
    required this.id,
    required this.userId,
    required this.subscriptionId,
    required this.amount,
    required this.currency,
    required this.status,
    required this.dueDate,
    required this.createdAt,
    this.paidAt,
    this.description,
    this.invoiceUrl,
    this.receiptUrl,
    final Map<String, dynamic>? items,
    final Map<String, dynamic>? metadata,
  }) : _items = items,
       _metadata = metadata;

  factory _$InvoiceImpl.fromJson(Map<String, dynamic> json) =>
      _$$InvoiceImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String subscriptionId;
  @override
  final double amount;
  @override
  final String currency;
  @override
  final InvoiceStatus status;
  @override
  final DateTime dueDate;
  @override
  final DateTime createdAt;
  @override
  final DateTime? paidAt;
  @override
  final String? description;
  @override
  final String? invoiceUrl;
  @override
  final String? receiptUrl;
  final Map<String, dynamic>? _items;
  @override
  Map<String, dynamic>? get items {
    final value = _items;
    if (value == null) return null;
    if (_items is EqualUnmodifiableMapView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'Invoice(id: $id, userId: $userId, subscriptionId: $subscriptionId, amount: $amount, currency: $currency, status: $status, dueDate: $dueDate, createdAt: $createdAt, paidAt: $paidAt, description: $description, invoiceUrl: $invoiceUrl, receiptUrl: $receiptUrl, items: $items, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InvoiceImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.subscriptionId, subscriptionId) ||
                other.subscriptionId == subscriptionId) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.dueDate, dueDate) || other.dueDate == dueDate) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.paidAt, paidAt) || other.paidAt == paidAt) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.invoiceUrl, invoiceUrl) ||
                other.invoiceUrl == invoiceUrl) &&
            (identical(other.receiptUrl, receiptUrl) ||
                other.receiptUrl == receiptUrl) &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    userId,
    subscriptionId,
    amount,
    currency,
    status,
    dueDate,
    createdAt,
    paidAt,
    description,
    invoiceUrl,
    receiptUrl,
    const DeepCollectionEquality().hash(_items),
    const DeepCollectionEquality().hash(_metadata),
  );

  /// Create a copy of Invoice
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InvoiceImplCopyWith<_$InvoiceImpl> get copyWith =>
      __$$InvoiceImplCopyWithImpl<_$InvoiceImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$InvoiceImplToJson(this);
  }
}

abstract class _Invoice implements Invoice {
  const factory _Invoice({
    required final String id,
    required final String userId,
    required final String subscriptionId,
    required final double amount,
    required final String currency,
    required final InvoiceStatus status,
    required final DateTime dueDate,
    required final DateTime createdAt,
    final DateTime? paidAt,
    final String? description,
    final String? invoiceUrl,
    final String? receiptUrl,
    final Map<String, dynamic>? items,
    final Map<String, dynamic>? metadata,
  }) = _$InvoiceImpl;

  factory _Invoice.fromJson(Map<String, dynamic> json) = _$InvoiceImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get subscriptionId;
  @override
  double get amount;
  @override
  String get currency;
  @override
  InvoiceStatus get status;
  @override
  DateTime get dueDate;
  @override
  DateTime get createdAt;
  @override
  DateTime? get paidAt;
  @override
  String? get description;
  @override
  String? get invoiceUrl;
  @override
  String? get receiptUrl;
  @override
  Map<String, dynamic>? get items;
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of Invoice
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InvoiceImplCopyWith<_$InvoiceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PaymentWebhook _$PaymentWebhookFromJson(Map<String, dynamic> json) {
  return _PaymentWebhook.fromJson(json);
}

/// @nodoc
mixin _$PaymentWebhook {
  String get id => throw _privateConstructorUsedError;
  String get eventType => throw _privateConstructorUsedError;
  Map<String, dynamic> get payload => throw _privateConstructorUsedError;
  DateTime get receivedAt => throw _privateConstructorUsedError;
  bool get isProcessed => throw _privateConstructorUsedError;
  DateTime? get processedAt => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;

  /// Serializes this PaymentWebhook to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PaymentWebhook
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PaymentWebhookCopyWith<PaymentWebhook> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentWebhookCopyWith<$Res> {
  factory $PaymentWebhookCopyWith(
    PaymentWebhook value,
    $Res Function(PaymentWebhook) then,
  ) = _$PaymentWebhookCopyWithImpl<$Res, PaymentWebhook>;
  @useResult
  $Res call({
    String id,
    String eventType,
    Map<String, dynamic> payload,
    DateTime receivedAt,
    bool isProcessed,
    DateTime? processedAt,
    String? errorMessage,
  });
}

/// @nodoc
class _$PaymentWebhookCopyWithImpl<$Res, $Val extends PaymentWebhook>
    implements $PaymentWebhookCopyWith<$Res> {
  _$PaymentWebhookCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentWebhook
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? eventType = null,
    Object? payload = null,
    Object? receivedAt = null,
    Object? isProcessed = null,
    Object? processedAt = freezed,
    Object? errorMessage = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            eventType: null == eventType
                ? _value.eventType
                : eventType // ignore: cast_nullable_to_non_nullable
                      as String,
            payload: null == payload
                ? _value.payload
                : payload // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            receivedAt: null == receivedAt
                ? _value.receivedAt
                : receivedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            isProcessed: null == isProcessed
                ? _value.isProcessed
                : isProcessed // ignore: cast_nullable_to_non_nullable
                      as bool,
            processedAt: freezed == processedAt
                ? _value.processedAt
                : processedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            errorMessage: freezed == errorMessage
                ? _value.errorMessage
                : errorMessage // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PaymentWebhookImplCopyWith<$Res>
    implements $PaymentWebhookCopyWith<$Res> {
  factory _$$PaymentWebhookImplCopyWith(
    _$PaymentWebhookImpl value,
    $Res Function(_$PaymentWebhookImpl) then,
  ) = __$$PaymentWebhookImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String eventType,
    Map<String, dynamic> payload,
    DateTime receivedAt,
    bool isProcessed,
    DateTime? processedAt,
    String? errorMessage,
  });
}

/// @nodoc
class __$$PaymentWebhookImplCopyWithImpl<$Res>
    extends _$PaymentWebhookCopyWithImpl<$Res, _$PaymentWebhookImpl>
    implements _$$PaymentWebhookImplCopyWith<$Res> {
  __$$PaymentWebhookImplCopyWithImpl(
    _$PaymentWebhookImpl _value,
    $Res Function(_$PaymentWebhookImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PaymentWebhook
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? eventType = null,
    Object? payload = null,
    Object? receivedAt = null,
    Object? isProcessed = null,
    Object? processedAt = freezed,
    Object? errorMessage = freezed,
  }) {
    return _then(
      _$PaymentWebhookImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        eventType: null == eventType
            ? _value.eventType
            : eventType // ignore: cast_nullable_to_non_nullable
                  as String,
        payload: null == payload
            ? _value._payload
            : payload // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        receivedAt: null == receivedAt
            ? _value.receivedAt
            : receivedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        isProcessed: null == isProcessed
            ? _value.isProcessed
            : isProcessed // ignore: cast_nullable_to_non_nullable
                  as bool,
        processedAt: freezed == processedAt
            ? _value.processedAt
            : processedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        errorMessage: freezed == errorMessage
            ? _value.errorMessage
            : errorMessage // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PaymentWebhookImpl implements _PaymentWebhook {
  const _$PaymentWebhookImpl({
    required this.id,
    required this.eventType,
    required final Map<String, dynamic> payload,
    required this.receivedAt,
    required this.isProcessed,
    this.processedAt,
    this.errorMessage,
  }) : _payload = payload;

  factory _$PaymentWebhookImpl.fromJson(Map<String, dynamic> json) =>
      _$$PaymentWebhookImplFromJson(json);

  @override
  final String id;
  @override
  final String eventType;
  final Map<String, dynamic> _payload;
  @override
  Map<String, dynamic> get payload {
    if (_payload is EqualUnmodifiableMapView) return _payload;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_payload);
  }

  @override
  final DateTime receivedAt;
  @override
  final bool isProcessed;
  @override
  final DateTime? processedAt;
  @override
  final String? errorMessage;

  @override
  String toString() {
    return 'PaymentWebhook(id: $id, eventType: $eventType, payload: $payload, receivedAt: $receivedAt, isProcessed: $isProcessed, processedAt: $processedAt, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentWebhookImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.eventType, eventType) ||
                other.eventType == eventType) &&
            const DeepCollectionEquality().equals(other._payload, _payload) &&
            (identical(other.receivedAt, receivedAt) ||
                other.receivedAt == receivedAt) &&
            (identical(other.isProcessed, isProcessed) ||
                other.isProcessed == isProcessed) &&
            (identical(other.processedAt, processedAt) ||
                other.processedAt == processedAt) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    eventType,
    const DeepCollectionEquality().hash(_payload),
    receivedAt,
    isProcessed,
    processedAt,
    errorMessage,
  );

  /// Create a copy of PaymentWebhook
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentWebhookImplCopyWith<_$PaymentWebhookImpl> get copyWith =>
      __$$PaymentWebhookImplCopyWithImpl<_$PaymentWebhookImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$PaymentWebhookImplToJson(this);
  }
}

abstract class _PaymentWebhook implements PaymentWebhook {
  const factory _PaymentWebhook({
    required final String id,
    required final String eventType,
    required final Map<String, dynamic> payload,
    required final DateTime receivedAt,
    required final bool isProcessed,
    final DateTime? processedAt,
    final String? errorMessage,
  }) = _$PaymentWebhookImpl;

  factory _PaymentWebhook.fromJson(Map<String, dynamic> json) =
      _$PaymentWebhookImpl.fromJson;

  @override
  String get id;
  @override
  String get eventType;
  @override
  Map<String, dynamic> get payload;
  @override
  DateTime get receivedAt;
  @override
  bool get isProcessed;
  @override
  DateTime? get processedAt;
  @override
  String? get errorMessage;

  /// Create a copy of PaymentWebhook
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentWebhookImplCopyWith<_$PaymentWebhookImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PaymentRefund _$PaymentRefundFromJson(Map<String, dynamic> json) {
  return _PaymentRefund.fromJson(json);
}

/// @nodoc
mixin _$PaymentRefund {
  String get id => throw _privateConstructorUsedError;
  String get transactionId => throw _privateConstructorUsedError;
  double get amount => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  RefundStatus get status => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime? get processedAt => throw _privateConstructorUsedError;
  String? get reason => throw _privateConstructorUsedError;
  String? get externalRefundId => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this PaymentRefund to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PaymentRefund
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PaymentRefundCopyWith<PaymentRefund> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentRefundCopyWith<$Res> {
  factory $PaymentRefundCopyWith(
    PaymentRefund value,
    $Res Function(PaymentRefund) then,
  ) = _$PaymentRefundCopyWithImpl<$Res, PaymentRefund>;
  @useResult
  $Res call({
    String id,
    String transactionId,
    double amount,
    String currency,
    RefundStatus status,
    DateTime createdAt,
    DateTime? processedAt,
    String? reason,
    String? externalRefundId,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class _$PaymentRefundCopyWithImpl<$Res, $Val extends PaymentRefund>
    implements $PaymentRefundCopyWith<$Res> {
  _$PaymentRefundCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentRefund
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? transactionId = null,
    Object? amount = null,
    Object? currency = null,
    Object? status = null,
    Object? createdAt = null,
    Object? processedAt = freezed,
    Object? reason = freezed,
    Object? externalRefundId = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            transactionId: null == transactionId
                ? _value.transactionId
                : transactionId // ignore: cast_nullable_to_non_nullable
                      as String,
            amount: null == amount
                ? _value.amount
                : amount // ignore: cast_nullable_to_non_nullable
                      as double,
            currency: null == currency
                ? _value.currency
                : currency // ignore: cast_nullable_to_non_nullable
                      as String,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as RefundStatus,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            processedAt: freezed == processedAt
                ? _value.processedAt
                : processedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            reason: freezed == reason
                ? _value.reason
                : reason // ignore: cast_nullable_to_non_nullable
                      as String?,
            externalRefundId: freezed == externalRefundId
                ? _value.externalRefundId
                : externalRefundId // ignore: cast_nullable_to_non_nullable
                      as String?,
            metadata: freezed == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PaymentRefundImplCopyWith<$Res>
    implements $PaymentRefundCopyWith<$Res> {
  factory _$$PaymentRefundImplCopyWith(
    _$PaymentRefundImpl value,
    $Res Function(_$PaymentRefundImpl) then,
  ) = __$$PaymentRefundImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String transactionId,
    double amount,
    String currency,
    RefundStatus status,
    DateTime createdAt,
    DateTime? processedAt,
    String? reason,
    String? externalRefundId,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class __$$PaymentRefundImplCopyWithImpl<$Res>
    extends _$PaymentRefundCopyWithImpl<$Res, _$PaymentRefundImpl>
    implements _$$PaymentRefundImplCopyWith<$Res> {
  __$$PaymentRefundImplCopyWithImpl(
    _$PaymentRefundImpl _value,
    $Res Function(_$PaymentRefundImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PaymentRefund
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? transactionId = null,
    Object? amount = null,
    Object? currency = null,
    Object? status = null,
    Object? createdAt = null,
    Object? processedAt = freezed,
    Object? reason = freezed,
    Object? externalRefundId = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _$PaymentRefundImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        transactionId: null == transactionId
            ? _value.transactionId
            : transactionId // ignore: cast_nullable_to_non_nullable
                  as String,
        amount: null == amount
            ? _value.amount
            : amount // ignore: cast_nullable_to_non_nullable
                  as double,
        currency: null == currency
            ? _value.currency
            : currency // ignore: cast_nullable_to_non_nullable
                  as String,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as RefundStatus,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        processedAt: freezed == processedAt
            ? _value.processedAt
            : processedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        reason: freezed == reason
            ? _value.reason
            : reason // ignore: cast_nullable_to_non_nullable
                  as String?,
        externalRefundId: freezed == externalRefundId
            ? _value.externalRefundId
            : externalRefundId // ignore: cast_nullable_to_non_nullable
                  as String?,
        metadata: freezed == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PaymentRefundImpl implements _PaymentRefund {
  const _$PaymentRefundImpl({
    required this.id,
    required this.transactionId,
    required this.amount,
    required this.currency,
    required this.status,
    required this.createdAt,
    this.processedAt,
    this.reason,
    this.externalRefundId,
    final Map<String, dynamic>? metadata,
  }) : _metadata = metadata;

  factory _$PaymentRefundImpl.fromJson(Map<String, dynamic> json) =>
      _$$PaymentRefundImplFromJson(json);

  @override
  final String id;
  @override
  final String transactionId;
  @override
  final double amount;
  @override
  final String currency;
  @override
  final RefundStatus status;
  @override
  final DateTime createdAt;
  @override
  final DateTime? processedAt;
  @override
  final String? reason;
  @override
  final String? externalRefundId;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'PaymentRefund(id: $id, transactionId: $transactionId, amount: $amount, currency: $currency, status: $status, createdAt: $createdAt, processedAt: $processedAt, reason: $reason, externalRefundId: $externalRefundId, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentRefundImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.transactionId, transactionId) ||
                other.transactionId == transactionId) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.processedAt, processedAt) ||
                other.processedAt == processedAt) &&
            (identical(other.reason, reason) || other.reason == reason) &&
            (identical(other.externalRefundId, externalRefundId) ||
                other.externalRefundId == externalRefundId) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    transactionId,
    amount,
    currency,
    status,
    createdAt,
    processedAt,
    reason,
    externalRefundId,
    const DeepCollectionEquality().hash(_metadata),
  );

  /// Create a copy of PaymentRefund
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentRefundImplCopyWith<_$PaymentRefundImpl> get copyWith =>
      __$$PaymentRefundImplCopyWithImpl<_$PaymentRefundImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PaymentRefundImplToJson(this);
  }
}

abstract class _PaymentRefund implements PaymentRefund {
  const factory _PaymentRefund({
    required final String id,
    required final String transactionId,
    required final double amount,
    required final String currency,
    required final RefundStatus status,
    required final DateTime createdAt,
    final DateTime? processedAt,
    final String? reason,
    final String? externalRefundId,
    final Map<String, dynamic>? metadata,
  }) = _$PaymentRefundImpl;

  factory _PaymentRefund.fromJson(Map<String, dynamic> json) =
      _$PaymentRefundImpl.fromJson;

  @override
  String get id;
  @override
  String get transactionId;
  @override
  double get amount;
  @override
  String get currency;
  @override
  RefundStatus get status;
  @override
  DateTime get createdAt;
  @override
  DateTime? get processedAt;
  @override
  String? get reason;
  @override
  String? get externalRefundId;
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of PaymentRefund
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentRefundImplCopyWith<_$PaymentRefundImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PaymentRequest _$PaymentRequestFromJson(Map<String, dynamic> json) {
  return _PaymentRequest.fromJson(json);
}

/// @nodoc
mixin _$PaymentRequest {
  String get id => throw _privateConstructorUsedError;
  String get requesterId => throw _privateConstructorUsedError;
  String get recipientId => throw _privateConstructorUsedError;
  double get amount => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  PaymentRequestStatus get status => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime? get dueDate => throw _privateConstructorUsedError;
  DateTime? get paidAt => throw _privateConstructorUsedError;
  String? get paymentMethodId => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this PaymentRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PaymentRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PaymentRequestCopyWith<PaymentRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentRequestCopyWith<$Res> {
  factory $PaymentRequestCopyWith(
    PaymentRequest value,
    $Res Function(PaymentRequest) then,
  ) = _$PaymentRequestCopyWithImpl<$Res, PaymentRequest>;
  @useResult
  $Res call({
    String id,
    String requesterId,
    String recipientId,
    double amount,
    String currency,
    String description,
    PaymentRequestStatus status,
    DateTime createdAt,
    DateTime? dueDate,
    DateTime? paidAt,
    String? paymentMethodId,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class _$PaymentRequestCopyWithImpl<$Res, $Val extends PaymentRequest>
    implements $PaymentRequestCopyWith<$Res> {
  _$PaymentRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? requesterId = null,
    Object? recipientId = null,
    Object? amount = null,
    Object? currency = null,
    Object? description = null,
    Object? status = null,
    Object? createdAt = null,
    Object? dueDate = freezed,
    Object? paidAt = freezed,
    Object? paymentMethodId = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            requesterId: null == requesterId
                ? _value.requesterId
                : requesterId // ignore: cast_nullable_to_non_nullable
                      as String,
            recipientId: null == recipientId
                ? _value.recipientId
                : recipientId // ignore: cast_nullable_to_non_nullable
                      as String,
            amount: null == amount
                ? _value.amount
                : amount // ignore: cast_nullable_to_non_nullable
                      as double,
            currency: null == currency
                ? _value.currency
                : currency // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as PaymentRequestStatus,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            dueDate: freezed == dueDate
                ? _value.dueDate
                : dueDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            paidAt: freezed == paidAt
                ? _value.paidAt
                : paidAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            paymentMethodId: freezed == paymentMethodId
                ? _value.paymentMethodId
                : paymentMethodId // ignore: cast_nullable_to_non_nullable
                      as String?,
            metadata: freezed == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PaymentRequestImplCopyWith<$Res>
    implements $PaymentRequestCopyWith<$Res> {
  factory _$$PaymentRequestImplCopyWith(
    _$PaymentRequestImpl value,
    $Res Function(_$PaymentRequestImpl) then,
  ) = __$$PaymentRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String requesterId,
    String recipientId,
    double amount,
    String currency,
    String description,
    PaymentRequestStatus status,
    DateTime createdAt,
    DateTime? dueDate,
    DateTime? paidAt,
    String? paymentMethodId,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class __$$PaymentRequestImplCopyWithImpl<$Res>
    extends _$PaymentRequestCopyWithImpl<$Res, _$PaymentRequestImpl>
    implements _$$PaymentRequestImplCopyWith<$Res> {
  __$$PaymentRequestImplCopyWithImpl(
    _$PaymentRequestImpl _value,
    $Res Function(_$PaymentRequestImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PaymentRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? requesterId = null,
    Object? recipientId = null,
    Object? amount = null,
    Object? currency = null,
    Object? description = null,
    Object? status = null,
    Object? createdAt = null,
    Object? dueDate = freezed,
    Object? paidAt = freezed,
    Object? paymentMethodId = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _$PaymentRequestImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        requesterId: null == requesterId
            ? _value.requesterId
            : requesterId // ignore: cast_nullable_to_non_nullable
                  as String,
        recipientId: null == recipientId
            ? _value.recipientId
            : recipientId // ignore: cast_nullable_to_non_nullable
                  as String,
        amount: null == amount
            ? _value.amount
            : amount // ignore: cast_nullable_to_non_nullable
                  as double,
        currency: null == currency
            ? _value.currency
            : currency // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as PaymentRequestStatus,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        dueDate: freezed == dueDate
            ? _value.dueDate
            : dueDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        paidAt: freezed == paidAt
            ? _value.paidAt
            : paidAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        paymentMethodId: freezed == paymentMethodId
            ? _value.paymentMethodId
            : paymentMethodId // ignore: cast_nullable_to_non_nullable
                  as String?,
        metadata: freezed == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PaymentRequestImpl implements _PaymentRequest {
  const _$PaymentRequestImpl({
    required this.id,
    required this.requesterId,
    required this.recipientId,
    required this.amount,
    required this.currency,
    required this.description,
    required this.status,
    required this.createdAt,
    this.dueDate,
    this.paidAt,
    this.paymentMethodId,
    final Map<String, dynamic>? metadata,
  }) : _metadata = metadata;

  factory _$PaymentRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$PaymentRequestImplFromJson(json);

  @override
  final String id;
  @override
  final String requesterId;
  @override
  final String recipientId;
  @override
  final double amount;
  @override
  final String currency;
  @override
  final String description;
  @override
  final PaymentRequestStatus status;
  @override
  final DateTime createdAt;
  @override
  final DateTime? dueDate;
  @override
  final DateTime? paidAt;
  @override
  final String? paymentMethodId;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'PaymentRequest(id: $id, requesterId: $requesterId, recipientId: $recipientId, amount: $amount, currency: $currency, description: $description, status: $status, createdAt: $createdAt, dueDate: $dueDate, paidAt: $paidAt, paymentMethodId: $paymentMethodId, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentRequestImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.requesterId, requesterId) ||
                other.requesterId == requesterId) &&
            (identical(other.recipientId, recipientId) ||
                other.recipientId == recipientId) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.dueDate, dueDate) || other.dueDate == dueDate) &&
            (identical(other.paidAt, paidAt) || other.paidAt == paidAt) &&
            (identical(other.paymentMethodId, paymentMethodId) ||
                other.paymentMethodId == paymentMethodId) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    requesterId,
    recipientId,
    amount,
    currency,
    description,
    status,
    createdAt,
    dueDate,
    paidAt,
    paymentMethodId,
    const DeepCollectionEquality().hash(_metadata),
  );

  /// Create a copy of PaymentRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentRequestImplCopyWith<_$PaymentRequestImpl> get copyWith =>
      __$$PaymentRequestImplCopyWithImpl<_$PaymentRequestImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$PaymentRequestImplToJson(this);
  }
}

abstract class _PaymentRequest implements PaymentRequest {
  const factory _PaymentRequest({
    required final String id,
    required final String requesterId,
    required final String recipientId,
    required final double amount,
    required final String currency,
    required final String description,
    required final PaymentRequestStatus status,
    required final DateTime createdAt,
    final DateTime? dueDate,
    final DateTime? paidAt,
    final String? paymentMethodId,
    final Map<String, dynamic>? metadata,
  }) = _$PaymentRequestImpl;

  factory _PaymentRequest.fromJson(Map<String, dynamic> json) =
      _$PaymentRequestImpl.fromJson;

  @override
  String get id;
  @override
  String get requesterId;
  @override
  String get recipientId;
  @override
  double get amount;
  @override
  String get currency;
  @override
  String get description;
  @override
  PaymentRequestStatus get status;
  @override
  DateTime get createdAt;
  @override
  DateTime? get dueDate;
  @override
  DateTime? get paidAt;
  @override
  String? get paymentMethodId;
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of PaymentRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentRequestImplCopyWith<_$PaymentRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SplitPayment _$SplitPaymentFromJson(Map<String, dynamic> json) {
  return _SplitPayment.fromJson(json);
}

/// @nodoc
mixin _$SplitPayment {
  String get id => throw _privateConstructorUsedError;
  String get organizerId => throw _privateConstructorUsedError;
  double get totalAmount => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  SplitPaymentStatus get status => throw _privateConstructorUsedError;
  List<SplitPaymentMember> get members => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime? get completedAt => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this SplitPayment to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SplitPayment
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SplitPaymentCopyWith<SplitPayment> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SplitPaymentCopyWith<$Res> {
  factory $SplitPaymentCopyWith(
    SplitPayment value,
    $Res Function(SplitPayment) then,
  ) = _$SplitPaymentCopyWithImpl<$Res, SplitPayment>;
  @useResult
  $Res call({
    String id,
    String organizerId,
    double totalAmount,
    String currency,
    SplitPaymentStatus status,
    List<SplitPaymentMember> members,
    DateTime createdAt,
    DateTime? completedAt,
    String? description,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class _$SplitPaymentCopyWithImpl<$Res, $Val extends SplitPayment>
    implements $SplitPaymentCopyWith<$Res> {
  _$SplitPaymentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SplitPayment
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? organizerId = null,
    Object? totalAmount = null,
    Object? currency = null,
    Object? status = null,
    Object? members = null,
    Object? createdAt = null,
    Object? completedAt = freezed,
    Object? description = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            organizerId: null == organizerId
                ? _value.organizerId
                : organizerId // ignore: cast_nullable_to_non_nullable
                      as String,
            totalAmount: null == totalAmount
                ? _value.totalAmount
                : totalAmount // ignore: cast_nullable_to_non_nullable
                      as double,
            currency: null == currency
                ? _value.currency
                : currency // ignore: cast_nullable_to_non_nullable
                      as String,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as SplitPaymentStatus,
            members: null == members
                ? _value.members
                : members // ignore: cast_nullable_to_non_nullable
                      as List<SplitPaymentMember>,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            completedAt: freezed == completedAt
                ? _value.completedAt
                : completedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            metadata: freezed == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$SplitPaymentImplCopyWith<$Res>
    implements $SplitPaymentCopyWith<$Res> {
  factory _$$SplitPaymentImplCopyWith(
    _$SplitPaymentImpl value,
    $Res Function(_$SplitPaymentImpl) then,
  ) = __$$SplitPaymentImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String organizerId,
    double totalAmount,
    String currency,
    SplitPaymentStatus status,
    List<SplitPaymentMember> members,
    DateTime createdAt,
    DateTime? completedAt,
    String? description,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class __$$SplitPaymentImplCopyWithImpl<$Res>
    extends _$SplitPaymentCopyWithImpl<$Res, _$SplitPaymentImpl>
    implements _$$SplitPaymentImplCopyWith<$Res> {
  __$$SplitPaymentImplCopyWithImpl(
    _$SplitPaymentImpl _value,
    $Res Function(_$SplitPaymentImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of SplitPayment
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? organizerId = null,
    Object? totalAmount = null,
    Object? currency = null,
    Object? status = null,
    Object? members = null,
    Object? createdAt = null,
    Object? completedAt = freezed,
    Object? description = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _$SplitPaymentImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        organizerId: null == organizerId
            ? _value.organizerId
            : organizerId // ignore: cast_nullable_to_non_nullable
                  as String,
        totalAmount: null == totalAmount
            ? _value.totalAmount
            : totalAmount // ignore: cast_nullable_to_non_nullable
                  as double,
        currency: null == currency
            ? _value.currency
            : currency // ignore: cast_nullable_to_non_nullable
                  as String,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as SplitPaymentStatus,
        members: null == members
            ? _value._members
            : members // ignore: cast_nullable_to_non_nullable
                  as List<SplitPaymentMember>,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        completedAt: freezed == completedAt
            ? _value.completedAt
            : completedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        metadata: freezed == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$SplitPaymentImpl implements _SplitPayment {
  const _$SplitPaymentImpl({
    required this.id,
    required this.organizerId,
    required this.totalAmount,
    required this.currency,
    required this.status,
    required final List<SplitPaymentMember> members,
    required this.createdAt,
    this.completedAt,
    this.description,
    final Map<String, dynamic>? metadata,
  }) : _members = members,
       _metadata = metadata;

  factory _$SplitPaymentImpl.fromJson(Map<String, dynamic> json) =>
      _$$SplitPaymentImplFromJson(json);

  @override
  final String id;
  @override
  final String organizerId;
  @override
  final double totalAmount;
  @override
  final String currency;
  @override
  final SplitPaymentStatus status;
  final List<SplitPaymentMember> _members;
  @override
  List<SplitPaymentMember> get members {
    if (_members is EqualUnmodifiableListView) return _members;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_members);
  }

  @override
  final DateTime createdAt;
  @override
  final DateTime? completedAt;
  @override
  final String? description;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'SplitPayment(id: $id, organizerId: $organizerId, totalAmount: $totalAmount, currency: $currency, status: $status, members: $members, createdAt: $createdAt, completedAt: $completedAt, description: $description, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SplitPaymentImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.organizerId, organizerId) ||
                other.organizerId == organizerId) &&
            (identical(other.totalAmount, totalAmount) ||
                other.totalAmount == totalAmount) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality().equals(other._members, _members) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.completedAt, completedAt) ||
                other.completedAt == completedAt) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    organizerId,
    totalAmount,
    currency,
    status,
    const DeepCollectionEquality().hash(_members),
    createdAt,
    completedAt,
    description,
    const DeepCollectionEquality().hash(_metadata),
  );

  /// Create a copy of SplitPayment
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SplitPaymentImplCopyWith<_$SplitPaymentImpl> get copyWith =>
      __$$SplitPaymentImplCopyWithImpl<_$SplitPaymentImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SplitPaymentImplToJson(this);
  }
}

abstract class _SplitPayment implements SplitPayment {
  const factory _SplitPayment({
    required final String id,
    required final String organizerId,
    required final double totalAmount,
    required final String currency,
    required final SplitPaymentStatus status,
    required final List<SplitPaymentMember> members,
    required final DateTime createdAt,
    final DateTime? completedAt,
    final String? description,
    final Map<String, dynamic>? metadata,
  }) = _$SplitPaymentImpl;

  factory _SplitPayment.fromJson(Map<String, dynamic> json) =
      _$SplitPaymentImpl.fromJson;

  @override
  String get id;
  @override
  String get organizerId;
  @override
  double get totalAmount;
  @override
  String get currency;
  @override
  SplitPaymentStatus get status;
  @override
  List<SplitPaymentMember> get members;
  @override
  DateTime get createdAt;
  @override
  DateTime? get completedAt;
  @override
  String? get description;
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of SplitPayment
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SplitPaymentImplCopyWith<_$SplitPaymentImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SplitPaymentMember _$SplitPaymentMemberFromJson(Map<String, dynamic> json) {
  return _SplitPaymentMember.fromJson(json);
}

/// @nodoc
mixin _$SplitPaymentMember {
  String get userId => throw _privateConstructorUsedError;
  double get amount => throw _privateConstructorUsedError;
  SplitPaymentMemberStatus get status => throw _privateConstructorUsedError;
  DateTime? get paidAt => throw _privateConstructorUsedError;
  String? get paymentMethodId => throw _privateConstructorUsedError;

  /// Serializes this SplitPaymentMember to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SplitPaymentMember
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SplitPaymentMemberCopyWith<SplitPaymentMember> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SplitPaymentMemberCopyWith<$Res> {
  factory $SplitPaymentMemberCopyWith(
    SplitPaymentMember value,
    $Res Function(SplitPaymentMember) then,
  ) = _$SplitPaymentMemberCopyWithImpl<$Res, SplitPaymentMember>;
  @useResult
  $Res call({
    String userId,
    double amount,
    SplitPaymentMemberStatus status,
    DateTime? paidAt,
    String? paymentMethodId,
  });
}

/// @nodoc
class _$SplitPaymentMemberCopyWithImpl<$Res, $Val extends SplitPaymentMember>
    implements $SplitPaymentMemberCopyWith<$Res> {
  _$SplitPaymentMemberCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SplitPaymentMember
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? amount = null,
    Object? status = null,
    Object? paidAt = freezed,
    Object? paymentMethodId = freezed,
  }) {
    return _then(
      _value.copyWith(
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            amount: null == amount
                ? _value.amount
                : amount // ignore: cast_nullable_to_non_nullable
                      as double,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as SplitPaymentMemberStatus,
            paidAt: freezed == paidAt
                ? _value.paidAt
                : paidAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            paymentMethodId: freezed == paymentMethodId
                ? _value.paymentMethodId
                : paymentMethodId // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$SplitPaymentMemberImplCopyWith<$Res>
    implements $SplitPaymentMemberCopyWith<$Res> {
  factory _$$SplitPaymentMemberImplCopyWith(
    _$SplitPaymentMemberImpl value,
    $Res Function(_$SplitPaymentMemberImpl) then,
  ) = __$$SplitPaymentMemberImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String userId,
    double amount,
    SplitPaymentMemberStatus status,
    DateTime? paidAt,
    String? paymentMethodId,
  });
}

/// @nodoc
class __$$SplitPaymentMemberImplCopyWithImpl<$Res>
    extends _$SplitPaymentMemberCopyWithImpl<$Res, _$SplitPaymentMemberImpl>
    implements _$$SplitPaymentMemberImplCopyWith<$Res> {
  __$$SplitPaymentMemberImplCopyWithImpl(
    _$SplitPaymentMemberImpl _value,
    $Res Function(_$SplitPaymentMemberImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of SplitPaymentMember
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? amount = null,
    Object? status = null,
    Object? paidAt = freezed,
    Object? paymentMethodId = freezed,
  }) {
    return _then(
      _$SplitPaymentMemberImpl(
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        amount: null == amount
            ? _value.amount
            : amount // ignore: cast_nullable_to_non_nullable
                  as double,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as SplitPaymentMemberStatus,
        paidAt: freezed == paidAt
            ? _value.paidAt
            : paidAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        paymentMethodId: freezed == paymentMethodId
            ? _value.paymentMethodId
            : paymentMethodId // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$SplitPaymentMemberImpl implements _SplitPaymentMember {
  const _$SplitPaymentMemberImpl({
    required this.userId,
    required this.amount,
    required this.status,
    this.paidAt,
    this.paymentMethodId,
  });

  factory _$SplitPaymentMemberImpl.fromJson(Map<String, dynamic> json) =>
      _$$SplitPaymentMemberImplFromJson(json);

  @override
  final String userId;
  @override
  final double amount;
  @override
  final SplitPaymentMemberStatus status;
  @override
  final DateTime? paidAt;
  @override
  final String? paymentMethodId;

  @override
  String toString() {
    return 'SplitPaymentMember(userId: $userId, amount: $amount, status: $status, paidAt: $paidAt, paymentMethodId: $paymentMethodId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SplitPaymentMemberImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.paidAt, paidAt) || other.paidAt == paidAt) &&
            (identical(other.paymentMethodId, paymentMethodId) ||
                other.paymentMethodId == paymentMethodId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, userId, amount, status, paidAt, paymentMethodId);

  /// Create a copy of SplitPaymentMember
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SplitPaymentMemberImplCopyWith<_$SplitPaymentMemberImpl> get copyWith =>
      __$$SplitPaymentMemberImplCopyWithImpl<_$SplitPaymentMemberImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$SplitPaymentMemberImplToJson(this);
  }
}

abstract class _SplitPaymentMember implements SplitPaymentMember {
  const factory _SplitPaymentMember({
    required final String userId,
    required final double amount,
    required final SplitPaymentMemberStatus status,
    final DateTime? paidAt,
    final String? paymentMethodId,
  }) = _$SplitPaymentMemberImpl;

  factory _SplitPaymentMember.fromJson(Map<String, dynamic> json) =
      _$SplitPaymentMemberImpl.fromJson;

  @override
  String get userId;
  @override
  double get amount;
  @override
  SplitPaymentMemberStatus get status;
  @override
  DateTime? get paidAt;
  @override
  String? get paymentMethodId;

  /// Create a copy of SplitPaymentMember
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SplitPaymentMemberImplCopyWith<_$SplitPaymentMemberImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
