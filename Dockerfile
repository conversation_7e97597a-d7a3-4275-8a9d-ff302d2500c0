# Multi-stage Dockerfile for Billionaires Social Flutter App
# Supports both development and production environments with n8n integration

# =============================================================================
# Base Stage - Common dependencies and tools
# =============================================================================
FROM ubuntu:22.04 AS base

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Set timezone
ENV TZ=UTC
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    unzip \
    xz-utils \
    zip \
    libglu1-mesa \
    openjdk-17-jdk \
    wget \
    gnupg \
    lsb-release \
    software-properties-common \
    build-essential \
    libssl-dev \
    clang \
    cmake \
    ninja-build \
    pkg-config \
    libgtk-3-dev \
    liblzma-dev \
    libstdc++6 \
    fonts-droid-fallback \
    ttf-wqy-zenhei \
    # Audio/Video processing dependencies
    ffmpeg \
    libavcodec-dev \
    libavformat-dev \
    libavutil-dev \
    libswscale-dev \
    libswresample-dev \
    # Image processing
    libvips-dev \
    imagemagick \
    # Additional Flutter web dependencies
    chromium-browser \
    xvfb \
    # Network tools
    netcat-openbsd \
    iputils-ping \
    # File system tools
    rsync \
    && rm -rf /var/lib/apt/lists/*

# Set JAVA_HOME
ENV JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64
ENV PATH=$PATH:$JAVA_HOME/bin

# Create app directory
WORKDIR /app

# =============================================================================
# Flutter SDK Stage
# =============================================================================
FROM base AS flutter-sdk

# Flutter version - update as needed
ENV FLUTTER_VERSION=3.24.5
ENV FLUTTER_HOME=/opt/flutter
ENV PATH=$PATH:$FLUTTER_HOME/bin

# Download and install Flutter SDK
RUN git clone https://github.com/flutter/flutter.git -b stable $FLUTTER_HOME \
    && cd $FLUTTER_HOME \
    && git checkout $FLUTTER_VERSION \
    && flutter precache \
    && flutter config --no-analytics \
    && flutter doctor

# =============================================================================
# Android SDK Stage
# =============================================================================
FROM flutter-sdk AS android-sdk

# Android SDK environment variables
ENV ANDROID_HOME=/opt/android-sdk
ENV ANDROID_SDK_ROOT=$ANDROID_HOME
ENV PATH=$PATH:$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools:$ANDROID_HOME/build-tools/34.0.0

# Create Android SDK directory
RUN mkdir -p $ANDROID_HOME/cmdline-tools

# Download and install Android command line tools
RUN wget -q https://dl.google.com/android/repository/commandlinetools-linux-11076708_latest.zip -O /tmp/cmdline-tools.zip \
    && unzip -q /tmp/cmdline-tools.zip -d $ANDROID_HOME/cmdline-tools \
    && mv $ANDROID_HOME/cmdline-tools/cmdline-tools $ANDROID_HOME/cmdline-tools/latest \
    && rm /tmp/cmdline-tools.zip

# Accept Android licenses and install required packages
RUN yes | sdkmanager --licenses \
    && sdkmanager "platform-tools" \
    && sdkmanager "platforms;android-34" \
    && sdkmanager "build-tools;34.0.0" \
    && sdkmanager "sources;android-34" \
    && sdkmanager "system-images;android-34;google_apis;x86_64"

# =============================================================================
# Node.js Stage (for Firebase Functions and n8n integration)
# =============================================================================
FROM android-sdk AS nodejs

# Install Node.js 22 (required for Firebase Functions)
RUN curl -fsSL https://deb.nodesource.com/setup_22.x | bash - \
    && apt-get install -y nodejs

# Install global npm packages
RUN npm install -g \
    firebase-tools@latest \
    @angular/cli \
    typescript

# Verify installations
RUN node --version && npm --version && firebase --version

# =============================================================================
# Development Stage
# =============================================================================
FROM nodejs AS development

# Set development environment
ENV FLUTTER_ENV=development
ENV NODE_ENV=development

# Install additional development tools
RUN apt-get update && apt-get install -y \
    vim \
    nano \
    htop \
    tree \
    jq \
    && rm -rf /var/lib/apt/lists/*

# Copy pubspec files first for better caching
COPY pubspec.yaml pubspec.lock ./

# Get Flutter dependencies
RUN flutter pub get

# Copy Firebase Functions package files
COPY functions/package*.json ./functions/
WORKDIR /app/functions
RUN npm ci
WORKDIR /app

# Copy the rest of the application
COPY . .

# Generate code and build runner
RUN flutter pub get \
    && flutter pub run build_runner build --delete-conflicting-outputs

# Expose ports for development
EXPOSE 3000 5000 5001 8080 8085 9099

# Set up Chrome for Flutter web development
ENV CHROME_EXECUTABLE=/usr/bin/chromium-browser
ENV DISPLAY=:99

# Development command with proper web server setup
CMD ["sh", "-c", "Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 & flutter run -d web-server --web-hostname 0.0.0.0 --web-port 3000"]

# =============================================================================
# Build Stage
# =============================================================================
FROM nodejs AS build

# Set production environment
ENV FLUTTER_ENV=production
ENV NODE_ENV=production

# Copy pubspec files
COPY pubspec.yaml pubspec.lock ./

# Get Flutter dependencies
RUN flutter pub get

# Copy Firebase Functions package files
COPY functions/package*.json ./functions/
WORKDIR /app/functions
RUN npm ci --only=production
WORKDIR /app

# Copy source code
COPY . .

# Ensure assets directory exists and copy assets
RUN mkdir -p assets && \
    if [ -d "app_store_assets" ]; then \
        cp -r app_store_assets/* assets/ 2>/dev/null || true; \
    fi

# Generate code and assets
RUN flutter pub get \
    && flutter pub run build_runner build --delete-conflicting-outputs \
    && flutter pub run flutter_launcher_icons:main

# Build Firebase Functions
WORKDIR /app/functions
RUN npm run build
WORKDIR /app

# Build Flutter web app
RUN flutter build web --release --web-renderer html

# Build Android APK (optional, for testing)
RUN flutter build apk --release --split-per-abi

# =============================================================================
# Production Stage
# =============================================================================
FROM nginx:alpine AS production

# Install Node.js for Firebase Functions
RUN apk add --no-cache nodejs npm

# Copy built web app
COPY --from=build /app/build/web /usr/share/nginx/html

# Copy Firebase Functions
COPY --from=build /app/functions/lib /app/functions/lib
COPY --from=build /app/functions/package*.json /app/functions/
COPY --from=build /app/functions/node_modules /app/functions/node_modules

# Copy Firebase configuration
COPY --from=build /app/firebase.json /app/
COPY --from=build /app/firestore.rules /app/
COPY --from=build /app/firestore.indexes.json /app/
COPY --from=build /app/storage.rules /app/

# Copy nginx configuration
COPY docker/nginx/nginx.conf /etc/nginx/nginx.conf

# Copy startup script
COPY docker/scripts/start.sh /start.sh
RUN chmod +x /start.sh

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# Start services
CMD ["/start.sh"]
