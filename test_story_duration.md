# Testing Story Duration Functionality

## How to Verify 6-Hour Story Duration is Working

### **Method 1: Check Console Logs**

1. **Set Story Duration to 6 Hours:**
   - Open your app
   - Go to Story Settings
   - Select "6 hours" from the duration dropdown
   - Save settings

2. **Create a Story:**
   - Create any story (photo, text, etc.)
   - Watch the console/debug output for these logs:

   ```
   🕐 Story Duration Settings:
      User selected: StoryDuration.sixHours
      Converted to: StoryDuration.sixHours
      Duration: 6 hours
      Expires at: [timestamp 6 hours from now]
   ```

   OR

   ```
   🎬 UnifiedStoryEditor Duration Settings:
      User selected: StoryDuration.sixHours
      Converted to: StoryDuration.sixHours
      Duration: 6 hours
   ```

### **Method 2: Check Firebase Database**

1. **Open Firebase Console:**
   - Go to your Firebase project
   - Navigate to Firestore Database
   - Open the `stories` collection

2. **Find Your Story:**
   - Look for the story you just created
   - Check the `expiresAt` field
   - Verify it's 6 hours after `createdAt`

   Example:
   ```
   createdAt: January 25, 2025 at 2:00:00 PM UTC
   expiresAt: January 25, 2025 at 8:00:00 PM UTC  ← Should be 6 hours later
   ```

### **Method 3: Check Story Settings Persistence**

1. **Verify Settings are Saved:**
   - Set duration to 6 hours
   - Close and restart the app
   - Go back to Story Settings
   - Confirm "6 hours" is still selected

2. **Check Firebase Settings:**
   - In Firebase Console, go to `user_settings/{userId}/story_settings/preferences`
   - Verify `duration` field shows `"sixHours"`

### **Method 4: Test Story Expiration**

1. **Create a 6-Hour Story:**
   - Set duration to 6 hours
   - Create a story
   - Note the creation time

2. **Wait and Check:**
   - After 6 hours, the story should automatically expire
   - Check if story is marked as `isExpired: true` in Firebase
   - Story should disappear from the story feed

### **Method 5: Compare Different Durations**

1. **Test All Durations:**
   - Create stories with 6h, 12h, and 24h durations
   - Check their `expiresAt` timestamps in Firebase
   - Verify they expire at the correct times

### **Expected Results:**

✅ **6-Hour Duration Working If:**
- Console logs show "Duration: 6 hours"
- Firebase `expiresAt` is 6 hours after `createdAt`
- Story settings persist after app restart
- Stories expire after exactly 6 hours
- Backend cleanup respects individual expiration times

❌ **Not Working If:**
- Console logs show "Duration: 24 hours" (fallback)
- Firebase `expiresAt` is 24 hours after `createdAt`
- Settings don't persist
- All stories expire after 24 hours regardless of setting

### **Quick Debug Commands:**

If you want to check the current implementation, run these in your Flutter app:

```dart
// Check current settings
final settingsService = getIt<StorySettingsService>();
final settings = await settingsService.getSettings();
print('Current duration setting: ${settings.duration}');

// Check duration mapping
final sharedDuration = _convertToSharedDuration(settings.duration);
final actualDuration = StoryConstants.expirationDurations[sharedDuration];
print('Actual duration: ${actualDuration?.inHours} hours');
```
